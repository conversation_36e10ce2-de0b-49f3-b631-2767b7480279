import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Card,
  Typography,
  Modal,
  Form,
  Row,
  Col,
  message,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬維修記錄數據
const mockRepairData = [
  {
    id: 'R2024001',
    customer: '台積電',
    customerContact: '張經理',
    product: 'MIO-X1000',
    productSerial: 'SN20240001',
    faultDescription: '主板無法啟動，疑似電源模組故障',
    status: 'REPAIRING',
    priority: 'HIGH',
    receivedDate: '2024-07-08',
    estimatedCompletion: '2024-07-15',
    assignedTechnician: '李技師',
    repairCost: 15000,
  },
  {
    id: 'R2024002',
    customer: '聯發科',
    customerContact: '王主任',
    product: 'MIO-S500',
    productSerial: 'SN20240002',
    faultDescription: '顯示器閃爍，可能是背光模組問題',
    status: 'PENDING_TEST',
    priority: 'MEDIUM',
    receivedDate: '2024-07-07',
    estimatedCompletion: '2024-07-12',
    assignedTechnician: '陳技師',
    repairCost: 8000,
  },
  {
    id: 'R2024003',
    customer: '鴻海精密',
    customerContact: '林工程師',
    product: 'MIO-Pro',
    productSerial: 'SN20240003',
    faultDescription: '軟體異常，需要重新安裝系統',
    status: 'COMPLETED',
    priority: 'LOW',
    receivedDate: '2024-07-06',
    estimatedCompletion: '2024-07-10',
    assignedTechnician: '黃技師',
    repairCost: 3000,
  },
];

const RepairManagement: React.FC = () => {
  const [data, setData] = useState(mockRepairData);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();
      company: 'ABC科技有限公司',
      contactPerson: '張經理',
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 2,
      name: '李小姐',
      email: '<EMAIL>',
      phone: '0923-456-789',
      address: '新北市板橋區中山路一段161號',
      company: '',
      contactPerson: '',
      isActive: true,
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-14T14:20:00Z',
    },
    {
      id: 3,
      name: '王先生',
      email: '<EMAIL>',
      phone: '0934-567-890',
      address: '台中市西屯區台灣大道三段99號',
      company: 'XYZ企業股份有限公司',
      contactPerson: '王總監',
      isActive: true,
      createdAt: '2024-01-13T09:15:00Z',
      updatedAt: '2024-01-13T09:15:00Z',
    },
  ]);

  const [products] = useState<Product[]>([
    {
      id: 1,
      name: 'iPhone 14',
      model: 'A2882',
      brand: 'Apple',
      categoryId: 1,
      category: { id: 1, name: '智慧型手機', description: '', isActive: true, createdAt: '', updatedAt: '' },
      description: 'Apple iPhone 14 智慧型手機',
      specifications: '6.1吋螢幕, A15晶片, 128GB儲存空間',
      warrantyPeriod: 12,
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 2,
      name: 'MacBook Pro',
      model: 'MBP-M2-13',
      brand: 'Apple',
      categoryId: 2,
      category: { id: 2, name: '筆記型電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
      description: 'MacBook Pro 13吋 M2晶片',
      specifications: '13.3吋螢幕, M2晶片, 256GB SSD',
      warrantyPeriod: 12,
      isActive: true,
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-14T14:20:00Z',
    },
    {
      id: 3,
      name: 'iPad Air',
      model: 'IPAD-AIR-5',
      brand: 'Apple',
      categoryId: 3,
      category: { id: 3, name: '平板電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
      description: 'iPad Air 第5代',
      specifications: '10.9吋螢幕, M1晶片, 64GB儲存空間',
      warrantyPeriod: 12,
      isActive: true,
      createdAt: '2024-01-13T09:15:00Z',
      updatedAt: '2024-01-13T09:15:00Z',
    },
  ]);

  const [technicians] = useState([
    { id: 3, name: '維修技師', email: '<EMAIL>' },
    { id: 4, name: '資深技師', email: '<EMAIL>' },
    { id: 5, name: '專業技師', email: '<EMAIL>' },
  ]);

  const handleAddRepair = () => {
    setEditingRepair(null);
    setIsFormVisible(true);
  };

  const handleEditRepair = (repair: RepairRecord) => {
    setEditingRepair(repair);
    setIsFormVisible(true);
  };

  const handleViewRepair = (repair: RepairRecord) => {
    setViewingRepair(repair);
    setIsDetailVisible(true);
  };

  const handleStatusUpdate = (repair: RepairRecord) => {
    // 這裡可以開啟狀態更新模態框
    message.info('狀態更新功能開發中');
  };

  const handleFormCancel = () => {
    setIsFormVisible(false);
    setEditingRepair(null);
  };

  const handleDetailCancel = () => {
    setIsDetailVisible(false);
    setViewingRepair(null);
  };

  const handleFormSubmit = async (data: CreateRepairRecordRequest | UpdateRepairRecordRequest) => {
    setFormLoading(true);
    try {
      if (editingRepair) {
        // 更新維修記錄
        // await repairService.updateRepairRecord(editingRepair.id, data as UpdateRepairRecordRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('維修記錄更新成功');
      } else {
        // 新增維修記錄
        // await repairService.createRepairRecord(data as CreateRepairRecordRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('維修記錄新增成功');
      }
      
      setIsFormVisible(false);
      setEditingRepair(null);
      // 這裡可以觸發維修記錄列表重新載入
    } catch (error: any) {
      message.error(error.message || `${editingRepair ? '更新' : '新增'}維修記錄失敗`);
    } finally {
      setFormLoading(false);
    }
  };

  const handleDetailEdit = (repair: RepairRecord) => {
    setIsDetailVisible(false);
    setViewingRepair(null);
    setEditingRepair(repair);
    setIsFormVisible(true);
  };

  const handleDetailStatusUpdate = (repair: RepairRecord) => {
    setIsDetailVisible(false);
    setViewingRepair(null);
    handleStatusUpdate(repair);
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        維修記錄管理
      </Title>

      <RepairList
        onAdd={handleAddRepair}
        onEdit={handleEditRepair}
        onView={handleViewRepair}
        onStatusUpdate={handleStatusUpdate}
      />

      <RepairForm
        visible={isFormVisible}
        repair={editingRepair}
        customers={customers}
        products={products}
        technicians={technicians}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

      <RepairDetail
        visible={isDetailVisible}
        repair={viewingRepair}
        onCancel={handleDetailCancel}
        onEdit={handleDetailEdit}
        onStatusUpdate={handleDetailStatusUpdate}
      />
    </div>
  );
};

export default RepairManagement;
