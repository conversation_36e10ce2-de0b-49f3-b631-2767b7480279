import bcrypt from 'bcrypt';
import { logger } from './logger';

// 密碼配置
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

// 密碼強度要求
export interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxLength: number;
}

// 默認密碼要求
const DEFAULT_PASSWORD_REQUIREMENTS: PasswordRequirements = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxLength: 128,
};

// 密碼驗證結果
export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong' | 'very-strong';
  score: number;
}

// 密碼工具類
export class PasswordUtils {
  // 加密密碼
  static async hashPassword(password: string): Promise<string> {
    try {
      if (!password) {
        throw new Error('密碼不能為空');
      }

      const salt = await bcrypt.genSalt(BCRYPT_ROUNDS);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      logger.debug('密碼加密成功');
      return hashedPassword;
    } catch (error) {
      logger.error('密碼加密失敗:', error);
      throw new Error('密碼加密失敗');
    }
  }

  // 驗證密碼
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      if (!password || !hashedPassword) {
        return false;
      }

      const isMatch = await bcrypt.compare(password, hashedPassword);
      
      if (isMatch) {
        logger.debug('密碼驗證成功');
      } else {
        logger.debug('密碼驗證失敗');
      }
      
      return isMatch;
    } catch (error) {
      logger.error('密碼驗證過程中發生錯誤:', error);
      return false;
    }
  }

  // 驗證密碼強度
  static validatePassword(
    password: string,
    requirements: Partial<PasswordRequirements> = {}
  ): PasswordValidationResult {
    const reqs = { ...DEFAULT_PASSWORD_REQUIREMENTS, ...requirements };
    const errors: string[] = [];
    let score = 0;

    // 檢查密碼是否存在
    if (!password) {
      return {
        isValid: false,
        errors: ['密碼不能為空'],
        strength: 'weak',
        score: 0,
      };
    }

    // 檢查長度
    if (password.length < reqs.minLength) {
      errors.push(`密碼長度至少需要 ${reqs.minLength} 個字符`);
    } else {
      score += 1;
    }

    if (password.length > reqs.maxLength) {
      errors.push(`密碼長度不能超過 ${reqs.maxLength} 個字符`);
    }

    // 檢查大寫字母
    if (reqs.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密碼必須包含至少一個大寫字母');
    } else if (/[A-Z]/.test(password)) {
      score += 1;
    }

    // 檢查小寫字母
    if (reqs.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密碼必須包含至少一個小寫字母');
    } else if (/[a-z]/.test(password)) {
      score += 1;
    }

    // 檢查數字
    if (reqs.requireNumbers && !/\d/.test(password)) {
      errors.push('密碼必須包含至少一個數字');
    } else if (/\d/.test(password)) {
      score += 1;
    }

    // 檢查特殊字符
    if (reqs.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密碼必須包含至少一個特殊字符');
    } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1;
    }

    // 額外的強度檢查
    if (password.length >= 12) {
      score += 1;
    }

    if (password.length >= 16) {
      score += 1;
    }

    // 檢查字符多樣性
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) {
      score += 1;
    }

    // 檢查常見密碼模式
    if (this.isCommonPassword(password)) {
      errors.push('密碼過於常見，請選擇更安全的密碼');
      score = Math.max(0, score - 2);
    }

    // 檢查重複字符
    if (this.hasRepeatingPatterns(password)) {
      errors.push('密碼包含過多重複字符或模式');
      score = Math.max(0, score - 1);
    }

    // 確定強度等級
    let strength: 'weak' | 'medium' | 'strong' | 'very-strong';
    if (score <= 2) {
      strength = 'weak';
    } else if (score <= 4) {
      strength = 'medium';
    } else if (score <= 6) {
      strength = 'strong';
    } else {
      strength = 'very-strong';
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength,
      score,
    };
  }

  // 生成安全密碼
  static generateSecurePassword(length: number = 16): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = uppercase + lowercase + numbers + specialChars;
    let password = '';

    // 確保至少包含每種類型的字符
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // 填充剩餘長度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打亂密碼字符順序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  // 檢查是否為常見密碼
  private static isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
      'football', 'baseball', 'liverpool', 'jordan', 'princess',
    ];

    const lowerPassword = password.toLowerCase();
    return commonPasswords.some(common => 
      lowerPassword.includes(common) || common.includes(lowerPassword)
    );
  }

  // 檢查重複模式
  private static hasRepeatingPatterns(password: string): boolean {
    // 檢查連續重複字符（如 aaa, 111）
    if (/(.)\1{2,}/.test(password)) {
      return true;
    }

    // 檢查鍵盤模式（如 qwerty, 123456）
    const keyboardPatterns = [
      'qwerty', 'asdf', 'zxcv', '123456', '654321',
      'qwertyuiop', 'asdfghjkl', 'zxcvbnm'
    ];

    const lowerPassword = password.toLowerCase();
    return keyboardPatterns.some(pattern => 
      lowerPassword.includes(pattern) || lowerPassword.includes(pattern.split('').reverse().join(''))
    );
  }

  // 檢查密碼是否需要更新
  static shouldUpdatePassword(hashedPassword: string): boolean {
    try {
      // 檢查是否使用了足夠的加密輪數
      const rounds = bcrypt.getRounds(hashedPassword);
      return rounds < BCRYPT_ROUNDS;
    } catch (error) {
      logger.error('檢查密碼更新需求時發生錯誤:', error);
      return true; // 如果無法檢查，建議更新
    }
  }

  // 生成密碼重置令牌
  static generateResetToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return token;
  }

  // 驗證bcrypt配置
  static validateBcryptConfig(): boolean {
    if (BCRYPT_ROUNDS < 10) {
      logger.warn('BCRYPT_ROUNDS 設置過低，建議至少使用 12 輪');
      return false;
    }

    if (BCRYPT_ROUNDS > 15) {
      logger.warn('BCRYPT_ROUNDS 設置過高，可能影響性能');
    }

    return true;
  }
}

// 導出常用函數
export const {
  hashPassword,
  verifyPassword,
  validatePassword,
  generateSecurePassword,
  shouldUpdatePassword,
  generateResetToken,
  validateBcryptConfig,
} = PasswordUtils;
