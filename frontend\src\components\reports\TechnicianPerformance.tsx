import React from 'react';
import { Card, Typography } from 'antd';
import { StatisticsQueryParams } from '../../services/statisticsService';

const { Title } = Typography;

interface TechnicianPerformanceProps {
  filters: StatisticsQueryParams;
}

const TechnicianPerformance: React.FC<TechnicianPerformanceProps> = ({ filters }) => {
  return (
    <Card>
      <Title level={3}>技師績效</Title>
      <p>技師績效分析功能開發中...</p>
      <ul>
        <li>技師工作量統計</li>
        <li>技師效率分析</li>
        <li>技師客戶評價</li>
        <li>技師專業技能分析</li>
      </ul>
    </Card>
  );
};

export default TechnicianPerformance;
