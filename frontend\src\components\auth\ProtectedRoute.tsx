import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { getCurrentUserAsync } from '../../store/slices/authSlice';
import { UserInfo } from '../../services/authService';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserInfo['role'];
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  fallback,
}) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const validateAuth = async () => {
      if (isAuthenticated && user) {
        // 如果已經有用戶資訊，直接驗證完成
        setIsValidating(false);
        return;
      }

      // 檢查本地存儲的 token 和用戶資訊
      const token = localStorage.getItem('authToken');
      const userInfo = localStorage.getItem('userInfo');

      if (token && userInfo) {
        try {
          // 如果是模擬 token，直接使用本地用戶資訊
          if (token.startsWith('mock-jwt-token-')) {
            console.log('🎯 使用模擬認證模式');
            const mockUser = JSON.parse(userInfo);
            // 這裡應該調用 Redux action 來設置用戶狀態
            // 暫時直接完成驗證
            setIsValidating(false);
            return;
          }

          // 嘗試獲取真實用戶資訊
          await dispatch(getCurrentUserAsync());
        } catch (error) {
          console.log('❌ 獲取用戶資訊失敗，清除認證狀態');
          // 如果獲取用戶資訊失敗，清除 token
          localStorage.removeItem('authToken');
          localStorage.removeItem('userInfo');
        }
      }

      setIsValidating(false);
    };

    validateAuth();
  }, [dispatch, isAuthenticated, user]);

  // 顯示載入中
  if (isValidating || isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        flexDirection: 'column'
      }}>
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
          size="large"
        />
        <div style={{ marginTop: 16, fontSize: 16, color: '#666' }}>
          驗證身份中...
        </div>
      </div>
    );
  }

  // 檢查認證狀態（包括模擬模式）
  const token = localStorage.getItem('authToken');
  const userInfo = localStorage.getItem('userInfo');

  // 如果未登入且沒有有效的本地認證，重定向到登入頁面
  if (!isAuthenticated && !user && (!token || !userInfo)) {
    return (
      <Navigate
        to="/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // 如果有本地認證但 Redux 狀態未更新，使用本地用戶資訊
  let currentUser = user;
  if (!user && userInfo) {
    try {
      currentUser = JSON.parse(userInfo);
    } catch (error) {
      console.error('解析用戶資訊失敗:', error);
      return (
        <Navigate
          to="/login"
          state={{ from: location }}
          replace
        />
      );
    }
  }

  // 檢查角色權限
  if (requiredRole && currentUser && !hasPermission(currentUser.role, requiredRole)) {
    return fallback || (
      <div style={{ padding: '50px' }}>
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您沒有權限訪問此頁面"
          icon={<ExclamationCircleOutlined />}
          extra={
            <div>
              <p>您的角色：{getRoleDisplayName(currentUser.role)}</p>
              <p>需要角色：{getRoleDisplayName(requiredRole)}</p>
              <Button type="primary" onClick={() => window.history.back()}>
                返回上一頁
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  return <>{children}</>;
};

// 權限檢查函數
const hasPermission = (userRole: UserInfo['role'], requiredRole: UserInfo['role']): boolean => {
  const roleHierarchy = {
    'VIEWER': 1,
    'TECHNICIAN': 2,
    'CUSTOMER_SERVICE': 3,
    'ADMIN': 4,
  };

  const userLevel = roleHierarchy[userRole];
  const requiredLevel = roleHierarchy[requiredRole];

  return userLevel >= requiredLevel;
};

// 角色顯示名稱
const getRoleDisplayName = (role: UserInfo['role']): string => {
  const roleNames = {
    'VIEWER': '查詢用戶',
    'TECHNICIAN': '維修人員',
    'CUSTOMER_SERVICE': '客服人員',
    'ADMIN': '系統管理員',
  };

  return roleNames[role] || role;
};

export default ProtectedRoute;
