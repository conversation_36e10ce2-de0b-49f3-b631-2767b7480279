import React, { useEffect, useState } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Space, 
  message, 
  Row,
  Col,
  Typography,
  Select,
  DatePicker,
  InputNumber,
  Tag
} from 'antd';
import { 
  UserOutlined, 
  ShoppingOutlined, 
  ToolOutlined, 
  DollarOutlined,
  CalendarOutlined,
  FileTextOutlined,
  TagsOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { RepairRecord, CreateRepairRecordRequest, UpdateRepairRecordRequest } from '../../services/repairService';
import { Customer } from '../../services/customerService';
import { Product } from '../../services/productService';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Text } = Typography;
const { Option } = Select;

interface RepairFormProps {
  visible: boolean;
  repair?: RepairRecord | null;
  customers: Customer[];
  products: Product[];
  technicians: Array<{ id: number; name: string; email: string }>;
  onCancel: () => void;
  onSubmit: (data: CreateRepairRecordRequest | UpdateRepairRecordRequest) => Promise<void>;
  loading?: boolean;
}

// 常見症狀選項
const commonSymptoms = [
  '螢幕破裂',
  '觸控失靈',
  '顯示異常',
  '無法開機',
  '電池續航短',
  '充電異常',
  '發熱嚴重',
  '聲音異常',
  '按鍵失靈',
  '相機故障',
  '網路連線問題',
  '系統當機',
  '資料遺失',
  '外殼損壞',
  '進水損壞',
];

const RepairForm: React.FC<RepairFormProps> = ({
  visible,
  repair,
  customers,
  products,
  technicians,
  onCancel,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([]);
  const [customSymptom, setCustomSymptom] = useState('');
  const isEditing = !!repair;

  useEffect(() => {
    if (visible) {
      if (repair) {
        // 編輯模式：填入現有數據
        form.setFieldsValue({
          customerId: repair.customerId,
          productId: repair.productId,
          issueDescription: repair.issueDescription,
          priority: repair.priority,
          assignedTechnicianId: repair.assignedTechnicianId,
          estimatedCost: repair.estimatedCost,
          actualCost: repair.actualCost,
          estimatedCompletionDate: repair.estimatedCompletionDate ? dayjs(repair.estimatedCompletionDate) : null,
          actualCompletionDate: repair.actualCompletionDate ? dayjs(repair.actualCompletionDate) : null,
          warrantyStatus: repair.warrantyStatus,
          repairNotes: repair.repairNotes,
          customerNotes: repair.customerNotes,
          status: repair.status,
        });
        setSelectedSymptoms(repair.symptoms || []);
      } else {
        // 新增模式：重置表單
        form.resetFields();
        form.setFieldsValue({
          priority: 'MEDIUM',
          warrantyStatus: 'OUT_OF_WARRANTY',
          status: 'PENDING_INSPECTION',
        });
        setSelectedSymptoms([]);
      }
      setCustomSymptom('');
    }
  }, [visible, repair, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const submitData = {
        customerId: values.customerId,
        productId: values.productId,
        issueDescription: values.issueDescription.trim(),
        symptoms: selectedSymptoms,
        priority: values.priority,
        assignedTechnicianId: values.assignedTechnicianId,
        estimatedCost: values.estimatedCost,
        estimatedCompletionDate: values.estimatedCompletionDate?.format('YYYY-MM-DD'),
        warrantyStatus: values.warrantyStatus,
        customerNotes: values.customerNotes?.trim(),
        ...(isEditing && {
          actualCost: values.actualCost,
          actualCompletionDate: values.actualCompletionDate?.format('YYYY-MM-DD'),
          repairNotes: values.repairNotes?.trim(),
          status: values.status,
        }),
      };

      await onSubmit(submitData);
      form.resetFields();
      setSelectedSymptoms([]);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedSymptoms([]);
    setCustomSymptom('');
    onCancel();
  };

  const handleSymptomSelect = (symptom: string) => {
    if (!selectedSymptoms.includes(symptom)) {
      setSelectedSymptoms([...selectedSymptoms, symptom]);
    }
  };

  const handleSymptomRemove = (symptom: string) => {
    setSelectedSymptoms(selectedSymptoms.filter(s => s !== symptom));
  };

  const handleCustomSymptomAdd = () => {
    if (customSymptom.trim() && !selectedSymptoms.includes(customSymptom.trim())) {
      setSelectedSymptoms([...selectedSymptoms, customSymptom.trim()]);
      setCustomSymptom('');
    }
  };

  const getCustomerOptions = () => {
    return customers.filter(customer => customer.isActive);
  };

  const getProductOptions = () => {
    return products.filter(product => product.isActive);
  };

  return (
    <Modal
      title={isEditing ? '編輯維修記錄' : '新增維修記錄'}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {isEditing ? '更新' : '新增'}
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="customerId"
              label="客戶"
              rules={[
                { required: true, message: '請選擇客戶' },
              ]}
            >
              <Select
                placeholder="請選擇客戶"
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {getCustomerOptions().map(customer => (
                  <Option key={customer.id} value={customer.id}>
                    <UserOutlined style={{ marginRight: 4 }} />
                    {customer.name} - {customer.phone}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="productId"
              label="產品"
              rules={[
                { required: true, message: '請選擇產品' },
              ]}
            >
              <Select
                placeholder="請選擇產品"
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {getProductOptions().map(product => (
                  <Option key={product.id} value={product.id}>
                    <ShoppingOutlined style={{ marginRight: 4 }} />
                    {product.name} ({product.brand} {product.model})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="issueDescription"
          label="問題描述"
          rules={[
            { required: true, message: '請輸入問題描述' },
            { min: 10, message: '問題描述至少需要10個字符' },
            { max: 500, message: '問題描述不能超過500個字符' },
          ]}
        >
          <TextArea
            placeholder="請詳細描述產品的問題和故障現象"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item label="故障症狀">
          <div style={{ marginBottom: 12 }}>
            <Text type="secondary">常見症狀（點擊選擇）：</Text>
            <div style={{ marginTop: 8 }}>
              {commonSymptoms.map(symptom => (
                <Tag
                  key={symptom}
                  style={{ 
                    cursor: 'pointer', 
                    marginBottom: 4,
                    backgroundColor: selectedSymptoms.includes(symptom) ? '#e6f7ff' : undefined,
                    borderColor: selectedSymptoms.includes(symptom) ? '#1890ff' : undefined,
                  }}
                  onClick={() => handleSymptomSelect(symptom)}
                >
                  {symptom}
                </Tag>
              ))}
            </div>
          </div>
          
          <div style={{ marginBottom: 12 }}>
            <Input.Group compact>
              <Input
                style={{ width: 'calc(100% - 80px)' }}
                placeholder="輸入自定義症狀"
                value={customSymptom}
                onChange={(e) => setCustomSymptom(e.target.value)}
                onPressEnter={handleCustomSymptomAdd}
              />
              <Button onClick={handleCustomSymptomAdd}>添加</Button>
            </Input.Group>
          </div>

          <div>
            <Text type="secondary">已選擇的症狀：</Text>
            <div style={{ marginTop: 8 }}>
              {selectedSymptoms.map(symptom => (
                <Tag
                  key={symptom}
                  closable
                  onClose={() => handleSymptomRemove(symptom)}
                  color="blue"
                  style={{ marginBottom: 4 }}
                >
                  {symptom}
                </Tag>
              ))}
              {selectedSymptoms.length === 0 && (
                <Text type="secondary">尚未選擇症狀</Text>
              )}
            </div>
          </div>
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="priority"
              label="優先級"
              rules={[
                { required: true, message: '請選擇優先級' },
              ]}
            >
              <Select placeholder="請選擇優先級">
                <Option value="LOW">
                  <Tag color="green">低</Tag> 一般維修
                </Option>
                <Option value="MEDIUM">
                  <Tag color="blue">中</Tag> 正常處理
                </Option>
                <Option value="HIGH">
                  <Tag color="orange">高</Tag> 優先處理
                </Option>
                <Option value="URGENT">
                  <Tag color="red">緊急</Tag> 立即處理
                </Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="warrantyStatus"
              label="保固狀態"
              rules={[
                { required: true, message: '請選擇保固狀態' },
              ]}
            >
              <Select placeholder="請選擇保固狀態">
                <Option value="IN_WARRANTY">
                  <Tag color="green">保固內</Tag>
                </Option>
                <Option value="OUT_OF_WARRANTY">
                  <Tag color="red">保固外</Tag>
                </Option>
                <Option value="EXTENDED_WARRANTY">
                  <Tag color="blue">延長保固</Tag>
                </Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="assignedTechnicianId"
              label="指派技師"
            >
              <Select
                placeholder="請選擇技師"
                allowClear
              >
                {technicians.map(tech => (
                  <Option key={tech.id} value={tech.id}>
                    <ToolOutlined style={{ marginRight: 4 }} />
                    {tech.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="estimatedCost"
              label="預估費用"
              rules={[
                { type: 'number', min: 0, message: '費用不能為負數' },
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="請輸入預估費用"
                prefix={<DollarOutlined />}
                min={0}
                precision={0}
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="estimatedCompletionDate"
              label="預計完成日期"
            >
              <DatePicker
                style={{ width: '100%' }}
                placeholder="請選擇預計完成日期"
                disabledDate={(current) => current && current < dayjs().startOf('day')}
              />
            </Form.Item>
          </Col>
        </Row>

        {isEditing && (
          <>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="status"
                  label="維修狀態"
                >
                  <Select placeholder="請選擇狀態">
                    <Option value="PENDING_INSPECTION">待檢測</Option>
                    <Option value="INSPECTING">檢測中</Option>
                    <Option value="PENDING_REPAIR">待維修</Option>
                    <Option value="REPAIRING">維修中</Option>
                    <Option value="PENDING_PARTS">待零件</Option>
                    <Option value="COMPLETED">已完成</Option>
                    <Option value="DELIVERED">已交付</Option>
                    <Option value="CANCELLED">已取消</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="actualCost"
                  label="實際費用"
                  rules={[
                    { type: 'number', min: 0, message: '費用不能為負數' },
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="請輸入實際費用"
                    prefix={<DollarOutlined />}
                    min={0}
                    precision={0}
                    formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="actualCompletionDate"
                  label="實際完成日期"
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="請選擇實際完成日期"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="repairNotes"
              label="維修備註"
              rules={[
                { max: 1000, message: '維修備註不能超過1000個字符' },
              ]}
            >
              <TextArea
                placeholder="請輸入維修過程、更換零件、測試結果等備註"
                rows={3}
                maxLength={1000}
                showCount
              />
            </Form.Item>
          </>
        )}

        <Form.Item
          name="customerNotes"
          label="客戶備註"
          rules={[
            { max: 500, message: '客戶備註不能超過500個字符' },
          ]}
        >
          <TextArea
            placeholder="請輸入客戶特殊要求或備註"
            rows={2}
            maxLength={500}
            showCount
          />
        </Form.Item>

        {!isEditing && (
          <div style={{ 
            background: '#f6f6f6', 
            padding: '12px', 
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              <strong>提示：</strong><br />
              • 問題描述請盡量詳細，有助於技師快速定位問題<br />
              • 症狀選擇可以多選，也可以自定義添加<br />
              • 優先級會影響維修排程順序<br />
              • 保固狀態會影響費用計算<br />
              • 新增的維修記錄預設狀態為「待檢測」
            </Text>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default RepairForm;
