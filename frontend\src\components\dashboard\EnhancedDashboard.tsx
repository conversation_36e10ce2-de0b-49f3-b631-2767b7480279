import React from 'react';
import { Row, Col, Card, Statistic, Typography, Button, Space, Progress, List, Tag } from 'antd';
import {
  ToolOutlined,
  TeamOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  TrendingUpOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

// 模擬數據
const mockData = {
  statistics: {
    totalRepairs: 1248,
    activeRepairs: 89,
    completedRepairs: 1159,
    totalCustomers: 456,
    totalProducts: 234,
    pendingQuotes: 23,
  },
  recentRepairs: [
    {
      id: 'R2024001',
      customer: '台積電',
      product: 'MIO-X1000',
      status: 'REPAIRING',
      priority: 'HIGH',
      receivedDate: '2024-07-08',
    },
    {
      id: 'R2024002',
      customer: '聯發科',
      product: 'MIO-S500',
      status: 'PENDING_TEST',
      priority: 'MEDIUM',
      receivedDate: '2024-07-07',
    },
    {
      id: 'R2024003',
      customer: '鴻海精密',
      product: 'MIO-Pro',
      status: 'COMPLETED',
      priority: 'LOW',
      receivedDate: '2024-07-06',
    },
  ],
  urgentTasks: [
    { id: 1, title: '緊急維修：MIO-X1000 主板故障', dueDate: '今天' },
    { id: 2, title: '客戶回訪：台積電維修滿意度調查', dueDate: '明天' },
    { id: 3, title: '零件補貨：電容器庫存不足', dueDate: '本週' },
  ],
};

const EnhancedDashboard: React.FC = () => {
  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING_INSPECTION': 'orange',
      'REPAIRING': 'blue',
      'PENDING_TEST': 'purple',
      'COMPLETED': 'green',
      'DELIVERED': 'cyan',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'PENDING_INSPECTION': '待檢查',
      'REPAIRING': '維修中',
      'PENDING_TEST': '待測試',
      'COMPLETED': '已完成',
      'DELIVERED': '已交付',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'HIGH': 'red',
      'MEDIUM': 'orange',
      'LOW': 'green',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  return (
    <div>
      {/* 頁面標題 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>儀表板</Title>
        <Text type="secondary">歡迎使用 IACT MIO維保管理系統</Text>
      </div>

      {/* 統計卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="總維修記錄"
              value={mockData.statistics.totalRepairs}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="進行中維修"
              value={mockData.statistics.activeRepairs}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已完成維修"
              value={mockData.statistics.completedRepairs}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="客戶總數"
              value={mockData.statistics.totalCustomers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近維修記錄 */}
        <Col xs={24} lg={14}>
          <Card
            title="最近維修記錄"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => navigate('/repairs')}
              >
                新增維修
              </Button>
            }
          >
            <List
              dataSource={mockData.recentRepairs}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button 
                      type="link" 
                      icon={<EyeOutlined />}
                      onClick={() => navigate('/repairs')}
                    >
                      查看
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{item.id}</Text>
                        <Tag color={getPriorityColor(item.priority)}>
                          {item.priority}
                        </Tag>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size="small">
                        <Text>客戶：{item.customer}</Text>
                        <Text>產品：{item.product}</Text>
                        <Text>接收日期：{item.receivedDate}</Text>
                      </Space>
                    }
                  />
                  <div>
                    <Tag color={getStatusColor(item.status)}>
                      {getStatusText(item.status)}
                    </Tag>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 右側面板 */}
        <Col xs={24} lg={10}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 緊急任務 */}
            <Card
              title={
                <Space>
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  緊急任務
                </Space>
              }
              size="small"
            >
              <List
                size="small"
                dataSource={mockData.urgentTasks}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={<Text style={{ fontSize: '14px' }}>{item.title}</Text>}
                      description={<Text type="secondary">截止：{item.dueDate}</Text>}
                    />
                  </List.Item>
                )}
              />
            </Card>

            {/* 系統狀態 */}
            <Card title="系統狀態" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>資料庫連接</Text>
                  <Progress percent={100} size="small" status="success" />
                </div>
                <div>
                  <Text>系統性能</Text>
                  <Progress percent={85} size="small" />
                </div>
                <div>
                  <Text>存儲空間</Text>
                  <Progress percent={65} size="small" />
                </div>
              </Space>
            </Card>

            {/* 快速操作 */}
            <Card title="快速操作" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button 
                  block 
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/repairs')}
                >
                  新增維修記錄
                </Button>
                <Button 
                  block 
                  icon={<TeamOutlined />}
                  onClick={() => navigate('/customers')}
                >
                  新增客戶
                </Button>
                <Button 
                  block 
                  icon={<ShoppingOutlined />}
                  onClick={() => navigate('/products')}
                >
                  產品管理
                </Button>
                <Button 
                  block 
                  icon={<FileTextOutlined />}
                  onClick={() => navigate('/reports')}
                >
                  查看報表
                </Button>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default EnhancedDashboard;
