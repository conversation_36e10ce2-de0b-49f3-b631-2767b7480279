import { Router } from 'express';
import ProductController from '../controllers/productController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  createProductValidation,
  updateProductValidation,
  productListValidation,
  productIdValidation,
  productSearchValidation,
  batchOperationValidation,
  modelParamValidation,
  productDetailValidation,
  createCategoryValidation,
  updateCategoryValidation,
  categoryListValidation,
  categoryIdValidation,
  productListWithRangeValidation,
  batchOperationWithPermissionValidation,
} from '../validators/productValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateProductRequest:
 *       type: object
 *       required:
 *         - name
 *         - model
 *         - brand
 *         - categoryId
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 200
 *           description: 產品名稱
 *         model:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 產品型號
 *         brand:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 品牌
 *         categoryId:
 *           type: string
 *           description: 產品類別ID
 *         description:
 *           type: string
 *           maxLength: 1000
 *           description: 產品描述
 *         specifications:
 *           type: string
 *           maxLength: 2000
 *           description: 產品規格
 *         warrantyPeriod:
 *           type: integer
 *           minimum: 0
 *           maximum: 120
 *           description: 保固期間（月）
 *         price:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 價格
 *         isActive:
 *           type: boolean
 *           default: true
 *           description: 是否活躍
 *     
 *     UpdateProductRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 200
 *           description: 產品名稱
 *         model:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 產品型號
 *         brand:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 品牌
 *         categoryId:
 *           type: string
 *           description: 產品類別ID
 *         description:
 *           type: string
 *           maxLength: 1000
 *           description: 產品描述
 *         specifications:
 *           type: string
 *           maxLength: 2000
 *           description: 產品規格
 *         warrantyPeriod:
 *           type: integer
 *           minimum: 0
 *           maximum: 120
 *           description: 保固期間（月）
 *         price:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 價格
 *         isActive:
 *           type: boolean
 *           description: 是否活躍
 *     
 *     CreateProductCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 類別名稱
 *         description:
 *           type: string
 *           maxLength: 500
 *           description: 類別描述
 *         parentId:
 *           type: string
 *           description: 父類別ID
 *         isActive:
 *           type: boolean
 *           default: true
 *           description: 是否活躍
 *     
 *     BatchProductOperation:
 *       type: object
 *       required:
 *         - productIds
 *         - operation
 *       properties:
 *         productIds:
 *           type: array
 *           items:
 *             type: string
 *           minItems: 1
 *           maxItems: 100
 *           description: 產品ID列表
 *         operation:
 *           type: string
 *           enum: [activate, deactivate, delete, updateCategory, updateBrand]
 *           description: 操作類型
 *         data:
 *           type: object
 *           properties:
 *             isActive:
 *               type: boolean
 *               description: 活躍狀態
 *             categoryId:
 *               type: string
 *               description: 新類別ID（updateCategory操作時必需）
 *             brand:
 *               type: string
 *               description: 新品牌（updateBrand操作時必需）
 */

// === 產品相關路由 ===

/**
 * @swagger
 * /api/v1/products:
 *   get:
 *     summary: 獲取產品列表
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *         description: 類別ID篩選
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: 品牌篩選
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *       - in: query
 *         name: hasPrice
 *         schema:
 *           type: boolean
 *         description: 有價格篩選
 *       - in: query
 *         name: priceMin
 *         schema:
 *           type: number
 *         description: 最低價格
 *       - in: query
 *         name: priceMax
 *         schema:
 *           type: number
 *         description: 最高價格
 *       - in: query
 *         name: warrantyMin
 *         schema:
 *           type: integer
 *         description: 最短保固期間
 *       - in: query
 *         name: warrantyMax
 *         schema:
 *           type: integer
 *         description: 最長保固期間
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, model, brand, price, warrantyPeriod, createdAt, updatedAt]
 *           default: createdAt
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取產品列表成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), productListWithRangeValidation, ProductController.getProductList);

/**
 * @swagger
 * /api/v1/products:
 *   post:
 *     summary: 創建產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProductRequest'
 *     responses:
 *       201:
 *         description: 產品創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       409:
 *         description: 產品型號已存在
 */
router.post('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), createProductValidation, ProductController.createProduct);

/**
 * @swagger
 * /api/v1/products/search:
 *   get:
 *     summary: 搜尋產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: 結果數量限制
 *     responses:
 *       200:
 *         description: 搜尋成功
 *       400:
 *         description: 搜尋參數錯誤
 *       401:
 *         description: 未認證
 */
router.get('/search', authenticate, productSearchValidation, ProductController.searchProducts);

/**
 * @swagger
 * /api/v1/products/statistics:
 *   get:
 *     summary: 獲取產品統計
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), ProductController.getProductStatistics);

/**
 * @swagger
 * /api/v1/products/batch:
 *   post:
 *     summary: 批量操作產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchProductOperation'
 *     responses:
 *       200:
 *         description: 批量操作完成
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/batch', authenticate, authorize(Role.ADMIN), batchOperationWithPermissionValidation, ProductController.batchOperation);

/**
 * @swagger
 * /api/v1/products/model/{model}:
 *   get:
 *     summary: 根據型號查找產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: model
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品型號
 *     responses:
 *       200:
 *         description: 查找成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 產品不存在
 */
router.get('/model/:model', authenticate, modelParamValidation, ProductController.findProductByModel);

/**
 * @swagger
 * /api/v1/products/check-model/{model}:
 *   get:
 *     summary: 檢查型號是否可用
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: model
 *         required: true
 *         schema:
 *           type: string
 *         description: 要檢查的產品型號
 *       - in: query
 *         name: excludeId
 *         schema:
 *           type: string
 *         description: 排除的產品ID
 *     responses:
 *       200:
 *         description: 檢查完成
 *       400:
 *         description: 型號格式錯誤
 *       401:
 *         description: 未認證
 */
router.get('/check-model/:model', authenticate, modelParamValidation, ProductController.checkModelAvailability);

/**
 * @swagger
 * /api/v1/products/{id}:
 *   get:
 *     summary: 根據ID獲取產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品ID
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含詳細資訊
 *     responses:
 *       200:
 *         description: 獲取產品成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 產品不存在
 */
router.get('/:id', authenticate, productDetailValidation, ProductController.getProductById);

/**
 * @swagger
 * /api/v1/products/{id}:
 *   put:
 *     summary: 更新產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductRequest'
 *     responses:
 *       200:
 *         description: 產品更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品不存在
 */
router.put('/:id', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), updateProductValidation, ProductController.updateProduct);

/**
 * @swagger
 * /api/v1/products/{id}:
 *   delete:
 *     summary: 刪除產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 產品刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), productIdValidation, ProductController.deleteProduct);

/**
 * @swagger
 * /api/v1/products/{id}/activate:
 *   post:
 *     summary: 激活產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品ID
 *     responses:
 *       200:
 *         description: 產品激活成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品不存在
 */
router.post('/:id/activate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), productIdValidation, ProductController.activateProduct);

/**
 * @swagger
 * /api/v1/products/{id}/deactivate:
 *   post:
 *     summary: 停用產品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 產品ID
 *     responses:
 *       200:
 *         description: 產品停用成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品不存在
 */
router.post('/:id/deactivate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), productIdValidation, ProductController.deactivateProduct);

export default router;
