import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Modal, 
  message, 
  Popconfirm,
  Select,
  Row,
  Col,
  Avatar,
  Tooltip,
  Badge,
  Dropdown
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  UserOutlined,
  MoreOutlined,
  LockOutlined,
  UnlockOutlined,
  ReloadOutlined,
  KeyOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { SystemUser, UserQueryParams } from '../../services/systemService';
import UserForm from './UserForm';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬數據
const mockUsers: SystemUser[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    firstName: '系統',
    lastName: '管理員',
    fullName: '系統管理員',
    role: {
      id: 1,
      name: 'ADMIN',
      displayName: '系統管理員',
      description: '擁有所有系統權限',
      level: 1,
      permissions: [],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    department: 'IT部門',
    position: '系統管理員',
    phone: '0912-345-678',
    isActive: true,
    lastLoginAt: '2024-01-20T14:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    permissions: []
  },
  {
    id: 2,
    username: 'service_manager',
    email: '<EMAIL>',
    firstName: '客服',
    lastName: '主管',
    fullName: '客服主管',
    role: {
      id: 2,
      name: 'CUSTOMER_SERVICE_MANAGER',
      displayName: '客服主管',
      description: '客服部門管理權限',
      level: 2,
      permissions: [],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    department: '客服部門',
    position: '部門主管',
    phone: '0923-456-789',
    isActive: true,
    lastLoginAt: '2024-01-20T13:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-20T13:15:00Z',
    permissions: []
  },
  {
    id: 3,
    username: 'tech_lead',
    email: '<EMAIL>',
    firstName: '維修',
    lastName: '技師',
    fullName: '維修技師',
    role: {
      id: 3,
      name: 'TECHNICIAN',
      displayName: '維修技師',
      description: '維修操作權限',
      level: 3,
      permissions: [],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    department: '維修部門',
    position: '資深技師',
    phone: '0934-567-890',
    isActive: true,
    lastLoginAt: '2024-01-20T12:45:00Z',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-20T12:45:00Z',
    permissions: []
  },
  {
    id: 4,
    username: 'viewer',
    email: '<EMAIL>',
    firstName: '查詢',
    lastName: '用戶',
    fullName: '查詢用戶',
    role: {
      id: 4,
      name: 'VIEWER',
      displayName: '查詢用戶',
      description: '僅查看權限',
      level: 4,
      permissions: [],
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    department: '業務部門',
    position: '業務員',
    phone: '0945-678-901',
    isActive: false,
    lastLoginAt: '2024-01-18T16:20:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-18T16:20:00Z',
    permissions: []
  }
];

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<SystemUser[]>(mockUsers);
  const [loading, setLoading] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<SystemUser | null>(null);
  const [searchText, setSearchText] = useState('');
  const [roleFilter, setRoleFilter] = useState<number | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockUsers.length,
  });

  useEffect(() => {
    fetchUsers();
  }, [searchText, roleFilter, statusFilter, pagination.current, pagination.pageSize]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params: UserQueryParams = {
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchText || undefined,
        roleId: roleFilter,
        isActive: statusFilter,
      };
      
      // 這裡會調用 systemService.getUsers(params)
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = [...mockUsers];
      
      if (searchText) {
        filteredData = filteredData.filter(user =>
          user.fullName.toLowerCase().includes(searchText.toLowerCase()) ||
          user.email.toLowerCase().includes(searchText.toLowerCase()) ||
          user.username.toLowerCase().includes(searchText.toLowerCase())
        );
      }
      
      if (roleFilter) {
        filteredData = filteredData.filter(user => user.role.id === roleFilter);
      }
      
      if (statusFilter !== undefined) {
        filteredData = filteredData.filter(user => user.isActive === statusFilter);
      }
      
      setUsers(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length,
      }));
    } catch (error) {
      message.error('載入用戶列表失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setIsFormVisible(true);
  };

  const handleEditUser = (user: SystemUser) => {
    setEditingUser(user);
    setIsFormVisible(true);
  };

  const handleDeleteUser = async (id: number) => {
    try {
      // 這裡會調用 systemService.deleteUser(id)
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('用戶刪除成功');
      fetchUsers();
    } catch (error) {
      message.error('刪除用戶失敗');
    }
  };

  const handleToggleStatus = async (user: SystemUser) => {
    try {
      // 這裡會調用 systemService.updateUser(user.id, { isActive: !user.isActive })
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`用戶${!user.isActive ? '啟用' : '停用'}成功`);
      fetchUsers();
    } catch (error) {
      message.error('更新用戶狀態失敗');
    }
  };

  const handleResetPassword = async (user: SystemUser) => {
    try {
      // 這裡會調用 systemService.resetPassword(user.id)
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('密碼重置成功，臨時密碼已發送到用戶郵箱');
    } catch (error) {
      message.error('密碼重置失敗');
    }
  };

  const handleFormSubmit = async (userData: any) => {
    try {
      if (editingUser) {
        // 更新用戶
        // await systemService.updateUser(editingUser.id, userData);
        message.success('用戶更新成功');
      } else {
        // 新增用戶
        // await systemService.createUser(userData);
        message.success('用戶新增成功');
      }
      setIsFormVisible(false);
      setEditingUser(null);
      fetchUsers();
    } catch (error) {
      message.error(`${editingUser ? '更新' : '新增'}用戶失敗`);
    }
  };

  const getRoleColor = (roleName: string) => {
    const colors = {
      'ADMIN': 'red',
      'CUSTOMER_SERVICE_MANAGER': 'orange',
      'TECHNICIAN': 'blue',
      'VIEWER': 'green'
    };
    return colors[roleName as keyof typeof colors] || 'default';
  };

  const getMoreActions = (user: SystemUser) => [
    {
      key: 'toggle-status',
      label: user.isActive ? '停用用戶' : '啟用用戶',
      icon: user.isActive ? <LockOutlined /> : <UnlockOutlined />,
      onClick: () => handleToggleStatus(user),
    },
    {
      key: 'reset-password',
      label: '重置密碼',
      icon: <KeyOutlined />,
      onClick: () => handleResetPassword(user),
    },
  ];

  const columns: ColumnsType<SystemUser> = [
    {
      title: '用戶',
      key: 'user',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            size={40} 
            src={record.avatar} 
            icon={<UserOutlined />}
            style={{ marginRight: 12 }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {record.fullName}
              {!record.isActive && (
                <Tag color="red" size="small" style={{ marginLeft: 8 }}>
                  已停用
                </Tag>
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              @{record.username}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '聯絡資訊',
      key: 'contact',
      width: 200,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.phone}
          </div>
        </div>
      ),
    },
    {
      title: '部門職位',
      key: 'department',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.department}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.position}
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role) => (
        <Tag color={getRoleColor(role.name)}>
          {role.displayName}
        </Tag>
      ),
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '啟用' : '停用'} 
        />
      ),
    },
    {
      title: '最後登入',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 120,
      render: (lastLoginAt) => (
        <div>
          {lastLoginAt ? (
            <>
              <div>{dayjs(lastLoginAt).format('MM/DD')}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {dayjs(lastLoginAt).format('HH:mm')}
              </div>
            </>
          ) : (
            <Text type="secondary">從未登入</Text>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{ 
              items: getMoreActions(record).map(action => ({
                ...action,
                onClick: () => action.onClick()
              }))
            }}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
          <Popconfirm
            title="確定要刪除這個用戶嗎？"
            description="刪除後將無法恢復，請謹慎操作。"
            onConfirm={() => handleDeleteUser(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col flex="auto">
              <Title level={4} style={{ margin: 0 }}>
                用戶管理
              </Title>
            </Col>
            <Col>
              <Space wrap>
                <Search
                  placeholder="搜尋用戶名、郵箱或姓名"
                  allowClear
                  style={{ width: 250 }}
                  onSearch={handleSearch}
                  enterButton={<SearchOutlined />}
                />
                <Select
                  placeholder="角色篩選"
                  allowClear
                  style={{ width: 120 }}
                  onChange={setRoleFilter}
                >
                  <Option value={1}>系統管理員</Option>
                  <Option value={2}>客服主管</Option>
                  <Option value={3}>維修技師</Option>
                  <Option value={4}>查詢用戶</Option>
                </Select>
                <Select
                  placeholder="狀態篩選"
                  allowClear
                  style={{ width: 100 }}
                  onChange={setStatusFilter}
                >
                  <Option value={true}>啟用</Option>
                  <Option value={false}>停用</Option>
                </Select>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchUsers}
                  loading={loading}
                >
                  重新整理
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddUser}
                >
                  新增用戶
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10,
              }));
            },
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>

      <UserForm
        visible={isFormVisible}
        user={editingUser}
        onCancel={() => {
          setIsFormVisible(false);
          setEditingUser(null);
        }}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
};

export default UserManagement;
