# SharePoint 整合指導手冊

## 📋 **SharePoint 環境需求**

### 支援的 SharePoint 版本
- **SharePoint Online** (Office 365) ✅ 推薦
- **SharePoint 2019** ✅ 支援
- **SharePoint 2016** ✅ 支援
- **SharePoint 2013** ⚠️ 部分支援

### 必要權限
- **網站權限**：參與者 (Contribute) 或更高
- **清單權限**：建立、讀取、更新、刪除
- **API 權限**：SharePoint REST API 存取

## 🌐 **URL 配置說明**

### SharePoint Online URL 格式
```
https://[租戶名稱].sharepoint.com/sites/[網站名稱]
```

#### 範例
```
https://contoso.sharepoint.com/sites/maintenance
https://yourcompany.sharepoint.com/sites/repair-management
https://abc123.sharepoint.com/sites/service-desk
```

### SharePoint On-Premises URL 格式
```
https://[伺服器名稱]/sites/[網站名稱]
http://[伺服器名稱]:port/sites/[網站名稱]
```

#### 範例
```
https://sharepoint.company.com/sites/maintenance
http://sp-server:8080/sites/repair-management
https://intranet.company.local/sites/service
```

## 🔧 **URL 配置步驟**

### 步驟 1：確認您的 SharePoint 網站 URL

#### 方法一：從瀏覽器複製
1. **開啟 SharePoint 網站**
2. **複製瀏覽器地址欄的 URL**
3. **移除頁面路徑，保留到網站根目錄**

```
完整 URL: https://contoso.sharepoint.com/sites/maintenance/Lists/Tasks/AllItems.aspx
網站 URL: https://contoso.sharepoint.com/sites/maintenance
```

#### 方法二：從 SharePoint 設定查看
1. **進入 SharePoint 網站**
2. **點擊設定齒輪 ⚙️**
3. **選擇 "網站資訊"**
4. **複製 "網站 URL"**

### 步驟 2：在系統中配置 URL

#### 在維保管理系統中設定
1. **啟動系統**：雙擊 `啟動系統.bat`
2. **登入系統**：使用 admin / admin123
3. **進入系統設定**：點擊左側導航 "系統設定"
4. **選擇存儲模式**：選擇 "SharePoint 整合"
5. **輸入網站 URL**：貼上您的 SharePoint 網站 URL

## 📊 **SharePoint 清單結構建立**

### 必要清單

#### 1. 維修記錄清單
**清單名稱**：`維修記錄`

**欄位設定**：
```
Title (文字) - 預設欄位，用作維修編號
RepairId (文字) - 維修編號
CustomerName (文字) - 客戶姓名
Phone (文字) - 聯絡電話
SerialNumber (文字) - 產品序號
ProductName (文字) - 產品名稱
ProductModel (文字) - 產品型號
Issue (多行文字) - 問題描述
ServiceStatus (選擇) - 維保狀態
  選項：客訴, 維保
RepairRecord (選擇) - 維修記錄
  選項：調整, 換馬達
TestResult (選擇) - 測試結果
  選項：正常, 異常, 待測試
Technician (文字) - 維保員
RepairDate (日期時間) - 維修日期
```

#### 2. 客戶資料清單
**清單名稱**：`客戶資料`

**欄位設定**：
```
Title (文字) - 客戶姓名
CustomerId (文字) - 客戶編號
CustomerName (文字) - 客戶姓名
Phone (文字) - 聯絡電話
Email (文字) - 電子郵箱
Address (多行文字) - 地址
```

#### 3. 零件資料清單
**清單名稱**：`零件資料`

**欄位設定**：
```
Title (文字) - 零件名稱
PartId (文字) - 零件編號
PartName (文字) - 零件名稱
Category (選擇) - 零件分類
  選項：電子零件, 機械零件, 消耗品, 工具
Model (文字) - 型號
Supplier (文字) - 供應商
Stock (數字) - 庫存數量
Price (數字) - 單價
MinStock (數字) - 最低庫存警告
Location (文字) - 存放位置
Notes (多行文字) - 備註
```

#### 4. 產品資料清單
**清單名稱**：`產品資料`

**欄位設定**：
```
Title (文字) - 產品名稱
ProductId (文字) - 產品編號
ProductName (文字) - 產品名稱
Brand (文字) - 品牌
Model (文字) - 型號
Category (選擇) - 產品分類
  選項：手機, 平板, 筆電, 桌機, 其他
```

## 🛠️ **清單建立步驟**

### 方法一：手動建立 (推薦)

#### 步驟 1：建立清單
1. **進入 SharePoint 網站**
2. **點擊 "新增" → "清單"**
3. **選擇 "空白清單"**
4. **輸入清單名稱**：如 "維修記錄"
5. **點擊 "建立"**

#### 步驟 2：新增欄位
1. **點擊 "新增欄位"**
2. **選擇欄位類型**：文字、數字、選擇等
3. **輸入欄位名稱**
4. **設定欄位屬性**
5. **點擊 "儲存"**

#### 步驟 3：重複建立其他清單
按照上述步驟建立所有必要清單

### 方法二：使用 PowerShell 腳本 (進階)

```powershell
# SharePoint Online PowerShell 腳本
Connect-PnPOnline -Url "https://yourtenant.sharepoint.com/sites/maintenance" -Interactive

# 建立維修記錄清單
$list = New-PnPList -Title "維修記錄" -Template GenericList
Add-PnPField -List $list -DisplayName "RepairId" -InternalName "RepairId" -Type Text
Add-PnPField -List $list -DisplayName "CustomerName" -InternalName "CustomerName" -Type Text
# ... 其他欄位
```

## 🔐 **權限設定**

### 使用者權限
確保維保管理系統的使用者具有以下權限：

#### 最低權限需求
- **讀取**：查看清單和項目
- **新增項目**：建立新的維修記錄
- **編輯項目**：修改現有記錄
- **刪除項目**：刪除記錄 (可選)

#### 建議權限等級
- **參與者 (Contribute)**：包含所有必要權限
- **設計者 (Design)**：如需修改清單結構

### API 權限
如果使用應用程式註冊：

#### Azure AD 應用程式權限
```
SharePoint:
- Sites.Read.All
- Sites.ReadWrite.All
- Sites.Manage.All (如需要)
```

## 🧪 **連線測試**

### 在系統中測試連線

#### 步驟 1：配置 URL
1. **進入系統設定**
2. **選擇 SharePoint 整合**
3. **輸入網站 URL**
4. **點擊 "🔗 測試連線"**

#### 步驟 2：檢查結果
- **✅ 連線成功**：顯示綠色成功訊息
- **❌ 連線失敗**：檢查 URL 和權限

### 手動測試方法

#### 瀏覽器測試
```
在瀏覽器中開啟：
https://your-sharepoint-url/_api/web

應該看到 XML 格式的網站資訊
```

#### REST API 測試
```javascript
// 在瀏覽器控制台執行
fetch('https://your-sharepoint-url/_api/web')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('錯誤:', error));
```

## 🔄 **數據遷移**

### 從 LocalStorage 遷移到 SharePoint

#### 自動遷移
1. **確保 SharePoint 連線成功**
2. **點擊 "📤 遷移數據"**
3. **等待遷移完成**
4. **確認數據已上傳**

#### 手動遷移
1. **匯出 LocalStorage 數據**：點擊 "📤 匯出所有數據"
2. **下載 JSON 檔案**
3. **切換到 SharePoint 模式**
4. **匯入數據**：點擊 "📥 匯入數據"

## 🚨 **常見問題和解決方案**

### 問題 1：連線失敗
**可能原因**：
- URL 格式錯誤
- 權限不足
- 網路問題
- CORS 設定

**解決方案**：
1. **檢查 URL 格式**
2. **確認權限設定**
3. **測試網路連線**
4. **聯絡 IT 管理員**

### 問題 2：清單不存在
**錯誤訊息**：`List 'XXX' does not exist`

**解決方案**：
1. **確認清單名稱正確**
2. **檢查清單是否已建立**
3. **確認權限可以存取清單**

### 問題 3：權限被拒絕
**錯誤訊息**：`Access denied`

**解決方案**：
1. **檢查使用者權限**
2. **確認 API 權限**
3. **聯絡 SharePoint 管理員**

## 📞 **技術支援**

### 自助檢查清單
- [ ] SharePoint 網站 URL 正確
- [ ] 使用者具有適當權限
- [ ] 所有必要清單已建立
- [ ] 清單欄位設定正確
- [ ] 網路連線正常

### 聯絡資訊
- **SharePoint 管理員**：確認權限和設定
- **IT 支援**：網路和基礎設施問題
- **系統文檔**：查看詳細技術文檔

## 🎯 **最佳實踐**

### 安全性
- 使用 HTTPS 連線
- 定期檢查權限設定
- 啟用稽核日誌

### 效能
- 限制清單項目數量 (<5000)
- 使用索引欄位
- 定期清理舊數據

### 維護
- 定期備份數據
- 監控系統效能
- 更新權限設定

---

**🎉 完成 SharePoint 整合後，您將擁有企業級的數據管理和協作能力！**
