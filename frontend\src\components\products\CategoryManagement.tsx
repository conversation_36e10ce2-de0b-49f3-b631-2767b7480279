import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Table, 
  Button, 
  Form, 
  Input, 
  Space, 
  message, 
  Popconfirm,
  Typography,
  Tag,
  Switch
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  TagOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { ProductCategory, CreateCategoryRequest, UpdateCategoryRequest } from '../../services/productService';

const { TextArea } = Input;
const { Text } = Typography;

interface CategoryManagementProps {
  visible: boolean;
  onCancel: () => void;
}

interface CategoryFormData {
  name: string;
  description?: string;
  isActive?: boolean;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  visible,
  onCancel,
}) => {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null);
  const [form] = Form.useForm();

  // 模擬分類數據
  const mockCategories: ProductCategory[] = [
    {
      id: 1,
      name: '智慧型手機',
      description: '各品牌智慧型手機產品',
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 2,
      name: '筆記型電腦',
      description: '筆記型電腦及相關配件',
      isActive: true,
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-14T14:20:00Z',
    },
    {
      id: 3,
      name: '平板電腦',
      description: '平板電腦產品',
      isActive: false,
      createdAt: '2024-01-13T09:15:00Z',
      updatedAt: '2024-01-13T09:15:00Z',
    },
  ];

  useEffect(() => {
    if (visible) {
      fetchCategories();
    }
  }, [visible]);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      // 這裡會調用 productCategoryService.getCategories()
      await new Promise(resolve => setTimeout(resolve, 500));
      setCategories(mockCategories);
    } catch (error) {
      message.error('載入分類資料失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    form.setFieldsValue({ isActive: true });
    setFormVisible(true);
  };

  const handleEdit = (category: ProductCategory) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      isActive: category.isActive,
    });
    setFormVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      // 這裡會調用 productCategoryService.deleteCategory(id)
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('分類刪除成功');
      fetchCategories();
    } catch (error) {
      message.error('刪除分類失敗');
    }
  };

  const handleToggleStatus = async (category: ProductCategory) => {
    try {
      // 這裡會調用 productCategoryService.activateCategory 或 deactivateCategory
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`分類${category.isActive ? '停用' : '激活'}成功`);
      fetchCategories();
    } catch (error) {
      message.error(`${category.isActive ? '停用' : '激活'}分類失敗`);
    }
  };

  const handleFormSubmit = async () => {
    try {
      const values: CategoryFormData = await form.validateFields();
      
      const submitData = {
        name: values.name.trim(),
        description: values.description?.trim() || undefined,
        ...(editingCategory && { isActive: values.isActive }),
      };

      if (editingCategory) {
        // 更新分類
        // await productCategoryService.updateCategory(editingCategory.id, submitData);
        await new Promise(resolve => setTimeout(resolve, 500));
        message.success('分類更新成功');
      } else {
        // 新增分類
        // await productCategoryService.createCategory(submitData);
        await new Promise(resolve => setTimeout(resolve, 500));
        message.success('分類新增成功');
      }
      
      setFormVisible(false);
      setEditingCategory(null);
      form.resetFields();
      fetchCategories();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleFormCancel = () => {
    setFormVisible(false);
    setEditingCategory(null);
    form.resetFields();
  };

  const columns: ColumnsType<ProductCategory> = [
    {
      title: '分類名稱',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Space>
          <TagOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (description) => description || '-',
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '啟用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '建立時間',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('zh-TW'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="text"
            onClick={() => handleToggleStatus(record)}
            style={{ color: record.isActive ? '#faad14' : '#52c41a' }}
          >
            {record.isActive ? '停用' : '激活'}
          </Button>
          <Popconfirm
            title="確定要刪除這個分類嗎？"
            description="刪除後將無法恢復，且該分類下的產品將變為未分類。"
            onConfirm={() => handleDelete(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="產品分類管理"
        open={visible}
        onCancel={onCancel}
        footer={[
          <Button key="close" onClick={onCancel}>
            關閉
          </Button>,
        ]}
        width={800}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增分類
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={categories}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: false,
          }}
          size="small"
        />
      </Modal>

      {/* 分類表單模態框 */}
      <Modal
        title={editingCategory ? '編輯分類' : '新增分類'}
        open={formVisible}
        onCancel={handleFormCancel}
        footer={[
          <Button key="cancel" onClick={handleFormCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleFormSubmit}
          >
            {editingCategory ? '更新' : '新增'}
          </Button>,
        ]}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="分類名稱"
            rules={[
              { required: true, message: '請輸入分類名稱' },
              { min: 2, message: '分類名稱至少需要2個字符' },
              { max: 50, message: '分類名稱不能超過50個字符' },
            ]}
          >
            <Input
              placeholder="請輸入分類名稱"
              maxLength={50}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="分類描述"
            rules={[
              { max: 200, message: '分類描述不能超過200個字符' },
            ]}
          >
            <TextArea
              placeholder="請輸入分類描述（選填）"
              rows={3}
              maxLength={200}
              showCount
            />
          </Form.Item>

          {editingCategory && (
            <Form.Item
              name="isActive"
              label="分類狀態"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="啟用"
                unCheckedChildren="停用"
              />
            </Form.Item>
          )}
        </Form>

        {!editingCategory && (
          <div style={{ 
            background: '#f6f6f6', 
            padding: '12px', 
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>提示：</strong><br />
              • 分類名稱必須唯一<br />
              • 新增的分類預設為啟用狀態<br />
              • 停用的分類不會在產品表單中顯示
            </Text>
          </div>
        )}
      </Modal>
    </>
  );
};

export default CategoryManagement;
