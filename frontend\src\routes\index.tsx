import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import LoginForm from '../components/auth/LoginForm';
import RegisterForm from '../components/auth/RegisterForm';
import UserProfile from '../components/auth/UserProfile';
import ProtectedRoute from '../components/auth/ProtectedRoute';
import MainLayout from '../components/layout/MainLayout';
import Dashboard from '../components/dashboard/Dashboard';
import SimpleDashboard from '../components/dashboard/SimpleDashboard';
import CustomerManagement from '../components/customers/CustomerManagement';
import ProductManagement from '../components/products/ProductManagement';
import RepairManagement from '../components/repairs/RepairManagement';
import ReportsManagement from '../components/reports/ReportsManagement';
import SystemManagement from '../components/system/SystemManagement';
import SharePointManagement from '../components/sharepoint/SharePointManagement';
import SystemTesting from '../components/testing/SystemTesting';

// 暫時的頁面組件
const ComingSoonPage: React.FC<{ title: string }> = ({ title }) => {
  return (
    <Result
      title={title}
      subTitle="此功能正在開發中，敬請期待"
      extra={
        <Button type="primary" onClick={() => window.history.back()}>
          返回上一頁
        </Button>
      }
    />
  );
};

// 404 頁面
const NotFoundPage: React.FC = () => {
  return (
    <Result
      status="404"
      title="404"
      subTitle="抱歉，您訪問的頁面不存在"
      extra={
        <Button type="primary" href="/dashboard">
          返回首頁
        </Button>
      }
    />
  );
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 重定向根路徑到登入頁面 */}
      <Route path="/" element={<Navigate to="/login" replace />} />

      {/* 認證頁面 */}
      <Route path="/login" element={<LoginForm />} />
      <Route path="/register" element={<RegisterForm />} />

      {/* 簡化的儀表板路由 - 暫時不使用 ProtectedRoute */}
      <Route path="/dashboard" element={<SimpleDashboard />} />

      {/* 受保護的路由 */}
      <Route path="/" element={
        <ProtectedRoute>
          <MainLayout />
        </ProtectedRoute>
      }>
        <Route path="repair-records" element={<RepairManagement />} />
        <Route path="customers" element={<CustomerManagement />} />
        <Route path="products" element={<ProductManagement />} />
        <Route path="parts" element={<ComingSoonPage title="零件管理" />} />
        <Route path="reports" element={<ReportsManagement />} />
        <Route path="sharepoint" element={<SharePointManagement />} />
        <Route path="testing" element={<SystemTesting />} />
        <Route path="profile" element={<UserProfile />} />
        <Route path="settings" element={<SystemManagement />} />

        {/* 管理員路由 */}
        <Route path="admin" element={
          <ProtectedRoute requiredRole="ADMIN">
            <Routes>
              <Route path="users" element={<ComingSoonPage title="用戶管理" />} />
              <Route path="settings" element={<ComingSoonPage title="系統設定" />} />
              <Route path="logs" element={<ComingSoonPage title="操作日誌" />} />
            </Routes>
          </ProtectedRoute>
        } />
      </Route>

      {/* 404 頁面 */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default AppRoutes;
