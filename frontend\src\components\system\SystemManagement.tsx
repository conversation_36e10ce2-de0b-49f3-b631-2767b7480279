import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space,
  Button,
  message
} from 'antd';
import { 
  UserOutlined,
  SettingOutlined,
  ShieldCheckOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import UserManagement from './UserManagement';
import RoleManagement from './RoleManagement';
import SystemSettings from './SystemSettings';
import AuditLogs from './AuditLogs';
import SystemMaintenance from './SystemMaintenance';
import SystemOverview from './SystemOverview';

const { Title } = Typography;
const { TabPane } = Tabs;

const SystemManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);

  const handleRefresh = () => {
    setLoading(true);
    // 觸發當前選項卡重新載入數據
    setTimeout(() => {
      setLoading(false);
      message.success('數據已更新');
    }, 1000);
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      // 這裡會調用 systemService.exportData
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('系統數據匯出成功');
    } catch (error) {
      message.error('數據匯出失敗');
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BarChartOutlined />
          系統總覽
        </span>
      ),
      children: <SystemOverview />,
    },
    {
      key: 'users',
      label: (
        <span>
          <UserOutlined />
          用戶管理
        </span>
      ),
      children: <UserManagement />,
    },
    {
      key: 'roles',
      label: (
        <span>
          <ShieldCheckOutlined />
          角色權限
        </span>
      ),
      children: <RoleManagement />,
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          系統設定
        </span>
      ),
      children: <SystemSettings />,
    },
    {
      key: 'audit',
      label: (
        <span>
          <FileTextOutlined />
          審計日誌
        </span>
      ),
      children: <AuditLogs />,
    },
    {
      key: 'maintenance',
      label: (
        <span>
          <DatabaseOutlined />
          系統維護
        </span>
      ),
      children: <SystemMaintenance />,
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            ⚙️ 系統設定
          </Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              重新整理
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
              loading={loading}
            >
              匯出數據
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </div>
  );
};

export default SystemManagement;
