import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tabs, 
  DatePicker, 
  Button, 
  Space,
  Select,
  message,
  Spin
} from 'antd';
import { 
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  TableOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import OverviewReport from './OverviewReport';
import RepairAnalytics from './RepairAnalytics';
import CustomerAnalytics from './CustomerAnalytics';
import ProductAnalytics from './ProductAnalytics';
import TechnicianPerformance from './TechnicianPerformance';
import RevenueAnalytics from './RevenueAnalytics';
import { StatisticsQueryParams } from '../../services/statisticsService';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const ReportsManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'days'),
    dayjs()
  ]);
  const [filters, setFilters] = useState<StatisticsQueryParams>({
    dateFrom: dayjs().subtract(30, 'days').format('YYYY-MM-DD'),
    dateTo: dayjs().format('YYYY-MM-DD'),
  });

  const handleDateRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates) {
      setDateRange(dates);
      setFilters(prev => ({
        ...prev,
        dateFrom: dates[0].format('YYYY-MM-DD'),
        dateTo: dates[1].format('YYYY-MM-DD'),
      }));
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleRefresh = () => {
    setLoading(true);
    // 觸發所有子組件重新載入數據
    setTimeout(() => {
      setLoading(false);
      message.success('數據已更新');
    }, 1000);
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      // 這裡會調用 statisticsService.exportReport
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('報表匯出成功，請檢查下載資料夾');
    } catch (error) {
      message.error('報表匯出失敗');
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BarChartOutlined />
          總覽報表
        </span>
      ),
      children: <OverviewReport filters={filters} />,
    },
    {
      key: 'repairs',
      label: (
        <span>
          <LineChartOutlined />
          維修分析
        </span>
      ),
      children: <RepairAnalytics filters={filters} />,
    },
    {
      key: 'customers',
      label: (
        <span>
          <PieChartOutlined />
          客戶分析
        </span>
      ),
      children: <CustomerAnalytics filters={filters} />,
    },
    {
      key: 'products',
      label: (
        <span>
          <TableOutlined />
          產品分析
        </span>
      ),
      children: <ProductAnalytics filters={filters} />,
    },
    {
      key: 'technicians',
      label: (
        <span>
          <BarChartOutlined />
          技師績效
        </span>
      ),
      children: <TechnicianPerformance filters={filters} />,
    },
    {
      key: 'revenue',
      label: (
        <span>
          <LineChartOutlined />
          營收分析
        </span>
      ),
      children: <RevenueAnalytics filters={filters} />,
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              📈 統計報表
            </Title>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                重新整理
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
                loading={loading}
              >
                匯出報表
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Space align="center">
              <FilterOutlined />
              <span style={{ fontWeight: 'bold' }}>篩選條件：</span>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>日期範圍：</span>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                format="YYYY-MM-DD"
                placeholder={['開始日期', '結束日期']}
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <span>狀態：</span>
              <Select
                style={{ width: 120 }}
                placeholder="所有狀態"
                allowClear
                onChange={(value) => handleFilterChange('status', value)}
              >
                <Option value="PENDING_INSPECTION">待檢測</Option>
                <Option value="INSPECTING">檢測中</Option>
                <Option value="PENDING_REPAIR">待維修</Option>
                <Option value="REPAIRING">維修中</Option>
                <Option value="PENDING_PARTS">待零件</Option>
                <Option value="COMPLETED">已完成</Option>
                <Option value="DELIVERED">已交付</Option>
                <Option value="CANCELLED">已取消</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>優先級：</span>
              <Select
                style={{ width: 100 }}
                placeholder="所有優先級"
                allowClear
                onChange={(value) => handleFilterChange('priority', value)}
              >
                <Option value="LOW">低</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="HIGH">高</Option>
                <Option value="URGENT">緊急</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>分組：</span>
              <Select
                style={{ width: 100 }}
                defaultValue="day"
                onChange={(value) => handleFilterChange('groupBy', value)}
              >
                <Option value="day">日</Option>
                <Option value="week">週</Option>
                <Option value="month">月</Option>
                <Option value="quarter">季</Option>
                <Option value="year">年</Option>
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Spin>
    </div>
  );
};

export default ReportsManagement;
