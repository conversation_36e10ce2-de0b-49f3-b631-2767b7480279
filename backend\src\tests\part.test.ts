import request from 'supertest';
import { PrismaClient, UserRole } from '@prisma/client';
import app from '../index';
import { PasswordUtils } from '../utils/password';
import { JWTUtils } from '../utils/jwt';

const prisma = new PrismaClient();

describe('Part Management API', () => {
  let adminToken: string;
  let customerServiceToken: string;
  let technicianToken: string;
  let viewerToken: string;
  let testUsers: any[] = [];
  let testParts: any[] = [];

  beforeAll(async () => {
    // 建立測試用戶
    const adminPassword = await PasswordUtils.hashPassword('AdminPass123!');
    const csPassword = await PasswordUtils.hashPassword('CSPass123!');
    const techPassword = await PasswordUtils.hashPassword('TechPass123!');
    const viewerPassword = await PasswordUtils.hashPassword('ViewerPass123!');

    const admin = await prisma.user.create({
      data: {
        username: 'testadmin_part',
        email: '<EMAIL>',
        passwordHash: adminPassword,
        fullName: 'Test Admin Part',
        role: UserRole.ADMIN,
      },
    });

    const customerService = await prisma.user.create({
      data: {
        username: 'testcs_part',
        email: '<EMAIL>',
        passwordHash: csPassword,
        fullName: 'Test Customer Service Part',
        role: UserRole.CUSTOMER_SERVICE,
      },
    });

    const technician = await prisma.user.create({
      data: {
        username: 'testtech_part',
        email: '<EMAIL>',
        passwordHash: techPassword,
        fullName: 'Test Technician Part',
        role: UserRole.TECHNICIAN,
      },
    });

    const viewer = await prisma.user.create({
      data: {
        username: 'testviewer_part',
        email: '<EMAIL>',
        passwordHash: viewerPassword,
        fullName: 'Test Viewer Part',
        role: UserRole.VIEWER,
      },
    });

    testUsers = [admin, customerService, technician, viewer];

    // 生成令牌
    adminToken = JWTUtils.generateAccessToken({
      userId: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: admin.role,
    });

    customerServiceToken = JWTUtils.generateAccessToken({
      userId: customerService.id.toString(),
      username: customerService.username,
      email: customerService.email,
      role: customerService.role,
    });

    technicianToken = JWTUtils.generateAccessToken({
      userId: technician.id.toString(),
      username: technician.username,
      email: technician.email,
      role: technician.role,
    });

    viewerToken = JWTUtils.generateAccessToken({
      userId: viewer.id.toString(),
      username: viewer.username,
      email: viewer.email,
      role: viewer.role,
    });

    // 建立測試零件
    const part1 = await prisma.part.create({
      data: {
        name: 'Test Part 1',
        partNumber: 'TP001',
        category: 'Electronic',
        brand: 'Test Brand',
        description: 'Test part description 1',
        specifications: 'Test specifications 1',
        unitPrice: 100,
        currency: 'TWD',
        supplier: 'Test Supplier',
        minimumStock: 10,
        currentStock: 50,
        reservedStock: 5,
        location: 'A1-B2',
      },
    });

    const part2 = await prisma.part.create({
      data: {
        name: 'Test Part 2',
        partNumber: 'TP002',
        category: 'Mechanical',
        brand: 'Another Brand',
        description: 'Test part description 2',
        unitPrice: 200,
        currency: 'TWD',
        minimumStock: 5,
        currentStock: 2, // 低庫存
        reservedStock: 0,
        isActive: false,
      },
    });

    const part3 = await prisma.part.create({
      data: {
        name: 'Test Part 3',
        partNumber: 'TP003',
        category: 'Electronic',
        brand: 'Test Brand',
        minimumStock: 20,
        currentStock: 0, // 缺貨
        reservedStock: 0,
      },
    });

    testParts = [part1, part2, part3];
  });

  afterAll(async () => {
    // 清理測試資料
    for (const part of testParts) {
      await prisma.stockTransaction.deleteMany({ where: { partId: part.id } });
      await prisma.part.delete({ where: { id: part.id } }).catch(() => {});
    }

    for (const user of testUsers) {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }

    // 清理其他測試零件
    await prisma.stockTransaction.deleteMany({
      where: {
        part: {
          name: { startsWith: 'Test Part' },
        },
      },
    });

    await prisma.part.deleteMany({
      where: {
        name: { startsWith: 'Test Part' },
      },
    });

    await prisma.$disconnect();
  });

  describe('GET /api/v1/parts', () => {
    test('should get part list as admin', async () => {
      const response = await request(app)
        .get('/api/v1/parts')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('parts');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.parts)).toBe(true);
    });

    test('should get part list as technician', async () => {
      const response = await request(app)
        .get('/api/v1/parts')
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('parts');
    });

    test('should get part list with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/parts?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.parts.length).toBeLessThanOrEqual(1);
    });

    test('should filter parts by category', async () => {
      const response = await request(app)
        .get('/api/v1/parts?category=Electronic')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.parts.every((part: any) => 
        part.category.includes('Electronic')
      )).toBe(true);
    });

    test('should filter parts by low stock', async () => {
      const response = await request(app)
        .get('/api/v1/parts?lowStock=true')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // 應該包含庫存低於最小庫存的零件
      expect(response.body.data.parts.some((part: any) => 
        part.currentStock <= part.minimumStock
      )).toBe(true);
    });

    test('should filter parts by out of stock', async () => {
      const response = await request(app)
        .get('/api/v1/parts?outOfStock=true')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // 應該只包含缺貨的零件
      expect(response.body.data.parts.every((part: any) => 
        part.currentStock <= 0
      )).toBe(true);
    });

    test('should fail without authentication', async () => {
      await request(app)
        .get('/api/v1/parts')
        .expect(401);
    });

    test('should fail with insufficient permissions', async () => {
      await request(app)
        .get('/api/v1/parts')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/parts', () => {
    test('should create part as admin', async () => {
      const partData = {
        name: 'New Test Part',
        partNumber: 'NTP001',
        category: 'Electronic',
        brand: 'New Test Brand',
        description: 'New test part description',
        specifications: 'New test specifications',
        unitPrice: 150,
        currency: 'TWD',
        supplier: 'New Test Supplier',
        minimumStock: 15,
        currentStock: 30,
        location: 'B1-C2',
      };

      const response = await request(app)
        .post('/api/v1/parts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(partData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.name).toBe(partData.name);
      expect(response.body.data.part.partNumber).toBe(partData.partNumber);
      expect(response.body.data.part.availableStock).toBe(partData.currentStock);
    });

    test('should create part as customer service', async () => {
      const partData = {
        name: 'CS Test Part',
        partNumber: 'CSTP001',
        category: 'Mechanical',
        brand: 'CS Test Brand',
        unitPrice: 80,
        currency: 'TWD',
        minimumStock: 5,
        currentStock: 20,
      };

      const response = await request(app)
        .post('/api/v1/parts')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(partData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.name).toBe(partData.name);
    });

    test('should fail with duplicate part number', async () => {
      const partData = {
        name: 'Duplicate Part Number',
        partNumber: 'TP001', // 已存在的零件編號
        category: 'Electronic',
        brand: 'Test Brand',
      };

      await request(app)
        .post('/api/v1/parts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(partData)
        .expect(400);
    });

    test('should fail with invalid data', async () => {
      const partData = {
        name: 'A', // 太短
        partNumber: '',
        category: '',
        unitPrice: -1, // 負數
      };

      await request(app)
        .post('/api/v1/parts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(partData)
        .expect(400);
    });

    test('should fail without permission', async () => {
      const partData = {
        name: 'Unauthorized Part',
        partNumber: 'UP001',
        category: 'Electronic',
        brand: 'Unauthorized Brand',
      };

      await request(app)
        .post('/api/v1/parts')
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(partData)
        .expect(403);
    });
  });

  describe('GET /api/v1/parts/:id', () => {
    test('should get part by id', async () => {
      const partId = testParts[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/parts/${partId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.id).toBe(partId);
      expect(response.body.data.part.availableStock).toBe(
        testParts[0].currentStock - testParts[0].reservedStock
      );
    });

    test('should get part with details', async () => {
      const partId = testParts[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/parts/${partId}?includeDetails=true`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part).toHaveProperty('statistics');
      expect(response.body.data.part).toHaveProperty('stockHistory');
      expect(response.body.data.part).toHaveProperty('usageHistory');
    });

    test('should fail with non-existent part', async () => {
      await request(app)
        .get('/api/v1/parts/999999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/v1/parts/:id', () => {
    test('should update part as admin', async () => {
      const partId = testParts[0].id.toString();
      const updateData = {
        name: 'Updated Part Name',
        unitPrice: 120,
        minimumStock: 15,
      };

      const response = await request(app)
        .put(`/api/v1/parts/${partId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.name).toBe(updateData.name);
      expect(response.body.data.part.unitPrice).toBe(updateData.unitPrice);
    });

    test('should update part as customer service', async () => {
      const partId = testParts[1].id.toString();
      const updateData = {
        name: 'CS Updated Part',
        brand: 'Updated Brand',
      };

      const response = await request(app)
        .put(`/api/v1/parts/${partId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.name).toBe(updateData.name);
    });

    test('should fail without permission', async () => {
      const partId = testParts[0].id.toString();
      const updateData = {
        name: 'Unauthorized Update',
      };

      await request(app)
        .put(`/api/v1/parts/${partId}`)
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('GET /api/v1/parts/search', () => {
    test('should search parts', async () => {
      const response = await request(app)
        .get('/api/v1/parts/search?q=Test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(Array.isArray(response.body.data.results)).toBe(true);
    });

    test('should fail with short search query', async () => {
      await request(app)
        .get('/api/v1/parts/search?q=A')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/parts/statistics', () => {
    test('should get part statistics', async () => {
      const response = await request(app)
        .get('/api/v1/parts/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('active');
      expect(response.body.data.statistics).toHaveProperty('lowStock');
      expect(response.body.data.statistics).toHaveProperty('outOfStock');
      expect(response.body.data.statistics).toHaveProperty('byCategory');
      expect(response.body.data.statistics).toHaveProperty('stockStatus');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/parts/statistics')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('GET /api/v1/parts/stock-alerts', () => {
    test('should get stock alerts', async () => {
      const response = await request(app)
        .get('/api/v1/parts/stock-alerts')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('alerts');
      expect(Array.isArray(response.body.data.alerts)).toBe(true);
      
      // 應該包含低庫存和缺貨警報
      expect(response.body.data.alerts.length).toBeGreaterThan(0);
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/parts/stock-alerts')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('GET /api/v1/parts/part-number/:partNumber', () => {
    test('should find part by part number', async () => {
      const response = await request(app)
        .get('/api/v1/parts/part-number/TP001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.partNumber).toBe('TP001');
    });

    test('should fail with non-existent part number', async () => {
      await request(app)
        .get('/api/v1/parts/part-number/NONEXISTENT')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/parts/check-part-number/:partNumber', () => {
    test('should check part number availability', async () => {
      const response = await request(app)
        .get('/api/v1/parts/check-part-number/AVAILABLE001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
    });

    test('should detect existing part number', async () => {
      const response = await request(app)
        .get('/api/v1/parts/check-part-number/TP001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(false);
    });
  });

  describe('POST /api/v1/parts/:id/activate', () => {
    test('should activate part', async () => {
      const partId = testParts[1].id.toString(); // 這個零件是停用的

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/activate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.isActive).toBe(true);
    });
  });

  describe('POST /api/v1/parts/:id/deactivate', () => {
    test('should deactivate part', async () => {
      const partId = testParts[0].id.toString();

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/deactivate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.part.isActive).toBe(false);
    });
  });

  describe('DELETE /api/v1/parts/:id', () => {
    test('should delete part as admin', async () => {
      // 先創建一個測試零件用於刪除
      const testPart = await prisma.part.create({
        data: {
          name: 'Part to Delete',
          partNumber: 'PTD001',
          category: 'Electronic',
          brand: 'Delete Brand',
          currentStock: 0, // 無庫存才能刪除
        },
      });

      const response = await request(app)
        .delete(`/api/v1/parts/${testPart.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should fail without admin permission', async () => {
      const partId = testParts[0].id.toString();

      await request(app)
        .delete(`/api/v1/parts/${partId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .expect(403);
    });
  });

  // === 庫存操作測試 ===

  describe('POST /api/v1/parts/stock-operation', () => {
    test('should perform stock operation as admin', async () => {
      const partId = testParts[0].id.toString();
      const operationData = {
        partId,
        type: 'IN',
        quantity: 10,
        reason: '採購入庫',
        referenceId: 'PO001',
      };

      const response = await request(app)
        .post('/api/v1/parts/stock-operation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(operationData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('IN');
      expect(response.body.data.result.operation.quantity).toBe(10);
    });

    test('should fail with invalid operation type', async () => {
      const partId = testParts[0].id.toString();
      const operationData = {
        partId,
        type: 'INVALID',
        quantity: 10,
        reason: '無效操作',
      };

      await request(app)
        .post('/api/v1/parts/stock-operation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(operationData)
        .expect(400);
    });

    test('should fail without permission', async () => {
      const partId = testParts[0].id.toString();
      const operationData = {
        partId,
        type: 'IN',
        quantity: 10,
        reason: '無權限操作',
      };

      await request(app)
        .post('/api/v1/parts/stock-operation')
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(operationData)
        .expect(403);
    });
  });

  describe('POST /api/v1/parts/:id/stock-in', () => {
    test('should perform stock in operation', async () => {
      const partId = testParts[0].id.toString();
      const stockInData = {
        quantity: 20,
        reason: '供應商交貨',
        referenceId: 'PO002',
      };

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/stock-in`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(stockInData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('IN');
      expect(response.body.data.result.operation.quantity).toBe(20);
    });

    test('should fail with zero quantity', async () => {
      const partId = testParts[0].id.toString();
      const stockInData = {
        quantity: 0,
        reason: '無效數量',
      };

      await request(app)
        .post(`/api/v1/parts/${partId}/stock-in`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(stockInData)
        .expect(400);
    });
  });

  describe('POST /api/v1/parts/:id/stock-out', () => {
    test('should perform stock out operation', async () => {
      const partId = testParts[0].id.toString();
      const stockOutData = {
        quantity: 5,
        reason: '維修使用',
        referenceId: 'RO001',
      };

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/stock-out`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(stockOutData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('OUT');
      expect(response.body.data.result.operation.quantity).toBe(5);
    });

    test('should fail with insufficient stock', async () => {
      const partId = testParts[2].id.toString(); // 缺貨的零件
      const stockOutData = {
        quantity: 10,
        reason: '庫存不足測試',
      };

      await request(app)
        .post(`/api/v1/parts/${partId}/stock-out`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(stockOutData)
        .expect(400);
    });
  });

  describe('POST /api/v1/parts/:id/adjust-stock', () => {
    test('should perform stock adjustment as admin', async () => {
      const partId = testParts[0].id.toString();
      const adjustData = {
        quantity: 100,
        reason: '盤點調整',
      };

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/adjust-stock`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(adjustData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('ADJUSTMENT');
      expect(response.body.data.result.newStock).toBe(100);
    });

    test('should fail without admin permission', async () => {
      const partId = testParts[0].id.toString();
      const adjustData = {
        quantity: 100,
        reason: '無權限調整',
      };

      await request(app)
        .post(`/api/v1/parts/${partId}/adjust-stock`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(adjustData)
        .expect(403);
    });
  });

  describe('POST /api/v1/parts/:id/reserve-stock', () => {
    test('should reserve stock', async () => {
      const partId = testParts[0].id.toString();
      const reserveData = {
        quantity: 10,
        reason: '維修預留',
        referenceId: 'RO002',
      };

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/reserve-stock`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(reserveData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('RESERVED');
      expect(response.body.data.result.operation.quantity).toBe(10);
    });

    test('should fail with insufficient available stock', async () => {
      const partId = testParts[2].id.toString(); // 缺貨的零件
      const reserveData = {
        quantity: 5,
        reason: '可用庫存不足測試',
      };

      await request(app)
        .post(`/api/v1/parts/${partId}/reserve-stock`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(reserveData)
        .expect(400);
    });
  });

  describe('POST /api/v1/parts/:id/release-stock', () => {
    test('should release reserved stock', async () => {
      // 先預留一些庫存
      const partId = testParts[0].id.toString();
      await request(app)
        .post(`/api/v1/parts/${partId}/reserve-stock`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          quantity: 5,
          reason: '測試預留',
          referenceId: 'TEST001',
        });

      // 然後釋放預留庫存
      const releaseData = {
        quantity: 3,
        reason: '釋放部分預留',
        referenceId: 'TEST001',
      };

      const response = await request(app)
        .post(`/api/v1/parts/${partId}/release-stock`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(releaseData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.operation.type).toBe('RELEASED');
      expect(response.body.data.result.operation.quantity).toBe(3);
    });
  });

  describe('POST /api/v1/parts/batch-stock-operation', () => {
    test('should perform batch stock operations as admin', async () => {
      const batchData = {
        operations: [
          {
            partId: testParts[0].id.toString(),
            type: 'IN',
            quantity: 5,
            reason: '批量入庫1',
          },
          {
            partId: testParts[1].id.toString(),
            type: 'IN',
            quantity: 10,
            reason: '批量入庫2',
          },
        ],
        batchReason: '批量採購入庫',
      };

      const response = await request(app)
        .post('/api/v1/parts/batch-stock-operation')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(batchData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.result.success).toBe(2);
      expect(response.body.data.result.failed).toBe(0);
    });

    test('should fail without admin permission', async () => {
      const batchData = {
        operations: [
          {
            partId: testParts[0].id.toString(),
            type: 'IN',
            quantity: 5,
            reason: '無權限批量操作',
          },
        ],
      };

      await request(app)
        .post('/api/v1/parts/batch-stock-operation')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(batchData)
        .expect(403);
    });
  });
});
