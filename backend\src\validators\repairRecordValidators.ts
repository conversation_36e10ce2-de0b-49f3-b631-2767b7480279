import { body, query, param, ValidationChain } from 'express-validator';

// 創建維修記錄驗證
export const createRepairRecordValidation: ValidationChain[] = [
  body('customerId')
    .notEmpty()
    .withMessage('客戶ID不能為空')
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  body('productId')
    .notEmpty()
    .withMessage('產品ID不能為空')
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  body('serialNumber')
    .optional()
    .isLength({ max: 100 })
    .withMessage('序號長度不能超過100個字符')
    .trim(),

  body('issueDescription')
    .notEmpty()
    .withMessage('問題描述不能為空')
    .isLength({ min: 10, max: 2000 })
    .withMessage('問題描述長度必須在10-2000個字符之間')
    .trim(),

  body('symptoms')
    .optional()
    .isArray()
    .withMessage('症狀必須是陣列'),

  body('symptoms.*')
    .optional()
    .isString()
    .withMessage('症狀項目必須是字串')
    .isLength({ min: 1, max: 200 })
    .withMessage('症狀項目長度必須在1-200個字符之間')
    .trim(),

  body('priority')
    .optional()
    .isIn(['LOW', 'NORMAL', 'HIGH', 'URGENT', 'CRITICAL'])
    .withMessage('優先級必須是LOW、NORMAL、HIGH、URGENT或CRITICAL'),

  body('assignedTechnicianId')
    .optional()
    .isNumeric()
    .withMessage('指派技師ID必須是數字'),

  body('estimatedCost')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('預估費用必須在0-999999999之間')
    .toFloat(),

  body('estimatedCompletionDate')
    .optional()
    .isISO8601()
    .withMessage('預計完成日期必須是有效的日期格式')
    .toDate(),

  body('warrantyStatus')
    .optional()
    .isIn(['IN_WARRANTY', 'OUT_OF_WARRANTY', 'EXTENDED_WARRANTY', 'UNKNOWN'])
    .withMessage('保固狀態必須是IN_WARRANTY、OUT_OF_WARRANTY、EXTENDED_WARRANTY或UNKNOWN'),

  body('warrantyExpiryDate')
    .optional()
    .isISO8601()
    .withMessage('保固到期日期必須是有效的日期格式')
    .toDate(),

  body('receivedDate')
    .optional()
    .isISO8601()
    .withMessage('接收日期必須是有效的日期格式')
    .toDate(),

  body('notes')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('備註長度不能超過2000個字符')
    .trim(),

  body('internalNotes')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('內部備註長度不能超過2000個字符')
    .trim(),
];

// 更新維修記錄驗證
export const updateRepairRecordValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  body('customerId')
    .optional()
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  body('productId')
    .optional()
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  body('serialNumber')
    .optional()
    .isLength({ max: 100 })
    .withMessage('序號長度不能超過100個字符')
    .trim(),

  body('issueDescription')
    .optional()
    .isLength({ min: 10, max: 2000 })
    .withMessage('問題描述長度必須在10-2000個字符之間')
    .trim(),

  body('symptoms')
    .optional()
    .isArray()
    .withMessage('症狀必須是陣列'),

  body('symptoms.*')
    .optional()
    .isString()
    .withMessage('症狀項目必須是字串')
    .isLength({ min: 1, max: 200 })
    .withMessage('症狀項目長度必須在1-200個字符之間')
    .trim(),

  body('priority')
    .optional()
    .isIn(['LOW', 'NORMAL', 'HIGH', 'URGENT', 'CRITICAL'])
    .withMessage('優先級必須是LOW、NORMAL、HIGH、URGENT或CRITICAL'),

  body('status')
    .optional()
    .isIn(['RECEIVED', 'DIAGNOSED', 'WAITING_PARTS', 'IN_PROGRESS', 'TESTING', 'COMPLETED', 'QUALITY_CHECK', 'READY_PICKUP', 'DELIVERED', 'CANCELLED', 'ON_HOLD'])
    .withMessage('狀態必須是有效的維修狀態'),

  body('assignedTechnicianId')
    .optional()
    .isNumeric()
    .withMessage('指派技師ID必須是數字'),

  body('estimatedCost')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('預估費用必須在0-999999999之間')
    .toFloat(),

  body('actualCost')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('實際費用必須在0-999999999之間')
    .toFloat(),

  body('estimatedCompletionDate')
    .optional()
    .isISO8601()
    .withMessage('預計完成日期必須是有效的日期格式')
    .toDate(),

  body('actualCompletionDate')
    .optional()
    .isISO8601()
    .withMessage('實際完成日期必須是有效的日期格式')
    .toDate(),

  body('warrantyStatus')
    .optional()
    .isIn(['IN_WARRANTY', 'OUT_OF_WARRANTY', 'EXTENDED_WARRANTY', 'UNKNOWN'])
    .withMessage('保固狀態必須是IN_WARRANTY、OUT_OF_WARRANTY、EXTENDED_WARRANTY或UNKNOWN'),

  body('warrantyExpiryDate')
    .optional()
    .isISO8601()
    .withMessage('保固到期日期必須是有效的日期格式')
    .toDate(),

  body('startedDate')
    .optional()
    .isISO8601()
    .withMessage('開始日期必須是有效的日期格式')
    .toDate(),

  body('completedDate')
    .optional()
    .isISO8601()
    .withMessage('完成日期必須是有效的日期格式')
    .toDate(),

  body('deliveredDate')
    .optional()
    .isISO8601()
    .withMessage('交付日期必須是有效的日期格式')
    .toDate(),

  body('notes')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('備註長度不能超過2000個字符')
    .trim(),

  body('internalNotes')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('內部備註長度不能超過2000個字符')
    .trim(),
];

// 維修記錄列表查詢驗證
export const repairRecordListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('customerId')
    .optional()
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  query('productId')
    .optional()
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  query('assignedTechnicianId')
    .optional()
    .isNumeric()
    .withMessage('指派技師ID必須是數字'),

  query('status')
    .optional()
    .isIn(['RECEIVED', 'DIAGNOSED', 'WAITING_PARTS', 'IN_PROGRESS', 'TESTING', 'COMPLETED', 'QUALITY_CHECK', 'READY_PICKUP', 'DELIVERED', 'CANCELLED', 'ON_HOLD'])
    .withMessage('狀態必須是有效的維修狀態'),

  query('priority')
    .optional()
    .isIn(['LOW', 'NORMAL', 'HIGH', 'URGENT', 'CRITICAL'])
    .withMessage('優先級必須是LOW、NORMAL、HIGH、URGENT或CRITICAL'),

  query('warrantyStatus')
    .optional()
    .isIn(['IN_WARRANTY', 'OUT_OF_WARRANTY', 'EXTENDED_WARRANTY', 'UNKNOWN'])
    .withMessage('保固狀態必須是IN_WARRANTY、OUT_OF_WARRANTY、EXTENDED_WARRANTY或UNKNOWN'),

  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('開始日期必須是有效的日期格式')
    .toDate(),

  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('結束日期必須是有效的日期格式')
    .toDate(),

  query('costMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低費用必須大於等於0')
    .toFloat(),

  query('costMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高費用必須大於等於0')
    .toFloat(),

  query('overdue')
    .optional()
    .isBoolean()
    .withMessage('逾期篩選必須是布林值')
    .toBoolean(),

  query('sortBy')
    .optional()
    .isIn(['repairNumber', 'receivedDate', 'priority', 'status', 'estimatedCompletionDate', 'actualCost', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 維修記錄ID參數驗證
export const repairRecordIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),
];

// 維修記錄搜尋驗證
export const repairRecordSearchValidation: ValidationChain[] = [
  query('q')
    .notEmpty()
    .withMessage('搜尋關鍵字不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('結果數量限制必須在1-50之間')
    .toInt(),
];

// 維修單號參數驗證
export const repairNumberParamValidation: ValidationChain[] = [
  param('repairNumber')
    .notEmpty()
    .withMessage('維修單號不能為空')
    .isLength({ min: 1, max: 50 })
    .withMessage('維修單號長度必須在1-50個字符之間')
    .trim(),
];

// 維修記錄詳細資訊查詢驗證
export const repairRecordDetailValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  query('includeDetails')
    .optional()
    .isBoolean()
    .withMessage('includeDetails必須是布林值')
    .toBoolean(),
];

// 更新維修狀態驗證
export const updateRepairStatusValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  body('status')
    .notEmpty()
    .withMessage('狀態不能為空')
    .isIn(['RECEIVED', 'DIAGNOSED', 'WAITING_PARTS', 'IN_PROGRESS', 'TESTING', 'COMPLETED', 'QUALITY_CHECK', 'READY_PICKUP', 'DELIVERED', 'CANCELLED', 'ON_HOLD'])
    .withMessage('狀態必須是有效的維修狀態'),

  body('reason')
    .optional()
    .isLength({ min: 1, max: 500 })
    .withMessage('原因長度必須在1-500個字符之間')
    .trim(),

  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('備註長度不能超過1000個字符')
    .trim(),

  body('estimatedCompletionDate')
    .optional()
    .isISO8601()
    .withMessage('預計完成日期必須是有效的日期格式')
    .toDate(),

  body('actualCompletionDate')
    .optional()
    .isISO8601()
    .withMessage('實際完成日期必須是有效的日期格式')
    .toDate(),
];

// 指派技師驗證
export const assignTechnicianValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  body('technicianId')
    .notEmpty()
    .withMessage('技師ID不能為空')
    .isNumeric()
    .withMessage('技師ID必須是數字'),
];

// 使用零件驗證
export const usePartValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  body('partId')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('數量不能為空')
    .isInt({ min: 1 })
    .withMessage('數量必須是正整數')
    .toInt(),

  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('備註長度不能超過500個字符')
    .trim(),

  body('isWarrantyPart')
    .optional()
    .isBoolean()
    .withMessage('isWarrantyPart必須是布林值'),
];

// 批量使用零件驗證
export const batchUsePartsValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('維修記錄ID不能為空')
    .isNumeric()
    .withMessage('維修記錄ID必須是數字'),

  body('parts')
    .isArray({ min: 1, max: 50 })
    .withMessage('零件列表必須是包含1-50個元素的陣列'),

  body('parts.*.partId')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('parts.*.quantity')
    .notEmpty()
    .withMessage('數量不能為空')
    .isInt({ min: 1 })
    .withMessage('數量必須是正整數')
    .toInt(),

  body('parts.*.notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('備註長度不能超過500個字符')
    .trim(),

  body('parts.*.isWarrantyPart')
    .optional()
    .isBoolean()
    .withMessage('isWarrantyPart必須是布林值'),

  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('批量操作備註長度不能超過1000個字符')
    .trim(),
];

// 自定義驗證函數：檢查日期範圍
export const validateDateRange = () => {
  return query().custom((value, { req }) => {
    const { dateFrom, dateTo } = req.query;
    
    if (dateFrom && dateTo && new Date(dateFrom as string) > new Date(dateTo as string)) {
      throw new Error('開始日期不能大於結束日期');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查費用範圍
export const validateCostRange = () => {
  return query().custom((value, { req }) => {
    const { costMin, costMax } = req.query;
    
    if (costMin && costMax && parseFloat(costMin as string) > parseFloat(costMax as string)) {
      throw new Error('最低費用不能大於最高費用');
    }
    
    return true;
  });
};

// 組合驗證：維修記錄列表（包含範圍檢查）
export const repairRecordListWithRangeValidation = [
  ...repairRecordListValidation,
  validateDateRange(),
  validateCostRange(),
];
