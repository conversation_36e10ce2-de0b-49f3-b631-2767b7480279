import { PrismaClient, User, UserRole } from '@prisma/client';
import { 
  UserInfo, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserQueryParams,
  UserListResponse,
  UserStatistics,
  BatchUserOperation,
  BatchOperationResult,
  UserSearchResult,
  DEFAULT_USER_PAGINATION
} from '../types/user';
import { logger } from '../utils/logger';

export class UserRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找用戶
  async findById(id: string): Promise<UserInfo | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: BigInt(id) },
        select: {
          id: true,
          username: true,
          email: true,
          fullName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) return null;

      return {
        ...user,
        id: user.id.toString(),
      };
    } catch (error) {
      logger.error('查找用戶失敗:', error);
      throw new Error('查找用戶失敗');
    }
  }

  // 根據用戶名查找用戶
  async findByUsername(username: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { username },
      });
    } catch (error) {
      logger.error('根據用戶名查找用戶失敗:', error);
      throw new Error('查找用戶失敗');
    }
  }

  // 根據電子郵件查找用戶
  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { email },
      });
    } catch (error) {
      logger.error('根據電子郵件查找用戶失敗:', error);
      throw new Error('查找用戶失敗');
    }
  }

  // 檢查用戶名或電子郵件是否存在
  async checkExistence(username: string, email: string, excludeId?: string): Promise<{
    usernameExists: boolean;
    emailExists: boolean;
  }> {
    try {
      const whereCondition = excludeId 
        ? { 
            OR: [{ username }, { email }],
            NOT: { id: BigInt(excludeId) }
          }
        : { OR: [{ username }, { email }] };

      const existingUsers = await this.prisma.user.findMany({
        where: whereCondition,
        select: { username: true, email: true },
      });

      return {
        usernameExists: existingUsers.some(user => user.username === username),
        emailExists: existingUsers.some(user => user.email === email),
      };
    } catch (error) {
      logger.error('檢查用戶存在性失敗:', error);
      throw new Error('檢查用戶存在性失敗');
    }
  }

  // 創建用戶
  async create(userData: CreateUserRequest & { passwordHash: string }): Promise<UserInfo> {
    try {
      const user = await this.prisma.user.create({
        data: {
          username: userData.username,
          email: userData.email,
          passwordHash: userData.passwordHash,
          fullName: userData.fullName,
          role: userData.role,
          isActive: userData.isActive ?? true,
        },
        select: {
          id: true,
          username: true,
          email: true,
          fullName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('用戶創建成功:', { userId: user.id, username: user.username });

      return {
        ...user,
        id: user.id.toString(),
      };
    } catch (error) {
      logger.error('創建用戶失敗:', error);
      throw new Error('創建用戶失敗');
    }
  }

  // 更新用戶
  async update(id: string, userData: UpdateUserRequest): Promise<UserInfo> {
    try {
      const user = await this.prisma.user.update({
        where: { id: BigInt(id) },
        data: userData,
        select: {
          id: true,
          username: true,
          email: true,
          fullName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('用戶更新成功:', { userId: user.id, username: user.username });

      return {
        ...user,
        id: user.id.toString(),
      };
    } catch (error) {
      logger.error('更新用戶失敗:', error);
      throw new Error('更新用戶失敗');
    }
  }

  // 更新密碼
  async updatePassword(id: string, passwordHash: string): Promise<void> {
    try {
      await this.prisma.user.update({
        where: { id: BigInt(id) },
        data: { passwordHash },
      });

      logger.info('用戶密碼更新成功:', { userId: id });
    } catch (error) {
      logger.error('更新密碼失敗:', error);
      throw new Error('更新密碼失敗');
    }
  }

  // 刪除用戶（軟刪除 - 設為非活躍）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.user.update({
        where: { id: BigInt(id) },
        data: { isActive: false },
      });

      logger.info('用戶軟刪除成功:', { userId: id });
    } catch (error) {
      logger.error('軟刪除用戶失敗:', error);
      throw new Error('刪除用戶失敗');
    }
  }

  // 硬刪除用戶
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.user.delete({
        where: { id: BigInt(id) },
      });

      logger.info('用戶硬刪除成功:', { userId: id });
    } catch (error) {
      logger.error('硬刪除用戶失敗:', error);
      throw new Error('刪除用戶失敗');
    }
  }

  // 獲取用戶列表
  async findMany(params: UserQueryParams): Promise<UserListResponse> {
    try {
      const {
        page = DEFAULT_USER_PAGINATION.page,
        limit = DEFAULT_USER_PAGINATION.limit,
        search,
        role,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_USER_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {};

      if (search) {
        where.OR = [
          { username: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { fullName: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (role) {
        where.role = role;
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      // 執行查詢
      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where,
          select: {
            id: true,
            username: true,
            email: true,
            fullName: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.user.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        users: users.map(user => ({
          ...user,
          id: user.id.toString(),
        })),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          role,
          isActive,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取用戶列表失敗:', error);
      throw new Error('獲取用戶列表失敗');
    }
  }

  // 搜尋用戶
  async search(query: string, limit: number = 10): Promise<UserSearchResult[]> {
    try {
      const users = await this.prisma.user.findMany({
        where: {
          OR: [
            { username: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { fullName: { contains: query, mode: 'insensitive' } },
          ],
          isActive: true,
        },
        select: {
          id: true,
          username: true,
          email: true,
          fullName: true,
          role: true,
          isActive: true,
        },
        take: limit,
      });

      return users.map(user => ({
        ...user,
        id: user.id.toString(),
        relevanceScore: this.calculateRelevanceScore(user, query),
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logger.error('搜尋用戶失敗:', error);
      throw new Error('搜尋用戶失敗');
    }
  }

  // 獲取用戶統計
  async getStatistics(): Promise<UserStatistics> {
    try {
      const [total, active, byRole, recentCounts] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.count({ where: { isActive: true } }),
        this.prisma.user.groupBy({
          by: ['role'],
          _count: true,
        }),
        this.getRecentRegistrationCounts(),
      ]);

      const roleStats = byRole.reduce((acc, item) => {
        acc[item.role] = item._count;
        return acc;
      }, {} as Record<UserRole, number>);

      return {
        total,
        active,
        inactive: total - active,
        byRole: roleStats,
        recentRegistrations: recentCounts,
      };
    } catch (error) {
      logger.error('獲取用戶統計失敗:', error);
      throw new Error('獲取用戶統計失敗');
    }
  }

  // 批量操作
  async batchOperation(operation: BatchUserOperation): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
    };

    try {
      for (const userId of operation.userIds) {
        try {
          switch (operation.operation) {
            case 'activate':
              await this.update(userId, { isActive: true });
              break;
            case 'deactivate':
              await this.update(userId, { isActive: false });
              break;
            case 'delete':
              await this.softDelete(userId);
              break;
            case 'updateRole':
              if (operation.data?.role) {
                await this.update(userId, { role: operation.data.role });
              }
              break;
          }
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            userId,
            error: error instanceof Error ? error.message : '未知錯誤',
          });
        }
      }

      logger.info('批量操作完成:', { 
        operation: operation.operation, 
        success: result.success, 
        failed: result.failed 
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw new Error('批量操作失敗');
    }
  }

  // 計算相關性分數
  private calculateRelevanceScore(user: any, query: string): number {
    const lowerQuery = query.toLowerCase();
    let score = 0;

    // 精確匹配得分更高
    if (user.username.toLowerCase() === lowerQuery) score += 100;
    else if (user.username.toLowerCase().includes(lowerQuery)) score += 50;

    if (user.email.toLowerCase() === lowerQuery) score += 100;
    else if (user.email.toLowerCase().includes(lowerQuery)) score += 50;

    if (user.fullName.toLowerCase() === lowerQuery) score += 100;
    else if (user.fullName.toLowerCase().includes(lowerQuery)) score += 50;

    // 開頭匹配得分較高
    if (user.username.toLowerCase().startsWith(lowerQuery)) score += 25;
    if (user.fullName.toLowerCase().startsWith(lowerQuery)) score += 25;

    return score;
  }

  // 獲取最近註冊統計
  private async getRecentRegistrationCounts() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [todayCount, weekCount, monthCount] = await Promise.all([
      this.prisma.user.count({ where: { createdAt: { gte: today } } }),
      this.prisma.user.count({ where: { createdAt: { gte: thisWeek } } }),
      this.prisma.user.count({ where: { createdAt: { gte: thisMonth } } }),
    ]);

    return {
      today: todayCount,
      thisWeek: weekCount,
      thisMonth: monthCount,
    };
  }
}
