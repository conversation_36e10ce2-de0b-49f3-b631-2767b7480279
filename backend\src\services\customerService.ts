import { CustomerRepository } from '../repositories/customerRepository';
import { logger } from '../utils/logger';
import { 
  CustomerInfo, 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerQueryParams,
  CustomerListResponse,
  CustomerStatistics,
  CustomerSearchResult,
  CustomerDetailInfo,
  BatchCustomerOperation,
  BatchOperationResult,
  CustomerValidationResult,
  CUSTOMER_VALIDATION_RULES
} from '../types/customer';

export class CustomerService {
  private customerRepository: CustomerRepository;

  constructor(customerRepository: CustomerRepository) {
    this.customerRepository = customerRepository;
  }

  // 根據ID獲取客戶
  async getCustomerById(id: string): Promise<CustomerInfo | null> {
    try {
      return await this.customerRepository.findById(id);
    } catch (error) {
      logger.error('獲取客戶失敗:', error);
      throw new Error('獲取客戶失敗');
    }
  }

  // 根據ID獲取客戶詳細資訊
  async getCustomerDetailById(id: string): Promise<CustomerDetailInfo | null> {
    try {
      return await this.customerRepository.findByIdWithDetails(id);
    } catch (error) {
      logger.error('獲取客戶詳細資訊失敗:', error);
      throw new Error('獲取客戶詳細資訊失敗');
    }
  }

  // 創建客戶
  async createCustomer(customerData: CreateCustomerRequest, createdBy?: string): Promise<CustomerInfo> {
    try {
      // 驗證客戶資料
      const validation = await this.validateCustomerData(customerData);
      if (!validation.isValid) {
        throw new Error(`客戶資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查電子郵件和電話是否已存在
      if (customerData.email || customerData.phone) {
        const existence = await this.customerRepository.checkExistence(
          customerData.email,
          customerData.phone
        );

        if (customerData.email && existence.emailExists) {
          throw new Error('電子郵件已存在');
        }

        if (customerData.phone && existence.phoneExists) {
          throw new Error('電話號碼已存在');
        }
      }

      // 創建客戶
      const newCustomer = await this.customerRepository.create(customerData);

      logger.info('客戶創建成功:', { 
        customerId: newCustomer.id, 
        name: newCustomer.name,
        createdBy 
      });

      return newCustomer;
    } catch (error) {
      logger.error('創建客戶失敗:', error);
      throw error;
    }
  }

  // 更新客戶
  async updateCustomer(id: string, customerData: UpdateCustomerRequest, updatedBy?: string): Promise<CustomerInfo> {
    try {
      // 檢查客戶是否存在
      const existingCustomer = await this.customerRepository.findById(id);
      if (!existingCustomer) {
        throw new Error('客戶不存在');
      }

      // 驗證更新資料
      const validation = await this.validateUpdateData(customerData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查電子郵件和電話是否已存在（排除當前客戶）
      if (customerData.email || customerData.phone) {
        const existence = await this.customerRepository.checkExistence(
          customerData.email,
          customerData.phone,
          id
        );

        if (customerData.email && existence.emailExists) {
          throw new Error('電子郵件已存在');
        }

        if (customerData.phone && existence.phoneExists) {
          throw new Error('電話號碼已存在');
        }
      }

      // 更新客戶
      const updatedCustomer = await this.customerRepository.update(id, customerData);

      logger.info('客戶更新成功:', { 
        customerId: id, 
        name: updatedCustomer.name,
        updatedBy,
        changes: Object.keys(customerData)
      });

      return updatedCustomer;
    } catch (error) {
      logger.error('更新客戶失敗:', error);
      throw error;
    }
  }

  // 刪除客戶
  async deleteCustomer(id: string, deletedBy?: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查客戶是否存在
      const existingCustomer = await this.customerRepository.findById(id);
      if (!existingCustomer) {
        throw new Error('客戶不存在');
      }

      // 檢查是否有關聯的維修記錄
      const customerDetail = await this.customerRepository.findByIdWithDetails(id);
      if (customerDetail && customerDetail.statistics.totalRepairs > 0 && hardDelete) {
        throw new Error('客戶有關聯的維修記錄，無法永久刪除');
      }

      if (hardDelete) {
        await this.customerRepository.hardDelete(id);
        logger.info('客戶硬刪除成功:', { customerId: id, deletedBy });
      } else {
        await this.customerRepository.softDelete(id);
        logger.info('客戶軟刪除成功:', { customerId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除客戶失敗:', error);
      throw error;
    }
  }

  // 獲取客戶列表
  async getCustomerList(params: CustomerQueryParams): Promise<CustomerListResponse> {
    try {
      return await this.customerRepository.findMany(params);
    } catch (error) {
      logger.error('獲取客戶列表失敗:', error);
      throw new Error('獲取客戶列表失敗');
    }
  }

  // 搜尋客戶
  async searchCustomers(query: string, limit: number = 10): Promise<CustomerSearchResult[]> {
    try {
      if (query.length < 2) {
        throw new Error('搜尋關鍵字至少需要2個字符');
      }

      return await this.customerRepository.search(query, limit);
    } catch (error) {
      logger.error('搜尋客戶失敗:', error);
      throw error;
    }
  }

  // 獲取客戶統計
  async getCustomerStatistics(): Promise<CustomerStatistics> {
    try {
      return await this.customerRepository.getStatistics();
    } catch (error) {
      logger.error('獲取客戶統計失敗:', error);
      throw new Error('獲取客戶統計失敗');
    }
  }

  // 批量操作客戶
  async batchOperation(operation: BatchCustomerOperation, operatedBy?: string): Promise<BatchOperationResult> {
    try {
      // 驗證批量操作
      if (operation.customerIds.length === 0) {
        throw new Error('未選擇任何客戶');
      }

      if (operation.customerIds.length > 100) {
        throw new Error('批量操作客戶數量不能超過100個');
      }

      const result = await this.customerRepository.batchOperation(operation);

      logger.info('批量操作完成:', { 
        operation: operation.operation,
        customerCount: operation.customerIds.length,
        success: result.success,
        failed: result.failed,
        operatedBy
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw error;
    }
  }

  // 激活客戶
  async activateCustomer(id: string, activatedBy?: string): Promise<CustomerInfo> {
    try {
      return await this.updateCustomer(id, { isActive: true }, activatedBy);
    } catch (error) {
      logger.error('激活客戶失敗:', error);
      throw error;
    }
  }

  // 停用客戶
  async deactivateCustomer(id: string, deactivatedBy?: string): Promise<CustomerInfo> {
    try {
      return await this.updateCustomer(id, { isActive: false }, deactivatedBy);
    } catch (error) {
      logger.error('停用客戶失敗:', error);
      throw error;
    }
  }

  // 根據電子郵件查找客戶
  async findCustomerByEmail(email: string): Promise<CustomerInfo | null> {
    try {
      const customer = await this.customerRepository.findByEmail(email);
      if (!customer) return null;

      return {
        id: customer.id.toString(),
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        companyName: customer.companyName,
        contactPerson: customer.contactPerson,
        taxId: customer.taxId,
        notes: customer.notes,
        isActive: customer.isActive,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      };
    } catch (error) {
      logger.error('根據電子郵件查找客戶失敗:', error);
      throw error;
    }
  }

  // 根據電話查找客戶
  async findCustomerByPhone(phone: string): Promise<CustomerInfo | null> {
    try {
      const customer = await this.customerRepository.findByPhone(phone);
      if (!customer) return null;

      return {
        id: customer.id.toString(),
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        companyName: customer.companyName,
        contactPerson: customer.contactPerson,
        taxId: customer.taxId,
        notes: customer.notes,
        isActive: customer.isActive,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      };
    } catch (error) {
      logger.error('根據電話查找客戶失敗:', error);
      throw error;
    }
  }

  // 驗證客戶資料
  private async validateCustomerData(customerData: CreateCustomerRequest): Promise<CustomerValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證客戶名稱
    if (!customerData.name || customerData.name.trim().length < CUSTOMER_VALIDATION_RULES.name.minLength) {
      errors.push(`客戶名稱至少需要${CUSTOMER_VALIDATION_RULES.name.minLength}個字符`);
    }

    if (customerData.name && customerData.name.length > CUSTOMER_VALIDATION_RULES.name.maxLength) {
      errors.push(`客戶名稱不能超過${CUSTOMER_VALIDATION_RULES.name.maxLength}個字符`);
    }

    // 驗證電子郵件
    if (customerData.email) {
      if (!CUSTOMER_VALIDATION_RULES.email.pattern.test(customerData.email)) {
        errors.push('電子郵件格式無效');
      }
      if (customerData.email.length > CUSTOMER_VALIDATION_RULES.email.maxLength) {
        errors.push(`電子郵件長度不能超過${CUSTOMER_VALIDATION_RULES.email.maxLength}個字符`);
      }
    }

    // 驗證電話
    if (customerData.phone) {
      if (!CUSTOMER_VALIDATION_RULES.phone.pattern.test(customerData.phone)) {
        errors.push('電話號碼格式無效');
      }
      if (customerData.phone.length > CUSTOMER_VALIDATION_RULES.phone.maxLength) {
        errors.push(`電話號碼長度不能超過${CUSTOMER_VALIDATION_RULES.phone.maxLength}個字符`);
      }
    }

    // 驗證地址
    if (customerData.address && customerData.address.length > CUSTOMER_VALIDATION_RULES.address.maxLength) {
      errors.push(`地址長度不能超過${CUSTOMER_VALIDATION_RULES.address.maxLength}個字符`);
    }

    // 驗證公司名稱
    if (customerData.companyName && customerData.companyName.length > CUSTOMER_VALIDATION_RULES.companyName.maxLength) {
      errors.push(`公司名稱長度不能超過${CUSTOMER_VALIDATION_RULES.companyName.maxLength}個字符`);
    }

    // 驗證聯絡人
    if (customerData.contactPerson && customerData.contactPerson.length > CUSTOMER_VALIDATION_RULES.contactPerson.maxLength) {
      errors.push(`聯絡人長度不能超過${CUSTOMER_VALIDATION_RULES.contactPerson.maxLength}個字符`);
    }

    // 驗證統一編號
    if (customerData.taxId) {
      if (!CUSTOMER_VALIDATION_RULES.taxId.pattern.test(customerData.taxId)) {
        errors.push('統一編號格式無效');
      }
      if (customerData.taxId.length > CUSTOMER_VALIDATION_RULES.taxId.maxLength) {
        errors.push(`統一編號長度不能超過${CUSTOMER_VALIDATION_RULES.taxId.maxLength}個字符`);
      }
    }

    // 驗證備註
    if (customerData.notes && customerData.notes.length > CUSTOMER_VALIDATION_RULES.notes.maxLength) {
      errors.push(`備註長度不能超過${CUSTOMER_VALIDATION_RULES.notes.maxLength}個字符`);
    }

    // 警告：缺少聯絡方式
    if (!customerData.email && !customerData.phone) {
      warnings.push('建議至少提供電子郵件或電話號碼');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證更新資料
  private async validateUpdateData(customerData: UpdateCustomerRequest): Promise<CustomerValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證客戶名稱
    if (customerData.name !== undefined) {
      if (customerData.name.trim().length < CUSTOMER_VALIDATION_RULES.name.minLength) {
        errors.push(`客戶名稱至少需要${CUSTOMER_VALIDATION_RULES.name.minLength}個字符`);
      }
      if (customerData.name.length > CUSTOMER_VALIDATION_RULES.name.maxLength) {
        errors.push(`客戶名稱不能超過${CUSTOMER_VALIDATION_RULES.name.maxLength}個字符`);
      }
    }

    // 驗證電子郵件
    if (customerData.email !== undefined && customerData.email) {
      if (!CUSTOMER_VALIDATION_RULES.email.pattern.test(customerData.email)) {
        errors.push('電子郵件格式無效');
      }
      if (customerData.email.length > CUSTOMER_VALIDATION_RULES.email.maxLength) {
        errors.push(`電子郵件長度不能超過${CUSTOMER_VALIDATION_RULES.email.maxLength}個字符`);
      }
    }

    // 驗證電話
    if (customerData.phone !== undefined && customerData.phone) {
      if (!CUSTOMER_VALIDATION_RULES.phone.pattern.test(customerData.phone)) {
        errors.push('電話號碼格式無效');
      }
      if (customerData.phone.length > CUSTOMER_VALIDATION_RULES.phone.maxLength) {
        errors.push(`電話號碼長度不能超過${CUSTOMER_VALIDATION_RULES.phone.maxLength}個字符`);
      }
    }

    // 其他欄位驗證...
    if (customerData.address !== undefined && customerData.address && customerData.address.length > CUSTOMER_VALIDATION_RULES.address.maxLength) {
      errors.push(`地址長度不能超過${CUSTOMER_VALIDATION_RULES.address.maxLength}個字符`);
    }

    if (customerData.companyName !== undefined && customerData.companyName && customerData.companyName.length > CUSTOMER_VALIDATION_RULES.companyName.maxLength) {
      errors.push(`公司名稱長度不能超過${CUSTOMER_VALIDATION_RULES.companyName.maxLength}個字符`);
    }

    if (customerData.contactPerson !== undefined && customerData.contactPerson && customerData.contactPerson.length > CUSTOMER_VALIDATION_RULES.contactPerson.maxLength) {
      errors.push(`聯絡人長度不能超過${CUSTOMER_VALIDATION_RULES.contactPerson.maxLength}個字符`);
    }

    if (customerData.taxId !== undefined && customerData.taxId) {
      if (!CUSTOMER_VALIDATION_RULES.taxId.pattern.test(customerData.taxId)) {
        errors.push('統一編號格式無效');
      }
      if (customerData.taxId.length > CUSTOMER_VALIDATION_RULES.taxId.maxLength) {
        errors.push(`統一編號長度不能超過${CUSTOMER_VALIDATION_RULES.taxId.maxLength}個字符`);
      }
    }

    if (customerData.notes !== undefined && customerData.notes && customerData.notes.length > CUSTOMER_VALIDATION_RULES.notes.maxLength) {
      errors.push(`備註長度不能超過${CUSTOMER_VALIDATION_RULES.notes.maxLength}個字符`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
