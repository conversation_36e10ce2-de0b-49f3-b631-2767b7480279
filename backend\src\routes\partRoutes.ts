import { Router } from 'express';
import PartController from '../controllers/partController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  createPartValidation,
  updatePartValidation,
  partListValidation,
  partIdValidation,
  partSearchValidation,
  partNumberParamValidation,
  partDetailValidation,
  stockOperationValidation,
  batchStockOperationValidation,
  stockInValidation,
  stockOutValidation,
  stockAdjustValidation,
  stockReserveValidation,
  stockReleaseValidation,
  partListWithRangeValidation,
  stockOperationWithQuantityValidation,
} from '../validators/partValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CreatePartRequest:
 *       type: object
 *       required:
 *         - name
 *         - partNumber
 *         - category
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 200
 *           description: 零件名稱
 *         partNumber:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           pattern: '^[A-Za-z0-9\-_]+$'
 *           description: 零件編號
 *         description:
 *           type: string
 *           maxLength: 1000
 *           description: 零件描述
 *         category:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 零件類別
 *         brand:
 *           type: string
 *           maxLength: 100
 *           description: 品牌
 *         model:
 *           type: string
 *           maxLength: 100
 *           description: 型號
 *         specifications:
 *           type: string
 *           maxLength: 2000
 *           description: 規格
 *         unitPrice:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 單價
 *         currency:
 *           type: string
 *           pattern: '^[A-Z]{3}$'
 *           description: 幣別（如TWD、USD）
 *         supplier:
 *           type: string
 *           maxLength: 200
 *           description: 供應商
 *         supplierPartNumber:
 *           type: string
 *           maxLength: 100
 *           description: 供應商零件編號
 *         minimumStock:
 *           type: integer
 *           minimum: 0
 *           maximum: 999999
 *           description: 最小庫存
 *         currentStock:
 *           type: integer
 *           minimum: 0
 *           maximum: 999999
 *           description: 目前庫存
 *         location:
 *           type: string
 *           maxLength: 200
 *           description: 存放位置
 *         isActive:
 *           type: boolean
 *           default: true
 *           description: 是否活躍
 *     
 *     UpdatePartRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 200
 *           description: 零件名稱
 *         partNumber:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           pattern: '^[A-Za-z0-9\-_]+$'
 *           description: 零件編號
 *         description:
 *           type: string
 *           maxLength: 1000
 *           description: 零件描述
 *         category:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: 零件類別
 *         brand:
 *           type: string
 *           maxLength: 100
 *           description: 品牌
 *         model:
 *           type: string
 *           maxLength: 100
 *           description: 型號
 *         specifications:
 *           type: string
 *           maxLength: 2000
 *           description: 規格
 *         unitPrice:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 單價
 *         currency:
 *           type: string
 *           pattern: '^[A-Z]{3}$'
 *           description: 幣別（如TWD、USD）
 *         supplier:
 *           type: string
 *           maxLength: 200
 *           description: 供應商
 *         supplierPartNumber:
 *           type: string
 *           maxLength: 100
 *           description: 供應商零件編號
 *         minimumStock:
 *           type: integer
 *           minimum: 0
 *           maximum: 999999
 *           description: 最小庫存
 *         location:
 *           type: string
 *           maxLength: 200
 *           description: 存放位置
 *         isActive:
 *           type: boolean
 *           description: 是否活躍
 *     
 *     StockOperationRequest:
 *       type: object
 *       required:
 *         - partId
 *         - type
 *         - quantity
 *         - reason
 *       properties:
 *         partId:
 *           type: string
 *           description: 零件ID
 *         type:
 *           type: string
 *           enum: [IN, OUT, ADJUSTMENT, RESERVED, RELEASED]
 *           description: 操作類型
 *         quantity:
 *           type: integer
 *           minimum: 0
 *           description: 數量
 *         reason:
 *           type: string
 *           minLength: 1
 *           maxLength: 500
 *           description: 操作原因
 *         referenceId:
 *           type: string
 *           maxLength: 100
 *           description: 參考ID
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: 備註
 *     
 *     BatchStockOperationRequest:
 *       type: object
 *       required:
 *         - operations
 *       properties:
 *         operations:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/StockOperationRequest'
 *           minItems: 1
 *           maxItems: 100
 *           description: 操作列表
 *         batchReason:
 *           type: string
 *           maxLength: 500
 *           description: 批量操作原因
 */

// === 零件相關路由 ===

/**
 * @swagger
 * /api/v1/parts:
 *   get:
 *     summary: 獲取零件列表
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 類別篩選
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: 品牌篩選
 *       - in: query
 *         name: supplier
 *         schema:
 *           type: string
 *         description: 供應商篩選
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *       - in: query
 *         name: lowStock
 *         schema:
 *           type: boolean
 *         description: 低庫存篩選
 *       - in: query
 *         name: outOfStock
 *         schema:
 *           type: boolean
 *         description: 缺貨篩選
 *       - in: query
 *         name: priceMin
 *         schema:
 *           type: number
 *         description: 最低價格
 *       - in: query
 *         name: priceMax
 *         schema:
 *           type: number
 *         description: 最高價格
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, partNumber, category, brand, unitPrice, currentStock, createdAt, updatedAt]
 *           default: createdAt
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取零件列表成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), partListWithRangeValidation, PartController.getPartList);

/**
 * @swagger
 * /api/v1/parts:
 *   post:
 *     summary: 創建零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePartRequest'
 *     responses:
 *       201:
 *         description: 零件創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       409:
 *         description: 零件編號已存在
 */
router.post('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), createPartValidation, PartController.createPart);

/**
 * @swagger
 * /api/v1/parts/search:
 *   get:
 *     summary: 搜尋零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: 結果數量限制
 *     responses:
 *       200:
 *         description: 搜尋成功
 *       400:
 *         description: 搜尋參數錯誤
 *       401:
 *         description: 未認證
 */
router.get('/search', authenticate, partSearchValidation, PartController.searchParts);

/**
 * @swagger
 * /api/v1/parts/statistics:
 *   get:
 *     summary: 獲取零件統計
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), PartController.getPartStatistics);

/**
 * @swagger
 * /api/v1/parts/stock-alerts:
 *   get:
 *     summary: 獲取庫存警報
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取庫存警報成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/stock-alerts', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), PartController.getStockAlerts);

/**
 * @swagger
 * /api/v1/parts/stock-operation:
 *   post:
 *     summary: 執行庫存操作
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/StockOperationRequest'
 *     responses:
 *       200:
 *         description: 庫存操作成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/stock-operation', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), stockOperationWithQuantityValidation, PartController.performStockOperation);

/**
 * @swagger
 * /api/v1/parts/batch-stock-operation:
 *   post:
 *     summary: 批量庫存操作
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchStockOperationRequest'
 *     responses:
 *       200:
 *         description: 批量庫存操作完成
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/batch-stock-operation', authenticate, authorize(Role.ADMIN), batchStockOperationValidation, PartController.performBatchStockOperation);

/**
 * @swagger
 * /api/v1/parts/part-number/{partNumber}:
 *   get:
 *     summary: 根據零件編號查找零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: partNumber
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件編號
 *     responses:
 *       200:
 *         description: 查找成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 零件不存在
 */
router.get('/part-number/:partNumber', authenticate, partNumberParamValidation, PartController.findPartByPartNumber);

/**
 * @swagger
 * /api/v1/parts/check-part-number/{partNumber}:
 *   get:
 *     summary: 檢查零件編號是否可用
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: partNumber
 *         required: true
 *         schema:
 *           type: string
 *         description: 要檢查的零件編號
 *       - in: query
 *         name: excludeId
 *         schema:
 *           type: string
 *         description: 排除的零件ID
 *     responses:
 *       200:
 *         description: 檢查完成
 *       400:
 *         description: 零件編號格式錯誤
 *       401:
 *         description: 未認證
 */
router.get('/check-part-number/:partNumber', authenticate, partNumberParamValidation, PartController.checkPartNumberAvailability);

/**
 * @swagger
 * /api/v1/parts/{id}:
 *   get:
 *     summary: 根據ID獲取零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含詳細資訊
 *     responses:
 *       200:
 *         description: 獲取零件成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 零件不存在
 */
router.get('/:id', authenticate, partDetailValidation, PartController.getPartById);

/**
 * @swagger
 * /api/v1/parts/{id}:
 *   put:
 *     summary: 更新零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdatePartRequest'
 *     responses:
 *       200:
 *         description: 零件更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.put('/:id', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), updatePartValidation, PartController.updatePart);

/**
 * @swagger
 * /api/v1/parts/{id}:
 *   delete:
 *     summary: 刪除零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 零件刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), partIdValidation, PartController.deletePart);

/**
 * @swagger
 * /api/v1/parts/{id}/activate:
 *   post:
 *     summary: 激活零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     responses:
 *       200:
 *         description: 零件激活成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/activate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), partIdValidation, PartController.activatePart);

/**
 * @swagger
 * /api/v1/parts/{id}/deactivate:
 *   post:
 *     summary: 停用零件
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     responses:
 *       200:
 *         description: 零件停用成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/deactivate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), partIdValidation, PartController.deactivatePart);

// === 庫存操作路由 ===

/**
 * @swagger
 * /api/v1/parts/{id}/stock-in:
 *   post:
 *     summary: 入庫操作
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - reason
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *                 description: 入庫數量
 *               reason:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 500
 *                 description: 入庫原因
 *               referenceId:
 *                 type: string
 *                 maxLength: 100
 *                 description: 參考ID
 *     responses:
 *       200:
 *         description: 入庫操作成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/stock-in', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), stockInValidation, PartController.stockIn);

/**
 * @swagger
 * /api/v1/parts/{id}/stock-out:
 *   post:
 *     summary: 出庫操作
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - reason
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *                 description: 出庫數量
 *               reason:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 500
 *                 description: 出庫原因
 *               referenceId:
 *                 type: string
 *                 maxLength: 100
 *                 description: 參考ID
 *     responses:
 *       200:
 *         description: 出庫操作成功
 *       400:
 *         description: 請求參數錯誤或庫存不足
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/stock-out', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), stockOutValidation, PartController.stockOut);

/**
 * @swagger
 * /api/v1/parts/{id}/adjust-stock:
 *   post:
 *     summary: 庫存調整
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - reason
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 0
 *                 description: 調整後的庫存數量
 *               reason:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 500
 *                 description: 調整原因
 *     responses:
 *       200:
 *         description: 庫存調整成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/adjust-stock', authenticate, authorize(Role.ADMIN), stockAdjustValidation, PartController.adjustStock);

/**
 * @swagger
 * /api/v1/parts/{id}/reserve-stock:
 *   post:
 *     summary: 預留庫存
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - reason
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *                 description: 預留數量
 *               reason:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 500
 *                 description: 預留原因
 *               referenceId:
 *                 type: string
 *                 maxLength: 100
 *                 description: 參考ID（如維修單號）
 *     responses:
 *       200:
 *         description: 庫存預留成功
 *       400:
 *         description: 請求參數錯誤或可用庫存不足
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/reserve-stock', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), stockReserveValidation, PartController.reserveStock);

/**
 * @swagger
 * /api/v1/parts/{id}/release-stock:
 *   post:
 *     summary: 釋放預留庫存
 *     tags: [Parts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 零件ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *               - reason
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *                 description: 釋放數量
 *               reason:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 500
 *                 description: 釋放原因
 *               referenceId:
 *                 type: string
 *                 maxLength: 100
 *                 description: 參考ID
 *     responses:
 *       200:
 *         description: 預留庫存釋放成功
 *       400:
 *         description: 請求參數錯誤或預留庫存不足
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 零件不存在
 */
router.post('/:id/release-stock', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), stockReleaseValidation, PartController.releaseReservedStock);

export default router;
