import { body, ValidationChain } from 'express-validator';

// 登入驗證
export const loginValidation: ValidationChain[] = [
  body('username')
    .notEmpty()
    .withMessage('用戶名不能為空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用戶名長度必須在3-50個字符之間')
    .trim(),

  body('password')
    .notEmpty()
    .withMessage('密碼不能為空')
    .isLength({ min: 6 })
    .withMessage('密碼長度至少6個字符'),

  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('rememberMe必須是布林值'),
];

// 註冊驗證
export const registerValidation: ValidationChain[] = [
  body('username')
    .notEmpty()
    .withMessage('用戶名不能為空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用戶名長度必須在3-50個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線')
    .trim(),

  body('email')
    .notEmpty()
    .withMessage('電子郵件不能為空')
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('password')
    .notEmpty()
    .withMessage('密碼不能為空')
    .isLength({ min: 8, max: 128 })
    .withMessage('密碼長度必須在8-128個字符之間')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字符'),

  body('fullName')
    .notEmpty()
    .withMessage('姓名不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('role')
    .optional()
    .isIn(['ADMIN', 'TECHNICIAN', 'CUSTOMER_SERVICE', 'VIEWER'])
    .withMessage('角色必須是有效的角色類型'),
];

// 刷新令牌驗證
export const refreshTokenValidation: ValidationChain[] = [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能為空')
    .isJWT()
    .withMessage('刷新令牌格式無效'),
];

// 修改密碼驗證
export const changePasswordValidation: ValidationChain[] = [
  body('currentPassword')
    .notEmpty()
    .withMessage('當前密碼不能為空'),

  body('newPassword')
    .notEmpty()
    .withMessage('新密碼不能為空')
    .isLength({ min: 8, max: 128 })
    .withMessage('新密碼長度必須在8-128個字符之間')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('新密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字符')
    .custom((value, { req }) => {
      if (value === req.body.currentPassword) {
        throw new Error('新密碼不能與當前密碼相同');
      }
      return true;
    }),
];

// 重置密碼請求驗證
export const resetPasswordRequestValidation: ValidationChain[] = [
  body('email')
    .notEmpty()
    .withMessage('電子郵件不能為空')
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail(),
];

// 重置密碼驗證
export const resetPasswordValidation: ValidationChain[] = [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能為空')
    .isLength({ min: 32, max: 32 })
    .withMessage('重置令牌格式無效'),

  body('newPassword')
    .notEmpty()
    .withMessage('新密碼不能為空')
    .isLength({ min: 8, max: 128 })
    .withMessage('新密碼長度必須在8-128個字符之間')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('新密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字符'),
];

// 更新用戶資料驗證
export const updateProfileValidation: ValidationChain[] = [
  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),
];

// 管理員創建用戶驗證
export const adminCreateUserValidation: ValidationChain[] = [
  body('username')
    .notEmpty()
    .withMessage('用戶名不能為空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用戶名長度必須在3-50個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線')
    .trim(),

  body('email')
    .notEmpty()
    .withMessage('電子郵件不能為空')
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('fullName')
    .notEmpty()
    .withMessage('姓名不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('role')
    .notEmpty()
    .withMessage('角色不能為空')
    .isIn(['ADMIN', 'TECHNICIAN', 'CUSTOMER_SERVICE', 'VIEWER'])
    .withMessage('角色必須是有效的角色類型'),

  body('password')
    .optional()
    .isLength({ min: 8, max: 128 })
    .withMessage('密碼長度必須在8-128個字符之間')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字符'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 管理員更新用戶驗證
export const adminUpdateUserValidation: ValidationChain[] = [
  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('role')
    .optional()
    .isIn(['ADMIN', 'TECHNICIAN', 'CUSTOMER_SERVICE', 'VIEWER'])
    .withMessage('角色必須是有效的角色類型'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 驗證用戶ID參數
export const validateUserIdParam = [
  body('userId')
    .optional()
    .isNumeric()
    .withMessage('用戶ID必須是數字')
    .toInt(),
];

// 自定義驗證函數：檢查密碼複雜度
export const validatePasswordComplexity = (password: string): boolean => {
  const hasLowerCase = /[a-z]/.test(password);
  const hasUpperCase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
  const hasMinLength = password.length >= 8;
  const hasMaxLength = password.length <= 128;

  return hasLowerCase && hasUpperCase && hasNumbers && hasSpecialChar && hasMinLength && hasMaxLength;
};

// 自定義驗證函數：檢查用戶名格式
export const validateUsernameFormat = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  return usernameRegex.test(username) && username.length >= 3 && username.length <= 50;
};

// 自定義驗證函數：檢查電子郵件格式
export const validateEmailFormat = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 100;
};
