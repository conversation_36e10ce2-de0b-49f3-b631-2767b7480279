import React from 'react';
import { 
  Modal, 
  Descriptions, 
  Tag, 
  Typography, 
  Space, 
  Timeline,
  Table,
  Button,
  Divider,
  Card,
  Row,
  Col,
  Avatar,
  Progress
} from 'antd';
import { 
  UserOutlined, 
  ShoppingOutlined, 
  ToolOutlined, 
  DollarOutlined,
  CalendarOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { RepairRecord } from '../../services/repairService';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;

interface RepairDetailProps {
  visible: boolean;
  repair: RepairRecord | null;
  onCancel: () => void;
  onEdit?: (repair: RepairRecord) => void;
  onStatusUpdate?: (repair: RepairRecord) => void;
}

const RepairDetail: React.FC<RepairDetailProps> = ({
  visible,
  repair,
  onCancel,
  onEdit,
  onStatusUpdate,
}) => {
  if (!repair) return null;

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING_INSPECTION': 'orange',
      'INSPECTING': 'blue',
      'PENDING_REPAIR': 'purple',
      'REPAIRING': 'cyan',
      'PENDING_PARTS': 'gold',
      'COMPLETED': 'green',
      'DELIVERED': 'lime',
      'CANCELLED': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'PENDING_INSPECTION': '待檢測',
      'INSPECTING': '檢測中',
      'PENDING_REPAIR': '待維修',
      'REPAIRING': '維修中',
      'PENDING_PARTS': '待零件',
      'COMPLETED': '已完成',
      'DELIVERED': '已交付',
      'CANCELLED': '已取消',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      'PENDING_INSPECTION': <ClockCircleOutlined />,
      'INSPECTING': <SyncOutlined spin />,
      'PENDING_REPAIR': <ExclamationCircleOutlined />,
      'REPAIRING': <SyncOutlined spin />,
      'PENDING_PARTS': <ClockCircleOutlined />,
      'COMPLETED': <CheckCircleOutlined />,
      'DELIVERED': <CheckCircleOutlined />,
      'CANCELLED': <StopOutlined />,
    };
    return icons[status as keyof typeof icons] || <ClockCircleOutlined />;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'LOW': 'green',
      'MEDIUM': 'blue',
      'HIGH': 'orange',
      'URGENT': 'red',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const getPriorityText = (priority: string) => {
    const texts = {
      'LOW': '低',
      'MEDIUM': '中',
      'HIGH': '高',
      'URGENT': '緊急',
    };
    return texts[priority as keyof typeof texts] || priority;
  };

  const getWarrantyColor = (warranty: string) => {
    const colors = {
      'IN_WARRANTY': 'green',
      'OUT_OF_WARRANTY': 'red',
      'EXTENDED_WARRANTY': 'blue',
    };
    return colors[warranty as keyof typeof colors] || 'default';
  };

  const getWarrantyText = (warranty: string) => {
    const texts = {
      'IN_WARRANTY': '保固內',
      'OUT_OF_WARRANTY': '保固外',
      'EXTENDED_WARRANTY': '延長保固',
    };
    return texts[warranty as keyof typeof texts] || warranty;
  };

  const getProgressPercent = (status: string) => {
    const progress = {
      'PENDING_INSPECTION': 10,
      'INSPECTING': 25,
      'PENDING_REPAIR': 40,
      'REPAIRING': 70,
      'PENDING_PARTS': 60,
      'COMPLETED': 90,
      'DELIVERED': 100,
      'CANCELLED': 0,
    };
    return progress[status as keyof typeof progress] || 0;
  };

  const getProgressStatus = (status: string) => {
    if (status === 'CANCELLED') return 'exception';
    if (status === 'DELIVERED') return 'success';
    return 'active';
  };

  // 模擬狀態歷史
  const mockStatusHistory = [
    {
      status: 'PENDING_INSPECTION',
      timestamp: repair.createdAt,
      user: '客服人員',
      notes: '客戶送修，等待檢測',
    },
    {
      status: 'INSPECTING',
      timestamp: '2024-01-16T09:00:00Z',
      user: '維修技師',
      notes: '開始檢測，確認故障原因',
    },
    {
      status: 'REPAIRING',
      timestamp: '2024-01-17T10:30:00Z',
      user: '維修技師',
      notes: '開始維修，更換相關零件',
    },
  ];

  // 模擬使用零件
  const mockUsedParts = [
    {
      id: 1,
      name: '螢幕總成',
      partNumber: 'SCR-001',
      quantity: 1,
      unitPrice: 2500,
      totalPrice: 2500,
    },
    {
      id: 2,
      name: '觸控IC',
      partNumber: 'IC-002',
      quantity: 1,
      unitPrice: 500,
      totalPrice: 500,
    },
  ];

  const partColumns = [
    {
      title: '零件名稱',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '零件編號',
      dataIndex: 'partNumber',
      key: 'partNumber',
    },
    {
      title: '數量',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'center' as const,
    },
    {
      title: '單價',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      render: (price: number) => `$${price.toLocaleString()}`,
    },
    {
      title: '小計',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (price: number) => `$${price.toLocaleString()}`,
    },
  ];

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          維修記錄詳情 - {repair.recordNumber}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          關閉
        </Button>,
        <Button key="edit" type="default" onClick={() => onEdit?.(repair)}>
          編輯
        </Button>,
        <Button key="status" type="primary" onClick={() => onStatusUpdate?.(repair)}>
          更新狀態
        </Button>,
      ]}
      width={1000}
      destroyOnClose
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 狀態進度 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row align="middle" gutter={16}>
            <Col flex="auto">
              <div style={{ marginBottom: 8 }}>
                <Space>
                  {getStatusIcon(repair.status)}
                  <Text strong>當前狀態：</Text>
                  <Tag color={getStatusColor(repair.status)}>
                    {getStatusText(repair.status)}
                  </Tag>
                </Space>
              </div>
              <Progress
                percent={getProgressPercent(repair.status)}
                status={getProgressStatus(repair.status)}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </Col>
            <Col>
              <Tag color={getPriorityColor(repair.priority)} style={{ fontSize: '14px', padding: '4px 8px' }}>
                {getPriorityText(repair.priority)}優先級
              </Tag>
            </Col>
          </Row>
        </Card>

        {/* 基本資訊 */}
        <Card title="基本資訊" size="small" style={{ marginBottom: 16 }}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="維修編號">
              <Text strong>{repair.recordNumber}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="建立時間">
              {dayjs(repair.createdAt).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
            <Descriptions.Item label="客戶姓名">
              <Space>
                <UserOutlined />
                {repair.customer?.name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="聯絡電話">
              {repair.customer?.phone}
            </Descriptions.Item>
            <Descriptions.Item label="產品名稱">
              <Space>
                <ShoppingOutlined />
                {repair.product?.name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="產品型號">
              {repair.product?.brand} {repair.product?.model}
            </Descriptions.Item>
            <Descriptions.Item label="指派技師">
              {repair.assignedTechnician ? (
                <Space>
                  <ToolOutlined />
                  {repair.assignedTechnician.name}
                </Space>
              ) : (
                <Text type="secondary">未指派</Text>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="保固狀態">
              <Tag color={getWarrantyColor(repair.warrantyStatus)}>
                {getWarrantyText(repair.warrantyStatus)}
              </Tag>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 問題描述 */}
        <Card title="問題描述" size="small" style={{ marginBottom: 16 }}>
          <Paragraph>{repair.issueDescription}</Paragraph>
          
          <div style={{ marginTop: 16 }}>
            <Text strong>故障症狀：</Text>
            <div style={{ marginTop: 8 }}>
              {repair.symptoms.map(symptom => (
                <Tag key={symptom} color="blue" style={{ marginBottom: 4 }}>
                  {symptom}
                </Tag>
              ))}
            </div>
          </div>

          {repair.customerNotes && (
            <div style={{ marginTop: 16 }}>
              <Text strong>客戶備註：</Text>
              <Paragraph style={{ marginTop: 8, background: '#f6f6f6', padding: 12, borderRadius: 6 }}>
                {repair.customerNotes}
              </Paragraph>
            </div>
          )}
        </Card>

        {/* 費用資訊 */}
        <Card title="費用資訊" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ textAlign: 'center', padding: 16, background: '#f0f2f5', borderRadius: 6 }}>
                <DollarOutlined style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }} />
                <div>
                  <Text type="secondary">預估費用</Text>
                  <div style={{ fontSize: 18, fontWeight: 'bold', color: '#faad14' }}>
                    {repair.estimatedCost ? `$${repair.estimatedCost.toLocaleString()}` : '未估價'}
                  </div>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center', padding: 16, background: '#f0f2f5', borderRadius: 6 }}>
                <DollarOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                <div>
                  <Text type="secondary">實際費用</Text>
                  <div style={{ fontSize: 18, fontWeight: 'bold', color: '#52c41a' }}>
                    {repair.actualCost ? `$${repair.actualCost.toLocaleString()}` : '未確定'}
                  </div>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center', padding: 16, background: '#f0f2f5', borderRadius: 6 }}>
                <CalendarOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                <div>
                  <Text type="secondary">預計完成</Text>
                  <div style={{ fontSize: 14, fontWeight: 'bold', color: '#1890ff' }}>
                    {repair.estimatedCompletionDate ? 
                      dayjs(repair.estimatedCompletionDate).format('MM/DD') : '未設定'}
                  </div>
                  {repair.actualCompletionDate && (
                    <div style={{ fontSize: 12, color: '#52c41a' }}>
                      實際: {dayjs(repair.actualCompletionDate).format('MM/DD')}
                    </div>
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 使用零件 */}
        {mockUsedParts.length > 0 && (
          <Card title="使用零件" size="small" style={{ marginBottom: 16 }}>
            <Table
              columns={partColumns}
              dataSource={mockUsedParts}
              rowKey="id"
              pagination={false}
              size="small"
              summary={(pageData) => {
                const total = pageData.reduce((sum, item) => sum + item.totalPrice, 0);
                return (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={4}>
                      <Text strong>零件費用小計</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      <Text strong>${total.toLocaleString()}</Text>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
          </Card>
        )}

        {/* 維修備註 */}
        {repair.repairNotes && (
          <Card title="維修備註" size="small" style={{ marginBottom: 16 }}>
            <Paragraph style={{ background: '#f6f6f6', padding: 12, borderRadius: 6 }}>
              {repair.repairNotes}
            </Paragraph>
          </Card>
        )}

        {/* 狀態歷史 */}
        <Card title="狀態歷史" size="small">
          <Timeline>
            {mockStatusHistory.map((history, index) => (
              <Timeline.Item
                key={index}
                color={getStatusColor(history.status)}
                dot={getStatusIcon(history.status)}
              >
                <div>
                  <Space>
                    <Tag color={getStatusColor(history.status)}>
                      {getStatusText(history.status)}
                    </Tag>
                    <Text type="secondary">
                      {dayjs(history.timestamp).format('MM/DD HH:mm')}
                    </Text>
                    <Text type="secondary">by {history.user}</Text>
                  </Space>
                  {history.notes && (
                    <div style={{ marginTop: 4, color: '#666' }}>
                      {history.notes}
                    </div>
                  )}
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>
      </div>
    </Modal>
  );
};

export default RepairDetail;
