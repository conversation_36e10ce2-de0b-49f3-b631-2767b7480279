import { body, query, param, ValidationChain } from 'express-validator';

// 創建零件驗證
export const createPartValidation: ValidationChain[] = [
  body('name')
    .notEmpty()
    .withMessage('零件名稱不能為空')
    .isLength({ min: 2, max: 200 })
    .withMessage('零件名稱長度必須在2-200個字符之間')
    .trim(),

  body('partNumber')
    .notEmpty()
    .withMessage('零件編號不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('零件編號長度必須在1-100個字符之間')
    .matches(/^[A-Za-z0-9\-_]+$/)
    .withMessage('零件編號只能包含字母、數字、連字符和底線')
    .trim(),

  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述長度不能超過1000個字符')
    .trim(),

  body('category')
    .notEmpty()
    .withMessage('類別不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('類別長度必須在1-100個字符之間')
    .trim(),

  body('brand')
    .optional()
    .isLength({ max: 100 })
    .withMessage('品牌長度不能超過100個字符')
    .trim(),

  body('model')
    .optional()
    .isLength({ max: 100 })
    .withMessage('型號長度不能超過100個字符')
    .trim(),

  body('specifications')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('規格長度不能超過2000個字符')
    .trim(),

  body('unitPrice')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('單價必須在0-999999999之間')
    .toFloat(),

  body('currency')
    .optional()
    .matches(/^[A-Z]{3}$/)
    .withMessage('幣別必須是3位大寫字母（如TWD、USD）')
    .trim(),

  body('supplier')
    .optional()
    .isLength({ max: 200 })
    .withMessage('供應商長度不能超過200個字符')
    .trim(),

  body('supplierPartNumber')
    .optional()
    .isLength({ max: 100 })
    .withMessage('供應商零件編號長度不能超過100個字符')
    .trim(),

  body('minimumStock')
    .optional()
    .isInt({ min: 0, max: 999999 })
    .withMessage('最小庫存必須在0-999999之間')
    .toInt(),

  body('currentStock')
    .optional()
    .isInt({ min: 0, max: 999999 })
    .withMessage('目前庫存必須在0-999999之間')
    .toInt(),

  body('location')
    .optional()
    .isLength({ max: 200 })
    .withMessage('位置長度不能超過200個字符')
    .trim(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更新零件驗證
export const updatePartValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('零件名稱長度必須在2-200個字符之間')
    .trim(),

  body('partNumber')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('零件編號長度必須在1-100個字符之間')
    .matches(/^[A-Za-z0-9\-_]+$/)
    .withMessage('零件編號只能包含字母、數字、連字符和底線')
    .trim(),

  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述長度不能超過1000個字符')
    .trim(),

  body('category')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('類別長度必須在1-100個字符之間')
    .trim(),

  body('brand')
    .optional()
    .isLength({ max: 100 })
    .withMessage('品牌長度不能超過100個字符')
    .trim(),

  body('model')
    .optional()
    .isLength({ max: 100 })
    .withMessage('型號長度不能超過100個字符')
    .trim(),

  body('specifications')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('規格長度不能超過2000個字符')
    .trim(),

  body('unitPrice')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('單價必須在0-999999999之間')
    .toFloat(),

  body('currency')
    .optional()
    .matches(/^[A-Z]{3}$/)
    .withMessage('幣別必須是3位大寫字母（如TWD、USD）')
    .trim(),

  body('supplier')
    .optional()
    .isLength({ max: 200 })
    .withMessage('供應商長度不能超過200個字符')
    .trim(),

  body('supplierPartNumber')
    .optional()
    .isLength({ max: 100 })
    .withMessage('供應商零件編號長度不能超過100個字符')
    .trim(),

  body('minimumStock')
    .optional()
    .isInt({ min: 0, max: 999999 })
    .withMessage('最小庫存必須在0-999999之間')
    .toInt(),

  body('location')
    .optional()
    .isLength({ max: 200 })
    .withMessage('位置長度不能超過200個字符')
    .trim(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 零件列表查詢驗證
export const partListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('category')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('類別長度必須在1-100個字符之間')
    .trim(),

  query('brand')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('品牌長度必須在1-100個字符之間')
    .trim(),

  query('supplier')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('供應商長度必須在1-200個字符之間')
    .trim(),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('活躍狀態篩選必須是布林值')
    .toBoolean(),

  query('lowStock')
    .optional()
    .isBoolean()
    .withMessage('低庫存篩選必須是布林值')
    .toBoolean(),

  query('outOfStock')
    .optional()
    .isBoolean()
    .withMessage('缺貨篩選必須是布林值')
    .toBoolean(),

  query('priceMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低價格必須大於等於0')
    .toFloat(),

  query('priceMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高價格必須大於等於0')
    .toFloat(),

  query('sortBy')
    .optional()
    .isIn(['name', 'partNumber', 'category', 'brand', 'unitPrice', 'currentStock', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 零件ID參數驗證
export const partIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),
];

// 零件搜尋驗證
export const partSearchValidation: ValidationChain[] = [
  query('q')
    .notEmpty()
    .withMessage('搜尋關鍵字不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('結果數量限制必須在1-50之間')
    .toInt(),
];

// 零件編號參數驗證
export const partNumberParamValidation: ValidationChain[] = [
  param('partNumber')
    .notEmpty()
    .withMessage('零件編號不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('零件編號長度必須在1-100個字符之間')
    .matches(/^[A-Za-z0-9\-_]+$/)
    .withMessage('零件編號只能包含字母、數字、連字符和底線')
    .trim(),

  query('excludeId')
    .optional()
    .isNumeric()
    .withMessage('排除ID必須是數字'),
];

// 零件詳細資訊查詢驗證
export const partDetailValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  query('includeDetails')
    .optional()
    .isBoolean()
    .withMessage('includeDetails必須是布林值')
    .toBoolean(),
];

// === 庫存操作驗證 ===

// 庫存操作驗證
export const stockOperationValidation: ValidationChain[] = [
  body('partId')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('type')
    .notEmpty()
    .withMessage('操作類型不能為空')
    .isIn(['IN', 'OUT', 'ADJUSTMENT', 'RESERVED', 'RELEASED'])
    .withMessage('操作類型必須是IN、OUT、ADJUSTMENT、RESERVED或RELEASED'),

  body('quantity')
    .notEmpty()
    .withMessage('數量不能為空')
    .isInt({ min: 0 })
    .withMessage('數量必須是非負整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('操作原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('操作原因長度必須在1-500個字符之間')
    .trim(),

  body('referenceId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('參考ID長度不能超過100個字符')
    .trim(),

  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('備註長度不能超過1000個字符')
    .trim(),
];

// 批量庫存操作驗證
export const batchStockOperationValidation: ValidationChain[] = [
  body('operations')
    .isArray({ min: 1, max: 100 })
    .withMessage('操作列表必須是包含1-100個元素的陣列'),

  body('operations.*.partId')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('operations.*.type')
    .notEmpty()
    .withMessage('操作類型不能為空')
    .isIn(['IN', 'OUT', 'ADJUSTMENT', 'RESERVED', 'RELEASED'])
    .withMessage('操作類型必須是IN、OUT、ADJUSTMENT、RESERVED或RELEASED'),

  body('operations.*.quantity')
    .notEmpty()
    .withMessage('數量不能為空')
    .isInt({ min: 0 })
    .withMessage('數量必須是非負整數')
    .toInt(),

  body('operations.*.reason')
    .notEmpty()
    .withMessage('操作原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('操作原因長度必須在1-500個字符之間')
    .trim(),

  body('batchReason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('批量操作原因長度不能超過500個字符')
    .trim(),
];

// 入庫操作驗證
export const stockInValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('入庫數量不能為空')
    .isInt({ min: 1 })
    .withMessage('入庫數量必須是正整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('入庫原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('入庫原因長度必須在1-500個字符之間')
    .trim(),

  body('referenceId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('參考ID長度不能超過100個字符')
    .trim(),
];

// 出庫操作驗證
export const stockOutValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('出庫數量不能為空')
    .isInt({ min: 1 })
    .withMessage('出庫數量必須是正整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('出庫原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('出庫原因長度必須在1-500個字符之間')
    .trim(),

  body('referenceId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('參考ID長度不能超過100個字符')
    .trim(),
];

// 庫存調整驗證
export const stockAdjustValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('調整後數量不能為空')
    .isInt({ min: 0 })
    .withMessage('調整後數量必須是非負整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('調整原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('調整原因長度必須在1-500個字符之間')
    .trim(),
];

// 預留庫存驗證
export const stockReserveValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('預留數量不能為空')
    .isInt({ min: 1 })
    .withMessage('預留數量必須是正整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('預留原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('預留原因長度必須在1-500個字符之間')
    .trim(),

  body('referenceId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('參考ID長度不能超過100個字符')
    .trim(),
];

// 釋放預留庫存驗證
export const stockReleaseValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('零件ID不能為空')
    .isNumeric()
    .withMessage('零件ID必須是數字'),

  body('quantity')
    .notEmpty()
    .withMessage('釋放數量不能為空')
    .isInt({ min: 1 })
    .withMessage('釋放數量必須是正整數')
    .toInt(),

  body('reason')
    .notEmpty()
    .withMessage('釋放原因不能為空')
    .isLength({ min: 1, max: 500 })
    .withMessage('釋放原因長度必須在1-500個字符之間')
    .trim(),

  body('referenceId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('參考ID長度不能超過100個字符')
    .trim(),
];

// 自定義驗證函數：檢查價格範圍
export const validatePriceRange = () => {
  return query().custom((value, { req }) => {
    const { priceMin, priceMax } = req.query;
    
    if (priceMin && priceMax && parseFloat(priceMin as string) > parseFloat(priceMax as string)) {
      throw new Error('最低價格不能大於最高價格');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查庫存操作數量
export const validateStockOperationQuantity = () => {
  return body().custom((value, { req }) => {
    const { type, quantity } = req.body;
    
    if (type !== 'ADJUSTMENT' && quantity <= 0) {
      throw new Error('除調整操作外，數量必須大於0');
    }
    
    if (type === 'ADJUSTMENT' && quantity < 0) {
      throw new Error('調整後的庫存數量不能小於0');
    }
    
    return true;
  });
};

// 組合驗證：零件列表（包含範圍檢查）
export const partListWithRangeValidation = [
  ...partListValidation,
  validatePriceRange(),
];

// 組合驗證：庫存操作（包含數量檢查）
export const stockOperationWithQuantityValidation = [
  ...stockOperationValidation,
  validateStockOperationQuantity(),
];
