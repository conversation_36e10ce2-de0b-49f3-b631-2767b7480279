# 客退維修品記錄管理系統 - 資料庫設計

## 1. 資料庫概述

### 1.1 設計原則
- 遵循第三正規化 (3NF)
- 確保資料完整性和一致性
- 優化查詢性能
- 支援未來擴展需求

### 1.2 命名規範
- 表名使用複數形式，小寫加底線
- 欄位名使用小寫加底線
- 主鍵統一命名為 `id`
- 外鍵命名為 `{表名}_id`

## 2. 資料表設計

### 2.1 用戶表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'technician', 'customer_service', 'viewer') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.2 客戶表 (customers)
```sql
CREATE TABLE customers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    company VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_email (email)
);
```

### 2.3 產品類別表 (product_categories)
```sql
CREATE TABLE product_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.4 產品表 (products)
```sql
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT,
    model VARCHAR(100) NOT NULL,
    brand VARCHAR(50),
    description TEXT,
    warranty_months INT DEFAULT 12,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id),
    INDEX idx_model (model),
    INDEX idx_brand (brand)
);
```

### 2.5 維修記錄表 (repair_records)
```sql
CREATE TABLE repair_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    repair_number VARCHAR(20) UNIQUE NOT NULL,
    customer_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_serial VARCHAR(100),
    purchase_date DATE,
    warranty_status ENUM('in_warranty', 'out_of_warranty', 'extended') DEFAULT 'in_warranty',
    fault_description TEXT NOT NULL,
    received_date DATE NOT NULL,
    estimated_completion_date DATE,
    actual_completion_date DATE,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending_inspection', 'inspecting', 'pending_quote', 'repairing', 
                'pending_test', 'testing', 'completed', 'delivered', 'paused', 'cancelled') 
                DEFAULT 'pending_inspection',
    assigned_technician_id BIGINT,
    repair_cost DECIMAL(10,2) DEFAULT 0.00,
    parts_cost DECIMAL(10,2) DEFAULT 0.00,
    total_cost DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (assigned_technician_id) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_repair_number (repair_number),
    INDEX idx_status (status),
    INDEX idx_received_date (received_date),
    INDEX idx_customer_id (customer_id),
    INDEX idx_assigned_technician (assigned_technician_id)
);
```

### 2.6 維修狀態歷史表 (repair_status_history)
```sql
CREATE TABLE repair_status_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    repair_record_id BIGINT NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by BIGINT NOT NULL,
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_record_id) REFERENCES repair_records(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_repair_record (repair_record_id),
    INDEX idx_changed_at (changed_at)
);
```

### 2.7 零件表 (parts)
```sql
CREATE TABLE parts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    part_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    unit_price DECIMAL(10,2) DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    minimum_stock INT DEFAULT 0,
    supplier VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_part_number (part_number),
    INDEX idx_name (name)
);
```

### 2.8 維修使用零件表 (repair_parts)
```sql
CREATE TABLE repair_parts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    repair_record_id BIGINT NOT NULL,
    part_id BIGINT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_record_id) REFERENCES repair_records(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES parts(id),
    INDEX idx_repair_record (repair_record_id),
    INDEX idx_part (part_id)
);
```

### 2.9 維修進度記錄表 (repair_progress)
```sql
CREATE TABLE repair_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    repair_record_id BIGINT NOT NULL,
    progress_date DATE NOT NULL,
    description TEXT NOT NULL,
    technician_id BIGINT NOT NULL,
    hours_spent DECIMAL(4,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_record_id) REFERENCES repair_records(id) ON DELETE CASCADE,
    FOREIGN KEY (technician_id) REFERENCES users(id),
    INDEX idx_repair_record (repair_record_id),
    INDEX idx_progress_date (progress_date)
);
```

### 2.10 SharePoint文件表 (sharepoint_files)
```sql
CREATE TABLE sharepoint_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    repair_record_id BIGINT,
    sharepoint_file_id VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by BIGINT,
    sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending',
    last_sync_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_record_id) REFERENCES repair_records(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_repair_record (repair_record_id),
    INDEX idx_sharepoint_file_id (sharepoint_file_id),
    INDEX idx_sync_status (sync_status)
);
```

### 2.11 SharePoint同步日誌表 (sharepoint_sync_log)
```sql
CREATE TABLE sharepoint_sync_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    operation_type ENUM('upload', 'download', 'update', 'delete', 'sync') NOT NULL,
    file_id BIGINT,
    sharepoint_file_id VARCHAR(255),
    status ENUM('success', 'failed') NOT NULL,
    error_message TEXT,
    sync_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    performed_by BIGINT,
    FOREIGN KEY (file_id) REFERENCES sharepoint_files(id),
    FOREIGN KEY (performed_by) REFERENCES users(id),
    INDEX idx_sync_date (sync_date),
    INDEX idx_status (status),
    INDEX idx_operation_type (operation_type)
);
```

### 2.12 系統設定表 (system_settings)
```sql
CREATE TABLE system_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by BIGINT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

## 3. 索引策略

### 3.1 主要查詢索引
- 維修記錄按狀態查詢
- 維修記錄按客戶查詢
- 維修記錄按日期範圍查詢
- 維修記錄按技術人員查詢

### 3.2 複合索引
```sql
-- 維修記錄複合索引
CREATE INDEX idx_repair_status_date ON repair_records(status, received_date);
CREATE INDEX idx_repair_customer_status ON repair_records(customer_id, status);
CREATE INDEX idx_repair_technician_status ON repair_records(assigned_technician_id, status);
```

## 4. 資料完整性約束

### 4.1 外鍵約束
- 所有外鍵關係已在表結構中定義
- 使用 CASCADE 刪除相關子記錄

### 4.2 檢查約束
```sql
-- 確保日期邏輯正確
ALTER TABLE repair_records 
ADD CONSTRAINT chk_completion_date 
CHECK (actual_completion_date >= received_date OR actual_completion_date IS NULL);

-- 確保成本為正數
ALTER TABLE repair_records 
ADD CONSTRAINT chk_positive_cost 
CHECK (repair_cost >= 0 AND parts_cost >= 0 AND total_cost >= 0);
```

## 5. 觸發器設計

### 5.1 自動生成維修單號
```sql
DELIMITER //
CREATE TRIGGER generate_repair_number 
BEFORE INSERT ON repair_records
FOR EACH ROW
BEGIN
    IF NEW.repair_number IS NULL OR NEW.repair_number = '' THEN
        SET NEW.repair_number = CONCAT('R', DATE_FORMAT(NOW(), '%Y%m%d'), 
                                      LPAD((SELECT COALESCE(MAX(SUBSTRING(repair_number, 10)), 0) + 1 
                                            FROM repair_records 
                                            WHERE repair_number LIKE CONCAT('R', DATE_FORMAT(NOW(), '%Y%m%d'), '%')), 4, '0'));
    END IF;
END//
DELIMITER ;
```

### 5.2 記錄狀態變更歷史
```sql
DELIMITER //
CREATE TRIGGER record_status_change 
AFTER UPDATE ON repair_records
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO repair_status_history (repair_record_id, old_status, new_status, changed_by)
        VALUES (NEW.id, OLD.status, NEW.status, NEW.updated_by);
    END IF;
END//
DELIMITER ;
```

## 6. 初始化資料

### 6.1 系統設定初始化
```sql
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('company_name', '維修服務中心', '公司名稱'),
('default_warranty_months', '12', '預設保固月數'),
('auto_notification', 'true', '自動通知功能'),
('backup_retention_days', '30', '備份保留天數');
```

### 6.2 預設用戶角色
```sql
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2b$12$...', '系統管理員', 'admin'),
('tech01', '<EMAIL>', '$2b$12$...', '技術人員01', 'technician');
```
