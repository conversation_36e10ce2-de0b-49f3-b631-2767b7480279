﻿# SharePoint List Creation Helper Script (Fixed Encoding)
# IACT MIO Maintenance Management System - Simplified Version

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  IACT MIO Maintenance Management System" -ForegroundColor Cyan
Write-Host "  SharePoint List Creation Helper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get SharePoint site URL from user
$SiteUrl = Read-Host "Please enter your SharePoint site URL"
Write-Host "Target SharePoint Site: $SiteUrl" -ForegroundColor Green
Write-Host ""

# Check and install PnP PowerShell module
Write-Host "Checking PnP PowerShell module..." -ForegroundColor Yellow
if (!(Get-Module -ListAvailable -Name PnP.PowerShell)) {
    Write-Host "Installing PnP PowerShell module, please wait..." -ForegroundColor Yellow
    try {
        Install-Module -Name PnP.PowerShell -Force -AllowClobber -Scope CurrentUser
        Write-Host "PnP PowerShell module installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "Module installation failed: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "PnP PowerShell module is already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "Connecting to SharePoint..." -ForegroundColor Yellow
Write-Host "Browser will open for authentication" -ForegroundColor Cyan

try {
    Connect-PnPOnline -Url $SiteUrl -Interactive
    Write-Host "SharePoint connection successful!" -ForegroundColor Green
} catch {
    Write-Host "SharePoint connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "   1. Check if the site URL is correct" -ForegroundColor White
    Write-Host "   2. Confirm you have site access permissions" -ForegroundColor White
    Write-Host "   3. Check network connection" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting SharePoint list creation..." -ForegroundColor Yellow

# Define lists to create with English names to avoid encoding issues
$listsToCreate = @(
    @{Name="RepairRecords"; DisplayName="Repair Records"; Description="Repair records management list"},
    @{Name="CustomerData"; DisplayName="Customer Data"; Description="Customer data management list"},
    @{Name="PartsData"; DisplayName="Parts Data"; Description="Parts data management list"},
    @{Name="ProductData"; DisplayName="Product Data"; Description="Product data management list"}
)

$successCount = 0
$skipCount = 0

foreach ($listInfo in $listsToCreate) {
    Write-Host "Creating list: $($listInfo.DisplayName)..." -ForegroundColor Cyan
    try {
        $existingList = Get-PnPList -Identity $listInfo.Name -ErrorAction SilentlyContinue
        if ($existingList) {
            Write-Host "   List already exists, skipping creation" -ForegroundColor Yellow
            $skipCount++
        } else {
            $newList = New-PnPList -Title $listInfo.Name -Template GenericList -Description $listInfo.Description
            Write-Host "   List created successfully" -ForegroundColor Green
            $successCount++
            
            # Add basic fields for each list
            if ($listInfo.Name -eq "RepairRecords") {
                Add-PnPField -List $newList -DisplayName "Repair ID" -InternalName "RepairId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Customer Name" -InternalName "CustomerName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Phone" -InternalName "Phone" -Type Text
                Add-PnPField -List $newList -DisplayName "Serial Number" -InternalName "SerialNumber" -Type Text
                Add-PnPField -List $newList -DisplayName "Product Name" -InternalName "ProductName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Product Model" -InternalName "ProductModel" -Type Text
                Add-PnPField -List $newList -DisplayName "Issue" -InternalName "Issue" -Type Note
                Add-PnPField -List $newList -DisplayName "Service Status" -InternalName "ServiceStatus" -Type Choice -Choices @("Complaint", "Maintenance")
                Add-PnPField -List $newList -DisplayName "Repair Record" -InternalName "RepairRecord" -Type Choice -Choices @("Adjustment", "Motor Replacement", "Cleaning", "Calibration", "Parts Replacement", "Software Update")
                Add-PnPField -List $newList -DisplayName "Test Result" -InternalName "TestResult" -Type Choice -Choices @("Normal", "Abnormal", "Pending Test")
                Add-PnPField -List $newList -DisplayName "Technician" -InternalName "Technician" -Type Text
                Add-PnPField -List $newList -DisplayName "Repair Date" -InternalName "RepairDate" -Type DateTime
                Write-Host "   Added fields to Repair Records list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "CustomerData") {
                Add-PnPField -List $newList -DisplayName "Customer ID" -InternalName "CustomerId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Customer Name" -InternalName "CustomerName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Phone" -InternalName "Phone" -Type Text
                Add-PnPField -List $newList -DisplayName "Email" -InternalName "Email" -Type Text
                Add-PnPField -List $newList -DisplayName "Address" -InternalName "Address" -Type Note
                Add-PnPField -List $newList -DisplayName "Company" -InternalName "Company" -Type Text
                Add-PnPField -List $newList -DisplayName "Status" -InternalName "Status" -Type Choice -Choices @("Active", "Inactive", "VIP", "Blacklist")
                Write-Host "   Added fields to Customer Data list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "PartsData") {
                Add-PnPField -List $newList -DisplayName "Part ID" -InternalName "PartId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Part Name" -InternalName "PartName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Category" -InternalName "Category" -Type Choice -Choices @("Electronic Parts", "Mechanical Parts", "Consumables", "Tools", "Accessories")
                Add-PnPField -List $newList -DisplayName "Model" -InternalName "Model" -Type Text
                Add-PnPField -List $newList -DisplayName "Supplier" -InternalName "Supplier" -Type Text
                Add-PnPField -List $newList -DisplayName "Stock" -InternalName "Stock" -Type Number
                Add-PnPField -List $newList -DisplayName "Price" -InternalName "Price" -Type Currency
                Add-PnPField -List $newList -DisplayName "Min Stock" -InternalName "MinStock" -Type Number
                Add-PnPField -List $newList -DisplayName "Location" -InternalName "Location" -Type Text
                Write-Host "   Added fields to Parts Data list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "ProductData") {
                Add-PnPField -List $newList -DisplayName "Product ID" -InternalName "ProductId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Product Name" -InternalName "ProductName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Brand" -InternalName "Brand" -Type Text
                Add-PnPField -List $newList -DisplayName "Model" -InternalName "Model" -Type Text
                Add-PnPField -List $newList -DisplayName "Category" -InternalName "Category" -Type Choice -Choices @("Mobile Phone", "Tablet", "Laptop", "Desktop", "Printer", "Other")
                Add-PnPField -List $newList -DisplayName "Description" -InternalName "Description" -Type Note
                Add-PnPField -List $newList -DisplayName "Warranty Period" -InternalName "WarrantyPeriod" -Type Text
                Write-Host "   Added fields to Product Data list" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "   List creation failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Creation Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Successfully created: $successCount lists" -ForegroundColor Green
Write-Host "Already existed: $skipCount lists" -ForegroundColor Yellow
Write-Host ""

if ($successCount -gt 0 -or $skipCount -gt 0) {
    Write-Host "SharePoint lists are ready!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "   1. Return to the maintenance management system" -ForegroundColor White
    Write-Host "   2. Go to System Settings -> SharePoint Integration" -ForegroundColor White
    Write-Host "   3. Click Test Connection button" -ForegroundColor White
    Write-Host "   4. Start using the system after successful connection" -ForegroundColor White
} else {
    Write-Host "No lists were successfully created" -ForegroundColor Red
    Write-Host "   Please check permissions or contact SharePoint administrator" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "IMPORTANT: The lists are created with English names to avoid encoding issues." -ForegroundColor Cyan
Write-Host "The system will automatically map to the correct display names." -ForegroundColor Cyan
Write-Host ""

Disconnect-PnPOnline
Read-Host "Press Enter to exit"
