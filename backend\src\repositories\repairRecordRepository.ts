import { PrismaClient, RepairRecord, RepairStatus as PrismaRepairStatus, RepairPriority as PrismaRepairPriority, WarrantyStatus as PrismaWarrantyStatus } from '@prisma/client';
import { 
  RepairRecordInfo, 
  CreateRepairRecordRequest, 
  UpdateRepairRecordRequest, 
  RepairRecordQueryParams,
  RepairRecordListResponse,
  RepairRecordStatistics,
  RepairRecordSearchResult,
  RepairRecordDetailInfo,
  RepairPartUsage,
  RepairStatusHistory,
  RepairTimelineEvent,
  UpdateRepairStatusRequest,
  UsePartRequest,
  BatchUsePartsRequest,
  RepairStatus,
  RepairPriority,
  WarrantyStatus,
  TimelineEventType,
  DEFAULT_REPAIR_RECORD_PAGINATION
} from '../types/repairRecord';
import { logger } from '../utils/logger';

export class RepairRecordRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找維修記錄
  async findById(id: string): Promise<RepairRecordInfo | null> {
    try {
      const repairRecord = await this.prisma.repairRecord.findUnique({
        where: { id: BigInt(id) },
        include: {
          customer: {
            select: {
              name: true,
              phone: true,
              email: true,
            },
          },
          product: {
            select: {
              name: true,
              model: true,
              brand: true,
            },
          },
          assignedTechnician: {
            select: {
              fullName: true,
            },
          },
        },
      });

      if (!repairRecord) return null;

      return this.mapRepairRecordToInfo(repairRecord);
    } catch (error) {
      logger.error('查找維修記錄失敗:', error);
      throw new Error('查找維修記錄失敗');
    }
  }

  // 根據維修單號查找維修記錄
  async findByRepairNumber(repairNumber: string): Promise<RepairRecord | null> {
    try {
      return await this.prisma.repairRecord.findFirst({
        where: { repairNumber },
      });
    } catch (error) {
      logger.error('根據維修單號查找維修記錄失敗:', error);
      throw new Error('查找維修記錄失敗');
    }
  }

  // 檢查維修單號是否存在
  async checkRepairNumberExists(repairNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const whereCondition = excludeId 
        ? { 
            repairNumber,
            NOT: { id: BigInt(excludeId) }
          }
        : { repairNumber };

      const existingRecord = await this.prisma.repairRecord.findFirst({
        where: whereCondition,
        select: { id: true },
      });

      return !!existingRecord;
    } catch (error) {
      logger.error('檢查維修單號存在性失敗:', error);
      throw new Error('檢查維修單號存在性失敗');
    }
  }

  // 生成維修單號
  async generateRepairNumber(): Promise<string> {
    try {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      
      const prefix = `R${year}${month}${day}`;
      
      // 查找今天最大的序號
      const lastRecord = await this.prisma.repairRecord.findFirst({
        where: {
          repairNumber: {
            startsWith: prefix,
          },
        },
        orderBy: {
          repairNumber: 'desc',
        },
        select: {
          repairNumber: true,
        },
      });

      let sequence = 1;
      if (lastRecord) {
        const lastSequence = parseInt(lastRecord.repairNumber.slice(-4));
        sequence = lastSequence + 1;
      }

      return `${prefix}${String(sequence).padStart(4, '0')}`;
    } catch (error) {
      logger.error('生成維修單號失敗:', error);
      throw new Error('生成維修單號失敗');
    }
  }

  // 創建維修記錄
  async create(recordData: CreateRepairRecordRequest, createdBy: string): Promise<RepairRecordInfo> {
    try {
      const repairNumber = await this.generateRepairNumber();

      const repairRecord = await this.prisma.repairRecord.create({
        data: {
          repairNumber,
          customerId: BigInt(recordData.customerId),
          productId: BigInt(recordData.productId),
          serialNumber: recordData.serialNumber,
          issueDescription: recordData.issueDescription,
          symptoms: recordData.symptoms || [],
          priority: recordData.priority || RepairPriority.NORMAL,
          status: RepairStatus.RECEIVED,
          assignedTechnicianId: recordData.assignedTechnicianId ? BigInt(recordData.assignedTechnicianId) : null,
          estimatedCost: recordData.estimatedCost,
          warrantyStatus: recordData.warrantyStatus || WarrantyStatus.UNKNOWN,
          warrantyExpiryDate: recordData.warrantyExpiryDate,
          receivedDate: recordData.receivedDate || new Date(),
          notes: recordData.notes,
          internalNotes: recordData.internalNotes,
          createdBy,
        },
        include: {
          customer: {
            select: {
              name: true,
              phone: true,
              email: true,
            },
          },
          product: {
            select: {
              name: true,
              model: true,
              brand: true,
            },
          },
          assignedTechnician: {
            select: {
              fullName: true,
            },
          },
        },
      });

      // 創建初始狀態歷史記錄
      await this.createStatusHistory({
        repairRecordId: repairRecord.id.toString(),
        toStatus: RepairStatus.RECEIVED,
        changedBy: createdBy,
        reason: '維修記錄創建',
      });

      // 創建時間軸事件
      await this.createTimelineEvent({
        repairRecordId: repairRecord.id.toString(),
        eventType: TimelineEventType.CREATED,
        title: '維修記錄已創建',
        description: `維修單號: ${repairNumber}`,
        performedBy: createdBy,
      });

      logger.info('維修記錄創建成功:', { repairRecordId: repairRecord.id, repairNumber });

      return this.mapRepairRecordToInfo(repairRecord);
    } catch (error) {
      logger.error('創建維修記錄失敗:', error);
      throw new Error('創建維修記錄失敗');
    }
  }

  // 更新維修記錄
  async update(id: string, recordData: UpdateRepairRecordRequest, updatedBy: string): Promise<RepairRecordInfo> {
    try {
      const updateData: any = { ...recordData };
      
      // 轉換ID欄位
      if (recordData.customerId) {
        updateData.customerId = BigInt(recordData.customerId);
      }
      if (recordData.productId) {
        updateData.productId = BigInt(recordData.productId);
      }
      if (recordData.assignedTechnicianId) {
        updateData.assignedTechnicianId = BigInt(recordData.assignedTechnicianId);
      }

      const repairRecord = await this.prisma.repairRecord.update({
        where: { id: BigInt(id) },
        data: updateData,
        include: {
          customer: {
            select: {
              name: true,
              phone: true,
              email: true,
            },
          },
          product: {
            select: {
              name: true,
              model: true,
              brand: true,
            },
          },
          assignedTechnician: {
            select: {
              fullName: true,
            },
          },
        },
      });

      logger.info('維修記錄更新成功:', { repairRecordId: repairRecord.id });

      return this.mapRepairRecordToInfo(repairRecord);
    } catch (error) {
      logger.error('更新維修記錄失敗:', error);
      throw new Error('更新維修記錄失敗');
    }
  }

  // 刪除維修記錄（軟刪除）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.repairRecord.update({
        where: { id: BigInt(id) },
        data: { 
          status: RepairStatus.CANCELLED,
          deletedAt: new Date(),
        },
      });

      logger.info('維修記錄軟刪除成功:', { repairRecordId: id });
    } catch (error) {
      logger.error('軟刪除維修記錄失敗:', error);
      throw new Error('刪除維修記錄失敗');
    }
  }

  // 硬刪除維修記錄
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.repairRecord.delete({
        where: { id: BigInt(id) },
      });

      logger.info('維修記錄硬刪除成功:', { repairRecordId: id });
    } catch (error) {
      logger.error('硬刪除維修記錄失敗:', error);
      throw new Error('刪除維修記錄失敗');
    }
  }

  // 獲取維修記錄列表
  async findMany(params: RepairRecordQueryParams): Promise<RepairRecordListResponse> {
    try {
      const {
        page = DEFAULT_REPAIR_RECORD_PAGINATION.page,
        limit = DEFAULT_REPAIR_RECORD_PAGINATION.limit,
        search,
        customerId,
        productId,
        assignedTechnicianId,
        status,
        priority,
        warrantyStatus,
        dateFrom,
        dateTo,
        costMin,
        costMax,
        overdue,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_REPAIR_RECORD_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {
        deletedAt: null, // 排除已刪除的記錄
      };

      if (search) {
        where.OR = [
          { repairNumber: { contains: search, mode: 'insensitive' } },
          { issueDescription: { contains: search, mode: 'insensitive' } },
          { serialNumber: { contains: search, mode: 'insensitive' } },
          { customer: { name: { contains: search, mode: 'insensitive' } } },
          { product: { model: { contains: search, mode: 'insensitive' } } },
        ];
      }

      if (customerId) {
        where.customerId = BigInt(customerId);
      }

      if (productId) {
        where.productId = BigInt(productId);
      }

      if (assignedTechnicianId) {
        where.assignedTechnicianId = BigInt(assignedTechnicianId);
      }

      if (status) {
        where.status = status;
      }

      if (priority) {
        where.priority = priority;
      }

      if (warrantyStatus) {
        where.warrantyStatus = warrantyStatus;
      }

      if (dateFrom || dateTo) {
        where.receivedDate = {};
        if (dateFrom) where.receivedDate.gte = dateFrom;
        if (dateTo) where.receivedDate.lte = dateTo;
      }

      if (costMin !== undefined || costMax !== undefined) {
        where.actualCost = {};
        if (costMin !== undefined) where.actualCost.gte = costMin;
        if (costMax !== undefined) where.actualCost.lte = costMax;
      }

      if (overdue) {
        where.AND = where.AND || [];
        where.AND.push({
          estimatedCompletionDate: { lt: new Date() },
          status: { notIn: [RepairStatus.COMPLETED, RepairStatus.DELIVERED, RepairStatus.CANCELLED] },
        });
      }

      // 執行查詢
      const [repairRecords, total] = await Promise.all([
        this.prisma.repairRecord.findMany({
          where,
          include: {
            customer: {
              select: {
                name: true,
                phone: true,
                email: true,
              },
            },
            product: {
              select: {
                name: true,
                model: true,
                brand: true,
              },
            },
            assignedTechnician: {
              select: {
                fullName: true,
              },
            },
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.repairRecord.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        repairRecords: repairRecords.map(record => this.mapRepairRecordToInfo(record)),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          customerId,
          productId,
          assignedTechnicianId,
          status,
          priority,
          warrantyStatus,
          dateRange: (dateFrom || dateTo) ? { from: dateFrom, to: dateTo } : undefined,
          costRange: (costMin !== undefined || costMax !== undefined) ? { min: costMin, max: costMax } : undefined,
          overdue,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取維修記錄列表失敗:', error);
      throw new Error('獲取維修記錄列表失敗');
    }
  }

  // 搜尋維修記錄
  async search(query: string, limit: number = 10): Promise<RepairRecordSearchResult[]> {
    try {
      const repairRecords = await this.prisma.repairRecord.findMany({
        where: {
          OR: [
            { repairNumber: { contains: query, mode: 'insensitive' } },
            { issueDescription: { contains: query, mode: 'insensitive' } },
            { serialNumber: { contains: query, mode: 'insensitive' } },
            { customer: { name: { contains: query, mode: 'insensitive' } } },
            { product: { model: { contains: query, mode: 'insensitive' } } },
          ],
          deletedAt: null,
        },
        include: {
          customer: {
            select: {
              name: true,
            },
          },
          product: {
            select: {
              model: true,
            },
          },
          assignedTechnician: {
            select: {
              fullName: true,
            },
          },
        },
        take: limit,
      });

      return repairRecords.map(record => ({
        id: record.id.toString(),
        repairNumber: record.repairNumber,
        customerName: record.customer?.name || 'Unknown',
        productModel: record.product?.model || 'Unknown',
        status: record.status as RepairStatus,
        priority: record.priority as RepairPriority,
        receivedDate: record.receivedDate,
        estimatedCompletionDate: record.estimatedCompletionDate,
        assignedTechnicianName: record.assignedTechnician?.fullName,
        relevanceScore: this.calculateRelevanceScore(record, query),
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logger.error('搜尋維修記錄失敗:', error);
      throw new Error('搜尋維修記錄失敗');
    }
  }

  // 創建狀態歷史記錄
  async createStatusHistory(data: {
    repairRecordId: string;
    fromStatus?: RepairStatus;
    toStatus: RepairStatus;
    changedBy: string;
    reason?: string;
    notes?: string;
  }): Promise<void> {
    try {
      await this.prisma.repairStatusHistory.create({
        data: {
          repairRecordId: BigInt(data.repairRecordId),
          fromStatus: data.fromStatus,
          toStatus: data.toStatus,
          changedBy: data.changedBy,
          reason: data.reason,
          notes: data.notes,
        },
      });
    } catch (error) {
      logger.error('創建狀態歷史記錄失敗:', error);
      throw new Error('創建狀態歷史記錄失敗');
    }
  }

  // 創建時間軸事件
  async createTimelineEvent(data: {
    repairRecordId: string;
    eventType: TimelineEventType;
    title: string;
    description?: string;
    performedBy: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    try {
      await this.prisma.repairTimelineEvent.create({
        data: {
          repairRecordId: BigInt(data.repairRecordId),
          eventType: data.eventType,
          title: data.title,
          description: data.description,
          performedBy: data.performedBy,
          metadata: data.metadata,
        },
      });
    } catch (error) {
      logger.error('創建時間軸事件失敗:', error);
      throw new Error('創建時間軸事件失敗');
    }
  }

  // 計算相關性分數
  private calculateRelevanceScore(record: any, query: string): number {
    const lowerQuery = query.toLowerCase();
    let score = 0;

    // 精確匹配得分更高
    if (record.repairNumber.toLowerCase() === lowerQuery) score += 100;
    else if (record.repairNumber.toLowerCase().includes(lowerQuery)) score += 75;

    if (record.customer?.name?.toLowerCase() === lowerQuery) score += 90;
    else if (record.customer?.name?.toLowerCase().includes(lowerQuery)) score += 60;

    if (record.product?.model?.toLowerCase() === lowerQuery) score += 80;
    else if (record.product?.model?.toLowerCase().includes(lowerQuery)) score += 50;

    if (record.serialNumber?.toLowerCase() === lowerQuery) score += 85;
    else if (record.serialNumber?.toLowerCase().includes(lowerQuery)) score += 55;

    if (record.issueDescription?.toLowerCase().includes(lowerQuery)) score += 30;

    // 開頭匹配得分較高
    if (record.repairNumber.toLowerCase().startsWith(lowerQuery)) score += 25;
    if (record.customer?.name?.toLowerCase().startsWith(lowerQuery)) score += 20;
    if (record.product?.model?.toLowerCase().startsWith(lowerQuery)) score += 15;

    return score;
  }

  // 獲取維修記錄詳細資訊
  async findByIdWithDetails(id: string): Promise<RepairRecordDetailInfo | null> {
    try {
      const repairRecord = await this.prisma.repairRecord.findUnique({
        where: { id: BigInt(id) },
        include: {
          customer: true,
          product: {
            include: {
              category: true,
            },
          },
          assignedTechnician: {
            select: {
              id: true,
              fullName: true,
              email: true,
              specialties: true,
            },
          },
          partsUsed: {
            include: {
              part: {
                select: {
                  name: true,
                  partNumber: true,
                },
              },
              usedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
            orderBy: { usedAt: 'desc' },
          },
          statusHistory: {
            include: {
              changedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
            orderBy: { changedAt: 'desc' },
          },
          timelineEvents: {
            include: {
              performedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
            orderBy: { performedAt: 'desc' },
          },
          attachments: {
            include: {
              uploadedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
            orderBy: { uploadedAt: 'desc' },
          },
          qualityChecks: {
            include: {
              checkedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
            orderBy: { checkedAt: 'desc' },
          },
          customerFeedback: true,
        },
      });

      if (!repairRecord) return null;

      // 計算總零件價值
      const totalPartsValue = repairRecord.partsUsed.reduce((total, partUsage) => {
        return total + (partUsage.totalCost ? parseFloat(partUsage.totalCost.toString()) : 0);
      }, 0);

      // 計算總人工費用（實際費用 - 零件費用）
      const totalLaborCost = (repairRecord.actualCost ? parseFloat(repairRecord.actualCost.toString()) : 0) - totalPartsValue;

      // 計算利潤率
      const profitMargin = repairRecord.actualCost
        ? ((parseFloat(repairRecord.actualCost.toString()) - totalPartsValue) / parseFloat(repairRecord.actualCost.toString())) * 100
        : 0;

      return {
        ...this.mapRepairRecordToInfo(repairRecord),
        customer: {
          id: repairRecord.customer.id.toString(),
          name: repairRecord.customer.name,
          phone: repairRecord.customer.phone,
          email: repairRecord.customer.email,
          address: repairRecord.customer.address,
        },
        product: {
          id: repairRecord.product.id.toString(),
          name: repairRecord.product.name,
          model: repairRecord.product.model,
          brand: repairRecord.product.brand,
          category: repairRecord.product.category?.name || 'Unknown',
          warrantyPeriod: repairRecord.product.warrantyPeriod,
        },
        assignedTechnician: repairRecord.assignedTechnician ? {
          id: repairRecord.assignedTechnician.id.toString(),
          fullName: repairRecord.assignedTechnician.fullName,
          email: repairRecord.assignedTechnician.email,
          specialties: repairRecord.assignedTechnician.specialties,
        } : undefined,
        partsUsed: repairRecord.partsUsed.map(partUsage => ({
          id: partUsage.id.toString(),
          partId: partUsage.partId.toString(),
          partName: partUsage.part.name,
          partNumber: partUsage.part.partNumber,
          quantity: partUsage.quantity,
          unitPrice: partUsage.unitPrice ? parseFloat(partUsage.unitPrice.toString()) : undefined,
          totalCost: partUsage.totalCost ? parseFloat(partUsage.totalCost.toString()) : undefined,
          usedBy: partUsage.usedBy,
          usedByName: partUsage.usedByUser?.fullName || 'Unknown',
          usedAt: partUsage.usedAt,
          notes: partUsage.notes,
          isWarrantyPart: partUsage.isWarrantyPart,
        })),
        statusHistory: repairRecord.statusHistory.map(history => ({
          id: history.id.toString(),
          fromStatus: history.fromStatus as RepairStatus,
          toStatus: history.toStatus as RepairStatus,
          changedBy: history.changedBy,
          changedByName: history.changedByUser?.fullName || 'Unknown',
          changedAt: history.changedAt,
          reason: history.reason,
          notes: history.notes,
        })),
        timelineEvents: repairRecord.timelineEvents.map(event => ({
          id: event.id.toString(),
          eventType: event.eventType as TimelineEventType,
          title: event.title,
          description: event.description,
          performedBy: event.performedBy,
          performedByName: event.performedByUser?.fullName || 'Unknown',
          performedAt: event.performedAt,
          metadata: event.metadata as Record<string, any>,
        })),
        attachments: repairRecord.attachments.map(attachment => ({
          id: attachment.id.toString(),
          fileName: attachment.fileName,
          originalName: attachment.originalName,
          fileSize: attachment.fileSize,
          mimeType: attachment.mimeType,
          fileType: attachment.fileType as any,
          description: attachment.description,
          uploadedBy: attachment.uploadedBy,
          uploadedByName: attachment.uploadedByUser?.fullName || 'Unknown',
          uploadedAt: attachment.uploadedAt,
          url: attachment.url,
        })),
        qualityChecks: repairRecord.qualityChecks.map(check => ({
          id: check.id.toString(),
          checkType: check.checkType as any,
          checkItems: check.checkItems as any[],
          overallResult: check.overallResult as any,
          checkedBy: check.checkedBy,
          checkedByName: check.checkedByUser?.fullName || 'Unknown',
          checkedAt: check.checkedAt,
          notes: check.notes,
        })),
        customerFeedback: repairRecord.customerFeedback ? {
          id: repairRecord.customerFeedback.id.toString(),
          satisfactionRating: repairRecord.customerFeedback.satisfactionRating,
          serviceRating: repairRecord.customerFeedback.serviceRating,
          qualityRating: repairRecord.customerFeedback.qualityRating,
          speedRating: repairRecord.customerFeedback.speedRating,
          comments: repairRecord.customerFeedback.comments,
          wouldRecommend: repairRecord.customerFeedback.wouldRecommend,
          submittedAt: repairRecord.customerFeedback.submittedAt,
        } : undefined,
        totalPartsValue,
        totalLaborCost,
        profitMargin,
      };
    } catch (error) {
      logger.error('獲取維修記錄詳細資訊失敗:', error);
      throw new Error('獲取維修記錄詳細資訊失敗');
    }
  }

  // 獲取維修記錄統計
  async getStatistics(): Promise<RepairRecordStatistics> {
    try {
      const [total, byStatus, byPriority, byTechnician, byWarrantyStatus, averageStats, overdueCount, recentCounts] = await Promise.all([
        this.prisma.repairRecord.count({ where: { deletedAt: null } }),
        this.getStatsByStatus(),
        this.getStatsByPriority(),
        this.getStatsByTechnician(),
        this.getStatsByWarrantyStatus(),
        this.getAverageStats(),
        this.getOverdueCount(),
        this.getRecentCounts(),
      ]);

      const completionRate = total > 0 ? (byStatus.find(s => s.status === RepairStatus.DELIVERED)?.count || 0) / total * 100 : 0;

      return {
        total,
        byStatus: byStatus.map(stat => ({
          ...stat,
          percentage: total > 0 ? (stat.count / total) * 100 : 0,
        })),
        byPriority: byPriority.map(stat => ({
          ...stat,
          percentage: total > 0 ? (stat.count / total) * 100 : 0,
        })),
        byTechnician,
        byWarrantyStatus: byWarrantyStatus.map(stat => ({
          ...stat,
          percentage: total > 0 ? (stat.count / total) * 100 : 0,
        })),
        averageRepairTime: averageStats.averageRepairTime,
        averageCost: averageStats.averageCost,
        totalRevenue: averageStats.totalRevenue,
        overdueCount,
        completionRate,
        recentTrends: recentCounts,
      };
    } catch (error) {
      logger.error('獲取維修記錄統計失敗:', error);
      throw new Error('獲取維修記錄統計失敗');
    }
  }

  // 獲取各狀態統計
  private async getStatsByStatus() {
    const statusStats = await this.prisma.repairRecord.groupBy({
      by: ['status'],
      _count: true,
      where: { deletedAt: null },
    });

    return statusStats.map(stat => ({
      status: stat.status as RepairStatus,
      count: stat._count,
    }));
  }

  // 獲取各優先級統計
  private async getStatsByPriority() {
    const priorityStats = await this.prisma.repairRecord.groupBy({
      by: ['priority'],
      _count: true,
      where: { deletedAt: null },
    });

    return priorityStats.map(stat => ({
      priority: stat.priority as RepairPriority,
      count: stat._count,
    }));
  }

  // 獲取各技師統計
  private async getStatsByTechnician() {
    const technicianStats = await this.prisma.repairRecord.groupBy({
      by: ['assignedTechnicianId'],
      _count: true,
      _avg: {
        // 這裡需要計算平均完成時間，暫時返回0
      },
      where: {
        deletedAt: null,
        assignedTechnicianId: { not: null },
      },
    });

    // 獲取技師資訊
    const technicianIds = technicianStats.map(stat => stat.assignedTechnicianId).filter(id => id !== null);
    const technicians = await this.prisma.user.findMany({
      where: { id: { in: technicianIds } },
      select: { id: true, fullName: true },
    });

    return technicianStats.map(stat => {
      const technician = technicians.find(t => t.id === stat.assignedTechnicianId);
      return {
        technicianId: stat.assignedTechnicianId?.toString() || '',
        technicianName: technician?.fullName || 'Unknown',
        count: stat._count,
        averageCompletionTime: 0, // 需要複雜計算，暫時返回0
      };
    });
  }

  // 獲取各保固狀態統計
  private async getStatsByWarrantyStatus() {
    const warrantyStats = await this.prisma.repairRecord.groupBy({
      by: ['warrantyStatus'],
      _count: true,
      where: { deletedAt: null },
    });

    return warrantyStats.map(stat => ({
      warrantyStatus: stat.warrantyStatus as WarrantyStatus,
      count: stat._count,
    }));
  }

  // 獲取平均統計
  private async getAverageStats() {
    const result = await this.prisma.repairRecord.aggregate({
      _avg: {
        actualCost: true,
      },
      _sum: {
        actualCost: true,
      },
      where: {
        deletedAt: null,
        actualCost: { not: null },
      },
    });

    return {
      averageRepairTime: 0, // 需要複雜計算，暫時返回0
      averageCost: result._avg.actualCost ? parseFloat(result._avg.actualCost.toString()) : 0,
      totalRevenue: result._sum.actualCost ? parseFloat(result._sum.actualCost.toString()) : 0,
    };
  }

  // 獲取逾期數量
  private async getOverdueCount() {
    return await this.prisma.repairRecord.count({
      where: {
        deletedAt: null,
        estimatedCompletionDate: { lt: new Date() },
        status: { notIn: [RepairStatus.COMPLETED, RepairStatus.DELIVERED, RepairStatus.CANCELLED] },
      },
    });
  }

  // 獲取最近統計
  private async getRecentCounts() {
    const now = new Date();
    const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastWeekStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const [thisWeek, lastWeek, thisMonth, lastMonth] = await Promise.all([
      this.prisma.repairRecord.count({ where: { createdAt: { gte: thisWeekStart }, deletedAt: null } }),
      this.prisma.repairRecord.count({ where: { createdAt: { gte: lastWeekStart, lt: thisWeekStart }, deletedAt: null } }),
      this.prisma.repairRecord.count({ where: { createdAt: { gte: thisMonthStart }, deletedAt: null } }),
      this.prisma.repairRecord.count({ where: { createdAt: { gte: lastMonthStart, lt: thisMonthStart }, deletedAt: null } }),
    ]);

    return {
      thisWeek,
      lastWeek,
      thisMonth,
      lastMonth,
    };
  }

  // 映射維修記錄資料到介面
  private mapRepairRecordToInfo(record: any): RepairRecordInfo {
    return {
      id: record.id.toString(),
      repairNumber: record.repairNumber,
      customerId: record.customerId.toString(),
      customerName: record.customer?.name || 'Unknown',
      customerPhone: record.customer?.phone,
      customerEmail: record.customer?.email,
      productId: record.productId.toString(),
      productName: record.product?.name || 'Unknown',
      productModel: record.product?.model || 'Unknown',
      productBrand: record.product?.brand || 'Unknown',
      serialNumber: record.serialNumber,
      issueDescription: record.issueDescription,
      symptoms: record.symptoms || [],
      priority: record.priority as RepairPriority,
      status: record.status as RepairStatus,
      assignedTechnicianId: record.assignedTechnicianId?.toString(),
      assignedTechnicianName: record.assignedTechnician?.fullName,
      estimatedCost: record.estimatedCost ? parseFloat(record.estimatedCost.toString()) : undefined,
      actualCost: record.actualCost ? parseFloat(record.actualCost.toString()) : undefined,
      estimatedCompletionDate: record.estimatedCompletionDate,
      actualCompletionDate: record.actualCompletionDate,
      warrantyStatus: record.warrantyStatus as WarrantyStatus,
      warrantyExpiryDate: record.warrantyExpiryDate,
      receivedDate: record.receivedDate,
      startedDate: record.startedDate,
      completedDate: record.completedDate,
      deliveredDate: record.deliveredDate,
      notes: record.notes,
      internalNotes: record.internalNotes,
      createdBy: record.createdBy,
      createdAt: record.createdAt,
      updatedAt: record.updatedAt,
    };
  }
}
