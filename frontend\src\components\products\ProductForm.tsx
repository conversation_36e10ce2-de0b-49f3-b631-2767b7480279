import React, { useEffect, useState } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Space, 
  message, 
  Switch,
  Row,
  Col,
  Typography,
  Select,
  InputNumber
} from 'antd';
import { 
  ShoppingOutlined, 
  BrandOutlined, 
  TagOutlined, 
  FileTextOutlined,
  SettingOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { Product, CreateProductRequest, UpdateProductRequest, ProductCategory } from '../../services/productService';

const { TextArea } = Input;
const { Text } = Typography;
const { Option } = Select;

interface ProductFormProps {
  visible: boolean;
  product?: Product | null;
  categories: ProductCategory[];
  onCancel: () => void;
  onSubmit: (data: CreateProductRequest | UpdateProductRequest) => Promise<void>;
  loading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
  visible,
  product,
  categories,
  onCancel,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [brands, setBrands] = useState<string[]>([]);
  const isEditing = !!product;

  // 模擬品牌列表
  const mockBrands = ['Apple', 'Samsung', 'Sony', 'LG', 'Asus', 'Acer', 'HP', 'Dell', 'Lenovo', 'MSI'];

  useEffect(() => {
    setBrands(mockBrands);
  }, []);

  useEffect(() => {
    if (visible) {
      if (product) {
        form.setFieldsValue({
          name: product.name,
          model: product.model,
          brand: product.brand,
          categoryId: product.categoryId,
          description: product.description || '',
          specifications: product.specifications || '',
          warrantyPeriod: product.warrantyPeriod,
          isActive: product.isActive,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          warrantyPeriod: 12,
          isActive: true,
        });
      }
    }
  }, [visible, product, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const submitData = {
        name: values.name.trim(),
        model: values.model.trim().toUpperCase(),
        brand: values.brand.trim(),
        categoryId: values.categoryId,
        description: values.description?.trim() || undefined,
        specifications: values.specifications?.trim() || undefined,
        warrantyPeriod: values.warrantyPeriod,
        ...(isEditing && { isActive: values.isActive }),
      };

      await onSubmit(submitData);
      form.resetFields();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 驗證型號唯一性（模擬）
  const validateModelUnique = async (_: any, value: string) => {
    if (!value || (isEditing && value.toUpperCase() === product?.model)) {
      return Promise.resolve();
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模擬檢查結果
    const existingModels = ['EXISTING-MODEL', 'TEST-MODEL'];
    if (existingModels.includes(value.toUpperCase())) {
      return Promise.reject(new Error('此型號已存在'));
    }
    
    return Promise.resolve();
  };

  const activeCategories = categories.filter(cat => cat.isActive);

  return (
    <Modal
      title={isEditing ? '編輯產品' : '新增產品'}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {isEditing ? '更新' : '新增'}
        </Button>,
      ]}
      width={700}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="產品名稱"
              rules={[
                { required: true, message: '請輸入產品名稱' },
                { min: 2, message: '產品名稱至少需要2個字符' },
                { max: 100, message: '產品名稱不能超過100個字符' },
              ]}
            >
              <Input
                prefix={<ShoppingOutlined />}
                placeholder="請輸入產品名稱"
                maxLength={100}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="model"
              label="產品型號"
              rules={[
                { required: true, message: '請輸入產品型號' },
                { min: 2, message: '產品型號至少需要2個字符' },
                { max: 50, message: '產品型號不能超過50個字符' },
                { pattern: /^[A-Z0-9\-_]+$/i, message: '型號只能包含字母、數字、連字符和底線' },
                { validator: validateModelUnique },
              ]}
            >
              <Input
                prefix={<TagOutlined />}
                placeholder="請輸入產品型號"
                maxLength={50}
                style={{ textTransform: 'uppercase' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="brand"
              label="品牌"
              rules={[
                { required: true, message: '請選擇或輸入品牌' },
                { max: 50, message: '品牌名稱不能超過50個字符' },
              ]}
            >
              <Select
                showSearch
                placeholder="請選擇或輸入品牌"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
                dropdownRender={(menu) => (
                  <div>
                    {menu}
                    <div style={{ padding: '8px', borderTop: '1px solid #f0f0f0' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        找不到品牌？直接輸入新品牌名稱
                      </Text>
                    </div>
                  </div>
                )}
              >
                {brands.map(brand => (
                  <Option key={brand} value={brand}>
                    <BrandOutlined style={{ marginRight: 4 }} />
                    {brand}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="categoryId"
              label="產品分類"
              rules={[
                { required: true, message: '請選擇產品分類' },
              ]}
            >
              <Select placeholder="請選擇產品分類">
                {activeCategories.map(category => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="warrantyPeriod"
              label="保固期限（月）"
              rules={[
                { required: true, message: '請輸入保固期限' },
                { type: 'number', min: 1, max: 120, message: '保固期限必須在1-120個月之間' },
              ]}
            >
              <InputNumber
                prefix={<ClockCircleOutlined />}
                placeholder="請輸入保固期限"
                min={1}
                max={120}
                style={{ width: '100%' }}
                addonAfter="個月"
              />
            </Form.Item>
          </Col>
          {isEditing && (
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="產品狀態"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Form.Item>
            </Col>
          )}
        </Row>

        <Form.Item
          name="description"
          label="產品描述"
          rules={[
            { max: 500, message: '產品描述不能超過500個字符' },
          ]}
        >
          <TextArea
            placeholder="請輸入產品描述（選填）"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="specifications"
          label="產品規格"
          rules={[
            { max: 1000, message: '產品規格不能超過1000個字符' },
          ]}
        >
          <TextArea
            prefix={<SettingOutlined />}
            placeholder="請輸入詳細的產品規格（選填）"
            rows={4}
            maxLength={1000}
            showCount
          />
        </Form.Item>

        {!isEditing && (
          <div style={{ 
            background: '#f6f6f6', 
            padding: '12px', 
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>提示：</strong><br />
              • 產品型號將自動轉換為大寫，且必須唯一<br />
              • 品牌可以從列表選擇或直接輸入新品牌<br />
              • 保固期限以月為單位，範圍1-120個月<br />
              • 產品描述和規格為選填項目<br />
              • 新增的產品預設為啟用狀態
            </Text>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default ProductForm;
