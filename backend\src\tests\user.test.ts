import request from 'supertest';
import { PrismaClient, UserRole } from '@prisma/client';
import app from '../index';
import { PasswordUtils } from '../utils/password';
import { JWTUtils } from '../utils/jwt';

const prisma = new PrismaClient();

describe('User Management API', () => {
  let adminToken: string;
  let technicianToken: string;
  let viewerToken: string;
  let testUsers: any[] = [];

  beforeAll(async () => {
    // 建立測試用戶
    const adminPassword = await PasswordUtils.hashPassword('AdminPass123!');
    const technicianPassword = await PasswordUtils.hashPassword('TechPass123!');
    const viewerPassword = await PasswordUtils.hashPassword('ViewerPass123!');

    const admin = await prisma.user.create({
      data: {
        username: 'testadmin',
        email: '<EMAIL>',
        passwordHash: adminPassword,
        fullName: 'Test Admin',
        role: UserRole.ADMIN,
      },
    });

    const technician = await prisma.user.create({
      data: {
        username: 'testtechnician',
        email: '<EMAIL>',
        passwordHash: technicianPassword,
        fullName: 'Test Technician',
        role: UserRole.TECHNICIAN,
      },
    });

    const viewer = await prisma.user.create({
      data: {
        username: 'testviewer',
        email: '<EMAIL>',
        passwordHash: viewerPassword,
        fullName: 'Test Viewer',
        role: UserRole.VIEWER,
      },
    });

    testUsers = [admin, technician, viewer];

    // 生成令牌
    adminToken = JWTUtils.generateAccessToken({
      userId: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: admin.role,
    });

    technicianToken = JWTUtils.generateAccessToken({
      userId: technician.id.toString(),
      username: technician.username,
      email: technician.email,
      role: technician.role,
    });

    viewerToken = JWTUtils.generateAccessToken({
      userId: viewer.id.toString(),
      username: viewer.username,
      email: viewer.email,
      role: viewer.role,
    });
  });

  afterAll(async () => {
    // 清理測試資料
    for (const user of testUsers) {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }

    // 清理其他測試用戶
    await prisma.user.deleteMany({
      where: {
        username: { startsWith: 'testuser' },
      },
    });

    await prisma.$disconnect();
  });

  describe('GET /api/v1/users', () => {
    test('should get user list as admin', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.users)).toBe(true);
    });

    test('should get user list with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/users?page=1&limit=2')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(2);
      expect(response.body.data.users.length).toBeLessThanOrEqual(2);
    });

    test('should filter users by role', async () => {
      const response = await request(app)
        .get('/api/v1/users?role=ADMIN')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users.every((user: any) => user.role === 'ADMIN')).toBe(true);
    });

    test('should search users', async () => {
      const response = await request(app)
        .get('/api/v1/users?search=admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users.some((user: any) => 
        user.username.includes('admin') || 
        user.email.includes('admin') || 
        user.fullName.toLowerCase().includes('admin')
      )).toBe(true);
    });

    test('should fail without authentication', async () => {
      await request(app)
        .get('/api/v1/users')
        .expect(401);
    });

    test('should fail with insufficient permissions', async () => {
      await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/users', () => {
    test('should create user as admin', async () => {
      const userData = {
        username: 'testuser001',
        email: '<EMAIL>',
        fullName: 'Test User 001',
        role: 'VIEWER',
        password: 'TestPass123!',
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(userData.username);
      expect(response.body.data.user.email).toBe(userData.email);
    });

    test('should create user without password (auto-generated)', async () => {
      const userData = {
        username: 'testuser002',
        email: '<EMAIL>',
        fullName: 'Test User 002',
        role: 'VIEWER',
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(userData.username);
    });

    test('should fail with duplicate username', async () => {
      const userData = {
        username: 'testadmin', // 已存在的用戶名
        email: '<EMAIL>',
        fullName: 'Duplicate User',
        role: 'VIEWER',
      };

      await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData)
        .expect(400);
    });

    test('should fail with invalid data', async () => {
      const userData = {
        username: 'ab', // 太短
        email: 'invalid-email',
        fullName: '',
        role: 'INVALID_ROLE',
      };

      await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userData)
        .expect(400);
    });

    test('should fail without admin permission', async () => {
      const userData = {
        username: 'testuser003',
        email: '<EMAIL>',
        fullName: 'Test User 003',
        role: 'VIEWER',
      };

      await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${technicianToken}`)
        .send(userData)
        .expect(403);
    });
  });

  describe('GET /api/v1/users/:id', () => {
    test('should get user by id as admin', async () => {
      const userId = testUsers[1].id.toString();

      const response = await request(app)
        .get(`/api/v1/users/${userId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.id).toBe(userId);
    });

    test('should get own user info', async () => {
      const userId = testUsers[1].id.toString();

      const response = await request(app)
        .get(`/api/v1/users/${userId}`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.id).toBe(userId);
    });

    test('should fail to get other user info without admin permission', async () => {
      const adminId = testUsers[0].id.toString();

      await request(app)
        .get(`/api/v1/users/${adminId}`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(403);
    });

    test('should fail with non-existent user', async () => {
      await request(app)
        .get('/api/v1/users/999999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/v1/users/:id', () => {
    test('should update user as admin', async () => {
      const userId = testUsers[2].id.toString();
      const updateData = {
        fullName: 'Updated Viewer Name',
        email: '<EMAIL>',
      };

      const response = await request(app)
        .put(`/api/v1/users/${userId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.fullName).toBe(updateData.fullName);
      expect(response.body.data.user.email).toBe(updateData.email);
    });

    test('should fail without admin permission', async () => {
      const userId = testUsers[2].id.toString();
      const updateData = {
        fullName: 'Unauthorized Update',
      };

      await request(app)
        .put(`/api/v1/users/${userId}`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('GET /api/v1/users/search', () => {
    test('should search users', async () => {
      const response = await request(app)
        .get('/api/v1/users/search?q=test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(Array.isArray(response.body.data.results)).toBe(true);
    });

    test('should fail with short search query', async () => {
      await request(app)
        .get('/api/v1/users/search?q=a')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/users/statistics', () => {
    test('should get user statistics as admin', async () => {
      const response = await request(app)
        .get('/api/v1/users/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('active');
      expect(response.body.data.statistics).toHaveProperty('byRole');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/users/statistics')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('PUT /api/v1/users/profile', () => {
    test('should update own profile', async () => {
      const updateData = {
        fullName: 'Updated Technician Name',
        email: '<EMAIL>',
      };

      const response = await request(app)
        .put('/api/v1/users/profile')
        .set('Authorization', `Bearer ${technicianToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.fullName).toBe(updateData.fullName);
    });

    test('should fail without authentication', async () => {
      const updateData = {
        fullName: 'Unauthorized Update',
      };

      await request(app)
        .put('/api/v1/users/profile')
        .send(updateData)
        .expect(401);
    });
  });

  describe('GET /api/v1/users/roles', () => {
    test('should get user roles', async () => {
      const response = await request(app)
        .get('/api/v1/users/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.roles).toHaveLength(4);
      expect(response.body.data.roles[0]).toHaveProperty('value');
      expect(response.body.data.roles[0]).toHaveProperty('label');
    });
  });

  describe('POST /api/v1/users/:id/activate', () => {
    test('should activate user as admin', async () => {
      // 先停用用戶
      await prisma.user.update({
        where: { id: testUsers[2].id },
        data: { isActive: false },
      });

      const userId = testUsers[2].id.toString();

      const response = await request(app)
        .post(`/api/v1/users/${userId}/activate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.isActive).toBe(true);
    });
  });

  describe('POST /api/v1/users/:id/deactivate', () => {
    test('should deactivate user as admin', async () => {
      const userId = testUsers[2].id.toString();

      const response = await request(app)
        .post(`/api/v1/users/${userId}/deactivate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.isActive).toBe(false);
    });
  });

  describe('PUT /api/v1/users/:id/role', () => {
    test('should change user role as admin', async () => {
      const userId = testUsers[2].id.toString();
      const newRole = { role: 'CUSTOMER_SERVICE' };

      const response = await request(app)
        .put(`/api/v1/users/${userId}/role`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newRole)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.role).toBe(newRole.role);
    });
  });

  describe('GET /api/v1/users/check-username/:username', () => {
    test('should check username availability', async () => {
      const response = await request(app)
        .get('/api/v1/users/check-username/availableusername')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
    });

    test('should detect existing username', async () => {
      const response = await request(app)
        .get('/api/v1/users/check-username/testadmin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(false);
    });
  });
});
