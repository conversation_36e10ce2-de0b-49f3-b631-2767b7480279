import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import app from '../index';
import { JWTUtils } from '../utils/jwt';
import { PasswordUtils } from '../utils/password';

const prisma = new PrismaClient();

describe('Authentication System', () => {
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    // 建立測試用戶
    const hashedPassword = await PasswordUtils.hashPassword('TestPass123!');
    testUser = await prisma.user.create({
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        fullName: 'Test User',
        role: 'VIEWER',
      },
    });
  });

  afterAll(async () => {
    // 清理測試資料
    if (testUser) {
      await prisma.user.delete({ where: { id: testUser.id } });
    }
    await prisma.$disconnect();
  });

  describe('POST /api/v1/auth/register', () => {
    afterEach(async () => {
      // 清理註冊的測試用戶
      await prisma.user.deleteMany({
        where: {
          username: { startsWith: 'newuser' },
        },
      });
    });

    test('should register a new user successfully', async () => {
      const userData = {
        username: 'newuser123',
        email: '<EMAIL>',
        password: 'NewPass123!',
        fullName: 'New User',
        role: 'VIEWER',
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(userData.username);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    test('should fail with invalid password', async () => {
      const userData = {
        username: 'newuser456',
        email: '<EMAIL>',
        password: 'weak',
        fullName: 'New User',
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should fail with duplicate username', async () => {
      const userData = {
        username: 'testuser', // 已存在的用戶名
        email: '<EMAIL>',
        password: 'ValidPass123!',
        fullName: 'Another User',
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/auth/login', () => {
    test('should login with valid credentials', async () => {
      const loginData = {
        username: 'testuser',
        password: 'TestPass123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe('testuser');
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');

      // 保存令牌供後續測試使用
      authToken = response.body.data.tokens.accessToken;
    });

    test('should fail with invalid password', async () => {
      const loginData = {
        username: 'testuser',
        password: 'wrongpassword',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should fail with non-existent user', async () => {
      const loginData = {
        username: 'nonexistent',
        password: 'TestPass123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should login with email', async () => {
      const loginData = {
        username: '<EMAIL>', // 使用電子郵件登入
        password: 'TestPass123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/v1/auth/me', () => {
    test('should get current user info with valid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe('testuser');
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('should fail without token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/v1/auth/change-password', () => {
    test('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'TestPass123!',
        newPassword: 'NewTestPass123!',
      };

      const response = await request(app)
        .put('/api/v1/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 驗證新密碼可以登入
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'testuser',
          password: 'NewTestPass123!',
        })
        .expect(200);

      expect(loginResponse.body.success).toBe(true);

      // 恢復原密碼
      await request(app)
        .put('/api/v1/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          currentPassword: 'NewTestPass123!',
          newPassword: 'TestPass123!',
        });
    });

    test('should fail with wrong current password', async () => {
      const passwordData = {
        currentPassword: 'WrongPassword123!',
        newPassword: 'NewTestPass123!',
      };

      const response = await request(app)
        .put('/api/v1/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('should fail with weak new password', async () => {
      const passwordData = {
        currentPassword: 'TestPass123!',
        newPassword: 'weak',
      };

      const response = await request(app)
        .put('/api/v1/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    let refreshToken: string;

    beforeAll(async () => {
      // 獲取刷新令牌
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'testuser',
          password: 'TestPass123!',
        });

      refreshToken = loginResponse.body.data.tokens.refreshToken;
    });

    test('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    test('should fail with invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/auth/verify', () => {
    test('should verify valid token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.valid).toBe(true);
      expect(response.body.data.payload).toHaveProperty('userId');
    });

    test('should fail with invalid token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/verify')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    test('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should fail without token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});

describe('JWT Utils', () => {
  const testPayload = {
    userId: '123',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'VIEWER',
  };

  test('should generate and verify access token', () => {
    const token = JWTUtils.generateAccessToken(testPayload);
    expect(token).toBeDefined();

    const decoded = JWTUtils.verifyAccessToken(token);
    expect(decoded.userId).toBe(testPayload.userId);
    expect(decoded.username).toBe(testPayload.username);
  });

  test('should generate token pair', () => {
    const tokenPair = JWTUtils.generateTokenPair(testPayload);
    expect(tokenPair).toHaveProperty('accessToken');
    expect(tokenPair).toHaveProperty('refreshToken');
    expect(tokenPair).toHaveProperty('expiresIn');
  });

  test('should detect expiring token', () => {
    const token = JWTUtils.generateAccessToken(testPayload);
    const isExpiring = JWTUtils.isTokenExpiringSoon(token, 60); // 60分鐘內過期
    expect(typeof isExpiring).toBe('boolean');
  });
});

describe('Password Utils', () => {
  test('should hash and verify password', async () => {
    const password = 'TestPassword123!';
    const hashedPassword = await PasswordUtils.hashPassword(password);
    
    expect(hashedPassword).toBeDefined();
    expect(hashedPassword).not.toBe(password);

    const isValid = await PasswordUtils.verifyPassword(password, hashedPassword);
    expect(isValid).toBe(true);

    const isInvalid = await PasswordUtils.verifyPassword('wrongpassword', hashedPassword);
    expect(isInvalid).toBe(false);
  });

  test('should validate password strength', () => {
    const strongPassword = 'StrongPass123!';
    const weakPassword = 'weak';

    const strongValidation = PasswordUtils.validatePassword(strongPassword);
    expect(strongValidation.isValid).toBe(true);
    expect(strongValidation.strength).toBe('strong');

    const weakValidation = PasswordUtils.validatePassword(weakPassword);
    expect(weakValidation.isValid).toBe(false);
    expect(weakValidation.errors.length).toBeGreaterThan(0);
  });

  test('should generate secure password', () => {
    const password = PasswordUtils.generateSecurePassword(16);
    expect(password).toHaveLength(16);

    const validation = PasswordUtils.validatePassword(password);
    expect(validation.isValid).toBe(true);
  });
});
