# 🚀 React 維修管理系統 - 啟動和偵錯指南

## 📋 系統架構概述

您的系統包含兩套不同的應用：

### 1. 現代化 React/TypeScript 應用 (推薦使用)
- **前端**: React + TypeScript + Ant Design + Redux Toolkit
- **後端**: Node.js + Express + TypeScript + Prisma
- **位置**: `frontend/` 和 `backend/` 目錄

### 2. 純 HTML 單頁應用 (備用版本)
- **檔案**: `complete-system-gui.html`
- **特點**: 獨立運行，無需後端服務

## 🔧 React 系統啟動步驟

### 第一步：啟動後端服務

```bash
# 方法 1: 使用批次檔案
cd backend
雙擊 start-dev.bat

# 方法 2: 使用命令列
cd backend
npm install  # 首次運行
npm run dev
```

**預期結果**：
- 後端服務在 `http://localhost:5000` 啟動
- 控制台顯示 "Server is running on port 5000"
- API 文檔可在 `http://localhost:5000/api-docs` 查看

### 第二步：啟動前端服務

```bash
# 方法 1: 使用批次檔案
cd frontend
雙擊 start-dev.bat

# 方法 2: 使用命令列
cd frontend
npm install  # 首次運行
npm run dev
```

**預期結果**：
- 前端服務在 `http://localhost:3000` 啟動
- 瀏覽器自動開啟登入頁面
- 控制台顯示 "Local: http://localhost:3000"

## 🔍 登入問題診斷

### 常見問題 1：按鈕沒有反應

**可能原因**：
- 後端服務未啟動
- 網路連接問題
- Redux 狀態管理錯誤

**診斷步驟**：
1. **檢查後端服務**：
   ```
   開啟 http://localhost:5000/api/v1/health
   應該看到 {"status": "ok"}
   ```

2. **檢查瀏覽器控制台**：
   ```
   按 F12 → Console 標籤
   查看是否有紅色錯誤訊息
   ```

3. **檢查網路請求**：
   ```
   按 F12 → Network 標籤
   點擊登入按鈕
   查看是否有 API 請求
   ```

### 常見問題 2：登入失敗

**測試帳號**：
```
管理員：<EMAIL> / admin123
客服：<EMAIL> / service123
技師：<EMAIL> / tech123
查詢：<EMAIL> / viewer123
```

**診斷步驟**：
1. **確認帳號格式**：必須使用完整的電子郵件格式
2. **檢查密碼**：確保密碼正確
3. **查看錯誤訊息**：登入失敗時會顯示具體錯誤

### 常見問題 3：頁面空白或載入失敗

**可能原因**：
- 前端服務未啟動
- 依賴套件未安裝
- 瀏覽器快取問題

**解決方案**：
```bash
# 清除快取並重新安裝
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run dev
```

## 🛠️ 進階偵錯

### React DevTools 偵錯

1. **安裝 React DevTools**：
   - Chrome: 搜尋 "React Developer Tools" 擴充功能
   - Firefox: 搜尋 "React Developer Tools" 附加元件

2. **檢查組件狀態**：
   ```
   F12 → React 標籤 → Components
   找到 LoginForm 組件
   查看 props 和 state
   ```

3. **檢查 Redux 狀態**：
   ```
   F12 → Redux 標籤
   查看 auth slice 的狀態
   監控 action 的派發
   ```

### 後端 API 測試

使用 Postman 或 curl 測試登入 API：

```bash
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

**預期回應**：
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "name": "系統管理員",
      "role": "ADMIN"
    }
  }
}
```

## 🚨 緊急修復方案

### 方案 1：使用模擬數據模式

如果後端無法啟動，前端會自動切換到模擬數據模式：

1. **確認模擬模式**：
   ```
   瀏覽器控制台會顯示：
   "後端服務未啟動，使用模擬數據模式"
   "使用模擬登入數據"
   ```

2. **模擬模式登入**：
   - 使用相同的測試帳號
   - 登入流程完全相同
   - 數據保存在 LocalStorage

### 方案 2：使用 HTML 版本

如果 React 版本有問題，可以使用備用的 HTML 版本：

```
雙擊 complete-system-gui.html
使用帳號：admin / admin123
```

### 方案 3：重置系統

完全重置並重新啟動：

```bash
# 停止所有服務 (Ctrl+C)

# 清除前端
cd frontend
rm -rf node_modules
npm install

# 清除後端
cd ../backend
rm -rf node_modules
npm install

# 重新啟動
npm run dev  # 在 backend 目錄
npm run dev  # 在 frontend 目錄 (新終端)
```

## 📊 系統狀態檢查清單

### 後端服務檢查
- [ ] Node.js 已安裝 (版本 >= 18)
- [ ] 後端依賴已安裝 (`npm install`)
- [ ] 後端服務正在運行 (port 5000)
- [ ] API 健康檢查通過
- [ ] 資料庫連接正常

### 前端服務檢查
- [ ] Node.js 已安裝 (版本 >= 18)
- [ ] 前端依賴已安裝 (`npm install`)
- [ ] 前端服務正在運行 (port 3000)
- [ ] 頁面正常載入
- [ ] React DevTools 可用

### 登入功能檢查
- [ ] 登入表單正常顯示
- [ ] 輸入框可以正常輸入
- [ ] 登入按鈕有視覺反應
- [ ] 網路請求正常發送
- [ ] 錯誤訊息正確顯示
- [ ] 登入成功後正確跳轉

## 💡 最佳實踐

### 開發環境設置
1. **使用兩個終端**：一個運行後端，一個運行前端
2. **保持服務運行**：開發期間不要關閉服務
3. **定期檢查日誌**：注意控制台的錯誤和警告
4. **使用 React DevTools**：幫助偵錯組件問題

### 問題排除順序
1. **檢查服務狀態** → 確保前後端都在運行
2. **檢查網路連接** → 確保 API 請求正常
3. **檢查瀏覽器控制台** → 查看 JavaScript 錯誤
4. **檢查 Redux 狀態** → 確認狀態管理正常
5. **檢查組件 props** → 確認數據傳遞正確

## 📞 技術支援

### 收集偵錯信息
當遇到問題時，請收集以下信息：

1. **系統信息**：
   - 作業系統版本
   - Node.js 版本 (`node --version`)
   - npm 版本 (`npm --version`)

2. **服務狀態**：
   - 後端服務是否運行
   - 前端服務是否運行
   - 控制台錯誤訊息

3. **瀏覽器信息**：
   - 瀏覽器類型和版本
   - 控制台錯誤截圖
   - Network 標籤的請求記錄

### 常見錯誤代碼
- `ERR_NETWORK`: 後端服務未啟動
- `ECONNREFUSED`: 連接被拒絕，檢查端口
- `401 Unauthorized`: 認證失敗，檢查帳號密碼
- `500 Internal Server Error`: 後端錯誤，檢查後端日誌

---

**🎯 快速啟動命令**：
```bash
# 終端 1 (後端)
cd backend && npm run dev

# 終端 2 (前端)
cd frontend && npm run dev
```

然後開啟 `http://localhost:3000` 開始使用！
