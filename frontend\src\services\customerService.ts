import { api, ApiResponse, PaginatedResponse } from './api';

// 客戶相關類型定義
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerRequest {
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
  contactPerson?: string;
}

export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  company?: string;
  contactPerson?: string;
  isActive?: boolean;
}

export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  sortBy?: 'name' | 'email' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface CustomerStatistics {
  total: number;
  active: number;
  inactive: number;
  recentCustomers: number;
  topCustomers: Array<{
    id: number;
    name: string;
    repairCount: number;
  }>;
}

// 客戶服務
export const customerService = {
  // 獲取客戶列表
  getCustomers: async (params?: CustomerQueryParams): Promise<ApiResponse<PaginatedResponse<Customer>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/customers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<Customer>>(url);
  },

  // 獲取單個客戶
  getCustomer: async (id: number): Promise<ApiResponse<Customer>> => {
    return await api.get<Customer>(`/customers/${id}`);
  },

  // 創建客戶
  createCustomer: async (customerData: CreateCustomerRequest): Promise<ApiResponse<Customer>> => {
    return await api.post<Customer>('/customers', customerData);
  },

  // 更新客戶
  updateCustomer: async (id: number, customerData: UpdateCustomerRequest): Promise<ApiResponse<Customer>> => {
    return await api.put<Customer>(`/customers/${id}`, customerData);
  },

  // 刪除客戶
  deleteCustomer: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/customers/${id}`);
  },

  // 激活客戶
  activateCustomer: async (id: number): Promise<ApiResponse<Customer>> => {
    return await api.post<Customer>(`/customers/${id}/activate`);
  },

  // 停用客戶
  deactivateCustomer: async (id: number): Promise<ApiResponse<Customer>> => {
    return await api.post<Customer>(`/customers/${id}/deactivate`);
  },

  // 搜尋客戶
  searchCustomers: async (query: string): Promise<ApiResponse<Customer[]>> => {
    return await api.get<Customer[]>(`/customers/search?q=${encodeURIComponent(query)}`);
  },

  // 獲取客戶統計
  getCustomerStatistics: async (): Promise<ApiResponse<CustomerStatistics>> => {
    return await api.get<CustomerStatistics>('/customers/statistics');
  },

  // 檢查客戶 email 是否可用
  checkEmailAvailability: async (email: string, excludeId?: number): Promise<ApiResponse<{ available: boolean }>> => {
    const params = new URLSearchParams({ email });
    if (excludeId) {
      params.append('excludeId', excludeId.toString());
    }
    return await api.get<{ available: boolean }>(`/customers/check-email?${params.toString()}`);
  },

  // 獲取客戶的維修記錄
  getCustomerRepairHistory: async (id: number, params?: { page?: number; limit?: number }): Promise<ApiResponse<any>> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    
    const url = `/customers/${id}/repair-history${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get(url);
  },

  // 批量操作客戶
  batchUpdateCustomers: async (ids: number[], action: 'activate' | 'deactivate' | 'delete'): Promise<ApiResponse<{ success: number; failed: number }>> => {
    return await api.post<{ success: number; failed: number }>('/customers/batch', {
      ids,
      action,
    });
  },
};

export default customerService;
