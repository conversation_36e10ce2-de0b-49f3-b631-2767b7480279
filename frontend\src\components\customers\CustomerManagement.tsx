import React, { useState } from 'react';
import { Typography, message } from 'antd';
import CustomerList from './CustomerList';
import CustomerForm from './CustomerForm';
import { Customer, CreateCustomerRequest, UpdateCustomerRequest } from '../../services/customerService';

const { Title } = Typography;

const CustomerManagement: React.FC = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setIsFormVisible(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setIsFormVisible(true);
  };

  const handleFormCancel = () => {
    setIsFormVisible(false);
    setEditingCustomer(null);
  };

  const handleFormSubmit = async (data: CreateCustomerRequest | UpdateCustomerRequest) => {
    setFormLoading(true);
    try {
      if (editingCustomer) {
        // 更新客戶
        // await customerService.updateCustomer(editingCustomer.id, data as UpdateCustomerRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('客戶資料更新成功');
      } else {
        // 新增客戶
        // await customerService.createCustomer(data as CreateCustomerRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('客戶新增成功');
      }
      
      setIsFormVisible(false);
      setEditingCustomer(null);
      // 這裡可以觸發客戶列表重新載入
    } catch (error: any) {
      message.error(error.message || `${editingCustomer ? '更新' : '新增'}客戶失敗`);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        客戶管理
      </Title>

      <CustomerList
        onAdd={handleAddCustomer}
        onEdit={handleEditCustomer}
      />

      <CustomerForm
        visible={isFormVisible}
        customer={editingCustomer}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default CustomerManagement;
