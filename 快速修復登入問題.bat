@echo off
chcp 65001 >nul
title IACT MIO維保管理系統 - 登入問題快速修復工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 登入問題快速修復工具                    ║
echo ║                  IACT MIO維保管理系統                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MAIN_MENU
echo 請選擇修復方案：
echo.
echo [1] 🔍 開啟偵錯工具
echo [2] 🚀 開啟主系統
echo [3] 🧹 清除瀏覽器快取
echo [4] 🔧 重新啟動系統
echo [5] 📋 查看修復指南
echo [6] 🆘 緊急修復模式
echo [0] ❌ 退出
echo.
set /p choice="請輸入選項 (0-6): "

if "%choice%"=="1" goto DEBUG_TOOL
if "%choice%"=="2" goto OPEN_SYSTEM
if "%choice%"=="3" goto CLEAR_CACHE
if "%choice%"=="4" goto RESTART_SYSTEM
if "%choice%"=="5" goto VIEW_GUIDE
if "%choice%"=="6" goto EMERGENCY_MODE
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:DEBUG_TOOL
echo.
echo 🔍 正在開啟登入偵錯工具...
if exist "登入偵錯工具.html" (
    start "" "登入偵錯工具.html"
    echo ✅ 偵錯工具已開啟
    echo.
    echo 💡 使用說明：
    echo    1. 點擊「執行完整診斷」按鈕
    echo    2. 查看所有檢查項目的狀態
    echo    3. 使用測試登入功能驗證
    echo    4. 根據結果採取相應修復措施
) else (
    echo ❌ 找不到偵錯工具檔案
    echo 請確認 "登入偵錯工具.html" 檔案存在
)
echo.
pause
goto MAIN_MENU

:OPEN_SYSTEM
echo.
echo 🚀 正在開啟主系統...
if exist "complete-system-gui.html" (
    start "" "complete-system-gui.html"
    echo ✅ 主系統已開啟
    echo.
    echo 💡 測試步驟：
    echo    1. 嘗試使用 admin / admin123 登入
    echo    2. 如果按鈕沒反應，按 F12 開啟控制台
    echo    3. 在控制台輸入：debugLoginSystem()
    echo    4. 查看詳細的偵錯信息
) else (
    echo ❌ 找不到主系統檔案
    echo 請確認 "complete-system-gui.html" 檔案存在
)
echo.
pause
goto MAIN_MENU

:CLEAR_CACHE
echo.
echo 🧹 清除瀏覽器快取...
echo.
echo 請手動執行以下步驟：
echo.
echo Chrome 瀏覽器：
echo   1. 按 Ctrl + Shift + Delete
echo   2. 選擇「所有時間」
echo   3. 勾選「快取圖片和檔案」
echo   4. 點擊「清除資料」
echo.
echo Firefox 瀏覽器：
echo   1. 按 Ctrl + Shift + Delete
echo   2. 選擇「全部」
echo   3. 勾選「快取」
echo   4. 點擊「立即清除」
echo.
echo Edge 瀏覽器：
echo   1. 按 Ctrl + Shift + Delete
echo   2. 選擇「所有時間」
echo   3. 勾選「快取的圖片和檔案」
echo   4. 點擊「立即清除」
echo.
echo ✅ 清除快取後請重新測試登入功能
echo.
pause
goto MAIN_MENU

:RESTART_SYSTEM
echo.
echo 🔧 重新啟動系統...
echo.
echo 正在關閉可能的瀏覽器程序...
taskkill /f /im chrome.exe >nul 2>&1
taskkill /f /im firefox.exe >nul 2>&1
taskkill /f /im msedge.exe >nul 2>&1
echo.
echo 等待 3 秒...
timeout /t 3 /nobreak >nul
echo.
echo 🚀 重新啟動主系統...
if exist "complete-system-gui.html" (
    start "" "complete-system-gui.html"
    echo ✅ 系統已重新啟動
) else (
    echo ❌ 找不到主系統檔案
)
echo.
pause
goto MAIN_MENU

:VIEW_GUIDE
echo.
echo 📋 正在開啟修復指南...
if exist "登入按鈕修復完整指南.md" (
    start "" "登入按鈕修復完整指南.md"
    echo ✅ 修復指南已開啟
) else (
    echo ❌ 找不到修復指南檔案
    echo.
    echo 📋 快速修復步驟：
    echo.
    echo 1. 🔍 檢查瀏覽器控制台錯誤
    echo    - 按 F12 開啟開發者工具
    echo    - 查看 Console 標籤中的錯誤訊息
    echo.
    echo 2. 🧹 清除瀏覽器快取
    echo    - 按 Ctrl + Shift + Delete
    echo    - 清除所有快取資料
    echo.
    echo 3. 🔄 嘗試無痕模式
    echo    - 按 Ctrl + Shift + N (Chrome)
    echo    - 在無痕視窗中測試登入
    echo.
    echo 4. 🛠️ 使用偵錯工具
    echo    - 開啟「登入偵錯工具.html」
    echo    - 執行完整診斷
    echo.
    echo 5. 🆘 緊急修復
    echo    - 在控制台輸入：forceLogin()
    echo    - 強制進入系統
)
echo.
pause
goto MAIN_MENU

:EMERGENCY_MODE
echo.
echo 🆘 緊急修復模式
echo.
echo ⚠️  警告：此模式將嘗試強制修復系統
echo    可能會清除部分設定，請確認後繼續
echo.
set /p confirm="確定要繼續嗎？(Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo 🔧 執行緊急修復...
echo.

echo [1/4] 備份現有設定...
if exist "backup" (
    rmdir /s /q backup >nul 2>&1
)
mkdir backup >nul 2>&1
copy "complete-system-gui.html" "backup\" >nul 2>&1
echo ✅ 設定已備份

echo.
echo [2/4] 清除瀏覽器數據...
echo 正在清除 LocalStorage 和 SessionStorage...
echo （需要在瀏覽器中手動確認）

echo.
echo [3/4] 重置系統檔案...
echo 檢查檔案完整性...
if exist "complete-system-gui.html" (
    echo ✅ 主系統檔案存在
) else (
    echo ❌ 主系統檔案遺失！
    echo 請重新下載系統檔案
    pause
    goto MAIN_MENU
)

echo.
echo [4/4] 啟動修復後的系統...
start "" "complete-system-gui.html"
echo.
echo ✅ 緊急修復完成！
echo.
echo 📋 修復後測試步驟：
echo    1. 在開啟的系統中按 F12
echo    2. 在控制台輸入：debugLoginSystem()
echo    3. 確認所有檢查項目正常
echo    4. 嘗試使用 admin / admin123 登入
echo.
pause
goto MAIN_MENU

:INVALID_CHOICE
echo.
echo ❌ 無效的選項，請重新選擇
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo 👋 感謝使用登入問題修復工具
echo.
echo 💡 如果問題仍未解決，請：
echo    1. 使用偵錯工具收集詳細信息
echo    2. 查看修復指南中的進階方案
echo    3. 嘗試不同的瀏覽器
echo.
echo 🎯 常用測試帳號：
echo    管理員：admin / admin123
echo    客服：service / service123
echo    技師：tech / tech123
echo    查詢：viewer / viewer123
echo.
pause
exit
