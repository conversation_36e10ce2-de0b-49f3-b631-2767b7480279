import { api, ApiResponse } from './api';

// 統計數據相關類型定義
export interface DashboardStatistics {
  totalRepairs: number;
  activeRepairs: number;
  completedRepairs: number;
  totalCustomers: number;
  totalProducts: number;
  totalParts: number;
  lowStockParts: number;
  totalRevenue: number;
  averageRepairTime: number;
  customerSatisfaction: number;
}

export interface RepairTrendData {
  date: string;
  newRepairs: number;
  completedRepairs: number;
  revenue: number;
}

export interface RepairStatusDistribution {
  status: string;
  statusName: string;
  count: number;
  percentage: number;
  color: string;
}

export interface RepairPriorityDistribution {
  priority: string;
  priorityName: string;
  count: number;
  percentage: number;
  color: string;
}

export interface TechnicianPerformance {
  technicianId: number;
  technicianName: string;
  totalRepairs: number;
  completedRepairs: number;
  averageRepairTime: number;
  customerRating: number;
  revenue: number;
  efficiency: number;
}

export interface CustomerAnalytics {
  customerId: number;
  customerName: string;
  totalRepairs: number;
  totalSpent: number;
  averageRepairCost: number;
  lastRepairDate: string;
  loyaltyScore: number;
}

export interface ProductAnalytics {
  productId: number;
  productName: string;
  brand: string;
  model: string;
  repairCount: number;
  averageRepairCost: number;
  commonIssues: string[];
  failureRate: number;
}

export interface PartUsageAnalytics {
  partId: number;
  partName: string;
  partNumber: string;
  usageCount: number;
  totalCost: number;
  averageCost: number;
  stockLevel: string;
  supplier: string;
}

export interface RevenueAnalytics {
  period: string;
  totalRevenue: number;
  repairRevenue: number;
  partRevenue: number;
  laborRevenue: number;
  profit: number;
  profitMargin: number;
}

export interface TimeAnalytics {
  averageInspectionTime: number;
  averageRepairTime: number;
  averageWaitingTime: number;
  averageTotalTime: number;
  onTimeDeliveryRate: number;
  overdueRepairs: number;
}

export interface QualityMetrics {
  firstTimeFixRate: number;
  reworkRate: number;
  customerSatisfactionScore: number;
  warrantyClaimRate: number;
  defectRate: number;
  returnRate: number;
}

export interface StatisticsQueryParams {
  dateFrom?: string;
  dateTo?: string;
  customerId?: number;
  productId?: number;
  technicianId?: number;
  status?: string;
  priority?: string;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv';
  reportType: string;
  dateRange: {
    from: string;
    to: string;
  };
  filters?: Record<string, any>;
  includeCharts?: boolean;
}

// 統計服務
export const statisticsService = {
  // 獲取儀表板統計
  getDashboardStatistics: async (): Promise<ApiResponse<DashboardStatistics>> => {
    return await api.get<DashboardStatistics>('/statistics/dashboard');
  },

  // 獲取維修趨勢數據
  getRepairTrends: async (params?: StatisticsQueryParams): Promise<ApiResponse<RepairTrendData[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/repair-trends${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<RepairTrendData[]>(url);
  },

  // 獲取維修狀態分布
  getRepairStatusDistribution: async (params?: StatisticsQueryParams): Promise<ApiResponse<RepairStatusDistribution[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/repair-status${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<RepairStatusDistribution[]>(url);
  },

  // 獲取維修優先級分布
  getRepairPriorityDistribution: async (params?: StatisticsQueryParams): Promise<ApiResponse<RepairPriorityDistribution[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/repair-priority${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<RepairPriorityDistribution[]>(url);
  },

  // 獲取技師績效數據
  getTechnicianPerformance: async (params?: StatisticsQueryParams): Promise<ApiResponse<TechnicianPerformance[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/technician-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<TechnicianPerformance[]>(url);
  },

  // 獲取客戶分析數據
  getCustomerAnalytics: async (params?: StatisticsQueryParams): Promise<ApiResponse<CustomerAnalytics[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/customer-analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<CustomerAnalytics[]>(url);
  },

  // 獲取產品分析數據
  getProductAnalytics: async (params?: StatisticsQueryParams): Promise<ApiResponse<ProductAnalytics[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/product-analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<ProductAnalytics[]>(url);
  },

  // 獲取零件使用分析
  getPartUsageAnalytics: async (params?: StatisticsQueryParams): Promise<ApiResponse<PartUsageAnalytics[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/part-usage${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PartUsageAnalytics[]>(url);
  },

  // 獲取營收分析
  getRevenueAnalytics: async (params?: StatisticsQueryParams): Promise<ApiResponse<RevenueAnalytics[]>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/revenue${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<RevenueAnalytics[]>(url);
  },

  // 獲取時間分析
  getTimeAnalytics: async (params?: StatisticsQueryParams): Promise<ApiResponse<TimeAnalytics>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/time-analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<TimeAnalytics>(url);
  },

  // 獲取品質指標
  getQualityMetrics: async (params?: StatisticsQueryParams): Promise<ApiResponse<QualityMetrics>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/statistics/quality-metrics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<QualityMetrics>(url);
  },

  // 匯出報表
  exportReport: async (options: ExportOptions): Promise<ApiResponse<{ downloadUrl: string }>> => {
    return await api.post<{ downloadUrl: string }>('/statistics/export', options);
  },

  // 獲取自定義報表
  getCustomReport: async (reportConfig: any): Promise<ApiResponse<any>> => {
    return await api.post<any>('/statistics/custom-report', reportConfig);
  },
};

export default statisticsService;
