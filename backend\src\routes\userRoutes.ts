import { Router } from 'express';
import UserController from '../controllers/userController';
import { authenticate, authorize, Role, authorizeOwnerOrAdmin } from '../middleware/auth';
import {
  createUserValidation,
  updateUserValidation,
  userListValidation,
  userIdValidation,
  userSearchValidation,
  batchOperationValidation,
  changeRoleValidation,
  updateProfileValidation,
  checkUsernameValidation,
  createUserWithPermissionValidation,
  batchOperationWithPermissionValidation,
} from '../validators/userValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateUserRequest:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - fullName
 *         - role
 *       properties:
 *         username:
 *           type: string
 *           minLength: 3
 *           maxLength: 50
 *           pattern: '^[a-zA-Z0-9_]+$'
 *           description: 用戶名
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 100
 *           description: 電子郵件
 *         fullName:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 姓名
 *         role:
 *           type: string
 *           enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *           description: 角色
 *         password:
 *           type: string
 *           minLength: 8
 *           maxLength: 128
 *           description: 密碼（可選，不提供則自動生成）
 *         isActive:
 *           type: boolean
 *           default: true
 *           description: 是否活躍
 *     
 *     UpdateUserRequest:
 *       type: object
 *       properties:
 *         fullName:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 姓名
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 100
 *           description: 電子郵件
 *         role:
 *           type: string
 *           enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *           description: 角色
 *         isActive:
 *           type: boolean
 *           description: 是否活躍
 *     
 *     UserListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             users:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *             pagination:
 *               $ref: '#/components/schemas/Pagination'
 *             filters:
 *               type: object
 *             sorting:
 *               type: object
 *     
 *     BatchUserOperation:
 *       type: object
 *       required:
 *         - userIds
 *         - operation
 *       properties:
 *         userIds:
 *           type: array
 *           items:
 *             type: string
 *           minItems: 1
 *           maxItems: 100
 *           description: 用戶ID列表
 *         operation:
 *           type: string
 *           enum: [activate, deactivate, delete, updateRole]
 *           description: 操作類型
 *         data:
 *           type: object
 *           properties:
 *             role:
 *               type: string
 *               enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *               description: 新角色（updateRole操作時必需）
 *             isActive:
 *               type: boolean
 *               description: 活躍狀態
 *     
 *     Pagination:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *         limit:
 *           type: integer
 *         total:
 *           type: integer
 *         totalPages:
 *           type: integer
 *         hasNext:
 *           type: boolean
 *         hasPrev:
 *           type: boolean
 */

/**
 * @swagger
 * /api/v1/users:
 *   get:
 *     summary: 獲取用戶列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *         description: 角色篩選
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [username, email, fullName, role, createdAt, updatedAt]
 *           default: createdAt
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取用戶列表成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserListResponse'
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.ADMIN, Role.CUSTOMER_SERVICE), userListValidation, UserController.getUserList);

/**
 * @swagger
 * /api/v1/users:
 *   post:
 *     summary: 創建用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *     responses:
 *       201:
 *         description: 用戶創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       409:
 *         description: 用戶名或電子郵件已存在
 */
router.post('/', authenticate, authorize(Role.ADMIN), createUserWithPermissionValidation, UserController.createUser);

/**
 * @swagger
 * /api/v1/users/search:
 *   get:
 *     summary: 搜尋用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: 結果數量限制
 *     responses:
 *       200:
 *         description: 搜尋成功
 *       400:
 *         description: 搜尋參數錯誤
 *       401:
 *         description: 未認證
 */
router.get('/search', authenticate, userSearchValidation, UserController.searchUsers);

/**
 * @swagger
 * /api/v1/users/statistics:
 *   get:
 *     summary: 獲取用戶統計
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.ADMIN, Role.CUSTOMER_SERVICE), UserController.getUserStatistics);

/**
 * @swagger
 * /api/v1/users/roles:
 *   get:
 *     summary: 獲取用戶角色列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取角色列表成功
 *       401:
 *         description: 未認證
 */
router.get('/roles', authenticate, UserController.getUserRoles);

/**
 * @swagger
 * /api/v1/users/batch:
 *   post:
 *     summary: 批量操作用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchUserOperation'
 *     responses:
 *       200:
 *         description: 批量操作完成
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/batch', authenticate, authorize(Role.ADMIN), batchOperationWithPermissionValidation, UserController.batchOperation);

/**
 * @swagger
 * /api/v1/users/profile:
 *   put:
 *     summary: 更新個人資料
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fullName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: 個人資料更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 */
router.put('/profile', authenticate, updateProfileValidation, UserController.updateProfile);

/**
 * @swagger
 * /api/v1/users/check-username/{username}:
 *   get:
 *     summary: 檢查用戶名是否可用
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: username
 *         required: true
 *         schema:
 *           type: string
 *         description: 要檢查的用戶名
 *       - in: query
 *         name: excludeId
 *         schema:
 *           type: string
 *         description: 排除的用戶ID
 *     responses:
 *       200:
 *         description: 檢查完成
 *       400:
 *         description: 用戶名格式錯誤
 *       401:
 *         description: 未認證
 */
router.get('/check-username/:username', authenticate, checkUsernameValidation, UserController.checkUsernameAvailability);

/**
 * @swagger
 * /api/v1/users/{id}:
 *   get:
 *     summary: 根據ID獲取用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     responses:
 *       200:
 *         description: 獲取用戶成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.get('/:id', authenticate, authorizeOwnerOrAdmin((req) => req.params.id), userIdValidation, UserController.getUserById);

/**
 * @swagger
 * /api/v1/users/{id}:
 *   put:
 *     summary: 更新用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserRequest'
 *     responses:
 *       200:
 *         description: 用戶更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.put('/:id', authenticate, authorize(Role.ADMIN), updateUserValidation, UserController.updateUser);

/**
 * @swagger
 * /api/v1/users/{id}:
 *   delete:
 *     summary: 刪除用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 用戶刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), userIdValidation, UserController.deleteUser);

/**
 * @swagger
 * /api/v1/users/{id}/activate:
 *   post:
 *     summary: 激活用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     responses:
 *       200:
 *         description: 用戶激活成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.post('/:id/activate', authenticate, authorize(Role.ADMIN), userIdValidation, UserController.activateUser);

/**
 * @swagger
 * /api/v1/users/{id}/deactivate:
 *   post:
 *     summary: 停用用戶
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     responses:
 *       200:
 *         description: 用戶停用成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.post('/:id/deactivate', authenticate, authorize(Role.ADMIN), userIdValidation, UserController.deactivateUser);

/**
 * @swagger
 * /api/v1/users/{id}/role:
 *   put:
 *     summary: 更改用戶角色
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *     responses:
 *       200:
 *         description: 用戶角色更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
router.put('/:id/role', authenticate, authorize(Role.ADMIN), changeRoleValidation, UserController.changeUserRole);

export default router;
