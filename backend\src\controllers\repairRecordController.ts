import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { RepairRecordService } from '../services/repairRecordService';
import { RepairRecordRepository } from '../repositories/repairRecordRepository';
import { PartRepository } from '../repositories/partRepository';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { 
  CreateRepairRecordRequest, 
  UpdateRepairRecordRequest, 
  RepairRecordQueryParams,
  UpdateRepairStatusRequest,
  UsePartRequest,
  BatchUsePartsRequest 
} from '../types/repairRecord';

// 初始化服務
const repairRecordRepository = new RepairRecordRepository(prisma);
const partRepository = new PartRepository(prisma);
const repairRecordService = new RepairRecordService(repairRecordRepository, partRepository);

export class RepairRecordController {
  // === 維修記錄相關方法 ===

  // 獲取維修記錄列表
  static async getRepairRecordList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: RepairRecordQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        customerId: req.query.customerId as string,
        productId: req.query.productId as string,
        assignedTechnicianId: req.query.assignedTechnicianId as string,
        status: req.query.status as any,
        priority: req.query.priority as any,
        warrantyStatus: req.query.warrantyStatus as any,
        dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
        dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
        costMin: req.query.costMin ? parseFloat(req.query.costMin as string) : undefined,
        costMax: req.query.costMax ? parseFloat(req.query.costMax as string) : undefined,
        overdue: req.query.overdue === 'true',
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await repairRecordService.getRepairRecordList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取維修記錄
  static async getRepairRecordById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeDetails = false } = req.query;

      let repairRecord;
      if (includeDetails === 'true') {
        repairRecord = await repairRecordService.getRepairRecordDetailById(id);
      } else {
        repairRecord = await repairRecordService.getRepairRecordById(id);
      }

      if (!repairRecord) {
        throw createError('維修記錄不存在', 404);
      }

      res.json({
        success: true,
        data: { repairRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建維修記錄
  static async createRepairRecord(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const recordData: CreateRepairRecordRequest = req.body;
      const createdBy = req.user?.userId || 'UNKNOWN';

      const newRecord = await repairRecordService.createRepairRecord(recordData, createdBy);

      res.status(201).json({
        success: true,
        message: '維修記錄創建成功',
        data: { repairRecord: newRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新維修記錄
  static async updateRepairRecord(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const recordData: UpdateRepairRecordRequest = req.body;
      const updatedBy = req.user?.userId || 'UNKNOWN';

      const updatedRecord = await repairRecordService.updateRepairRecord(id, recordData, updatedBy);

      res.json({
        success: true,
        message: '維修記錄更新成功',
        data: { repairRecord: updatedRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除維修記錄
  static async deleteRepairRecord(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId || 'UNKNOWN';

      await repairRecordService.deleteRepairRecord(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '維修記錄已永久刪除' : '維修記錄已取消',
      });
    } catch (error) {
      next(error);
    }
  }

  // 搜尋維修記錄
  static async searchRepairRecords(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw createError('搜尋關鍵字不能為空', 400);
      }

      const results = await repairRecordService.searchRepairRecords(query, parseInt(limit as string));

      res.json({
        success: true,
        data: { 
          query,
          results,
          count: results.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取維修記錄統計
  static async getRepairRecordStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await repairRecordService.getRepairRecordStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新維修狀態
  static async updateRepairStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const statusData: UpdateRepairStatusRequest = req.body;
      const updatedBy = req.user?.userId || 'UNKNOWN';

      const updatedRecord = await repairRecordService.updateRepairStatus(id, statusData, updatedBy);

      res.json({
        success: true,
        message: '維修狀態更新成功',
        data: { repairRecord: updatedRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // 指派技師
  static async assignTechnician(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { technicianId } = req.body;
      const assignedBy = req.user?.userId || 'UNKNOWN';

      if (!technicianId) {
        throw createError('技師ID不能為空', 400);
      }

      const updatedRecord = await repairRecordService.assignTechnician(id, technicianId, assignedBy);

      res.json({
        success: true,
        message: '技師指派成功',
        data: { repairRecord: updatedRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據維修單號查找維修記錄
  static async findRepairRecordByRepairNumber(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { repairNumber } = req.params;

      if (!repairNumber) {
        throw createError('維修單號不能為空', 400);
      }

      const repairRecord = await repairRecordService.findRepairRecordByRepairNumber(repairNumber);

      if (!repairRecord) {
        throw createError('維修記錄不存在', 404);
      }

      res.json({
        success: true,
        data: { repairRecord },
      });
    } catch (error) {
      next(error);
    }
  }

  // === 零件使用相關方法 ===

  // 使用零件
  static async usePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const partRequest: UsePartRequest = req.body;
      const usedBy = req.user?.userId || 'UNKNOWN';

      await repairRecordService.usePart(id, partRequest, usedBy);

      res.json({
        success: true,
        message: '零件使用成功',
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量使用零件
  static async batchUseParts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const batchRequest: BatchUsePartsRequest = req.body;
      const usedBy = req.user?.userId || 'UNKNOWN';

      await repairRecordService.batchUseParts(id, batchRequest, usedBy);

      res.json({
        success: true,
        message: '批量零件使用成功',
      });
    } catch (error) {
      next(error);
    }
  }

  // === 我的維修記錄相關方法 ===

  // 獲取我指派的維修記錄
  static async getMyAssignedRepairRecords(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const technicianId = req.user?.userId;
      if (!technicianId) {
        throw createError('用戶資訊不完整', 400);
      }

      const queryParams: RepairRecordQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        assignedTechnicianId: technicianId,
        status: req.query.status as any,
        priority: req.query.priority as any,
        sortBy: req.query.sortBy as any || 'receivedDate',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await repairRecordService.getRepairRecordList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取我創建的維修記錄
  static async getMyCreatedRepairRecords(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const createdBy = req.user?.userId;
      if (!createdBy) {
        throw createError('用戶資訊不完整', 400);
      }

      // 這裡需要在查詢參數中添加createdBy篩選
      // 暫時使用搜尋功能
      const queryParams: RepairRecordQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await repairRecordService.getRepairRecordList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取逾期維修記錄
  static async getOverdueRepairRecords(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const queryParams: RepairRecordQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        overdue: true,
        assignedTechnicianId: req.query.assignedTechnicianId as string,
        sortBy: 'estimatedCompletionDate',
        sortOrder: 'asc',
      };

      const result = await repairRecordService.getRepairRecordList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
}

export default RepairRecordController;
