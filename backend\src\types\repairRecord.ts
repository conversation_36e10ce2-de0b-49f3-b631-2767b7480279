// 維修記錄基本資訊介面
export interface RepairRecordInfo {
  id: string;
  repairNumber: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  productId: string;
  productName: string;
  productModel: string;
  productBrand: string;
  serialNumber?: string;
  issueDescription: string;
  symptoms: string[];
  priority: RepairPriority;
  status: RepairStatus;
  assignedTechnicianId?: string;
  assignedTechnicianName?: string;
  estimatedCost?: number;
  actualCost?: number;
  estimatedCompletionDate?: Date;
  actualCompletionDate?: Date;
  warrantyStatus: WarrantyStatus;
  warrantyExpiryDate?: Date;
  receivedDate: Date;
  startedDate?: Date;
  completedDate?: Date;
  deliveredDate?: Date;
  notes?: string;
  internalNotes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// 創建維修記錄請求介面
export interface CreateRepairRecordRequest {
  customerId: string;
  productId: string;
  serialNumber?: string;
  issueDescription: string;
  symptoms?: string[];
  priority?: RepairPriority;
  assignedTechnicianId?: string;
  estimatedCost?: number;
  estimatedCompletionDate?: Date;
  warrantyStatus?: WarrantyStatus;
  warrantyExpiryDate?: Date;
  receivedDate?: Date;
  notes?: string;
  internalNotes?: string;
}

// 更新維修記錄請求介面
export interface UpdateRepairRecordRequest {
  customerId?: string;
  productId?: string;
  serialNumber?: string;
  issueDescription?: string;
  symptoms?: string[];
  priority?: RepairPriority;
  status?: RepairStatus;
  assignedTechnicianId?: string;
  estimatedCost?: number;
  actualCost?: number;
  estimatedCompletionDate?: Date;
  actualCompletionDate?: Date;
  warrantyStatus?: WarrantyStatus;
  warrantyExpiryDate?: Date;
  startedDate?: Date;
  completedDate?: Date;
  deliveredDate?: Date;
  notes?: string;
  internalNotes?: string;
}

// 維修記錄查詢參數介面
export interface RepairRecordQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  customerId?: string;
  productId?: string;
  assignedTechnicianId?: string;
  status?: RepairStatus;
  priority?: RepairPriority;
  warrantyStatus?: WarrantyStatus;
  dateFrom?: Date;
  dateTo?: Date;
  costMin?: number;
  costMax?: number;
  overdue?: boolean;
  sortBy?: 'repairNumber' | 'receivedDate' | 'priority' | 'status' | 'estimatedCompletionDate' | 'actualCost' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 維修記錄列表響應介面
export interface RepairRecordListResponse {
  repairRecords: RepairRecordInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    customerId?: string;
    productId?: string;
    assignedTechnicianId?: string;
    status?: RepairStatus;
    priority?: RepairPriority;
    warrantyStatus?: WarrantyStatus;
    dateRange?: { from?: Date; to?: Date };
    costRange?: { min?: number; max?: number };
    overdue?: boolean;
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 維修記錄統計介面
export interface RepairRecordStatistics {
  total: number;
  byStatus: Array<{
    status: RepairStatus;
    count: number;
    percentage: number;
  }>;
  byPriority: Array<{
    priority: RepairPriority;
    count: number;
    percentage: number;
  }>;
  byTechnician: Array<{
    technicianId: string;
    technicianName: string;
    count: number;
    averageCompletionTime: number;
  }>;
  byWarrantyStatus: Array<{
    warrantyStatus: WarrantyStatus;
    count: number;
    percentage: number;
  }>;
  averageRepairTime: number;
  averageCost: number;
  totalRevenue: number;
  overdueCount: number;
  completionRate: number;
  customerSatisfactionRate?: number;
  recentTrends: {
    thisWeek: number;
    lastWeek: number;
    thisMonth: number;
    lastMonth: number;
  };
}

// 維修記錄詳細資訊介面
export interface RepairRecordDetailInfo extends RepairRecordInfo {
  customer: {
    id: string;
    name: string;
    phone?: string;
    email?: string;
    address?: string;
  };
  product: {
    id: string;
    name: string;
    model: string;
    brand: string;
    category: string;
    warrantyPeriod?: number;
  };
  assignedTechnician?: {
    id: string;
    fullName: string;
    email: string;
    specialties?: string[];
  };
  partsUsed: RepairPartUsage[];
  statusHistory: RepairStatusHistory[];
  timelineEvents: RepairTimelineEvent[];
  attachments: RepairAttachment[];
  qualityChecks: QualityCheck[];
  customerFeedback?: CustomerFeedback;
  totalPartsValue: number;
  totalLaborCost: number;
  profitMargin: number;
}

// 維修零件使用記錄介面
export interface RepairPartUsage {
  id: string;
  partId: string;
  partName: string;
  partNumber: string;
  quantity: number;
  unitPrice?: number;
  totalCost?: number;
  usedBy: string;
  usedByName: string;
  usedAt: Date;
  notes?: string;
  isWarrantyPart: boolean;
}

// 維修狀態歷史記錄介面
export interface RepairStatusHistory {
  id: string;
  fromStatus?: RepairStatus;
  toStatus: RepairStatus;
  changedBy: string;
  changedByName: string;
  changedAt: Date;
  reason?: string;
  notes?: string;
}

// 維修時間軸事件介面
export interface RepairTimelineEvent {
  id: string;
  eventType: TimelineEventType;
  title: string;
  description?: string;
  performedBy: string;
  performedByName: string;
  performedAt: Date;
  metadata?: Record<string, any>;
}

// 維修附件介面
export interface RepairAttachment {
  id: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  fileType: AttachmentType;
  description?: string;
  uploadedBy: string;
  uploadedByName: string;
  uploadedAt: Date;
  url: string;
}

// 品質檢查記錄介面
export interface QualityCheck {
  id: string;
  checkType: QualityCheckType;
  checkItems: QualityCheckItem[];
  overallResult: QualityResult;
  checkedBy: string;
  checkedByName: string;
  checkedAt: Date;
  notes?: string;
}

// 品質檢查項目介面
export interface QualityCheckItem {
  id: string;
  itemName: string;
  description?: string;
  result: QualityResult;
  notes?: string;
}

// 客戶回饋介面
export interface CustomerFeedback {
  id: string;
  satisfactionRating: number; // 1-5
  serviceRating: number; // 1-5
  qualityRating: number; // 1-5
  speedRating: number; // 1-5
  comments?: string;
  wouldRecommend: boolean;
  submittedAt: Date;
}

// 維修狀態更新請求介面
export interface UpdateRepairStatusRequest {
  status: RepairStatus;
  reason?: string;
  notes?: string;
  estimatedCompletionDate?: Date;
  actualCompletionDate?: Date;
}

// 零件使用請求介面
export interface UsePartRequest {
  partId: string;
  quantity: number;
  notes?: string;
  isWarrantyPart?: boolean;
}

// 批量零件使用請求介面
export interface BatchUsePartsRequest {
  parts: UsePartRequest[];
  notes?: string;
}

// 維修記錄搜尋結果介面
export interface RepairRecordSearchResult {
  id: string;
  repairNumber: string;
  customerName: string;
  productModel: string;
  status: RepairStatus;
  priority: RepairPriority;
  receivedDate: Date;
  estimatedCompletionDate?: Date;
  assignedTechnicianName?: string;
  relevanceScore: number;
}

// 維修記錄驗證結果介面
export interface RepairRecordValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 維修狀態枚舉
export enum RepairStatus {
  RECEIVED = 'RECEIVED',           // 已接收
  DIAGNOSED = 'DIAGNOSED',         // 已診斷
  WAITING_PARTS = 'WAITING_PARTS', // 等待零件
  IN_PROGRESS = 'IN_PROGRESS',     // 維修中
  TESTING = 'TESTING',             // 測試中
  COMPLETED = 'COMPLETED',         // 已完成
  QUALITY_CHECK = 'QUALITY_CHECK', // 品質檢查
  READY_PICKUP = 'READY_PICKUP',   // 待取件
  DELIVERED = 'DELIVERED',         // 已交付
  CANCELLED = 'CANCELLED',         // 已取消
  ON_HOLD = 'ON_HOLD',            // 暫停
}

// 維修優先級枚舉
export enum RepairPriority {
  LOW = 'LOW',           // 低
  NORMAL = 'NORMAL',     // 一般
  HIGH = 'HIGH',         // 高
  URGENT = 'URGENT',     // 緊急
  CRITICAL = 'CRITICAL', // 危急
}

// 保固狀態枚舉
export enum WarrantyStatus {
  IN_WARRANTY = 'IN_WARRANTY',       // 保固內
  OUT_OF_WARRANTY = 'OUT_OF_WARRANTY', // 保固外
  EXTENDED_WARRANTY = 'EXTENDED_WARRANTY', // 延長保固
  UNKNOWN = 'UNKNOWN',               // 未知
}

// 時間軸事件類型枚舉
export enum TimelineEventType {
  CREATED = 'CREATED',               // 建立
  STATUS_CHANGED = 'STATUS_CHANGED', // 狀態變更
  ASSIGNED = 'ASSIGNED',             // 指派
  PART_USED = 'PART_USED',          // 使用零件
  NOTE_ADDED = 'NOTE_ADDED',        // 新增備註
  ATTACHMENT_ADDED = 'ATTACHMENT_ADDED', // 新增附件
  QUALITY_CHECK = 'QUALITY_CHECK',   // 品質檢查
  CUSTOMER_CONTACT = 'CUSTOMER_CONTACT', // 客戶聯繫
  COST_UPDATED = 'COST_UPDATED',     // 費用更新
}

// 附件類型枚舉
export enum AttachmentType {
  IMAGE = 'IMAGE',         // 圖片
  DOCUMENT = 'DOCUMENT',   // 文件
  VIDEO = 'VIDEO',         // 影片
  AUDIO = 'AUDIO',         // 音訊
  OTHER = 'OTHER',         // 其他
}

// 品質檢查類型枚舉
export enum QualityCheckType {
  FUNCTIONAL = 'FUNCTIONAL',     // 功能檢查
  COSMETIC = 'COSMETIC',         // 外觀檢查
  PERFORMANCE = 'PERFORMANCE',   // 性能檢查
  SAFETY = 'SAFETY',             // 安全檢查
  FINAL = 'FINAL',               // 最終檢查
}

// 品質結果枚舉
export enum QualityResult {
  PASS = 'PASS',         // 通過
  FAIL = 'FAIL',         // 失敗
  WARNING = 'WARNING',   // 警告
  NOT_TESTED = 'NOT_TESTED', // 未測試
}

// 維修記錄排序選項
export const REPAIR_RECORD_SORT_OPTIONS = [
  { value: 'repairNumber', label: '維修單號' },
  { value: 'receivedDate', label: '接收日期' },
  { value: 'priority', label: '優先級' },
  { value: 'status', label: '狀態' },
  { value: 'estimatedCompletionDate', label: '預計完成日期' },
  { value: 'actualCost', label: '實際費用' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 維修記錄篩選選項
export const REPAIR_RECORD_FILTER_OPTIONS = {
  status: [
    { value: RepairStatus.RECEIVED, label: '已接收' },
    { value: RepairStatus.DIAGNOSED, label: '已診斷' },
    { value: RepairStatus.WAITING_PARTS, label: '等待零件' },
    { value: RepairStatus.IN_PROGRESS, label: '維修中' },
    { value: RepairStatus.TESTING, label: '測試中' },
    { value: RepairStatus.COMPLETED, label: '已完成' },
    { value: RepairStatus.QUALITY_CHECK, label: '品質檢查' },
    { value: RepairStatus.READY_PICKUP, label: '待取件' },
    { value: RepairStatus.DELIVERED, label: '已交付' },
    { value: RepairStatus.CANCELLED, label: '已取消' },
    { value: RepairStatus.ON_HOLD, label: '暫停' },
  ],
  priority: [
    { value: RepairPriority.LOW, label: '低' },
    { value: RepairPriority.NORMAL, label: '一般' },
    { value: RepairPriority.HIGH, label: '高' },
    { value: RepairPriority.URGENT, label: '緊急' },
    { value: RepairPriority.CRITICAL, label: '危急' },
  ],
  warrantyStatus: [
    { value: WarrantyStatus.IN_WARRANTY, label: '保固內' },
    { value: WarrantyStatus.OUT_OF_WARRANTY, label: '保固外' },
    { value: WarrantyStatus.EXTENDED_WARRANTY, label: '延長保固' },
    { value: WarrantyStatus.UNKNOWN, label: '未知' },
  ],
} as const;

// 預設分頁設定
export const DEFAULT_REPAIR_RECORD_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const;

// 維修記錄搜尋配置
export const REPAIR_RECORD_SEARCH_CONFIG = {
  minSearchLength: 2,
  maxSearchLength: 100,
  searchFields: ['repairNumber', 'customerName', 'productModel', 'issueDescription', 'serialNumber'],
  fuzzySearchThreshold: 0.6,
} as const;

// 維修記錄驗證規則
export const REPAIR_RECORD_VALIDATION_RULES = {
  issueDescription: {
    minLength: 10,
    maxLength: 2000,
    required: true,
  },
  serialNumber: {
    maxLength: 100,
    required: false,
  },
  estimatedCost: {
    min: 0,
    max: 999999999,
    required: false,
  },
  actualCost: {
    min: 0,
    max: 999999999,
    required: false,
  },
  notes: {
    maxLength: 2000,
    required: false,
  },
  internalNotes: {
    maxLength: 2000,
    required: false,
  },
} as const;

// 維修狀態流程配置
export const REPAIR_STATUS_FLOW = {
  [RepairStatus.RECEIVED]: [RepairStatus.DIAGNOSED, RepairStatus.CANCELLED, RepairStatus.ON_HOLD],
  [RepairStatus.DIAGNOSED]: [RepairStatus.WAITING_PARTS, RepairStatus.IN_PROGRESS, RepairStatus.CANCELLED, RepairStatus.ON_HOLD],
  [RepairStatus.WAITING_PARTS]: [RepairStatus.IN_PROGRESS, RepairStatus.CANCELLED, RepairStatus.ON_HOLD],
  [RepairStatus.IN_PROGRESS]: [RepairStatus.TESTING, RepairStatus.COMPLETED, RepairStatus.WAITING_PARTS, RepairStatus.ON_HOLD],
  [RepairStatus.TESTING]: [RepairStatus.QUALITY_CHECK, RepairStatus.IN_PROGRESS, RepairStatus.COMPLETED],
  [RepairStatus.QUALITY_CHECK]: [RepairStatus.READY_PICKUP, RepairStatus.IN_PROGRESS, RepairStatus.COMPLETED],
  [RepairStatus.COMPLETED]: [RepairStatus.READY_PICKUP, RepairStatus.DELIVERED],
  [RepairStatus.READY_PICKUP]: [RepairStatus.DELIVERED],
  [RepairStatus.DELIVERED]: [],
  [RepairStatus.CANCELLED]: [],
  [RepairStatus.ON_HOLD]: [RepairStatus.RECEIVED, RepairStatus.DIAGNOSED, RepairStatus.WAITING_PARTS, RepairStatus.IN_PROGRESS],
} as const;

// 維修優先級權重（用於排序）
export const REPAIR_PRIORITY_WEIGHT = {
  [RepairPriority.CRITICAL]: 5,
  [RepairPriority.URGENT]: 4,
  [RepairPriority.HIGH]: 3,
  [RepairPriority.NORMAL]: 2,
  [RepairPriority.LOW]: 1,
} as const;

// 維修記錄匯出格式
export const REPAIR_RECORD_EXPORT_FORMATS = {
  csv: {
    mimeType: 'text/csv',
    extension: 'csv',
    headers: [
      '維修單號', '客戶姓名', '客戶電話', '產品型號', '序號', '問題描述',
      '優先級', '狀態', '指派技師', '預估費用', '實際費用', '保固狀態',
      '接收日期', '預計完成日期', '實際完成日期', '交付日期', '備註'
    ],
  },
  excel: {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xlsx',
    sheetName: '維修記錄',
  },
  pdf: {
    mimeType: 'application/pdf',
    extension: 'pdf',
  },
} as const;
