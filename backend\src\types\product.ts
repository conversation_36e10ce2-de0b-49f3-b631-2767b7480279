// 產品基本資訊介面
export interface ProductInfo {
  id: string;
  name: string;
  model: string;
  brand: string;
  categoryId: string;
  category?: ProductCategoryInfo;
  description?: string;
  specifications?: string;
  warrantyPeriod?: number; // 保固期間（月）
  price?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 產品類別資訊介面
export interface ProductCategoryInfo {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  parent?: ProductCategoryInfo;
  children?: ProductCategoryInfo[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 創建產品請求介面
export interface CreateProductRequest {
  name: string;
  model: string;
  brand: string;
  categoryId: string;
  description?: string;
  specifications?: string;
  warrantyPeriod?: number;
  price?: number;
  isActive?: boolean;
}

// 更新產品請求介面
export interface UpdateProductRequest {
  name?: string;
  model?: string;
  brand?: string;
  categoryId?: string;
  description?: string;
  specifications?: string;
  warrantyPeriod?: number;
  price?: number;
  isActive?: boolean;
}

// 創建產品類別請求介面
export interface CreateProductCategoryRequest {
  name: string;
  description?: string;
  parentId?: string;
  isActive?: boolean;
}

// 更新產品類別請求介面
export interface UpdateProductCategoryRequest {
  name?: string;
  description?: string;
  parentId?: string;
  isActive?: boolean;
}

// 產品查詢參數介面
export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  brand?: string;
  isActive?: boolean;
  hasPrice?: boolean;
  priceMin?: number;
  priceMax?: number;
  warrantyMin?: number;
  warrantyMax?: number;
  sortBy?: 'name' | 'model' | 'brand' | 'price' | 'warrantyPeriod' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 產品類別查詢參數介面
export interface ProductCategoryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  parentId?: string;
  isActive?: boolean;
  includeChildren?: boolean;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 產品列表響應介面
export interface ProductListResponse {
  products: ProductInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    categoryId?: string;
    brand?: string;
    isActive?: boolean;
    hasPrice?: boolean;
    priceRange?: { min?: number; max?: number };
    warrantyRange?: { min?: number; max?: number };
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 產品類別列表響應介面
export interface ProductCategoryListResponse {
  categories: ProductCategoryInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    parentId?: string;
    isActive?: boolean;
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 產品統計介面
export interface ProductStatistics {
  total: number;
  active: number;
  inactive: number;
  withPrice: number;
  withoutPrice: number;
  byCategory: Array<{
    categoryId: string;
    categoryName: string;
    count: number;
  }>;
  byBrand: Array<{
    brand: string;
    count: number;
  }>;
  priceStatistics: {
    average: number;
    min: number;
    max: number;
    median: number;
  };
  warrantyStatistics: {
    average: number;
    min: number;
    max: number;
  };
  recentProducts: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}

// 產品類別統計介面
export interface ProductCategoryStatistics {
  total: number;
  active: number;
  inactive: number;
  rootCategories: number;
  subCategories: number;
  categoriesWithProducts: number;
  categoriesWithoutProducts: number;
  averageProductsPerCategory: number;
  topCategories: Array<{
    categoryId: string;
    categoryName: string;
    productCount: number;
  }>;
}

// 產品搜尋結果介面
export interface ProductSearchResult {
  id: string;
  name: string;
  model: string;
  brand: string;
  categoryName: string;
  price?: number;
  isActive: boolean;
  relevanceScore: number;
  repairCount?: number;
}

// 產品詳細資訊介面（包含關聯資料）
export interface ProductDetailInfo extends ProductInfo {
  repairRecords: Array<{
    id: string;
    repairNumber: string;
    customerName: string;
    status: string;
    createdAt: Date;
    completedAt?: Date;
  }>;
  statistics: {
    totalRepairs: number;
    completedRepairs: number;
    pendingRepairs: number;
    averageRepairTime: number;
    commonIssues: Array<{
      issue: string;
      count: number;
    }>;
  };
}

// 批量操作請求介面
export interface BatchProductOperation {
  productIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'updateCategory' | 'updateBrand';
  data?: {
    isActive?: boolean;
    categoryId?: string;
    brand?: string;
  };
}

// 批量操作結果介面
export interface BatchOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    productId: string;
    error: string;
  }>;
}

// 產品驗證結果介面
export interface ProductValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 產品匯入請求介面
export interface ProductImportRequest {
  products: CreateProductRequest[];
  options: {
    skipDuplicates: boolean;
    updateExisting: boolean;
    validateOnly: boolean;
  };
}

// 產品匯入結果介面
export interface ProductImportResult {
  total: number;
  created: number;
  updated: number;
  skipped: number;
  failed: number;
  errors: Array<{
    row: number;
    product: CreateProductRequest;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    product: CreateProductRequest;
    warning: string;
  }>;
}

// 產品狀態枚舉
export enum ProductStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DISCONTINUED = 'DISCONTINUED',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
}

// 產品排序選項
export const PRODUCT_SORT_OPTIONS = [
  { value: 'name', label: '產品名稱' },
  { value: 'model', label: '型號' },
  { value: 'brand', label: '品牌' },
  { value: 'price', label: '價格' },
  { value: 'warrantyPeriod', label: '保固期間' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 產品類別排序選項
export const PRODUCT_CATEGORY_SORT_OPTIONS = [
  { value: 'name', label: '類別名稱' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 產品篩選選項
export const PRODUCT_FILTER_OPTIONS = {
  status: [
    { value: true, label: '活躍' },
    { value: false, label: '停用' },
  ],
  priceRange: [
    { value: { min: 0, max: 1000 }, label: '1000元以下' },
    { value: { min: 1000, max: 5000 }, label: '1000-5000元' },
    { value: { min: 5000, max: 10000 }, label: '5000-10000元' },
    { value: { min: 10000 }, label: '10000元以上' },
  ],
  warrantyPeriod: [
    { value: { min: 0, max: 6 }, label: '6個月以下' },
    { value: { min: 6, max: 12 }, label: '6-12個月' },
    { value: { min: 12, max: 24 }, label: '1-2年' },
    { value: { min: 24 }, label: '2年以上' },
  ],
} as const;

// 預設分頁設定
export const DEFAULT_PRODUCT_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const;

// 產品搜尋配置
export const PRODUCT_SEARCH_CONFIG = {
  minSearchLength: 2,
  maxSearchLength: 100,
  searchFields: ['name', 'model', 'brand', 'description'],
  fuzzySearchThreshold: 0.6,
} as const;

// 產品驗證規則
export const PRODUCT_VALIDATION_RULES = {
  name: {
    minLength: 2,
    maxLength: 200,
    required: true,
  },
  model: {
    minLength: 1,
    maxLength: 100,
    required: true,
  },
  brand: {
    minLength: 1,
    maxLength: 100,
    required: true,
  },
  description: {
    maxLength: 1000,
    required: false,
  },
  specifications: {
    maxLength: 2000,
    required: false,
  },
  warrantyPeriod: {
    min: 0,
    max: 120, // 最大10年
    required: false,
  },
  price: {
    min: 0,
    max: 999999999,
    required: false,
  },
} as const;

// 產品類別驗證規則
export const PRODUCT_CATEGORY_VALIDATION_RULES = {
  name: {
    minLength: 2,
    maxLength: 100,
    required: true,
  },
  description: {
    maxLength: 500,
    required: false,
  },
} as const;

// 常見品牌列表
export const COMMON_BRANDS = [
  'Apple', 'Samsung', 'Sony', 'LG', 'Panasonic',
  'ASUS', 'Acer', 'HP', 'Dell', 'Lenovo',
  'Canon', 'Nikon', 'Epson', 'Brother',
  'Philips', 'Bosch', 'Siemens', 'Whirlpool',
] as const;

// 產品類別樹狀結構介面
export interface ProductCategoryTree {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  productCount: number;
  children: ProductCategoryTree[];
}

// 產品匯出格式
export const PRODUCT_EXPORT_FORMATS = {
  csv: {
    mimeType: 'text/csv',
    extension: 'csv',
    headers: [
      'ID', '產品名稱', '型號', '品牌', '類別', '描述', 
      '規格', '保固期間', '價格', '狀態', '創建時間'
    ],
  },
  excel: {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xlsx',
    sheetName: '產品資料',
  },
  json: {
    mimeType: 'application/json',
    extension: 'json',
  },
} as const;
