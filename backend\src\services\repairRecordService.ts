import { RepairRecordRepository } from '../repositories/repairRecordRepository';
import { PartRepository } from '../repositories/partRepository';
import { logger } from '../utils/logger';
import { 
  RepairRecordInfo, 
  CreateRepairRecordRequest, 
  UpdateRepairRecordRequest, 
  RepairRecordQueryParams,
  RepairRecordListResponse,
  RepairRecordStatistics,
  RepairRecordSearchResult,
  RepairRecordDetailInfo,
  UpdateRepairStatusRequest,
  UsePartRequest,
  BatchUsePartsRequest,
  RepairRecordValidationResult,
  RepairStatus,
  RepairPriority,
  WarrantyStatus,
  TimelineEventType,
  REPAIR_RECORD_VALIDATION_RULES,
  REPAIR_STATUS_FLOW
} from '../types/repairRecord';

export class RepairRecordService {
  private repairRecordRepository: RepairRecordRepository;
  private partRepository: PartRepository;

  constructor(repairRecordRepository: RepairRecordRepository, partRepository: PartRepository) {
    this.repairRecordRepository = repairRecordRepository;
    this.partRepository = partRepository;
  }

  // 根據ID獲取維修記錄
  async getRepairRecordById(id: string): Promise<RepairRecordInfo | null> {
    try {
      return await this.repairRecordRepository.findById(id);
    } catch (error) {
      logger.error('獲取維修記錄失敗:', error);
      throw new Error('獲取維修記錄失敗');
    }
  }

  // 根據ID獲取維修記錄詳細資訊
  async getRepairRecordDetailById(id: string): Promise<RepairRecordDetailInfo | null> {
    try {
      return await this.repairRecordRepository.findByIdWithDetails(id);
    } catch (error) {
      logger.error('獲取維修記錄詳細資訊失敗:', error);
      throw new Error('獲取維修記錄詳細資訊失敗');
    }
  }

  // 創建維修記錄
  async createRepairRecord(recordData: CreateRepairRecordRequest, createdBy: string): Promise<RepairRecordInfo> {
    try {
      // 驗證維修記錄資料
      const validation = await this.validateRepairRecordData(recordData);
      if (!validation.isValid) {
        throw new Error(`維修記錄資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查客戶是否存在
      // TODO: 實作客戶存在性檢查

      // 檢查產品是否存在
      // TODO: 實作產品存在性檢查

      // 檢查指派技師是否存在（如果有指派）
      if (recordData.assignedTechnicianId) {
        // TODO: 實作技師存在性檢查
      }

      // 創建維修記錄
      const newRecord = await this.repairRecordRepository.create(recordData, createdBy);

      logger.info('維修記錄創建成功:', { 
        repairRecordId: newRecord.id, 
        repairNumber: newRecord.repairNumber,
        createdBy 
      });

      return newRecord;
    } catch (error) {
      logger.error('創建維修記錄失敗:', error);
      throw error;
    }
  }

  // 更新維修記錄
  async updateRepairRecord(id: string, recordData: UpdateRepairRecordRequest, updatedBy: string): Promise<RepairRecordInfo> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(id);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 驗證更新資料
      const validation = await this.validateUpdateData(recordData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查狀態變更是否合法
      if (recordData.status && recordData.status !== existingRecord.status) {
        const isValidTransition = this.isValidStatusTransition(existingRecord.status, recordData.status);
        if (!isValidTransition) {
          throw new Error(`無效的狀態轉換: ${existingRecord.status} -> ${recordData.status}`);
        }
      }

      // 更新維修記錄
      const updatedRecord = await this.repairRecordRepository.update(id, recordData, updatedBy);

      // 如果狀態有變更，記錄狀態歷史
      if (recordData.status && recordData.status !== existingRecord.status) {
        await this.repairRecordRepository.createStatusHistory({
          repairRecordId: id,
          fromStatus: existingRecord.status,
          toStatus: recordData.status,
          changedBy: updatedBy,
          reason: '狀態更新',
        });

        // 創建時間軸事件
        await this.repairRecordRepository.createTimelineEvent({
          repairRecordId: id,
          eventType: TimelineEventType.STATUS_CHANGED,
          title: `狀態變更為 ${recordData.status}`,
          description: `從 ${existingRecord.status} 變更為 ${recordData.status}`,
          performedBy: updatedBy,
        });
      }

      logger.info('維修記錄更新成功:', { 
        repairRecordId: id, 
        updatedBy,
        changes: Object.keys(recordData)
      });

      return updatedRecord;
    } catch (error) {
      logger.error('更新維修記錄失敗:', error);
      throw error;
    }
  }

  // 刪除維修記錄
  async deleteRepairRecord(id: string, deletedBy: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(id);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 檢查是否可以刪除
      if (existingRecord.status === RepairStatus.IN_PROGRESS || existingRecord.status === RepairStatus.TESTING) {
        throw new Error('進行中的維修記錄無法刪除');
      }

      // 檢查是否有零件使用記錄
      const recordDetail = await this.repairRecordRepository.findByIdWithDetails(id);
      if (recordDetail && recordDetail.partsUsed.length > 0 && hardDelete) {
        throw new Error('有零件使用記錄的維修記錄無法永久刪除');
      }

      if (hardDelete) {
        await this.repairRecordRepository.hardDelete(id);
        logger.info('維修記錄硬刪除成功:', { repairRecordId: id, deletedBy });
      } else {
        await this.repairRecordRepository.softDelete(id);
        logger.info('維修記錄軟刪除成功:', { repairRecordId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除維修記錄失敗:', error);
      throw error;
    }
  }

  // 獲取維修記錄列表
  async getRepairRecordList(params: RepairRecordQueryParams): Promise<RepairRecordListResponse> {
    try {
      return await this.repairRecordRepository.findMany(params);
    } catch (error) {
      logger.error('獲取維修記錄列表失敗:', error);
      throw new Error('獲取維修記錄列表失敗');
    }
  }

  // 搜尋維修記錄
  async searchRepairRecords(query: string, limit: number = 10): Promise<RepairRecordSearchResult[]> {
    try {
      if (query.length < 2) {
        throw new Error('搜尋關鍵字至少需要2個字符');
      }

      return await this.repairRecordRepository.search(query, limit);
    } catch (error) {
      logger.error('搜尋維修記錄失敗:', error);
      throw error;
    }
  }

  // 獲取維修記錄統計
  async getRepairRecordStatistics(): Promise<RepairRecordStatistics> {
    try {
      return await this.repairRecordRepository.getStatistics();
    } catch (error) {
      logger.error('獲取維修記錄統計失敗:', error);
      throw new Error('獲取維修記錄統計失敗');
    }
  }

  // 更新維修狀態
  async updateRepairStatus(id: string, statusData: UpdateRepairStatusRequest, updatedBy: string): Promise<RepairRecordInfo> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(id);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 檢查狀態轉換是否合法
      const isValidTransition = this.isValidStatusTransition(existingRecord.status, statusData.status);
      if (!isValidTransition) {
        throw new Error(`無效的狀態轉換: ${existingRecord.status} -> ${statusData.status}`);
      }

      // 準備更新資料
      const updateData: UpdateRepairRecordRequest = {
        status: statusData.status,
      };

      // 根據狀態設定相應的日期
      switch (statusData.status) {
        case RepairStatus.IN_PROGRESS:
          if (!existingRecord.startedDate) {
            updateData.startedDate = new Date();
          }
          break;
        case RepairStatus.COMPLETED:
          updateData.completedDate = statusData.actualCompletionDate || new Date();
          updateData.actualCompletionDate = statusData.actualCompletionDate || new Date();
          break;
        case RepairStatus.DELIVERED:
          updateData.deliveredDate = new Date();
          break;
      }

      if (statusData.estimatedCompletionDate) {
        updateData.estimatedCompletionDate = statusData.estimatedCompletionDate;
      }

      // 更新維修記錄
      const updatedRecord = await this.repairRecordRepository.update(id, updateData, updatedBy);

      // 記錄狀態歷史
      await this.repairRecordRepository.createStatusHistory({
        repairRecordId: id,
        fromStatus: existingRecord.status,
        toStatus: statusData.status,
        changedBy: updatedBy,
        reason: statusData.reason || '狀態更新',
        notes: statusData.notes,
      });

      // 創建時間軸事件
      await this.repairRecordRepository.createTimelineEvent({
        repairRecordId: id,
        eventType: TimelineEventType.STATUS_CHANGED,
        title: `狀態變更為 ${statusData.status}`,
        description: statusData.reason || `從 ${existingRecord.status} 變更為 ${statusData.status}`,
        performedBy: updatedBy,
      });

      logger.info('維修狀態更新成功:', { 
        repairRecordId: id,
        fromStatus: existingRecord.status,
        toStatus: statusData.status,
        updatedBy
      });

      return updatedRecord;
    } catch (error) {
      logger.error('更新維修狀態失敗:', error);
      throw error;
    }
  }

  // 指派技師
  async assignTechnician(id: string, technicianId: string, assignedBy: string): Promise<RepairRecordInfo> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(id);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 檢查技師是否存在
      // TODO: 實作技師存在性檢查

      // 更新維修記錄
      const updatedRecord = await this.repairRecordRepository.update(id, {
        assignedTechnicianId: technicianId,
      }, assignedBy);

      // 創建時間軸事件
      await this.repairRecordRepository.createTimelineEvent({
        repairRecordId: id,
        eventType: TimelineEventType.ASSIGNED,
        title: '指派技師',
        description: `指派技師: ${updatedRecord.assignedTechnicianName}`,
        performedBy: assignedBy,
      });

      logger.info('技師指派成功:', { 
        repairRecordId: id,
        technicianId,
        assignedBy
      });

      return updatedRecord;
    } catch (error) {
      logger.error('指派技師失敗:', error);
      throw error;
    }
  }

  // 使用零件
  async usePart(repairRecordId: string, partRequest: UsePartRequest, usedBy: string): Promise<void> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(repairRecordId);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 檢查零件是否存在
      const part = await this.partRepository.findById(partRequest.partId);
      if (!part) {
        throw new Error('零件不存在');
      }

      // 檢查庫存是否足夠
      if (part.availableStock < partRequest.quantity) {
        throw new Error('零件庫存不足');
      }

      // 執行出庫操作
      await this.partRepository.performStockOperation({
        partId: partRequest.partId,
        type: 'OUT',
        quantity: partRequest.quantity,
        reason: `維修使用 - ${existingRecord.repairNumber}`,
        referenceId: repairRecordId,
      }, usedBy);

      // 記錄零件使用
      // TODO: 實作零件使用記錄創建

      // 創建時間軸事件
      await this.repairRecordRepository.createTimelineEvent({
        repairRecordId,
        eventType: TimelineEventType.PART_USED,
        title: '使用零件',
        description: `使用零件: ${part.name} x${partRequest.quantity}`,
        performedBy: usedBy,
        metadata: {
          partId: partRequest.partId,
          partName: part.name,
          partNumber: part.partNumber,
          quantity: partRequest.quantity,
          isWarrantyPart: partRequest.isWarrantyPart,
        },
      });

      logger.info('零件使用成功:', { 
        repairRecordId,
        partId: partRequest.partId,
        quantity: partRequest.quantity,
        usedBy
      });
    } catch (error) {
      logger.error('使用零件失敗:', error);
      throw error;
    }
  }

  // 批量使用零件
  async batchUseParts(repairRecordId: string, batchRequest: BatchUsePartsRequest, usedBy: string): Promise<void> {
    try {
      // 檢查維修記錄是否存在
      const existingRecord = await this.repairRecordRepository.findById(repairRecordId);
      if (!existingRecord) {
        throw new Error('維修記錄不存在');
      }

      // 驗證所有零件
      for (const partRequest of batchRequest.parts) {
        const part = await this.partRepository.findById(partRequest.partId);
        if (!part) {
          throw new Error(`零件不存在: ${partRequest.partId}`);
        }
        if (part.availableStock < partRequest.quantity) {
          throw new Error(`零件庫存不足: ${part.name}`);
        }
      }

      // 批量執行出庫操作
      const stockOperations = batchRequest.parts.map(partRequest => ({
        partId: partRequest.partId,
        type: 'OUT' as const,
        quantity: partRequest.quantity,
        reason: `維修使用 - ${existingRecord.repairNumber}`,
        referenceId: repairRecordId,
      }));

      await this.partRepository.performBatchStockOperation({
        operations: stockOperations,
        batchReason: batchRequest.notes || '批量零件使用',
      }, usedBy);

      // 記錄所有零件使用
      // TODO: 實作批量零件使用記錄創建

      // 創建時間軸事件
      await this.repairRecordRepository.createTimelineEvent({
        repairRecordId,
        eventType: TimelineEventType.PART_USED,
        title: '批量使用零件',
        description: `批量使用 ${batchRequest.parts.length} 種零件`,
        performedBy: usedBy,
        metadata: {
          partsCount: batchRequest.parts.length,
          parts: batchRequest.parts,
        },
      });

      logger.info('批量零件使用成功:', { 
        repairRecordId,
        partsCount: batchRequest.parts.length,
        usedBy
      });
    } catch (error) {
      logger.error('批量使用零件失敗:', error);
      throw error;
    }
  }

  // 根據維修單號查找維修記錄
  async findRepairRecordByRepairNumber(repairNumber: string): Promise<RepairRecordInfo | null> {
    try {
      const record = await this.repairRecordRepository.findByRepairNumber(repairNumber);
      if (!record) return null;

      return await this.repairRecordRepository.findById(record.id.toString());
    } catch (error) {
      logger.error('根據維修單號查找維修記錄失敗:', error);
      throw error;
    }
  }

  // 檢查狀態轉換是否合法
  private isValidStatusTransition(fromStatus: RepairStatus, toStatus: RepairStatus): boolean {
    const allowedTransitions = REPAIR_STATUS_FLOW[fromStatus];
    return allowedTransitions.includes(toStatus);
  }

  // 驗證維修記錄資料
  private async validateRepairRecordData(recordData: CreateRepairRecordRequest): Promise<RepairRecordValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證問題描述
    if (!recordData.issueDescription || recordData.issueDescription.trim().length < REPAIR_RECORD_VALIDATION_RULES.issueDescription.minLength) {
      errors.push(`問題描述至少需要${REPAIR_RECORD_VALIDATION_RULES.issueDescription.minLength}個字符`);
    }

    if (recordData.issueDescription && recordData.issueDescription.length > REPAIR_RECORD_VALIDATION_RULES.issueDescription.maxLength) {
      errors.push(`問題描述不能超過${REPAIR_RECORD_VALIDATION_RULES.issueDescription.maxLength}個字符`);
    }

    // 驗證序號
    if (recordData.serialNumber && recordData.serialNumber.length > REPAIR_RECORD_VALIDATION_RULES.serialNumber.maxLength) {
      errors.push(`序號不能超過${REPAIR_RECORD_VALIDATION_RULES.serialNumber.maxLength}個字符`);
    }

    // 驗證預估費用
    if (recordData.estimatedCost !== undefined) {
      if (recordData.estimatedCost < REPAIR_RECORD_VALIDATION_RULES.estimatedCost.min) {
        errors.push(`預估費用不能小於${REPAIR_RECORD_VALIDATION_RULES.estimatedCost.min}`);
      }
      if (recordData.estimatedCost > REPAIR_RECORD_VALIDATION_RULES.estimatedCost.max) {
        errors.push(`預估費用不能超過${REPAIR_RECORD_VALIDATION_RULES.estimatedCost.max}`);
      }
    }

    // 驗證備註
    if (recordData.notes && recordData.notes.length > REPAIR_RECORD_VALIDATION_RULES.notes.maxLength) {
      errors.push(`備註不能超過${REPAIR_RECORD_VALIDATION_RULES.notes.maxLength}個字符`);
    }

    if (recordData.internalNotes && recordData.internalNotes.length > REPAIR_RECORD_VALIDATION_RULES.internalNotes.maxLength) {
      errors.push(`內部備註不能超過${REPAIR_RECORD_VALIDATION_RULES.internalNotes.maxLength}個字符`);
    }

    // 警告：缺少預估費用
    if (recordData.estimatedCost === undefined) {
      warnings.push('建議設定預估費用');
    }

    // 警告：缺少預計完成日期
    if (recordData.estimatedCompletionDate === undefined) {
      warnings.push('建議設定預計完成日期');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證更新資料
  private async validateUpdateData(recordData: UpdateRepairRecordRequest): Promise<RepairRecordValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證問題描述
    if (recordData.issueDescription !== undefined) {
      if (recordData.issueDescription.trim().length < REPAIR_RECORD_VALIDATION_RULES.issueDescription.minLength) {
        errors.push(`問題描述至少需要${REPAIR_RECORD_VALIDATION_RULES.issueDescription.minLength}個字符`);
      }
      if (recordData.issueDescription.length > REPAIR_RECORD_VALIDATION_RULES.issueDescription.maxLength) {
        errors.push(`問題描述不能超過${REPAIR_RECORD_VALIDATION_RULES.issueDescription.maxLength}個字符`);
      }
    }

    // 驗證序號
    if (recordData.serialNumber !== undefined && recordData.serialNumber && recordData.serialNumber.length > REPAIR_RECORD_VALIDATION_RULES.serialNumber.maxLength) {
      errors.push(`序號不能超過${REPAIR_RECORD_VALIDATION_RULES.serialNumber.maxLength}個字符`);
    }

    // 驗證費用
    if (recordData.estimatedCost !== undefined) {
      if (recordData.estimatedCost < REPAIR_RECORD_VALIDATION_RULES.estimatedCost.min) {
        errors.push(`預估費用不能小於${REPAIR_RECORD_VALIDATION_RULES.estimatedCost.min}`);
      }
      if (recordData.estimatedCost > REPAIR_RECORD_VALIDATION_RULES.estimatedCost.max) {
        errors.push(`預估費用不能超過${REPAIR_RECORD_VALIDATION_RULES.estimatedCost.max}`);
      }
    }

    if (recordData.actualCost !== undefined) {
      if (recordData.actualCost < REPAIR_RECORD_VALIDATION_RULES.actualCost.min) {
        errors.push(`實際費用不能小於${REPAIR_RECORD_VALIDATION_RULES.actualCost.min}`);
      }
      if (recordData.actualCost > REPAIR_RECORD_VALIDATION_RULES.actualCost.max) {
        errors.push(`實際費用不能超過${REPAIR_RECORD_VALIDATION_RULES.actualCost.max}`);
      }
    }

    // 驗證備註
    if (recordData.notes !== undefined && recordData.notes && recordData.notes.length > REPAIR_RECORD_VALIDATION_RULES.notes.maxLength) {
      errors.push(`備註不能超過${REPAIR_RECORD_VALIDATION_RULES.notes.maxLength}個字符`);
    }

    if (recordData.internalNotes !== undefined && recordData.internalNotes && recordData.internalNotes.length > REPAIR_RECORD_VALIDATION_RULES.internalNotes.maxLength) {
      errors.push(`內部備註不能超過${REPAIR_RECORD_VALIDATION_RULES.internalNotes.maxLength}個字符`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
