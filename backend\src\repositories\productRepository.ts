import { PrismaClient, Product, ProductCategory } from '@prisma/client';
import { 
  ProductInfo, 
  ProductCategoryInfo,
  CreateProductRequest, 
  UpdateProductRequest, 
  CreateProductCategoryRequest,
  UpdateProductCategoryRequest,
  ProductQueryParams,
  ProductCategoryQueryParams,
  ProductListResponse,
  ProductCategoryListResponse,
  ProductStatistics,
  ProductCategoryStatistics,
  ProductSearchResult,
  ProductDetailInfo,
  BatchProductOperation,
  BatchOperationResult,
  ProductCategoryTree,
  DEFAULT_PRODUCT_PAGINATION
} from '../types/product';
import { logger } from '../utils/logger';

export class ProductRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找產品
  async findById(id: string): Promise<ProductInfo | null> {
    try {
      const product = await this.prisma.product.findUnique({
        where: { id: BigInt(id) },
        include: {
          category: true,
        },
      });

      if (!product) return null;

      return {
        id: product.id.toString(),
        name: product.name,
        model: product.model,
        brand: product.brand,
        categoryId: product.categoryId.toString(),
        category: product.category ? {
          id: product.category.id.toString(),
          name: product.category.name,
          description: product.category.description,
          parentId: product.category.parentId?.toString(),
          isActive: product.category.isActive,
          createdAt: product.category.createdAt,
          updatedAt: product.category.updatedAt,
        } : undefined,
        description: product.description,
        specifications: product.specifications,
        warrantyPeriod: product.warrantyPeriod,
        price: product.price ? parseFloat(product.price.toString()) : undefined,
        isActive: product.isActive,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      };
    } catch (error) {
      logger.error('查找產品失敗:', error);
      throw new Error('查找產品失敗');
    }
  }

  // 根據型號查找產品
  async findByModel(model: string): Promise<Product | null> {
    try {
      return await this.prisma.product.findFirst({
        where: { model },
      });
    } catch (error) {
      logger.error('根據型號查找產品失敗:', error);
      throw new Error('查找產品失敗');
    }
  }

  // 檢查產品是否存在
  async checkExistence(model: string, excludeId?: string): Promise<boolean> {
    try {
      const whereCondition = excludeId 
        ? { 
            model,
            NOT: { id: BigInt(excludeId) }
          }
        : { model };

      const existingProduct = await this.prisma.product.findFirst({
        where: whereCondition,
        select: { id: true },
      });

      return !!existingProduct;
    } catch (error) {
      logger.error('檢查產品存在性失敗:', error);
      throw new Error('檢查產品存在性失敗');
    }
  }

  // 創建產品
  async create(productData: CreateProductRequest): Promise<ProductInfo> {
    try {
      const product = await this.prisma.product.create({
        data: {
          name: productData.name,
          model: productData.model,
          brand: productData.brand,
          categoryId: BigInt(productData.categoryId),
          description: productData.description,
          specifications: productData.specifications,
          warrantyPeriod: productData.warrantyPeriod,
          price: productData.price,
          isActive: productData.isActive ?? true,
        },
        include: {
          category: true,
        },
      });

      logger.info('產品創建成功:', { productId: product.id, name: product.name });

      return {
        id: product.id.toString(),
        name: product.name,
        model: product.model,
        brand: product.brand,
        categoryId: product.categoryId.toString(),
        category: product.category ? {
          id: product.category.id.toString(),
          name: product.category.name,
          description: product.category.description,
          parentId: product.category.parentId?.toString(),
          isActive: product.category.isActive,
          createdAt: product.category.createdAt,
          updatedAt: product.category.updatedAt,
        } : undefined,
        description: product.description,
        specifications: product.specifications,
        warrantyPeriod: product.warrantyPeriod,
        price: product.price ? parseFloat(product.price.toString()) : undefined,
        isActive: product.isActive,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      };
    } catch (error) {
      logger.error('創建產品失敗:', error);
      throw new Error('創建產品失敗');
    }
  }

  // 更新產品
  async update(id: string, productData: UpdateProductRequest): Promise<ProductInfo> {
    try {
      const updateData: any = { ...productData };
      if (productData.categoryId) {
        updateData.categoryId = BigInt(productData.categoryId);
      }

      const product = await this.prisma.product.update({
        where: { id: BigInt(id) },
        data: updateData,
        include: {
          category: true,
        },
      });

      logger.info('產品更新成功:', { productId: product.id, name: product.name });

      return {
        id: product.id.toString(),
        name: product.name,
        model: product.model,
        brand: product.brand,
        categoryId: product.categoryId.toString(),
        category: product.category ? {
          id: product.category.id.toString(),
          name: product.category.name,
          description: product.category.description,
          parentId: product.category.parentId?.toString(),
          isActive: product.category.isActive,
          createdAt: product.category.createdAt,
          updatedAt: product.category.updatedAt,
        } : undefined,
        description: product.description,
        specifications: product.specifications,
        warrantyPeriod: product.warrantyPeriod,
        price: product.price ? parseFloat(product.price.toString()) : undefined,
        isActive: product.isActive,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      };
    } catch (error) {
      logger.error('更新產品失敗:', error);
      throw new Error('更新產品失敗');
    }
  }

  // 刪除產品（軟刪除）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.product.update({
        where: { id: BigInt(id) },
        data: { isActive: false },
      });

      logger.info('產品軟刪除成功:', { productId: id });
    } catch (error) {
      logger.error('軟刪除產品失敗:', error);
      throw new Error('刪除產品失敗');
    }
  }

  // 硬刪除產品
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.product.delete({
        where: { id: BigInt(id) },
      });

      logger.info('產品硬刪除成功:', { productId: id });
    } catch (error) {
      logger.error('硬刪除產品失敗:', error);
      throw new Error('刪除產品失敗');
    }
  }

  // 獲取產品列表
  async findMany(params: ProductQueryParams): Promise<ProductListResponse> {
    try {
      const {
        page = DEFAULT_PRODUCT_PAGINATION.page,
        limit = DEFAULT_PRODUCT_PAGINATION.limit,
        search,
        categoryId,
        brand,
        isActive,
        hasPrice,
        priceMin,
        priceMax,
        warrantyMin,
        warrantyMax,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_PRODUCT_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { model: { contains: search, mode: 'insensitive' } },
          { brand: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (categoryId) {
        where.categoryId = BigInt(categoryId);
      }

      if (brand) {
        where.brand = { contains: brand, mode: 'insensitive' };
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      if (hasPrice !== undefined) {
        if (hasPrice) {
          where.price = { not: null };
        } else {
          where.price = null;
        }
      }

      if (priceMin !== undefined || priceMax !== undefined) {
        where.price = {};
        if (priceMin !== undefined) where.price.gte = priceMin;
        if (priceMax !== undefined) where.price.lte = priceMax;
      }

      if (warrantyMin !== undefined || warrantyMax !== undefined) {
        where.warrantyPeriod = {};
        if (warrantyMin !== undefined) where.warrantyPeriod.gte = warrantyMin;
        if (warrantyMax !== undefined) where.warrantyPeriod.lte = warrantyMax;
      }

      // 執行查詢
      const [products, total] = await Promise.all([
        this.prisma.product.findMany({
          where,
          include: {
            category: true,
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.product.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        products: products.map(product => ({
          id: product.id.toString(),
          name: product.name,
          model: product.model,
          brand: product.brand,
          categoryId: product.categoryId.toString(),
          category: product.category ? {
            id: product.category.id.toString(),
            name: product.category.name,
            description: product.category.description,
            parentId: product.category.parentId?.toString(),
            isActive: product.category.isActive,
            createdAt: product.category.createdAt,
            updatedAt: product.category.updatedAt,
          } : undefined,
          description: product.description,
          specifications: product.specifications,
          warrantyPeriod: product.warrantyPeriod,
          price: product.price ? parseFloat(product.price.toString()) : undefined,
          isActive: product.isActive,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          categoryId,
          brand,
          isActive,
          hasPrice,
          priceRange: (priceMin !== undefined || priceMax !== undefined) ? { min: priceMin, max: priceMax } : undefined,
          warrantyRange: (warrantyMin !== undefined || warrantyMax !== undefined) ? { min: warrantyMin, max: warrantyMax } : undefined,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取產品列表失敗:', error);
      throw new Error('獲取產品列表失敗');
    }
  }

  // 搜尋產品
  async search(query: string, limit: number = 10): Promise<ProductSearchResult[]> {
    try {
      const products = await this.prisma.product.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { model: { contains: query, mode: 'insensitive' } },
            { brand: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
          ],
          isActive: true,
        },
        include: {
          category: {
            select: {
              name: true,
            },
          },
          _count: {
            select: {
              repairRecords: true,
            },
          },
        },
        take: limit,
      });

      return products.map(product => ({
        id: product.id.toString(),
        name: product.name,
        model: product.model,
        brand: product.brand,
        categoryName: product.category?.name || 'Unknown',
        price: product.price ? parseFloat(product.price.toString()) : undefined,
        isActive: product.isActive,
        relevanceScore: this.calculateRelevanceScore(product, query),
        repairCount: product._count.repairRecords,
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logger.error('搜尋產品失敗:', error);
      throw new Error('搜尋產品失敗');
    }
  }

  // 獲取產品詳細資訊
  async findByIdWithDetails(id: string): Promise<ProductDetailInfo | null> {
    try {
      const product = await this.prisma.product.findUnique({
        where: { id: BigInt(id) },
        include: {
          category: true,
          repairRecords: {
            select: {
              id: true,
              repairNumber: true,
              status: true,
              createdAt: true,
              completedAt: true,
              customer: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          _count: {
            select: {
              repairRecords: true,
            },
          },
        },
      });

      if (!product) return null;

      // 計算統計資訊
      const completedRepairs = product.repairRecords.filter(r => r.status === 'COMPLETED');
      const pendingRepairs = product.repairRecords.filter(r => r.status !== 'COMPLETED');
      
      const averageRepairTime = completedRepairs.length > 0
        ? completedRepairs.reduce((sum, repair) => {
            if (repair.completedAt) {
              return sum + (repair.completedAt.getTime() - repair.createdAt.getTime());
            }
            return sum;
          }, 0) / completedRepairs.length / (1000 * 60 * 60 * 24) // 轉換為天數
        : 0;

      return {
        id: product.id.toString(),
        name: product.name,
        model: product.model,
        brand: product.brand,
        categoryId: product.categoryId.toString(),
        category: product.category ? {
          id: product.category.id.toString(),
          name: product.category.name,
          description: product.category.description,
          parentId: product.category.parentId?.toString(),
          isActive: product.category.isActive,
          createdAt: product.category.createdAt,
          updatedAt: product.category.updatedAt,
        } : undefined,
        description: product.description,
        specifications: product.specifications,
        warrantyPeriod: product.warrantyPeriod,
        price: product.price ? parseFloat(product.price.toString()) : undefined,
        isActive: product.isActive,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        repairRecords: product.repairRecords.map(record => ({
          id: record.id.toString(),
          repairNumber: record.repairNumber,
          customerName: record.customer?.name || 'Unknown',
          status: record.status,
          createdAt: record.createdAt,
          completedAt: record.completedAt,
        })),
        statistics: {
          totalRepairs: product._count.repairRecords,
          completedRepairs: completedRepairs.length,
          pendingRepairs: pendingRepairs.length,
          averageRepairTime: Math.round(averageRepairTime),
          commonIssues: [], // TODO: 實作常見問題統計
        },
      };
    } catch (error) {
      logger.error('獲取產品詳細資訊失敗:', error);
      throw new Error('獲取產品詳細資訊失敗');
    }
  }

  // 計算相關性分數
  private calculateRelevanceScore(product: any, query: string): number {
    const lowerQuery = query.toLowerCase();
    let score = 0;

    // 精確匹配得分更高
    if (product.name.toLowerCase() === lowerQuery) score += 100;
    else if (product.name.toLowerCase().includes(lowerQuery)) score += 50;

    if (product.model.toLowerCase() === lowerQuery) score += 100;
    else if (product.model.toLowerCase().includes(lowerQuery)) score += 75;

    if (product.brand.toLowerCase() === lowerQuery) score += 80;
    else if (product.brand.toLowerCase().includes(lowerQuery)) score += 40;

    if (product.description?.toLowerCase().includes(lowerQuery)) score += 20;

    // 開頭匹配得分較高
    if (product.name.toLowerCase().startsWith(lowerQuery)) score += 25;
    if (product.model.toLowerCase().startsWith(lowerQuery)) score += 30;
    if (product.brand.toLowerCase().startsWith(lowerQuery)) score += 20;

    return score;
  }

  // 獲取產品統計
  async getStatistics(): Promise<ProductStatistics> {
    try {
      const [total, active, withPrice, byCategory, byBrand, priceStats, warrantyStats, recentCounts] = await Promise.all([
        this.prisma.product.count(),
        this.prisma.product.count({ where: { isActive: true } }),
        this.prisma.product.count({ where: { price: { not: null } } }),
        this.getProductsByCategory(),
        this.getProductsByBrand(),
        this.getPriceStatistics(),
        this.getWarrantyStatistics(),
        this.getRecentProductCounts(),
      ]);

      return {
        total,
        active,
        inactive: total - active,
        withPrice,
        withoutPrice: total - withPrice,
        byCategory,
        byBrand,
        priceStatistics: priceStats,
        warrantyStatistics: warrantyStats,
        recentProducts: recentCounts,
      };
    } catch (error) {
      logger.error('獲取產品統計失敗:', error);
      throw new Error('獲取產品統計失敗');
    }
  }

  // 批量操作
  async batchOperation(operation: BatchProductOperation): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
    };

    try {
      for (const productId of operation.productIds) {
        try {
          switch (operation.operation) {
            case 'activate':
              await this.update(productId, { isActive: true });
              break;
            case 'deactivate':
              await this.update(productId, { isActive: false });
              break;
            case 'delete':
              await this.softDelete(productId);
              break;
            case 'updateCategory':
              if (operation.data?.categoryId) {
                await this.update(productId, { categoryId: operation.data.categoryId });
              }
              break;
            case 'updateBrand':
              if (operation.data?.brand) {
                await this.update(productId, { brand: operation.data.brand });
              }
              break;
          }
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            productId,
            error: error instanceof Error ? error.message : '未知錯誤',
          });
        }
      }

      logger.info('批量操作完成:', {
        operation: operation.operation,
        success: result.success,
        failed: result.failed
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw new Error('批量操作失敗');
    }
  }

  // 獲取各類別產品數量
  private async getProductsByCategory() {
    const categoryStats = await this.prisma.product.groupBy({
      by: ['categoryId'],
      _count: true,
      where: { isActive: true },
    });

    const categories = await this.prisma.productCategory.findMany({
      where: {
        id: { in: categoryStats.map(stat => stat.categoryId) },
      },
      select: { id: true, name: true },
    });

    return categoryStats.map(stat => {
      const category = categories.find(c => c.id === stat.categoryId);
      return {
        categoryId: stat.categoryId.toString(),
        categoryName: category?.name || 'Unknown',
        count: stat._count,
      };
    });
  }

  // 獲取各品牌產品數量
  private async getProductsByBrand() {
    const brandStats = await this.prisma.product.groupBy({
      by: ['brand'],
      _count: true,
      where: { isActive: true },
      orderBy: { _count: { _all: 'desc' } },
      take: 10,
    });

    return brandStats.map(stat => ({
      brand: stat.brand,
      count: stat._count,
    }));
  }

  // 獲取價格統計
  private async getPriceStatistics() {
    const priceData = await this.prisma.product.findMany({
      where: {
        price: { not: null },
        isActive: true,
      },
      select: { price: true },
    });

    if (priceData.length === 0) {
      return { average: 0, min: 0, max: 0, median: 0 };
    }

    const prices = priceData.map(p => parseFloat(p.price!.toString())).sort((a, b) => a - b);
    const sum = prices.reduce((acc, price) => acc + price, 0);
    const median = prices.length % 2 === 0
      ? (prices[prices.length / 2 - 1] + prices[prices.length / 2]) / 2
      : prices[Math.floor(prices.length / 2)];

    return {
      average: Math.round(sum / prices.length),
      min: prices[0],
      max: prices[prices.length - 1],
      median: Math.round(median),
    };
  }

  // 獲取保固期統計
  private async getWarrantyStatistics() {
    const warrantyData = await this.prisma.product.findMany({
      where: {
        warrantyPeriod: { not: null },
        isActive: true,
      },
      select: { warrantyPeriod: true },
    });

    if (warrantyData.length === 0) {
      return { average: 0, min: 0, max: 0 };
    }

    const warranties = warrantyData.map(w => w.warrantyPeriod!);
    const sum = warranties.reduce((acc, warranty) => acc + warranty, 0);

    return {
      average: Math.round(sum / warranties.length),
      min: Math.min(...warranties),
      max: Math.max(...warranties),
    };
  }

  // 獲取最近產品統計
  private async getRecentProductCounts() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [todayCount, weekCount, monthCount] = await Promise.all([
      this.prisma.product.count({ where: { createdAt: { gte: today } } }),
      this.prisma.product.count({ where: { createdAt: { gte: thisWeek } } }),
      this.prisma.product.count({ where: { createdAt: { gte: thisMonth } } }),
    ]);

    return {
      today: todayCount,
      thisWeek: weekCount,
      thisMonth: monthCount,
    };
  }
}
