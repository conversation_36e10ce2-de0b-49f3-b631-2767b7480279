import { api, ApiResponse, PaginatedResponse } from './api';

// 維修記錄相關類型定義
export interface RepairRecord {
  id: number;
  recordNumber: string;
  customerId: number;
  customer?: {
    id: number;
    name: string;
    email: string;
    phone: string;
  };
  productId: number;
  product?: {
    id: number;
    name: string;
    model: string;
    brand: string;
  };
  issueDescription: string;
  symptoms: string[];
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'PENDING_INSPECTION' | 'INSPECTING' | 'PENDING_REPAIR' | 'REPAIRING' | 'PENDING_PARTS' | 'COMPLETED' | 'DELIVERED' | 'CANCELLED';
  assignedTechnicianId?: number;
  assignedTechnician?: {
    id: number;
    name: string;
    email: string;
  };
  estimatedCost?: number;
  actualCost?: number;
  estimatedCompletionDate?: string;
  actualCompletionDate?: string;
  warrantyStatus: 'IN_WARRANTY' | 'OUT_OF_WARRANTY' | 'EXTENDED_WARRANTY';
  repairNotes?: string;
  customerNotes?: string;
  usedParts: RepairPart[];
  statusHistory: RepairStatusHistory[];
  attachments: RepairAttachment[];
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

export interface RepairPart {
  id: number;
  repairRecordId: number;
  partId: number;
  part?: {
    id: number;
    name: string;
    partNumber: string;
    unitPrice: number;
  };
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
  createdAt: string;
}

export interface RepairStatusHistory {
  id: number;
  repairRecordId: number;
  fromStatus?: string;
  toStatus: string;
  notes?: string;
  changedBy: number;
  changedByUser?: {
    id: number;
    name: string;
  };
  changedAt: string;
}

export interface RepairAttachment {
  id: number;
  repairRecordId: number;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: number;
  uploadedByUser?: {
    id: number;
    name: string;
  };
  uploadedAt: string;
}

export interface CreateRepairRecordRequest {
  customerId: number;
  productId: number;
  issueDescription: string;
  symptoms: string[];
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  assignedTechnicianId?: number;
  estimatedCost?: number;
  estimatedCompletionDate?: string;
  warrantyStatus: 'IN_WARRANTY' | 'OUT_OF_WARRANTY' | 'EXTENDED_WARRANTY';
  customerNotes?: string;
}

export interface UpdateRepairRecordRequest {
  customerId?: number;
  productId?: number;
  issueDescription?: string;
  symptoms?: string[];
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status?: 'PENDING_INSPECTION' | 'INSPECTING' | 'PENDING_REPAIR' | 'REPAIRING' | 'PENDING_PARTS' | 'COMPLETED' | 'DELIVERED' | 'CANCELLED';
  assignedTechnicianId?: number;
  estimatedCost?: number;
  actualCost?: number;
  estimatedCompletionDate?: string;
  actualCompletionDate?: string;
  warrantyStatus?: 'IN_WARRANTY' | 'OUT_OF_WARRANTY' | 'EXTENDED_WARRANTY';
  repairNotes?: string;
  customerNotes?: string;
}

export interface RepairQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  customerId?: number;
  productId?: number;
  status?: string;
  priority?: string;
  assignedTechnicianId?: number;
  warrantyStatus?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'recordNumber' | 'createdAt' | 'updatedAt' | 'priority' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface RepairStatistics {
  total: number;
  byStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  byPriority: Array<{
    priority: string;
    count: number;
    percentage: number;
  }>;
  byTechnician: Array<{
    technicianId: number;
    technicianName: string;
    count: number;
    avgCompletionTime: number;
  }>;
  avgRepairTime: number;
  avgCost: number;
  completionRate: number;
  customerSatisfaction: number;
}

// 維修記錄服務
export const repairService = {
  // 獲取維修記錄列表
  getRepairRecords: async (params?: RepairQueryParams): Promise<ApiResponse<PaginatedResponse<RepairRecord>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/repair-records${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<RepairRecord>>(url);
  },

  // 獲取單個維修記錄
  getRepairRecord: async (id: number): Promise<ApiResponse<RepairRecord>> => {
    return await api.get<RepairRecord>(`/repair-records/${id}`);
  },

  // 創建維修記錄
  createRepairRecord: async (repairData: CreateRepairRecordRequest): Promise<ApiResponse<RepairRecord>> => {
    return await api.post<RepairRecord>('/repair-records', repairData);
  },

  // 更新維修記錄
  updateRepairRecord: async (id: number, repairData: UpdateRepairRecordRequest): Promise<ApiResponse<RepairRecord>> => {
    return await api.put<RepairRecord>(`/repair-records/${id}`, repairData);
  },

  // 刪除維修記錄
  deleteRepairRecord: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/repair-records/${id}`);
  },

  // 更新維修狀態
  updateRepairStatus: async (id: number, status: string, notes?: string): Promise<ApiResponse<RepairRecord>> => {
    return await api.post<RepairRecord>(`/repair-records/${id}/status`, {
      status,
      notes,
    });
  },

  // 指派技師
  assignTechnician: async (id: number, technicianId: number, notes?: string): Promise<ApiResponse<RepairRecord>> => {
    return await api.post<RepairRecord>(`/repair-records/${id}/assign`, {
      technicianId,
      notes,
    });
  },

  // 添加使用零件
  addUsedPart: async (id: number, partData: { partId: number; quantity: number; notes?: string }): Promise<ApiResponse<RepairPart>> => {
    return await api.post<RepairPart>(`/repair-records/${id}/parts`, partData);
  },

  // 移除使用零件
  removeUsedPart: async (id: number, partId: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/repair-records/${id}/parts/${partId}`);
  },

  // 上傳附件
  uploadAttachment: async (id: number, file: File): Promise<ApiResponse<RepairAttachment>> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return await api.post<RepairAttachment>(`/repair-records/${id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 刪除附件
  deleteAttachment: async (id: number, attachmentId: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/repair-records/${id}/attachments/${attachmentId}`);
  },

  // 獲取維修統計
  getRepairStatistics: async (params?: { dateFrom?: string; dateTo?: string }): Promise<ApiResponse<RepairStatistics>> => {
    const queryParams = new URLSearchParams();
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);
    
    const url = `/repair-records/statistics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<RepairStatistics>(url);
  },

  // 搜尋維修記錄
  searchRepairRecords: async (query: string): Promise<ApiResponse<RepairRecord[]>> => {
    return await api.get<RepairRecord[]>(`/repair-records/search?q=${encodeURIComponent(query)}`);
  },

  // 批量操作維修記錄
  batchUpdateRepairRecords: async (ids: number[], action: string, data?: any): Promise<ApiResponse<{ success: number; failed: number }>> => {
    return await api.post<{ success: number; failed: number }>('/repair-records/batch', {
      ids,
      action,
      data,
    });
  },
};

export default repairService;
