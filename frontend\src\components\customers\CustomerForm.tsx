import React, { useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Space, 
  message, 
  Switch,
  Row,
  Col,
  Typography 
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined, 
  HomeOutlined,
  BankOutlined,
  ContactsOutlined
} from '@ant-design/icons';
import { Customer, CreateCustomerRequest, UpdateCustomerRequest } from '../../services/customerService';

const { TextArea } = Input;
const { Text } = Typography;

interface CustomerFormProps {
  visible: boolean;
  customer?: Customer | null;
  onCancel: () => void;
  onSubmit: (data: CreateCustomerRequest | UpdateCustomerRequest) => Promise<void>;
  loading?: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  visible,
  customer,
  onCancel,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const isEditing = !!customer;

  useEffect(() => {
    if (visible) {
      if (customer) {
        // 編輯模式：填入現有數據
        form.setFieldsValue({
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
          address: customer.address,
          company: customer.company || '',
          contactPerson: customer.contactPerson || '',
          isActive: customer.isActive,
        });
      } else {
        // 新增模式：重置表單
        form.resetFields();
        form.setFieldsValue({
          isActive: true, // 預設為啟用狀態
        });
      }
    }
  }, [visible, customer, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const submitData = {
        name: values.name.trim(),
        email: values.email.trim().toLowerCase(),
        phone: values.phone.trim(),
        address: values.address.trim(),
        company: values.company?.trim() || undefined,
        contactPerson: values.contactPerson?.trim() || undefined,
        ...(isEditing && { isActive: values.isActive }),
      };

      await onSubmit(submitData);
      form.resetFields();
    } catch (error) {
      // 表單驗證失敗或提交失敗
      console.error('Form submission error:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 驗證電話號碼格式
  const validatePhone = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    
    const phoneRegex = /^(\+886|0)?[2-9]\d{1,2}-?\d{3}-?\d{3,4}$/;
    if (!phoneRegex.test(value.replace(/\s/g, ''))) {
      return Promise.reject(new Error('請輸入有效的電話號碼格式'));
    }
    
    return Promise.resolve();
  };

  // 驗證郵件唯一性（模擬）
  const validateEmailUnique = async (_: any, value: string) => {
    if (!value || (isEditing && value === customer?.email)) {
      return Promise.resolve();
    }
    
    // 這裡會調用 customerService.checkEmailAvailability
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模擬檢查結果
    const existingEmails = ['<EMAIL>', '<EMAIL>'];
    if (existingEmails.includes(value.toLowerCase())) {
      return Promise.reject(new Error('此電子郵件已被使用'));
    }
    
    return Promise.resolve();
  };

  return (
    <Modal
      title={isEditing ? '編輯客戶' : '新增客戶'}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {isEditing ? '更新' : '新增'}
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="客戶姓名"
              rules={[
                { required: true, message: '請輸入客戶姓名' },
                { min: 2, message: '姓名至少需要2個字符' },
                { max: 50, message: '姓名不能超過50個字符' },
                { pattern: /^[a-zA-Z\u4e00-\u9fa5\s]+$/, message: '姓名只能包含中文、英文和空格' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="請輸入客戶姓名"
                maxLength={50}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="email"
              label="電子郵件"
              rules={[
                { required: true, message: '請輸入電子郵件' },
                { type: 'email', message: '請輸入有效的電子郵件格式' },
                { validator: validateEmailUnique },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="請輸入電子郵件"
                maxLength={100}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="聯絡電話"
              rules={[
                { required: true, message: '請輸入聯絡電話' },
                { validator: validatePhone },
              ]}
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="例：02-1234-5678 或 0912-345-678"
                maxLength={20}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="company"
              label="公司名稱"
              rules={[
                { max: 100, message: '公司名稱不能超過100個字符' },
              ]}
            >
              <Input
                prefix={<BankOutlined />}
                placeholder="請輸入公司名稱（選填）"
                maxLength={100}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="address"
          label="聯絡地址"
          rules={[
            { required: true, message: '請輸入聯絡地址' },
            { min: 5, message: '地址至少需要5個字符' },
            { max: 200, message: '地址不能超過200個字符' },
          ]}
        >
          <TextArea
            prefix={<HomeOutlined />}
            placeholder="請輸入完整的聯絡地址"
            rows={2}
            maxLength={200}
            showCount
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="contactPerson"
              label="聯絡人"
              rules={[
                { max: 50, message: '聯絡人姓名不能超過50個字符' },
              ]}
            >
              <Input
                prefix={<ContactsOutlined />}
                placeholder="請輸入聯絡人姓名（選填）"
                maxLength={50}
              />
            </Form.Item>
          </Col>
          {isEditing && (
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="帳戶狀態"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Form.Item>
            </Col>
          )}
        </Row>

        {!isEditing && (
          <div style={{ 
            background: '#f6f6f6', 
            padding: '12px', 
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <strong>提示：</strong><br />
              • 電子郵件將作為客戶的唯一識別，請確保正確性<br />
              • 聯絡電話支援市話和手機格式<br />
              • 公司名稱和聯絡人為選填項目<br />
              • 新增的客戶預設為啟用狀態
            </Text>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default CustomerForm;
