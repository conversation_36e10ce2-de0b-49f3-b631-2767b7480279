import { body, query, param, ValidationChain } from 'express-validator';

// 創建客戶驗證
export const createCustomerValidation: ValidationChain[] = [
  body('name')
    .notEmpty()
    .withMessage('客戶名稱不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('客戶名稱長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('phone')
    .optional()
    .matches(/^[\d\s\-\+\(\)]+$/)
    .withMessage('電話號碼格式無效')
    .isLength({ max: 20 })
    .withMessage('電話號碼長度不能超過20個字符')
    .trim(),

  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址長度不能超過500個字符')
    .trim(),

  body('companyName')
    .optional()
    .isLength({ max: 200 })
    .withMessage('公司名稱長度不能超過200個字符')
    .trim(),

  body('contactPerson')
    .optional()
    .isLength({ max: 100 })
    .withMessage('聯絡人長度不能超過100個字符')
    .trim(),

  body('taxId')
    .optional()
    .matches(/^[\w\-]+$/)
    .withMessage('統一編號格式無效')
    .isLength({ max: 50 })
    .withMessage('統一編號長度不能超過50個字符')
    .trim(),

  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('備註長度不能超過1000個字符')
    .trim(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更新客戶驗證
export const updateCustomerValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('客戶ID不能為空')
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('客戶名稱長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('phone')
    .optional()
    .matches(/^[\d\s\-\+\(\)]+$/)
    .withMessage('電話號碼格式無效')
    .isLength({ max: 20 })
    .withMessage('電話號碼長度不能超過20個字符')
    .trim(),

  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('地址長度不能超過500個字符')
    .trim(),

  body('companyName')
    .optional()
    .isLength({ max: 200 })
    .withMessage('公司名稱長度不能超過200個字符')
    .trim(),

  body('contactPerson')
    .optional()
    .isLength({ max: 100 })
    .withMessage('聯絡人長度不能超過100個字符')
    .trim(),

  body('taxId')
    .optional()
    .matches(/^[\w\-]+$/)
    .withMessage('統一編號格式無效')
    .isLength({ max: 50 })
    .withMessage('統一編號長度不能超過50個字符')
    .trim(),

  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('備註長度不能超過1000個字符')
    .trim(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 客戶列表查詢驗證
export const customerListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('活躍狀態篩選必須是布林值')
    .toBoolean(),

  query('hasEmail')
    .optional()
    .isBoolean()
    .withMessage('電子郵件篩選必須是布林值')
    .toBoolean(),

  query('hasPhone')
    .optional()
    .isBoolean()
    .withMessage('電話篩選必須是布林值')
    .toBoolean(),

  query('hasCompany')
    .optional()
    .isBoolean()
    .withMessage('公司篩選必須是布林值')
    .toBoolean(),

  query('sortBy')
    .optional()
    .isIn(['name', 'email', 'phone', 'companyName', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 客戶ID參數驗證
export const customerIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('客戶ID不能為空')
    .isNumeric()
    .withMessage('客戶ID必須是數字'),
];

// 客戶搜尋驗證
export const customerSearchValidation: ValidationChain[] = [
  query('q')
    .notEmpty()
    .withMessage('搜尋關鍵字不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('結果數量限制必須在1-50之間')
    .toInt(),
];

// 批量操作驗證
export const batchOperationValidation: ValidationChain[] = [
  body('customerIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('客戶ID列表必須是包含1-100個元素的陣列'),

  body('customerIds.*')
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  body('operation')
    .notEmpty()
    .withMessage('操作類型不能為空')
    .isIn(['activate', 'deactivate', 'delete', 'export'])
    .withMessage('操作類型必須是activate、deactivate、delete或export'),

  body('data')
    .optional()
    .isObject()
    .withMessage('操作資料必須是物件'),

  body('data.isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),

  body('data.exportFormat')
    .if(body('operation').equals('export'))
    .optional()
    .isIn(['csv', 'excel', 'json'])
    .withMessage('匯出格式必須是csv、excel或json'),
];

// 電子郵件參數驗證
export const emailParamValidation: ValidationChain[] = [
  param('email')
    .notEmpty()
    .withMessage('電子郵件不能為空')
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail(),

  query('excludeId')
    .optional()
    .isNumeric()
    .withMessage('排除ID必須是數字'),
];

// 電話參數驗證
export const phoneParamValidation: ValidationChain[] = [
  param('phone')
    .notEmpty()
    .withMessage('電話號碼不能為空')
    .matches(/^[\d\s\-\+\(\)]+$/)
    .withMessage('電話號碼格式無效')
    .trim(),

  query('excludeId')
    .optional()
    .isNumeric()
    .withMessage('排除ID必須是數字'),
];

// 客戶詳細資訊查詢驗證
export const customerDetailValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('客戶ID不能為空')
    .isNumeric()
    .withMessage('客戶ID必須是數字'),

  query('includeDetails')
    .optional()
    .isBoolean()
    .withMessage('includeDetails必須是布林值')
    .toBoolean(),
];

// 自定義驗證函數：檢查聯絡方式
export const validateContactInfo = () => {
  return body().custom((value, { req }) => {
    const { email, phone } = req.body;
    
    if (!email && !phone) {
      throw new Error('至少需要提供電子郵件或電話號碼其中一項');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查電子郵件格式
export const validateEmailFormat = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 自定義驗證函數：檢查電話格式
export const validatePhoneFormat = (phone: string): boolean => {
  const phoneRegex = /^[\d\s\-\+\(\)]+$/;
  return phoneRegex.test(phone) && phone.length <= 20;
};

// 自定義驗證函數：檢查統一編號格式
export const validateTaxIdFormat = (taxId: string): boolean => {
  const taxIdRegex = /^[\w\-]+$/;
  return taxIdRegex.test(taxId) && taxId.length <= 50;
};

// 自定義驗證函數：檢查客戶名稱格式
export const validateCustomerNameFormat = (name: string): boolean => {
  return name.trim().length >= 2 && name.length <= 100;
};

// 組合驗證：創建客戶（包含聯絡方式檢查）
export const createCustomerWithContactValidation = [
  ...createCustomerValidation,
  // validateContactInfo(), // 可選：強制要求聯絡方式
];

// 組合驗證：批量操作（包含權限檢查）
export const batchOperationWithPermissionValidation = [
  ...batchOperationValidation,
  // 可以在這裡添加額外的權限檢查
];

// 驗證客戶匯入資料
export const customerImportValidation: ValidationChain[] = [
  body('customers')
    .isArray({ min: 1, max: 1000 })
    .withMessage('客戶資料必須是包含1-1000個元素的陣列'),

  body('customers.*.name')
    .notEmpty()
    .withMessage('客戶名稱不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('客戶名稱長度必須在2-100個字符之間'),

  body('customers.*.email')
    .optional()
    .isEmail()
    .withMessage('電子郵件格式無效'),

  body('customers.*.phone')
    .optional()
    .matches(/^[\d\s\-\+\(\)]+$/)
    .withMessage('電話號碼格式無效'),

  body('options')
    .optional()
    .isObject()
    .withMessage('選項必須是物件'),

  body('options.skipDuplicates')
    .optional()
    .isBoolean()
    .withMessage('skipDuplicates必須是布林值'),

  body('options.updateExisting')
    .optional()
    .isBoolean()
    .withMessage('updateExisting必須是布林值'),

  body('options.validateOnly')
    .optional()
    .isBoolean()
    .withMessage('validateOnly必須是布林值'),
];
