import { api, ApiResponse, PaginatedResponse } from './api';

// 零件相關類型定義
export interface Part {
  id: number;
  name: string;
  partNumber: string;
  category: string;
  brand?: string;
  model?: string;
  description?: string;
  unitPrice: number;
  currency: string;
  supplier?: string;
  supplierPartNumber?: string;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  minimumStock: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePartRequest {
  name: string;
  partNumber: string;
  category: string;
  brand?: string;
  model?: string;
  description?: string;
  unitPrice: number;
  currency: string;
  supplier?: string;
  supplierPartNumber?: string;
  currentStock: number;
  minimumStock: number;
}

export interface UpdatePartRequest {
  name?: string;
  partNumber?: string;
  category?: string;
  brand?: string;
  model?: string;
  description?: string;
  unitPrice?: number;
  currency?: string;
  supplier?: string;
  supplierPartNumber?: string;
  minimumStock?: number;
  isActive?: boolean;
}

export interface PartQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  brand?: string;
  supplier?: string;
  isActive?: boolean;
  lowStock?: boolean;
  outOfStock?: boolean;
  priceMin?: number;
  priceMax?: number;
  sortBy?: 'name' | 'partNumber' | 'currentStock' | 'unitPrice' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface StockOperationRequest {
  partId: number;
  type: 'IN' | 'OUT' | 'ADJUSTMENT' | 'RESERVED' | 'RELEASED';
  quantity: number;
  reason: string;
  referenceId?: string;
  notes?: string;
}

export interface StockOperationResult {
  success: boolean;
  message: string;
  newStock: number;
  operation: {
    id: number;
    type: string;
    quantity: number;
    reason: string;
    timestamp: string;
  };
}

export interface StockAlert {
  id: number;
  partId: number;
  partName: string;
  partNumber: string;
  alertType: 'LOW_STOCK' | 'CRITICAL_STOCK' | 'OUT_OF_STOCK';
  currentStock: number;
  minimumStock: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  createdAt: string;
}

export interface PartStatistics {
  total: number;
  active: number;
  inactive: number;
  lowStock: number;
  outOfStock: number;
  totalValue: number;
  averagePrice: number;
  byCategory: Array<{
    category: string;
    count: number;
    value: number;
  }>;
  byBrand: Array<{
    brand: string;
    count: number;
  }>;
  stockStatus: {
    adequate: number;
    low: number;
    critical: number;
    outOfStock: number;
  };
}

// 零件服務
export const partService = {
  // 獲取零件列表
  getParts: async (params?: PartQueryParams): Promise<ApiResponse<PaginatedResponse<Part>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/parts${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<Part>>(url);
  },

  // 獲取單個零件
  getPart: async (id: number): Promise<ApiResponse<Part>> => {
    return await api.get<Part>(`/parts/${id}`);
  },

  // 創建零件
  createPart: async (partData: CreatePartRequest): Promise<ApiResponse<Part>> => {
    return await api.post<Part>('/parts', partData);
  },

  // 更新零件
  updatePart: async (id: number, partData: UpdatePartRequest): Promise<ApiResponse<Part>> => {
    return await api.put<Part>(`/parts/${id}`, partData);
  },

  // 刪除零件
  deletePart: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/parts/${id}`);
  },

  // 激活零件
  activatePart: async (id: number): Promise<ApiResponse<Part>> => {
    return await api.post<Part>(`/parts/${id}/activate`);
  },

  // 停用零件
  deactivatePart: async (id: number): Promise<ApiResponse<Part>> => {
    return await api.post<Part>(`/parts/${id}/deactivate`);
  },

  // 搜尋零件
  searchParts: async (query: string): Promise<ApiResponse<Part[]>> => {
    return await api.get<Part[]>(`/parts/search?q=${encodeURIComponent(query)}`);
  },

  // 獲取零件統計
  getPartStatistics: async (): Promise<ApiResponse<PartStatistics>> => {
    return await api.get<PartStatistics>('/parts/statistics');
  },

  // 檢查零件編號是否可用
  checkPartNumberAvailability: async (partNumber: string, excludeId?: number): Promise<ApiResponse<{ available: boolean }>> => {
    const params = new URLSearchParams({ partNumber });
    if (excludeId) {
      params.append('excludeId', excludeId.toString());
    }
    return await api.get<{ available: boolean }>(`/parts/check-part-number?${params.toString()}`);
  },

  // 庫存操作
  performStockOperation: async (operation: StockOperationRequest): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>('/parts/stock-operation', operation);
  },

  // 入庫操作
  stockIn: async (partId: number, quantity: number, reason: string, referenceId?: string): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>(`/parts/${partId}/stock-in`, {
      quantity,
      reason,
      referenceId,
    });
  },

  // 出庫操作
  stockOut: async (partId: number, quantity: number, reason: string, referenceId?: string): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>(`/parts/${partId}/stock-out`, {
      quantity,
      reason,
      referenceId,
    });
  },

  // 庫存調整
  adjustStock: async (partId: number, quantity: number, reason: string): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>(`/parts/${partId}/adjust-stock`, {
      quantity,
      reason,
    });
  },

  // 預留庫存
  reserveStock: async (partId: number, quantity: number, reason: string, referenceId?: string): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>(`/parts/${partId}/reserve-stock`, {
      quantity,
      reason,
      referenceId,
    });
  },

  // 釋放預留庫存
  releaseStock: async (partId: number, quantity: number, reason: string, referenceId?: string): Promise<ApiResponse<StockOperationResult>> => {
    return await api.post<StockOperationResult>(`/parts/${partId}/release-stock`, {
      quantity,
      reason,
      referenceId,
    });
  },

  // 獲取庫存警報
  getStockAlerts: async (): Promise<ApiResponse<StockAlert[]>> => {
    return await api.get<StockAlert[]>('/parts/stock-alerts');
  },

  // 批量操作零件
  batchUpdateParts: async (ids: number[], action: 'activate' | 'deactivate' | 'delete'): Promise<ApiResponse<{ success: number; failed: number }>> => {
    return await api.post<{ success: number; failed: number }>('/parts/batch', {
      ids,
      action,
    });
  },
};

export default partService;
