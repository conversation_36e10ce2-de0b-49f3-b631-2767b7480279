import React from 'react';

const TestApp: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontSize: '24px',
      textAlign: 'center'
    }}>
      <div>
        <h1>🎉 React 應用正常運行！</h1>
        <p>如果您看到這個頁面，表示 React 前端已經成功啟動</p>
        <button 
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
          onClick={() => {
            localStorage.setItem('authToken', 'test-token');
            localStorage.setItem('userInfo', JSON.stringify({
              id: 1,
              email: '<EMAIL>',
              name: '測試用戶',
              role: 'ADMIN'
            }));
            alert('模擬登入成功！');
          }}
        >
          測試模擬登入
        </button>
      </div>
    </div>
  );
};

export default TestApp;
