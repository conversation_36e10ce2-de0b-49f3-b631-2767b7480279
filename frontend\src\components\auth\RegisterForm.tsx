import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Alert, Select, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, UserAddOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { registerAsync, clearError } from '../../store/slices/authSlice';
import { RegisterRequest } from '../../services/authService';

const { Title, Text } = Typography;
const { Option } = Select;

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: 'CUSTOMER_SERVICE' | 'TECHNICIAN' | 'VIEWER';
}

const RegisterForm: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  
  const { isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // 如果已經登入，重定向到儀表板
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    // 清除錯誤信息
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleSubmit = async (values: RegisterFormData) => {
    const registerData: RegisterRequest = {
      name: values.name,
      email: values.email,
      password: values.password,
      role: values.role,
    };

    try {
      const result = await dispatch(registerAsync(registerData));
      if (registerAsync.fulfilled.match(result)) {
        // 註冊成功，重定向到登入頁面
        navigate('/login', { 
          state: { 
            message: '註冊成功！請使用您的帳號登入。',
            email: values.email 
          } 
        });
      }
    } catch (error) {
      // 錯誤已經在 slice 中處理
    }
  };

  const validateConfirmPassword = (_: any, value: string) => {
    if (!value || form.getFieldValue('password') === value) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('兩次輸入的密碼不一致'));
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 450,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <UserAddOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            用戶註冊
          </Title>
          <Text type="secondary">
            客退維修品記錄管理系統
          </Text>
        </div>

        {error && (
          <Alert
            message="註冊失敗"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => dispatch(clearError())}
            style={{ marginBottom: '24px' }}
          />
        )}

        <Form
          form={form}
          name="register"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[
              { required: true, message: '請輸入姓名' },
              { min: 2, message: '姓名至少需要2個字符' },
              { max: 50, message: '姓名不能超過50個字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="請輸入姓名"
              autoComplete="name"
            />
          </Form.Item>

          <Form.Item
            name="email"
            label="電子郵件"
            rules={[
              { required: true, message: '請輸入電子郵件' },
              { type: 'email', message: '請輸入有效的電子郵件格式' },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="請輸入電子郵件"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="role"
            label="角色"
            rules={[
              { required: true, message: '請選擇角色' },
            ]}
          >
            <Select placeholder="請選擇您的角色">
              <Option value="VIEWER">查詢用戶 - 僅能查看資料</Option>
              <Option value="TECHNICIAN">維修人員 - 可管理維修記錄</Option>
              <Option value="CUSTOMER_SERVICE">客服人員 - 可管理客戶和維修</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="password"
            label="密碼"
            rules={[
              { required: true, message: '請輸入密碼' },
              { min: 6, message: '密碼至少需要6個字符' },
              { max: 50, message: '密碼不能超過50個字符' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: '密碼必須包含大小寫字母和數字',
              },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="請輸入密碼"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="確認密碼"
            dependencies={['password']}
            rules={[
              { required: true, message: '請確認密碼' },
              { validator: validateConfirmPassword },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="請再次輸入密碼"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              {isLoading ? '註冊中...' : '註冊'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>或</Divider>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            已經有帳號？{' '}
            <Link to="/login" style={{ color: '#1890ff' }}>
              立即登入
            </Link>
          </Text>
        </div>

        <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f6f6f6', borderRadius: '6px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <strong>角色說明：</strong><br />
            • 查詢用戶：僅能查看維修記錄和統計資料<br />
            • 維修人員：可管理維修記錄和零件使用<br />
            • 客服人員：可管理客戶資料和維修流程<br />
            • 系統管理員：由現有管理員指派
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default RegisterForm;
