import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, Avatar, Space, Divider, Alert, Modal, message } from 'antd';
import { UserOutlined, EditOutlined, LockOutlined, SaveOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { updateProfileAsync, changePasswordAsync, clearError } from '../../store/slices/authSlice';
import { UpdateProfileRequest, ChangePasswordRequest } from '../../services/authService';

const { Title, Text } = Typography;

interface ProfileFormData {
  name: string;
  email: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const UserProfile: React.FC = () => {
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const dispatch = useAppDispatch();
  
  const { user, isLoading, error } = useAppSelector((state) => state.auth);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);

  // 初始化表單數據
  React.useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        name: user.name,
        email: user.email,
      });
    }
  }, [user, profileForm]);

  const handleUpdateProfile = async (values: ProfileFormData) => {
    const updateData: UpdateProfileRequest = {
      name: values.name,
      email: values.email,
    };

    try {
      const result = await dispatch(updateProfileAsync(updateData));
      if (updateProfileAsync.fulfilled.match(result)) {
        message.success('個人資料更新成功');
        setIsEditingProfile(false);
      }
    } catch (error) {
      // 錯誤已經在 slice 中處理
    }
  };

  const handleChangePassword = async (values: PasswordFormData) => {
    const passwordData: ChangePasswordRequest = {
      currentPassword: values.currentPassword,
      newPassword: values.newPassword,
    };

    try {
      setPasswordLoading(true);
      const result = await dispatch(changePasswordAsync(passwordData));
      if (changePasswordAsync.fulfilled.match(result)) {
        message.success('密碼修改成功');
        setIsPasswordModalVisible(false);
        passwordForm.resetFields();
      }
    } catch (error) {
      // 錯誤已經在 slice 中處理
    } finally {
      setPasswordLoading(false);
    }
  };

  const validateConfirmPassword = (_: any, value: string) => {
    if (!value || passwordForm.getFieldValue('newPassword') === value) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('兩次輸入的密碼不一致'));
  };

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      'VIEWER': '查詢用戶',
      'TECHNICIAN': '維修人員',
      'CUSTOMER_SERVICE': '客服人員',
      'ADMIN': '系統管理員',
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  const getRoleColor = (role: string) => {
    const roleColors = {
      'VIEWER': '#87d068',
      'TECHNICIAN': '#108ee9',
      'CUSTOMER_SERVICE': '#f50',
      'ADMIN': '#722ed1',
    };
    return roleColors[role as keyof typeof roleColors] || '#666';
  };

  if (!user) {
    return (
      <Alert
        message="無法載入用戶資訊"
        description="請重新登入"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        個人資料
      </Title>

      <div style={{ maxWidth: 800 }}>
        {/* 基本資訊卡片 */}
        <Card 
          title="基本資訊" 
          extra={
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              onClick={() => setIsEditingProfile(!isEditingProfile)}
            >
              {isEditingProfile ? '取消編輯' : '編輯資料'}
            </Button>
          }
          style={{ marginBottom: 24 }}
        >
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
            <Avatar 
              size={80} 
              icon={<UserOutlined />} 
              style={{ marginRight: 24 }}
            />
            <div>
              <Title level={4} style={{ margin: 0 }}>
                {user.name}
              </Title>
              <Text type="secondary" style={{ fontSize: 16 }}>
                {user.email}
              </Text>
              <br />
              <Text 
                style={{ 
                  color: getRoleColor(user.role),
                  fontWeight: 'bold',
                  fontSize: 14
                }}
              >
                {getRoleDisplayName(user.role)}
              </Text>
            </div>
          </div>

          {error && (
            <Alert
              message="更新失敗"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => dispatch(clearError())}
              style={{ marginBottom: 16 }}
            />
          )}

          <Form
            form={profileForm}
            layout="vertical"
            onFinish={handleUpdateProfile}
            disabled={!isEditingProfile}
          >
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '請輸入姓名' },
                { min: 2, message: '姓名至少需要2個字符' },
                { max: 50, message: '姓名不能超過50個字符' },
              ]}
            >
              <Input placeholder="請輸入姓名" />
            </Form.Item>

            <Form.Item
              name="email"
              label="電子郵件"
              rules={[
                { required: true, message: '請輸入電子郵件' },
                { type: 'email', message: '請輸入有效的電子郵件格式' },
              ]}
            >
              <Input placeholder="請輸入電子郵件" />
            </Form.Item>

            {isEditingProfile && (
              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={isLoading}
                    icon={<SaveOutlined />}
                  >
                    保存更改
                  </Button>
                  <Button onClick={() => setIsEditingProfile(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            )}
          </Form>
        </Card>

        {/* 帳戶資訊卡片 */}
        <Card title="帳戶資訊" style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>帳戶狀態：</Text>
              <Text style={{ color: user.isActive ? '#52c41a' : '#f5222d', marginLeft: 8 }}>
                {user.isActive ? '正常' : '已停用'}
              </Text>
            </div>
            <Button 
              type="default" 
              icon={<LockOutlined />}
              onClick={() => setIsPasswordModalVisible(true)}
            >
              修改密碼
            </Button>
          </div>
          
          <Divider />
          
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Text type="secondary">註冊時間：</Text>
              <Text>{new Date(user.createdAt).toLocaleString('zh-TW')}</Text>
            </div>
            <div>
              <Text type="secondary">最後更新：</Text>
              <Text>{new Date(user.updatedAt).toLocaleString('zh-TW')}</Text>
            </div>
          </div>
        </Card>
      </div>

      {/* 修改密碼模態框 */}
      <Modal
        title="修改密碼"
        open={isPasswordModalVisible}
        onCancel={() => {
          setIsPasswordModalVisible(false);
          passwordForm.resetFields();
          dispatch(clearError());
        }}
        footer={null}
        destroyOnClose
      >
        {error && (
          <Alert
            message="修改密碼失敗"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => dispatch(clearError())}
            style={{ marginBottom: 16 }}
          />
        )}

        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="currentPassword"
            label="當前密碼"
            rules={[
              { required: true, message: '請輸入當前密碼' },
            ]}
          >
            <Input.Password placeholder="請輸入當前密碼" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密碼"
            rules={[
              { required: true, message: '請輸入新密碼' },
              { min: 6, message: '密碼至少需要6個字符' },
              { max: 50, message: '密碼不能超過50個字符' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: '密碼必須包含大小寫字母和數字',
              },
            ]}
          >
            <Input.Password placeholder="請輸入新密碼" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="確認新密碼"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '請確認新密碼' },
              { validator: validateConfirmPassword },
            ]}
          >
            <Input.Password placeholder="請再次輸入新密碼" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button 
                onClick={() => {
                  setIsPasswordModalVisible(false);
                  passwordForm.resetFields();
                  dispatch(clearError());
                }}
              >
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={passwordLoading}
              >
                確認修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserProfile;
