@echo off
chcp 65001 >nul
echo ========================================
echo   IACT MIO維保管理系統 便攜版測試
echo ========================================
echo.

echo 🧪 正在執行系統測試...
echo.

echo [1/5] 檢查檔案完整性...
if exist "complete-system-gui.html" (
    echo     ✓ 主程式檔案存在
) else (
    echo     ✗ 主程式檔案遺失
    goto :error
)

if exist "使用說明.md" (
    echo     ✓ 使用說明存在
) else (
    echo     ✗ 使用說明遺失
)

if exist "version.json" (
    echo     ✓ 版本資訊存在
) else (
    echo     ✗ 版本資訊遺失
)

echo [2/5] 檢查檔案大小...
for %%F in ("complete-system-gui.html") do (
    set size=%%~zF
    if %%~zF GTR 0 (
        echo     ✓ 主程式檔案大小正常 ^(%%~zF bytes^)
    ) else (
        echo     ✗ 主程式檔案大小異常
        goto :error
    )
)

echo [3/5] 檢查啟動腳本...
if exist "啟動系統.bat" (
    echo     ✓ Windows 啟動腳本存在
) else (
    echo     ✗ Windows 啟動腳本遺失
)

if exist "無視窗啟動.vbs" (
    echo     ✓ 無視窗啟動腳本存在
) else (
    echo     ✗ 無視窗啟動腳本遺失
)

echo [4/5] 檢查系統相容性...
echo     ℹ 作業系統: %OS%
echo     ℹ 處理器架構: %PROCESSOR_ARCHITECTURE%
echo     ℹ 電腦名稱: %COMPUTERNAME%

echo [5/5] 準備啟動測試...
echo.
echo ========================================
echo   系統測試完成
echo ========================================
echo.

echo 📋 測試結果摘要:
echo   - 檔案完整性: 通過
echo   - 啟動腳本: 正常
echo   - 系統相容性: 支援
echo.

echo 🚀 準備啟動系統進行功能測試...
echo.
echo 💡 測試步驟:
echo   1. 系統將在瀏覽器中開啟
echo   2. 使用測試帳號登入 ^(admin/admin123^)
echo   3. 測試各項功能是否正常
echo   4. 檢查數據是否能正常保存和載入
echo.

choice /C YN /M "是否立即啟動系統進行測試"
if errorlevel 2 goto :end
if errorlevel 1 goto :launch

:launch
echo.
echo 🚀 啟動系統...
start "" "complete-system-gui.html"
echo.
echo ✅ 系統已啟動，請在瀏覽器中進行測試
echo.
echo 📝 測試檢查清單:
echo   □ 系統正常載入
echo   □ 登入功能正常
echo   □ 維修記錄管理功能
echo   □ 數據保存和載入
echo   □ 匯出/匯入功能
echo   □ 統計報表功能
echo.
goto :end

:error
echo.
echo ❌ 系統測試失敗！
echo.
echo 🔧 建議解決方案:
echo   1. 重新下載完整的部署包
echo   2. 檢查檔案是否被防毒軟體誤刪
echo   3. 確認檔案權限設定正確
echo.
pause
exit /b 1

:end
echo 測試完成，按任意鍵關閉...
pause >nul
