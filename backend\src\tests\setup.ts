import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// 載入測試環境變數
dotenv.config({ path: '.env.test' });

// 設定測試環境
process.env.NODE_ENV = 'test';

// 全域測試設定
beforeAll(async () => {
  // 可以在這裡設定全域測試初始化
});

afterAll(async () => {
  // 清理全域資源
});

// 擴展 Jest 匹配器
expect.extend({
  toBeValidDate(received) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },
});

// 類型聲明
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidDate(): R;
    }
  }
}
