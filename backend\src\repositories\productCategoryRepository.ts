import { PrismaClient, ProductCategory } from '@prisma/client';
import { 
  ProductCategoryInfo,
  CreateProductCategoryRequest,
  UpdateProductCategoryRequest,
  ProductCategoryQueryParams,
  ProductCategoryListResponse,
  ProductCategoryStatistics,
  ProductCategoryTree,
  DEFAULT_PRODUCT_PAGINATION
} from '../types/product';
import { logger } from '../utils/logger';

export class ProductCategoryRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找產品類別
  async findById(id: string): Promise<ProductCategoryInfo | null> {
    try {
      const category = await this.prisma.productCategory.findUnique({
        where: { id: BigInt(id) },
        include: {
          parent: true,
          children: true,
        },
      });

      if (!category) return null;

      return {
        id: category.id.toString(),
        name: category.name,
        description: category.description,
        parentId: category.parentId?.toString(),
        parent: category.parent ? {
          id: category.parent.id.toString(),
          name: category.parent.name,
          description: category.parent.description,
          parentId: category.parent.parentId?.toString(),
          isActive: category.parent.isActive,
          createdAt: category.parent.createdAt,
          updatedAt: category.parent.updatedAt,
        } : undefined,
        children: category.children?.map(child => ({
          id: child.id.toString(),
          name: child.name,
          description: child.description,
          parentId: child.parentId?.toString(),
          isActive: child.isActive,
          createdAt: child.createdAt,
          updatedAt: child.updatedAt,
        })),
        isActive: category.isActive,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
      };
    } catch (error) {
      logger.error('查找產品類別失敗:', error);
      throw new Error('查找產品類別失敗');
    }
  }

  // 根據名稱查找產品類別
  async findByName(name: string): Promise<ProductCategory | null> {
    try {
      return await this.prisma.productCategory.findFirst({
        where: { name },
      });
    } catch (error) {
      logger.error('根據名稱查找產品類別失敗:', error);
      throw new Error('查找產品類別失敗');
    }
  }

  // 檢查產品類別是否存在
  async checkExistence(name: string, excludeId?: string): Promise<boolean> {
    try {
      const whereCondition = excludeId 
        ? { 
            name,
            NOT: { id: BigInt(excludeId) }
          }
        : { name };

      const existingCategory = await this.prisma.productCategory.findFirst({
        where: whereCondition,
        select: { id: true },
      });

      return !!existingCategory;
    } catch (error) {
      logger.error('檢查產品類別存在性失敗:', error);
      throw new Error('檢查產品類別存在性失敗');
    }
  }

  // 創建產品類別
  async create(categoryData: CreateProductCategoryRequest): Promise<ProductCategoryInfo> {
    try {
      const createData: any = {
        name: categoryData.name,
        description: categoryData.description,
        isActive: categoryData.isActive ?? true,
      };

      if (categoryData.parentId) {
        createData.parentId = BigInt(categoryData.parentId);
      }

      const category = await this.prisma.productCategory.create({
        data: createData,
        include: {
          parent: true,
          children: true,
        },
      });

      logger.info('產品類別創建成功:', { categoryId: category.id, name: category.name });

      return {
        id: category.id.toString(),
        name: category.name,
        description: category.description,
        parentId: category.parentId?.toString(),
        parent: category.parent ? {
          id: category.parent.id.toString(),
          name: category.parent.name,
          description: category.parent.description,
          parentId: category.parent.parentId?.toString(),
          isActive: category.parent.isActive,
          createdAt: category.parent.createdAt,
          updatedAt: category.parent.updatedAt,
        } : undefined,
        children: category.children?.map(child => ({
          id: child.id.toString(),
          name: child.name,
          description: child.description,
          parentId: child.parentId?.toString(),
          isActive: child.isActive,
          createdAt: child.createdAt,
          updatedAt: child.updatedAt,
        })),
        isActive: category.isActive,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
      };
    } catch (error) {
      logger.error('創建產品類別失敗:', error);
      throw new Error('創建產品類別失敗');
    }
  }

  // 更新產品類別
  async update(id: string, categoryData: UpdateProductCategoryRequest): Promise<ProductCategoryInfo> {
    try {
      const updateData: any = { ...categoryData };
      if (categoryData.parentId) {
        updateData.parentId = BigInt(categoryData.parentId);
      }

      const category = await this.prisma.productCategory.update({
        where: { id: BigInt(id) },
        data: updateData,
        include: {
          parent: true,
          children: true,
        },
      });

      logger.info('產品類別更新成功:', { categoryId: category.id, name: category.name });

      return {
        id: category.id.toString(),
        name: category.name,
        description: category.description,
        parentId: category.parentId?.toString(),
        parent: category.parent ? {
          id: category.parent.id.toString(),
          name: category.parent.name,
          description: category.parent.description,
          parentId: category.parent.parentId?.toString(),
          isActive: category.parent.isActive,
          createdAt: category.parent.createdAt,
          updatedAt: category.parent.updatedAt,
        } : undefined,
        children: category.children?.map(child => ({
          id: child.id.toString(),
          name: child.name,
          description: child.description,
          parentId: child.parentId?.toString(),
          isActive: child.isActive,
          createdAt: child.createdAt,
          updatedAt: child.updatedAt,
        })),
        isActive: category.isActive,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
      };
    } catch (error) {
      logger.error('更新產品類別失敗:', error);
      throw new Error('更新產品類別失敗');
    }
  }

  // 刪除產品類別（軟刪除）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.productCategory.update({
        where: { id: BigInt(id) },
        data: { isActive: false },
      });

      logger.info('產品類別軟刪除成功:', { categoryId: id });
    } catch (error) {
      logger.error('軟刪除產品類別失敗:', error);
      throw new Error('刪除產品類別失敗');
    }
  }

  // 硬刪除產品類別
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.productCategory.delete({
        where: { id: BigInt(id) },
      });

      logger.info('產品類別硬刪除成功:', { categoryId: id });
    } catch (error) {
      logger.error('硬刪除產品類別失敗:', error);
      throw new Error('刪除產品類別失敗');
    }
  }

  // 獲取產品類別列表
  async findMany(params: ProductCategoryQueryParams): Promise<ProductCategoryListResponse> {
    try {
      const {
        page = DEFAULT_PRODUCT_PAGINATION.page,
        limit = DEFAULT_PRODUCT_PAGINATION.limit,
        search,
        parentId,
        isActive,
        includeChildren = false,
        sortBy = 'name',
        sortOrder = 'asc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_PRODUCT_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (parentId !== undefined) {
        if (parentId === null || parentId === '') {
          where.parentId = null;
        } else {
          where.parentId = BigInt(parentId);
        }
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      // 執行查詢
      const [categories, total] = await Promise.all([
        this.prisma.productCategory.findMany({
          where,
          include: {
            parent: true,
            children: includeChildren,
            _count: {
              select: {
                products: true,
              },
            },
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.productCategory.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        categories: categories.map(category => ({
          id: category.id.toString(),
          name: category.name,
          description: category.description,
          parentId: category.parentId?.toString(),
          parent: category.parent ? {
            id: category.parent.id.toString(),
            name: category.parent.name,
            description: category.parent.description,
            parentId: category.parent.parentId?.toString(),
            isActive: category.parent.isActive,
            createdAt: category.parent.createdAt,
            updatedAt: category.parent.updatedAt,
          } : undefined,
          children: category.children?.map(child => ({
            id: child.id.toString(),
            name: child.name,
            description: child.description,
            parentId: child.parentId?.toString(),
            isActive: child.isActive,
            createdAt: child.createdAt,
            updatedAt: child.updatedAt,
          })),
          isActive: category.isActive,
          createdAt: category.createdAt,
          updatedAt: category.updatedAt,
        })),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          parentId,
          isActive,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取產品類別列表失敗:', error);
      throw new Error('獲取產品類別列表失敗');
    }
  }

  // 獲取產品類別樹狀結構
  async getCategoryTree(): Promise<ProductCategoryTree[]> {
    try {
      const categories = await this.prisma.productCategory.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
        orderBy: { name: 'asc' },
      });

      // 建立樹狀結構
      const categoryMap = new Map<string, ProductCategoryTree>();
      const rootCategories: ProductCategoryTree[] = [];

      // 初始化所有類別
      categories.forEach(category => {
        categoryMap.set(category.id.toString(), {
          id: category.id.toString(),
          name: category.name,
          description: category.description,
          isActive: category.isActive,
          productCount: category._count.products,
          children: [],
        });
      });

      // 建立父子關係
      categories.forEach(category => {
        const categoryNode = categoryMap.get(category.id.toString())!;
        
        if (category.parentId) {
          const parentNode = categoryMap.get(category.parentId.toString());
          if (parentNode) {
            parentNode.children.push(categoryNode);
          }
        } else {
          rootCategories.push(categoryNode);
        }
      });

      return rootCategories;
    } catch (error) {
      logger.error('獲取產品類別樹狀結構失敗:', error);
      throw new Error('獲取產品類別樹狀結構失敗');
    }
  }

  // 獲取產品類別統計
  async getStatistics(): Promise<ProductCategoryStatistics> {
    try {
      const [total, active, rootCount, withProducts, topCategories] = await Promise.all([
        this.prisma.productCategory.count(),
        this.prisma.productCategory.count({ where: { isActive: true } }),
        this.prisma.productCategory.count({ where: { parentId: null } }),
        this.getCategoriesWithProducts(),
        this.getTopCategories(),
      ]);

      const subCategories = total - rootCount;
      const categoriesWithProducts = withProducts.length;
      const categoriesWithoutProducts = total - categoriesWithProducts;
      const averageProductsPerCategory = categoriesWithProducts > 0 
        ? withProducts.reduce((sum, cat) => sum + cat.productCount, 0) / categoriesWithProducts
        : 0;

      return {
        total,
        active,
        inactive: total - active,
        rootCategories: rootCount,
        subCategories,
        categoriesWithProducts,
        categoriesWithoutProducts,
        averageProductsPerCategory: Math.round(averageProductsPerCategory * 100) / 100,
        topCategories,
      };
    } catch (error) {
      logger.error('獲取產品類別統計失敗:', error);
      throw new Error('獲取產品類別統計失敗');
    }
  }

  // 獲取有產品的類別
  private async getCategoriesWithProducts() {
    const categoriesWithProducts = await this.prisma.productCategory.findMany({
      where: {
        products: {
          some: {},
        },
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return categoriesWithProducts.map(category => ({
      categoryId: category.id.toString(),
      categoryName: category.name,
      productCount: category._count.products,
    }));
  }

  // 獲取頂級類別
  private async getTopCategories() {
    const topCategories = await this.prisma.productCategory.findMany({
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        products: {
          _count: 'desc',
        },
      },
      take: 10,
    });

    return topCategories.map(category => ({
      categoryId: category.id.toString(),
      categoryName: category.name,
      productCount: category._count.products,
    }));
  }
}
