// 客戶基本資訊介面
export interface CustomerInfo {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  companyName?: string;
  contactPerson?: string;
  taxId?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 創建客戶請求介面
export interface CreateCustomerRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  companyName?: string;
  contactPerson?: string;
  taxId?: string;
  notes?: string;
  isActive?: boolean;
}

// 更新客戶請求介面
export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  companyName?: string;
  contactPerson?: string;
  taxId?: string;
  notes?: string;
  isActive?: boolean;
}

// 客戶查詢參數介面
export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  hasEmail?: boolean;
  hasPhone?: boolean;
  hasCompany?: boolean;
  sortBy?: 'name' | 'email' | 'phone' | 'companyName' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 客戶列表響應介面
export interface CustomerListResponse {
  customers: CustomerInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    isActive?: boolean;
    hasEmail?: boolean;
    hasPhone?: boolean;
    hasCompany?: boolean;
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 客戶統計介面
export interface CustomerStatistics {
  total: number;
  active: number;
  inactive: number;
  withEmail: number;
  withPhone: number;
  withCompany: number;
  withoutContact: number;
  recentCustomers: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  topCustomers: Array<{
    id: string;
    name: string;
    repairCount: number;
    totalAmount?: number;
  }>;
}

// 客戶搜尋結果介面
export interface CustomerSearchResult {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  companyName?: string;
  contactPerson?: string;
  isActive: boolean;
  relevanceScore: number;
  repairCount?: number;
}

// 客戶詳細資訊介面（包含關聯資料）
export interface CustomerDetailInfo extends CustomerInfo {
  repairRecords: Array<{
    id: string;
    repairNumber: string;
    productModel: string;
    status: string;
    createdAt: Date;
    completedAt?: Date;
  }>;
  statistics: {
    totalRepairs: number;
    completedRepairs: number;
    pendingRepairs: number;
    totalAmount: number;
    averageRepairTime: number;
  };
}

// 批量操作請求介面
export interface BatchCustomerOperation {
  customerIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'export';
  data?: {
    isActive?: boolean;
    exportFormat?: 'csv' | 'excel' | 'json';
  };
}

// 批量操作結果介面
export interface BatchOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    customerId: string;
    error: string;
  }>;
  exportUrl?: string;
}

// 客戶驗證結果介面
export interface CustomerValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 客戶匯入請求介面
export interface CustomerImportRequest {
  customers: CreateCustomerRequest[];
  options: {
    skipDuplicates: boolean;
    updateExisting: boolean;
    validateOnly: boolean;
  };
}

// 客戶匯入結果介面
export interface CustomerImportResult {
  total: number;
  created: number;
  updated: number;
  skipped: number;
  failed: number;
  errors: Array<{
    row: number;
    customer: CreateCustomerRequest;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    customer: CreateCustomerRequest;
    warning: string;
  }>;
}

// 客戶聯絡方式類型
export enum ContactType {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  ADDRESS = 'ADDRESS',
  COMPANY = 'COMPANY',
}

// 客戶類型枚舉
export enum CustomerType {
  INDIVIDUAL = 'INDIVIDUAL',
  COMPANY = 'COMPANY',
  GOVERNMENT = 'GOVERNMENT',
  EDUCATION = 'EDUCATION',
}

// 客戶狀態枚舉
export enum CustomerStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  BLACKLISTED = 'BLACKLISTED',
}

// 客戶排序選項
export const CUSTOMER_SORT_OPTIONS = [
  { value: 'name', label: '客戶名稱' },
  { value: 'email', label: '電子郵件' },
  { value: 'phone', label: '電話號碼' },
  { value: 'companyName', label: '公司名稱' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 客戶篩選選項
export const CUSTOMER_FILTER_OPTIONS = {
  status: [
    { value: true, label: '活躍' },
    { value: false, label: '停用' },
  ],
  contactInfo: [
    { value: 'hasEmail', label: '有電子郵件' },
    { value: 'hasPhone', label: '有電話' },
    { value: 'hasCompany', label: '有公司資訊' },
  ],
  customerType: [
    { value: CustomerType.INDIVIDUAL, label: '個人客戶' },
    { value: CustomerType.COMPANY, label: '企業客戶' },
    { value: CustomerType.GOVERNMENT, label: '政府機關' },
    { value: CustomerType.EDUCATION, label: '教育機構' },
  ],
} as const;

// 預設分頁設定
export const DEFAULT_CUSTOMER_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const;

// 客戶搜尋配置
export const CUSTOMER_SEARCH_CONFIG = {
  minSearchLength: 2,
  maxSearchLength: 100,
  searchFields: ['name', 'email', 'phone', 'companyName', 'contactPerson'],
  fuzzySearchThreshold: 0.6,
} as const;

// 客戶驗證規則
export const CUSTOMER_VALIDATION_RULES = {
  name: {
    minLength: 2,
    maxLength: 100,
    required: true,
  },
  email: {
    maxLength: 100,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    required: false,
  },
  phone: {
    maxLength: 20,
    pattern: /^[\d\s\-\+\(\)]+$/,
    required: false,
  },
  address: {
    maxLength: 500,
    required: false,
  },
  companyName: {
    maxLength: 200,
    required: false,
  },
  contactPerson: {
    maxLength: 100,
    required: false,
  },
  taxId: {
    maxLength: 50,
    pattern: /^[\w\-]+$/,
    required: false,
  },
  notes: {
    maxLength: 1000,
    required: false,
  },
} as const;

// 客戶匯出格式
export const CUSTOMER_EXPORT_FORMATS = {
  csv: {
    mimeType: 'text/csv',
    extension: 'csv',
    headers: [
      'ID', '客戶名稱', '電子郵件', '電話', '地址', 
      '公司名稱', '聯絡人', '統一編號', '備註', '狀態', '創建時間'
    ],
  },
  excel: {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xlsx',
    sheetName: '客戶資料',
  },
  json: {
    mimeType: 'application/json',
    extension: 'json',
  },
} as const;

// 客戶活動類型
export enum CustomerActivityType {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  ACTIVATED = 'ACTIVATED',
  DEACTIVATED = 'DEACTIVATED',
  REPAIR_CREATED = 'REPAIR_CREATED',
  REPAIR_COMPLETED = 'REPAIR_COMPLETED',
  CONTACT_UPDATED = 'CONTACT_UPDATED',
}

// 客戶活動記錄介面
export interface CustomerActivity {
  id: string;
  customerId: string;
  type: CustomerActivityType;
  description: string;
  metadata?: Record<string, any>;
  performedBy?: string;
  createdAt: Date;
}

// 客戶關係介面
export interface CustomerRelationship {
  id: string;
  customerId: string;
  relatedCustomerId: string;
  relationshipType: 'PARENT_COMPANY' | 'SUBSIDIARY' | 'PARTNER' | 'BRANCH';
  description?: string;
  createdAt: Date;
}

// 客戶標籤介面
export interface CustomerTag {
  id: string;
  name: string;
  color: string;
  description?: string;
  createdAt: Date;
}

// 客戶標籤關聯介面
export interface CustomerTagAssignment {
  customerId: string;
  tagId: string;
  assignedBy: string;
  assignedAt: Date;
}
