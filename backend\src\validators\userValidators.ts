import { body, query, param, ValidationChain } from 'express-validator';
import { UserRole } from '@prisma/client';

// 創建用戶驗證
export const createUserValidation: ValidationChain[] = [
  body('username')
    .notEmpty()
    .withMessage('用戶名不能為空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用戶名長度必須在3-50個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線')
    .trim(),

  body('email')
    .notEmpty()
    .withMessage('電子郵件不能為空')
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('fullName')
    .notEmpty()
    .withMessage('姓名不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('role')
    .notEmpty()
    .withMessage('角色不能為空')
    .isIn(Object.values(UserRole))
    .withMessage('角色必須是有效的角色類型'),

  body('password')
    .optional()
    .isLength({ min: 8, max: 128 })
    .withMessage('密碼長度必須在8-128個字符之間')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字符'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更新用戶驗證
export const updateUserValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('用戶ID不能為空')
    .isNumeric()
    .withMessage('用戶ID必須是數字'),

  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),

  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('角色必須是有效的角色類型'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 用戶列表查詢驗證
export const userListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('角色篩選必須是有效的角色類型'),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('活躍狀態篩選必須是布林值')
    .toBoolean(),

  query('sortBy')
    .optional()
    .isIn(['username', 'email', 'fullName', 'role', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 用戶ID參數驗證
export const userIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('用戶ID不能為空')
    .isNumeric()
    .withMessage('用戶ID必須是數字'),
];

// 用戶搜尋驗證
export const userSearchValidation: ValidationChain[] = [
  query('q')
    .notEmpty()
    .withMessage('搜尋關鍵字不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('結果數量限制必須在1-50之間')
    .toInt(),
];

// 批量操作驗證
export const batchOperationValidation: ValidationChain[] = [
  body('userIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('用戶ID列表必須是包含1-100個元素的陣列'),

  body('userIds.*')
    .isNumeric()
    .withMessage('用戶ID必須是數字'),

  body('operation')
    .notEmpty()
    .withMessage('操作類型不能為空')
    .isIn(['activate', 'deactivate', 'delete', 'updateRole'])
    .withMessage('操作類型必須是activate、deactivate、delete或updateRole'),

  body('data')
    .optional()
    .isObject()
    .withMessage('操作資料必須是物件'),

  body('data.role')
    .if(body('operation').equals('updateRole'))
    .notEmpty()
    .withMessage('更新角色操作必須提供角色')
    .isIn(Object.values(UserRole))
    .withMessage('角色必須是有效的角色類型'),

  body('data.isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更改角色驗證
export const changeRoleValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('用戶ID不能為空')
    .isNumeric()
    .withMessage('用戶ID必須是數字'),

  body('role')
    .notEmpty()
    .withMessage('角色不能為空')
    .isIn(Object.values(UserRole))
    .withMessage('角色必須是有效的角色類型'),
];

// 更新個人資料驗證
export const updateProfileValidation: ValidationChain[] = [
  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名長度必須在2-100個字符之間')
    .trim(),

  body('email')
    .optional()
    .isEmail()
    .withMessage('請提供有效的電子郵件地址')
    .normalizeEmail()
    .isLength({ max: 100 })
    .withMessage('電子郵件長度不能超過100個字符'),
];

// 檢查用戶名可用性驗證
export const checkUsernameValidation: ValidationChain[] = [
  param('username')
    .notEmpty()
    .withMessage('用戶名不能為空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用戶名長度必須在3-50個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線')
    .trim(),

  query('excludeId')
    .optional()
    .isNumeric()
    .withMessage('排除ID必須是數字'),
];

// 自定義驗證函數：檢查角色權限
export const validateRolePermission = (requiredRole: UserRole) => {
  return body('role').custom((value, { req }) => {
    const userRole = req.user?.role as UserRole;
    
    // 只有管理員可以設置管理員角色
    if (value === UserRole.ADMIN && userRole !== UserRole.ADMIN) {
      throw new Error('只有管理員可以設置管理員角色');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查批量操作權限
export const validateBatchOperationPermission = () => {
  return body('operation').custom((value, { req }) => {
    const userRole = req.user?.role as UserRole;
    
    // 只有管理員可以執行刪除操作
    if (value === 'delete' && userRole !== UserRole.ADMIN) {
      throw new Error('只有管理員可以執行刪除操作');
    }
    
    // 只有管理員可以批量更改角色
    if (value === 'updateRole' && userRole !== UserRole.ADMIN) {
      throw new Error('只有管理員可以批量更改角色');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查用戶名格式
export const validateUsernameFormat = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  return usernameRegex.test(username) && username.length >= 3 && username.length <= 50;
};

// 自定義驗證函數：檢查電子郵件格式
export const validateEmailFormat = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 100;
};

// 自定義驗證函數：檢查姓名格式
export const validateFullNameFormat = (fullName: string): boolean => {
  return fullName.length >= 2 && fullName.length <= 100;
};

// 組合驗證：創建用戶（包含權限檢查）
export const createUserWithPermissionValidation = [
  ...createUserValidation,
  validateRolePermission(UserRole.ADMIN),
];

// 組合驗證：批量操作（包含權限檢查）
export const batchOperationWithPermissionValidation = [
  ...batchOperationValidation,
  validateBatchOperationPermission(),
];
