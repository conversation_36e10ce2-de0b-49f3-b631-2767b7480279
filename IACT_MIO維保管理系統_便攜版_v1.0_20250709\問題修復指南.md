# IACT MIO維保管理系統 問題修復指南

## 🚨 常見問題和解決方案

### 問題一：系統功能問題

#### 症狀
- 系統無法正常載入
- 功能按鈕無反應
- 頁面顯示不完整

#### 解決方案
1. **重新整理頁面**
   ```
   按 F5 或 Ctrl+R 重新整理頁面
   ```

2. **檢查瀏覽器版本**
   ```
   確保使用以下瀏覽器版本：
   - Chrome 80+
   - Firefox 75+
   - Edge 80+
   - Safari 13+
   ```

3. **清除瀏覽器快取**
   ```
   Chrome: Ctrl+Shift+Delete
   Firefox: Ctrl+Shift+Delete
   Edge: Ctrl+Shift+Delete
   ```

4. **使用診斷工具**
   ```
   雙擊 "診斷工具.bat" 執行系統診斷
   ```

### 問題二：數據載入問題

#### 症狀
- 之前保存的數據消失
- 新增的記錄無法保存
- 系統顯示"使用預設數據"

#### 解決方案
1. **檢查 LocalStorage 支援**
   ```javascript
   // 在瀏覽器控制台執行
   console.log('LocalStorage 支援:', typeof(Storage) !== "undefined");
   ```

2. **檢查瀏覽器設定**
   ```
   確保瀏覽器允許本地存儲：
   - Chrome: 設定 → 隱私權和安全性 → Cookie 和其他網站資料
   - Firefox: 設定 → 隱私權與安全性 → Cookie 和網站資料
   ```

3. **手動恢復數據**
   ```
   如果有備份檔案：
   1. 進入系統設定
   2. 點擊 "📥 匯入數據"
   3. 選擇備份的 JSON 檔案
   ```

4. **重新初始化系統**
   ```javascript
   // 在瀏覽器控制台執行
   localStorage.clear();
   location.reload();
   ```

### 問題三：頁面顯示問題

#### 症狀
- 頁面佈局混亂
- 文字顯示異常
- 按鈕位置錯誤

#### 解決方案
1. **檢查螢幕解析度**
   ```
   建議最低解析度：1024x768
   最佳解析度：1920x1080 或更高
   ```

2. **調整瀏覽器縮放**
   ```
   設定瀏覽器縮放為 100%：
   - 按 Ctrl+0 重設縮放
   - 或在瀏覽器設定中調整
   ```

3. **使用相容模式**
   ```
   如果使用 IE 瀏覽器：
   - 按 F12 開啟開發者工具
   - 切換到 Edge 模式
   ```

### 問題四：登入問題

#### 症狀
- 無法登入系統
- 登入後頁面空白
- 帳號密碼錯誤

#### 解決方案
1. **確認登入資訊**
   ```
   管理員帳號：
   - 用戶名：admin
   - 密碼：admin123
   
   客服帳號：
   - 用戶名：service
   - 密碼：service123
   ```

2. **檢查大小寫**
   ```
   確保用戶名和密碼的大小寫正確
   ```

3. **清除登入狀態**
   ```javascript
   // 在瀏覽器控制台執行
   localStorage.removeItem('currentUser');
   location.reload();
   ```

## 🔧 診斷工具使用

### 啟動診斷工具
1. **Windows 用戶**
   ```
   雙擊 "診斷工具.bat"
   ```

2. **其他平台用戶**
   ```
   雙擊 "系統診斷工具.html"
   ```

### 診斷項目
- ✅ 瀏覽器相容性檢查
- ✅ 檔案完整性檢查
- ✅ 數據存儲檢查
- ✅ 系統功能檢查

### 診斷結果解讀
- **✅ 綠色**：正常，無問題
- **⚠️ 橙色**：警告，可能有問題
- **❌ 紅色**：錯誤，需要修復

## 🛠️ 進階修復

### 完全重置系統
```javascript
// 在瀏覽器控制台執行以下代碼
// ⚠️ 警告：這會清除所有數據

// 1. 清除所有 LocalStorage 數據
for (let key in localStorage) {
    if (key.startsWith('maintenance_') || 
        key.startsWith('sharepoint') || 
        key === 'storageMode') {
        localStorage.removeItem(key);
    }
}

// 2. 重新載入頁面
location.reload();
```

### 手動數據備份
```javascript
// 在瀏覽器控制台執行以下代碼
// 匯出所有數據

const backupData = {};
for (let key in localStorage) {
    if (key.startsWith('maintenance_')) {
        backupData[key] = localStorage.getItem(key);
    }
}

console.log('備份數據:', JSON.stringify(backupData, null, 2));
// 複製控制台輸出的 JSON 數據保存為檔案
```

### 手動數據恢復
```javascript
// 在瀏覽器控制台執行以下代碼
// 恢復備份數據

const restoreData = {
    // 貼上備份的 JSON 數據
};

Object.keys(restoreData).forEach(key => {
    localStorage.setItem(key, restoreData[key]);
});

location.reload();
```

## 📞 技術支援

### 系統檢查清單
在聯絡技術支援前，請完成以下檢查：

- [ ] 已執行系統診斷工具
- [ ] 已嘗試重新整理頁面
- [ ] 已檢查瀏覽器版本
- [ ] 已清除瀏覽器快取
- [ ] 已確認登入資訊正確

### 錯誤資訊收集
如果問題持續存在，請收集以下資訊：

1. **瀏覽器資訊**
   ```javascript
   // 在控制台執行
   console.log('瀏覽器:', navigator.userAgent);
   ```

2. **錯誤訊息**
   ```
   按 F12 開啟開發者工具
   查看 Console 標籤中的錯誤訊息
   截圖錯誤訊息
   ```

3. **系統狀態**
   ```javascript
   // 在控制台執行
   runSystemDiagnostics();
   ```

### 聯絡方式
- **技術文檔**：查看 使用說明.md
- **診斷工具**：使用 系統診斷工具.html
- **問題回報**：提供詳細的錯誤資訊和重現步驟

## 🔄 預防措施

### 定期維護
1. **定期備份數據**
   ```
   建議每週使用系統內建的匯出功能備份數據
   ```

2. **保持瀏覽器更新**
   ```
   定期更新瀏覽器到最新版本
   ```

3. **避免清除瀏覽器數據**
   ```
   清除瀏覽器數據時要小心，避免清除 LocalStorage
   ```

### 最佳實踐
1. **使用推薦瀏覽器**
   ```
   推薦使用 Chrome 或 Firefox 最新版本
   ```

2. **保持檔案完整**
   ```
   不要修改或刪除系統檔案
   ```

3. **定期檢查系統**
   ```
   定期執行診斷工具檢查系統狀態
   ```

## 🎯 快速修復

### 5分鐘快速修復
1. **重新整理頁面** (F5)
2. **執行診斷工具** (雙擊 診斷工具.bat)
3. **檢查診斷結果**
4. **根據建議修復問題**

### 緊急恢復
如果系統完全無法使用：
1. **重新下載系統檔案**
2. **清除所有瀏覽器數據**
3. **重新啟動系統**
4. **從備份恢復數據**

---

**💡 提示**：大多數問題都可以通過重新整理頁面或執行診斷工具來解決。如果問題持續存在，請按照本指南逐步排除問題。
