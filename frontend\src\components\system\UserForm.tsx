import React from 'react';
import { Modal, Typography } from 'antd';
import { SystemUser } from '../../services/systemService';

const { Title } = Typography;

interface UserFormProps {
  visible: boolean;
  user?: SystemUser | null;
  onCancel: () => void;
  onSubmit: (data: any) => Promise<void>;
}

const UserForm: React.FC<UserFormProps> = ({ visible, user, onCancel, onSubmit }) => {
  return (
    <Modal
      title={user ? '編輯用戶' : '新增用戶'}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Title level={4}>用戶表單功能開發中...</Title>
      <p>將包含以下功能：</p>
      <ul>
        <li>基本資訊編輯</li>
        <li>角色選擇</li>
        <li>權限設定</li>
        <li>密碼設定</li>
      </ul>
    </Modal>
  );
};

export default UserForm;
