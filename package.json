{"name": "iact-mio-maintenance-system", "version": "1.0.0", "description": "IACT MIO維保管理系統 - 專業的維修記錄管理系統", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "build": "echo 'Building deployment package...' && node build.js", "test": "echo 'Running tests...' && node test.js"}, "keywords": ["maintenance", "repair", "management", "system", "維修管理", "維保系統"], "author": "IACT MIO", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/iact-mio-maintenance-system.git"}, "bugs": {"url": "https://github.com/your-username/iact-mio-maintenance-system/issues"}, "homepage": "https://github.com/your-username/iact-mio-maintenance-system#readme"}