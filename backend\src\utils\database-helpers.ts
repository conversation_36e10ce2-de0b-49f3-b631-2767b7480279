import { PrismaClient } from '@prisma/client';
import { logger } from './logger';

// 資料庫工具函數
export class DatabaseHelpers {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 生成維修單號
  async generateRepairNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const datePrefix = `R${year}${month}${day}`;

    // 查找今天最大的序號
    const lastRecord = await this.prisma.repairRecord.findFirst({
      where: {
        repairNumber: {
          startsWith: datePrefix,
        },
      },
      orderBy: {
        repairNumber: 'desc',
      },
    });

    let nextSequence = 1;
    if (lastRecord) {
      const lastSequence = parseInt(lastRecord.repairNumber.slice(-3));
      nextSequence = lastSequence + 1;
    }

    return `${datePrefix}${String(nextSequence).padStart(3, '0')}`;
  }

  // 檢查維修單號是否存在
  async isRepairNumberExists(repairNumber: string): Promise<boolean> {
    const record = await this.prisma.repairRecord.findUnique({
      where: { repairNumber },
    });
    return !!record;
  }

  // 獲取用戶統計
  async getUserStatistics() {
    const [total, active, byRole] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({ where: { isActive: true } }),
      this.prisma.user.groupBy({
        by: ['role'],
        _count: true,
      }),
    ]);

    return {
      total,
      active,
      inactive: total - active,
      byRole: byRole.reduce((acc, item) => {
        acc[item.role] = item._count;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  // 獲取維修記錄統計
  async getRepairStatistics() {
    const [total, byStatus, byPriority] = await Promise.all([
      this.prisma.repairRecord.count(),
      this.prisma.repairRecord.groupBy({
        by: ['status'],
        _count: true,
      }),
      this.prisma.repairRecord.groupBy({
        by: ['priority'],
        _count: true,
      }),
    ]);

    return {
      total,
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {} as Record<string, number>),
      byPriority: byPriority.reduce((acc, item) => {
        acc[item.priority] = item._count;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  // 獲取客戶統計
  async getCustomerStatistics() {
    const [total, withEmail, withPhone, withCompany] = await Promise.all([
      this.prisma.customer.count(),
      this.prisma.customer.count({ where: { email: { not: null } } }),
      this.prisma.customer.count({ where: { phone: { not: null } } }),
      this.prisma.customer.count({ where: { company: { not: null } } }),
    ]);

    return {
      total,
      withEmail,
      withPhone,
      withCompany,
    };
  }

  // 獲取產品統計
  async getProductStatistics() {
    const [total, byCategory, byBrand] = await Promise.all([
      this.prisma.product.count(),
      this.prisma.product.groupBy({
        by: ['categoryId'],
        _count: true,
        include: {
          category: {
            select: { name: true },
          },
        },
      }),
      this.prisma.product.groupBy({
        by: ['brand'],
        _count: true,
        where: { brand: { not: null } },
      }),
    ]);

    return {
      total,
      byCategory: byCategory.reduce((acc, item) => {
        const categoryName = item.category?.name || 'Unknown';
        acc[categoryName] = item._count;
        return acc;
      }, {} as Record<string, number>),
      byBrand: byBrand.reduce((acc, item) => {
        acc[item.brand || 'Unknown'] = item._count;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  // 獲取零件庫存統計
  async getPartsStatistics() {
    const [total, lowStock, outOfStock, totalValue] = await Promise.all([
      this.prisma.part.count(),
      this.prisma.part.count({
        where: {
          stockQuantity: {
            lte: this.prisma.part.fields.minimumStock,
          },
        },
      }),
      this.prisma.part.count({ where: { stockQuantity: 0 } }),
      this.prisma.part.aggregate({
        _sum: {
          stockQuantity: true,
        },
      }),
    ]);

    return {
      total,
      lowStock,
      outOfStock,
      totalQuantity: totalValue._sum.stockQuantity || 0,
    };
  }

  // 獲取完整的儀表板統計
  async getDashboardStatistics() {
    try {
      const [users, repairs, customers, products, parts] = await Promise.all([
        this.getUserStatistics(),
        this.getRepairStatistics(),
        this.getCustomerStatistics(),
        this.getProductStatistics(),
        this.getPartsStatistics(),
      ]);

      return {
        users,
        repairs,
        customers,
        products,
        parts,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('獲取儀表板統計失敗:', error);
      throw error;
    }
  }

  // 清理測試資料
  async cleanupTestData() {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('清理測試資料只能在測試環境中執行');
    }

    logger.info('🧹 清理測試資料...');

    await this.prisma.$transaction([
      this.prisma.sharePointSyncLog.deleteMany(),
      this.prisma.sharePointFile.deleteMany(),
      this.prisma.repairProgress.deleteMany(),
      this.prisma.repairPart.deleteMany(),
      this.prisma.repairStatusHistory.deleteMany(),
      this.prisma.repairRecord.deleteMany(),
      this.prisma.part.deleteMany(),
      this.prisma.product.deleteMany(),
      this.prisma.productCategory.deleteMany(),
      this.prisma.customer.deleteMany(),
      this.prisma.systemSetting.deleteMany(),
      this.prisma.user.deleteMany(),
    ]);

    logger.info('✅ 測試資料清理完成');
  }

  // 重置自動遞增ID
  async resetAutoIncrement() {
    if (process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test') {
      throw new Error('重置自動遞增ID只能在開發或測試環境中執行');
    }

    const tables = [
      'users',
      'customers',
      'product_categories',
      'products',
      'repair_records',
      'repair_status_history',
      'parts',
      'repair_parts',
      'repair_progress',
      'sharepoint_files',
      'sharepoint_sync_log',
      'system_settings',
    ];

    for (const table of tables) {
      await this.prisma.$executeRawUnsafe(`ALTER TABLE ${table} AUTO_INCREMENT = 1`);
    }

    logger.info('✅ 自動遞增ID重置完成');
  }

  // 驗證資料完整性
  async validateDataIntegrity() {
    const issues: string[] = [];

    // 檢查孤立的維修記錄
    const orphanedRepairs = await this.prisma.repairRecord.count({
      where: {
        OR: [
          { customer: null },
          { product: null },
          { createdByUser: null },
        ],
      },
    });

    if (orphanedRepairs > 0) {
      issues.push(`發現 ${orphanedRepairs} 個孤立的維修記錄`);
    }

    // 檢查無效的產品類別關聯
    const invalidProducts = await this.prisma.product.count({
      where: {
        categoryId: { not: null },
        category: null,
      },
    });

    if (invalidProducts > 0) {
      issues.push(`發現 ${invalidProducts} 個無效的產品類別關聯`);
    }

    // 檢查負庫存
    const negativeStock = await this.prisma.part.count({
      where: { stockQuantity: { lt: 0 } },
    });

    if (negativeStock > 0) {
      issues.push(`發現 ${negativeStock} 個負庫存零件`);
    }

    return {
      isValid: issues.length === 0,
      issues,
      checkedAt: new Date().toISOString(),
    };
  }
}

// 建立全域實例
export const createDatabaseHelpers = (prisma: PrismaClient) => {
  return new DatabaseHelpers(prisma);
};
