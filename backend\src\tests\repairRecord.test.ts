import request from 'supertest';
import { PrismaClient, UserRole } from '@prisma/client';
import app from '../index';
import { PasswordUtils } from '../utils/password';
import { JWTUtils } from '../utils/jwt';

const prisma = new PrismaClient();

describe('Repair Record Management API', () => {
  let adminToken: string;
  let customerServiceToken: string;
  let technicianToken: string;
  let viewerToken: string;
  let testUsers: any[] = [];
  let testCustomers: any[] = [];
  let testProducts: any[] = [];
  let testParts: any[] = [];
  let testRepairRecords: any[] = [];

  beforeAll(async () => {
    // 建立測試用戶
    const adminPassword = await PasswordUtils.hashPassword('AdminPass123!');
    const csPassword = await PasswordUtils.hashPassword('CSPass123!');
    const techPassword = await PasswordUtils.hashPassword('TechPass123!');
    const viewerPassword = await PasswordUtils.hashPassword('ViewerPass123!');

    const admin = await prisma.user.create({
      data: {
        username: 'testadmin_repair',
        email: '<EMAIL>',
        passwordHash: adminPassword,
        fullName: 'Test Admin Repair',
        role: UserRole.ADMIN,
      },
    });

    const customerService = await prisma.user.create({
      data: {
        username: 'testcs_repair',
        email: '<EMAIL>',
        passwordHash: csPassword,
        fullName: 'Test Customer Service Repair',
        role: UserRole.CUSTOMER_SERVICE,
      },
    });

    const technician = await prisma.user.create({
      data: {
        username: 'testtech_repair',
        email: '<EMAIL>',
        passwordHash: techPassword,
        fullName: 'Test Technician Repair',
        role: UserRole.TECHNICIAN,
      },
    });

    const viewer = await prisma.user.create({
      data: {
        username: 'testviewer_repair',
        email: '<EMAIL>',
        passwordHash: viewerPassword,
        fullName: 'Test Viewer Repair',
        role: UserRole.VIEWER,
      },
    });

    testUsers = [admin, customerService, technician, viewer];

    // 生成令牌
    adminToken = JWTUtils.generateAccessToken({
      userId: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: admin.role,
    });

    customerServiceToken = JWTUtils.generateAccessToken({
      userId: customerService.id.toString(),
      username: customerService.username,
      email: customerService.email,
      role: customerService.role,
    });

    technicianToken = JWTUtils.generateAccessToken({
      userId: technician.id.toString(),
      username: technician.username,
      email: technician.email,
      role: technician.role,
    });

    viewerToken = JWTUtils.generateAccessToken({
      userId: viewer.id.toString(),
      username: viewer.username,
      email: viewer.email,
      role: viewer.role,
    });

    // 建立測試客戶
    const customer1 = await prisma.customer.create({
      data: {
        name: 'Test Customer 1',
        phone: '0912345678',
        email: '<EMAIL>',
        address: 'Test Address 1',
      },
    });

    const customer2 = await prisma.customer.create({
      data: {
        name: 'Test Customer 2',
        phone: '0987654321',
        email: '<EMAIL>',
        address: 'Test Address 2',
      },
    });

    testCustomers = [customer1, customer2];

    // 建立測試產品類別
    const category = await prisma.productCategory.create({
      data: {
        name: 'Test Category',
        description: 'Test category for repair records',
      },
    });

    // 建立測試產品
    const product1 = await prisma.product.create({
      data: {
        name: 'Test Product 1',
        model: 'TP001',
        brand: 'Test Brand',
        categoryId: category.id,
        warrantyPeriod: 12,
      },
    });

    const product2 = await prisma.product.create({
      data: {
        name: 'Test Product 2',
        model: 'TP002',
        brand: 'Test Brand',
        categoryId: category.id,
        warrantyPeriod: 24,
      },
    });

    testProducts = [product1, product2];

    // 建立測試零件
    const part1 = await prisma.part.create({
      data: {
        name: 'Test Part 1',
        partNumber: 'TPR001',
        category: 'Electronic',
        brand: 'Test Brand',
        unitPrice: 100,
        currency: 'TWD',
        currentStock: 50,
        minimumStock: 10,
      },
    });

    const part2 = await prisma.part.create({
      data: {
        name: 'Test Part 2',
        partNumber: 'TPR002',
        category: 'Mechanical',
        brand: 'Test Brand',
        unitPrice: 200,
        currency: 'TWD',
        currentStock: 30,
        minimumStock: 5,
      },
    });

    testParts = [part1, part2];

    // 建立測試維修記錄
    const repairRecord1 = await prisma.repairRecord.create({
      data: {
        repairNumber: 'R20240101001',
        customerId: customer1.id,
        productId: product1.id,
        serialNumber: 'SN001',
        issueDescription: 'Test issue description 1',
        symptoms: ['symptom1', 'symptom2'],
        priority: 'NORMAL',
        status: 'RECEIVED',
        assignedTechnicianId: technician.id,
        estimatedCost: 500,
        warrantyStatus: 'IN_WARRANTY',
        receivedDate: new Date(),
        createdBy: customerService.id.toString(),
      },
    });

    const repairRecord2 = await prisma.repairRecord.create({
      data: {
        repairNumber: 'R20240101002',
        customerId: customer2.id,
        productId: product2.id,
        issueDescription: 'Test issue description 2',
        priority: 'HIGH',
        status: 'IN_PROGRESS',
        estimatedCost: 800,
        warrantyStatus: 'OUT_OF_WARRANTY',
        receivedDate: new Date(),
        startedDate: new Date(),
        createdBy: customerService.id.toString(),
      },
    });

    const repairRecord3 = await prisma.repairRecord.create({
      data: {
        repairNumber: 'R20240101003',
        customerId: customer1.id,
        productId: product1.id,
        issueDescription: 'Test issue description 3',
        priority: 'URGENT',
        status: 'COMPLETED',
        estimatedCost: 300,
        actualCost: 350,
        warrantyStatus: 'IN_WARRANTY',
        receivedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        startedDate: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
        completedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        createdBy: customerService.id.toString(),
      },
    });

    testRepairRecords = [repairRecord1, repairRecord2, repairRecord3];
  });

  afterAll(async () => {
    // 清理測試資料
    for (const record of testRepairRecords) {
      await prisma.repairStatusHistory.deleteMany({ where: { repairRecordId: record.id } });
      await prisma.repairTimelineEvent.deleteMany({ where: { repairRecordId: record.id } });
      await prisma.repairRecord.delete({ where: { id: record.id } }).catch(() => {});
    }

    for (const part of testParts) {
      await prisma.stockTransaction.deleteMany({ where: { partId: part.id } });
      await prisma.part.delete({ where: { id: part.id } }).catch(() => {});
    }

    for (const product of testProducts) {
      await prisma.product.delete({ where: { id: product.id } }).catch(() => {});
    }

    for (const customer of testCustomers) {
      await prisma.customer.delete({ where: { id: customer.id } }).catch(() => {});
    }

    for (const user of testUsers) {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }

    // 清理其他測試維修記錄
    await prisma.repairStatusHistory.deleteMany({
      where: {
        repairRecord: {
          repairNumber: { startsWith: 'R2024' },
        },
      },
    });

    await prisma.repairTimelineEvent.deleteMany({
      where: {
        repairRecord: {
          repairNumber: { startsWith: 'R2024' },
        },
      },
    });

    await prisma.repairRecord.deleteMany({
      where: {
        repairNumber: { startsWith: 'R2024' },
      },
    });

    await prisma.$disconnect();
  });

  describe('GET /api/v1/repair-records', () => {
    test('should get repair record list as admin', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('repairRecords');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.repairRecords)).toBe(true);
    });

    test('should get repair record list as technician', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records')
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('repairRecords');
    });

    test('should get repair record list with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.repairRecords.length).toBeLessThanOrEqual(1);
    });

    test('should filter repair records by status', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records?status=IN_PROGRESS')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.repairRecords.every((record: any) => 
        record.status === 'IN_PROGRESS'
      )).toBe(true);
    });

    test('should filter repair records by priority', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records?priority=HIGH')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.repairRecords.every((record: any) => 
        record.priority === 'HIGH'
      )).toBe(true);
    });

    test('should filter repair records by assigned technician', async () => {
      const response = await request(app)
        .get(`/api/v1/repair-records?assignedTechnicianId=${testUsers[2].id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.repairRecords.every((record: any) => 
        record.assignedTechnicianId === testUsers[2].id.toString()
      )).toBe(true);
    });

    test('should fail without authentication', async () => {
      await request(app)
        .get('/api/v1/repair-records')
        .expect(401);
    });

    test('should fail with insufficient permissions', async () => {
      await request(app)
        .get('/api/v1/repair-records')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/repair-records', () => {
    test('should create repair record as admin', async () => {
      const recordData = {
        customerId: testCustomers[0].id.toString(),
        productId: testProducts[0].id.toString(),
        serialNumber: 'SN_NEW_001',
        issueDescription: 'New test issue description',
        symptoms: ['new symptom 1', 'new symptom 2'],
        priority: 'NORMAL',
        assignedTechnicianId: testUsers[2].id.toString(),
        estimatedCost: 600,
        warrantyStatus: 'IN_WARRANTY',
        notes: 'Test notes',
      };

      const response = await request(app)
        .post('/api/v1/repair-records')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(recordData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.customerId).toBe(recordData.customerId);
      expect(response.body.data.repairRecord.productId).toBe(recordData.productId);
      expect(response.body.data.repairRecord.status).toBe('RECEIVED');
      expect(response.body.data.repairRecord.repairNumber).toMatch(/^R\d{8}\d{4}$/);
    });

    test('should create repair record as customer service', async () => {
      const recordData = {
        customerId: testCustomers[1].id.toString(),
        productId: testProducts[1].id.toString(),
        issueDescription: 'Customer service test issue',
        priority: 'HIGH',
        estimatedCost: 400,
        warrantyStatus: 'OUT_OF_WARRANTY',
      };

      const response = await request(app)
        .post('/api/v1/repair-records')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(recordData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.customerId).toBe(recordData.customerId);
    });

    test('should fail with invalid data', async () => {
      const recordData = {
        customerId: testCustomers[0].id.toString(),
        productId: testProducts[0].id.toString(),
        issueDescription: 'Short', // 太短
        estimatedCost: -100, // 負數
      };

      await request(app)
        .post('/api/v1/repair-records')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(recordData)
        .expect(400);
    });

    test('should fail without permission', async () => {
      const recordData = {
        customerId: testCustomers[0].id.toString(),
        productId: testProducts[0].id.toString(),
        issueDescription: 'Unauthorized repair record creation',
      };

      await request(app)
        .post('/api/v1/repair-records')
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(recordData)
        .expect(403);
    });
  });

  describe('GET /api/v1/repair-records/:id', () => {
    test('should get repair record by id', async () => {
      const recordId = testRepairRecords[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/repair-records/${recordId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.id).toBe(recordId);
      expect(response.body.data.repairRecord.repairNumber).toBe(testRepairRecords[0].repairNumber);
    });

    test('should get repair record with details', async () => {
      const recordId = testRepairRecords[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/repair-records/${recordId}?includeDetails=true`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord).toHaveProperty('customer');
      expect(response.body.data.repairRecord).toHaveProperty('product');
      expect(response.body.data.repairRecord).toHaveProperty('statusHistory');
      expect(response.body.data.repairRecord).toHaveProperty('timelineEvents');
    });

    test('should fail with non-existent repair record', async () => {
      await request(app)
        .get('/api/v1/repair-records/999999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/v1/repair-records/:id', () => {
    test('should update repair record as admin', async () => {
      const recordId = testRepairRecords[0].id.toString();
      const updateData = {
        issueDescription: 'Updated issue description',
        priority: 'HIGH',
        estimatedCost: 700,
      };

      const response = await request(app)
        .put(`/api/v1/repair-records/${recordId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.issueDescription).toBe(updateData.issueDescription);
      expect(response.body.data.repairRecord.priority).toBe(updateData.priority);
    });

    test('should update repair record as customer service', async () => {
      const recordId = testRepairRecords[1].id.toString();
      const updateData = {
        notes: 'Updated by customer service',
        estimatedCost: 900,
      };

      const response = await request(app)
        .put(`/api/v1/repair-records/${recordId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.notes).toBe(updateData.notes);
    });

    test('should fail without permission', async () => {
      const recordId = testRepairRecords[0].id.toString();
      const updateData = {
        issueDescription: 'Unauthorized update',
      };

      await request(app)
        .put(`/api/v1/repair-records/${recordId}`)
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('GET /api/v1/repair-records/search', () => {
    test('should search repair records', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/search?q=Test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(Array.isArray(response.body.data.results)).toBe(true);
    });

    test('should search repair records by repair number', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/search?q=R20240101001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.results.length).toBeGreaterThan(0);
      expect(response.body.data.results[0].repairNumber).toBe('R20240101001');
    });

    test('should fail with short search query', async () => {
      await request(app)
        .get('/api/v1/repair-records/search?q=A')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/repair-records/statistics', () => {
    test('should get repair record statistics', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('byStatus');
      expect(response.body.data.statistics).toHaveProperty('byPriority');
      expect(response.body.data.statistics).toHaveProperty('byTechnician');
      expect(response.body.data.statistics).toHaveProperty('averageRepairTime');
      expect(response.body.data.statistics).toHaveProperty('completionRate');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/repair-records/statistics')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('PUT /api/v1/repair-records/:id/status', () => {
    test('should update repair status as technician', async () => {
      const recordId = testRepairRecords[0].id.toString();
      const statusData = {
        status: 'DIAGNOSED',
        reason: 'Diagnosis completed',
        notes: 'Found the issue',
      };

      const response = await request(app)
        .put(`/api/v1/repair-records/${recordId}/status`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .send(statusData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.status).toBe('DIAGNOSED');
    });

    test('should fail with invalid status transition', async () => {
      const recordId = testRepairRecords[2].id.toString(); // COMPLETED status
      const statusData = {
        status: 'RECEIVED', // Invalid transition
        reason: 'Invalid transition test',
      };

      await request(app)
        .put(`/api/v1/repair-records/${recordId}/status`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .send(statusData)
        .expect(400);
    });
  });

  describe('POST /api/v1/repair-records/:id/assign-technician', () => {
    test('should assign technician as customer service', async () => {
      const recordId = testRepairRecords[1].id.toString();
      const assignData = {
        technicianId: testUsers[2].id.toString(),
      };

      const response = await request(app)
        .post(`/api/v1/repair-records/${recordId}/assign-technician`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(assignData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.assignedTechnicianId).toBe(assignData.technicianId);
    });

    test('should fail without permission', async () => {
      const recordId = testRepairRecords[0].id.toString();
      const assignData = {
        technicianId: testUsers[2].id.toString(),
      };

      await request(app)
        .post(`/api/v1/repair-records/${recordId}/assign-technician`)
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(assignData)
        .expect(403);
    });
  });

  describe('GET /api/v1/repair-records/repair-number/:repairNumber', () => {
    test('should find repair record by repair number', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/repair-number/R20240101001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.repairRecord.repairNumber).toBe('R20240101001');
    });

    test('should fail with non-existent repair number', async () => {
      await request(app)
        .get('/api/v1/repair-records/repair-number/NONEXISTENT')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/repair-records/my-assigned', () => {
    test('should get my assigned repair records as technician', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/my-assigned')
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('repairRecords');
      // 應該只包含指派給該技師的維修記錄
      expect(response.body.data.repairRecords.every((record: any) => 
        record.assignedTechnicianId === testUsers[2].id.toString()
      )).toBe(true);
    });
  });

  describe('GET /api/v1/repair-records/overdue', () => {
    test('should get overdue repair records', async () => {
      const response = await request(app)
        .get('/api/v1/repair-records/overdue')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('repairRecords');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/repair-records/overdue')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('DELETE /api/v1/repair-records/:id', () => {
    test('should delete repair record as admin', async () => {
      // 先創建一個測試維修記錄用於刪除
      const testRecord = await prisma.repairRecord.create({
        data: {
          repairNumber: 'R20240101999',
          customerId: testCustomers[0].id,
          productId: testProducts[0].id,
          issueDescription: 'Record to delete',
          status: 'CANCELLED', // 只有取消狀態的記錄才能刪除
          createdBy: testUsers[1].id.toString(),
        },
      });

      const response = await request(app)
        .delete(`/api/v1/repair-records/${testRecord.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should fail without admin permission', async () => {
      const recordId = testRepairRecords[0].id.toString();

      await request(app)
        .delete(`/api/v1/repair-records/${recordId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .expect(403);
    });
  });
});
