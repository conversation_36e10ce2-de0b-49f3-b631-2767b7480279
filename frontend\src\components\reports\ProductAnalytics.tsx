import React from 'react';
import { Card, Typography } from 'antd';
import { StatisticsQueryParams } from '../../services/statisticsService';

const { Title } = Typography;

interface ProductAnalyticsProps {
  filters: StatisticsQueryParams;
}

const ProductAnalytics: React.FC<ProductAnalyticsProps> = ({ filters }) => {
  return (
    <Card>
      <Title level={3}>產品分析</Title>
      <p>產品分析功能開發中...</p>
      <ul>
        <li>產品故障率分析</li>
        <li>產品維修成本分析</li>
        <li>產品生命週期分析</li>
        <li>品牌維修統計</li>
      </ul>
    </Card>
  );
};

export default ProductAnalytics;
