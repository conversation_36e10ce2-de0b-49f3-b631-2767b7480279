@echo off
chcp 65001 >nul
title IACT MIO維保管理系統 - React版本啟動工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ⚛️ React 維修管理系統                      ║
echo ║                     快速啟動工具                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MAIN_MENU
echo 請選擇啟動選項：
echo.
echo [1] 🚀 啟動完整系統 (前端 + 後端)
echo [2] 🔧 僅啟動後端服務
echo [3] ⚛️ 僅啟動前端服務
echo [4] 🔍 開啟偵錯工具
echo [5] 📚 開啟 API 文檔
echo [6] 🛠️ 系統維護選項
echo [7] ❓ 查看使用說明
echo [0] ❌ 退出
echo.
set /p choice="請輸入選項 (0-7): "

if "%choice%"=="1" goto START_FULL_SYSTEM
if "%choice%"=="2" goto START_BACKEND
if "%choice%"=="3" goto START_FRONTEND
if "%choice%"=="4" goto OPEN_DEBUG_TOOL
if "%choice%"=="5" goto OPEN_API_DOCS
if "%choice%"=="6" goto MAINTENANCE_MENU
if "%choice%"=="7" goto SHOW_HELP
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:START_FULL_SYSTEM
echo.
echo 🚀 正在啟動完整系統...
echo.

REM 檢查 Node.js
call :CHECK_NODEJS
if %errorlevel% neq 0 goto MAIN_MENU

echo [1/4] 檢查後端依賴...
cd /d "%~dp0backend"
if not exist "node_modules" (
    echo 正在安裝後端依賴...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 後端依賴安裝失敗
        pause
        goto MAIN_MENU
    )
)

echo [2/4] 檢查前端依賴...
cd /d "%~dp0frontend"
if not exist "node_modules" (
    echo 正在安裝前端依賴...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依賴安裝失敗
        pause
        goto MAIN_MENU
    )
)

echo [3/4] 啟動後端服務...
cd /d "%~dp0backend"
start "後端服務" cmd /k "echo 🔧 後端服務啟動中... && npm run dev"

echo 等待後端服務啟動...
timeout /t 5 /nobreak >nul

echo [4/4] 啟動前端服務...
cd /d "%~dp0frontend"
start "前端服務" cmd /k "echo ⚛️ 前端服務啟動中... && npm run dev"

echo.
echo ✅ 系統啟動完成！
echo.
echo 📋 服務信息：
echo    後端 API: http://localhost:5000
echo    前端應用: http://localhost:3000
echo    API 文檔: http://localhost:5000/api-docs
echo.
echo 💡 測試帳號：
echo    管理員: <EMAIL> / admin123
echo    客服: <EMAIL> / service123
echo    技師: <EMAIL> / tech123
echo    查詢: <EMAIL> / viewer123
echo.
echo 🔍 如果遇到問題，請使用偵錯工具進行診斷
echo.

REM 等待一下再開啟瀏覽器
timeout /t 8 /nobreak >nul
start "" "http://localhost:3000"

pause
goto MAIN_MENU

:START_BACKEND
echo.
echo 🔧 正在啟動後端服務...
call :CHECK_NODEJS
if %errorlevel% neq 0 goto MAIN_MENU

cd /d "%~dp0backend"
if not exist "node_modules" (
    echo 正在安裝依賴...
    npm install
)

echo 啟動後端服務...
echo 服務將在 http://localhost:5000 運行
echo.
npm run dev
pause
goto MAIN_MENU

:START_FRONTEND
echo.
echo ⚛️ 正在啟動前端服務...
call :CHECK_NODEJS
if %errorlevel% neq 0 goto MAIN_MENU

cd /d "%~dp0frontend"
if not exist "node_modules" (
    echo 正在安裝依賴...
    npm install
)

echo 啟動前端服務...
echo 服務將在 http://localhost:3000 運行
echo.
npm run dev
pause
goto MAIN_MENU

:OPEN_DEBUG_TOOL
echo.
echo 🔍 正在開啟偵錯工具...
if exist "React登入偵錯工具.html" (
    start "" "React登入偵錯工具.html"
    echo ✅ 偵錯工具已開啟
) else (
    echo ❌ 找不到偵錯工具檔案
)
echo.
pause
goto MAIN_MENU

:OPEN_API_DOCS
echo.
echo 📚 正在開啟 API 文檔...
start "" "http://localhost:5000/api-docs"
echo ✅ API 文檔已開啟
echo 如果頁面無法載入，請確認後端服務正在運行
echo.
pause
goto MAIN_MENU

:MAINTENANCE_MENU
echo.
echo 🛠️ 系統維護選項：
echo.
echo [1] 🧹 清除所有 node_modules
echo [2] 🔄 重新安裝所有依賴
echo [3] 🗑️ 清除瀏覽器數據
echo [4] 📊 檢查系統狀態
echo [5] 🔙 返回主選單
echo.
set /p maintenance_choice="請選擇維護選項 (1-5): "

if "%maintenance_choice%"=="1" goto CLEAN_NODE_MODULES
if "%maintenance_choice%"=="2" goto REINSTALL_DEPS
if "%maintenance_choice%"=="3" goto CLEAR_BROWSER_DATA
if "%maintenance_choice%"=="4" goto CHECK_SYSTEM_STATUS
if "%maintenance_choice%"=="5" goto MAIN_MENU
goto MAINTENANCE_MENU

:CLEAN_NODE_MODULES
echo.
echo 🧹 清除 node_modules...
if exist "backend\node_modules" (
    echo 清除後端 node_modules...
    rmdir /s /q "backend\node_modules"
)
if exist "frontend\node_modules" (
    echo 清除前端 node_modules...
    rmdir /s /q "frontend\node_modules"
)
echo ✅ 清除完成
pause
goto MAINTENANCE_MENU

:REINSTALL_DEPS
echo.
echo 🔄 重新安裝依賴...
call :CLEAN_NODE_MODULES

echo 安裝後端依賴...
cd /d "%~dp0backend"
npm install

echo 安裝前端依賴...
cd /d "%~dp0frontend"
npm install

echo ✅ 依賴重新安裝完成
pause
goto MAINTENANCE_MENU

:CLEAR_BROWSER_DATA
echo.
echo 🗑️ 清除瀏覽器數據指引：
echo.
echo Chrome:
echo   1. 按 Ctrl + Shift + Delete
echo   2. 選擇「所有時間」
echo   3. 勾選「Cookie 和其他網站資料」、「快取圖片和檔案」
echo   4. 點擊「清除資料」
echo.
echo Firefox:
echo   1. 按 Ctrl + Shift + Delete
echo   2. 選擇「全部」
echo   3. 勾選「Cookie」、「快取」
echo   4. 點擊「立即清除」
echo.
pause
goto MAINTENANCE_MENU

:CHECK_SYSTEM_STATUS
echo.
echo 📊 檢查系統狀態...
echo.

call :CHECK_NODEJS
echo.

echo 檢查後端依賴...
if exist "backend\node_modules" (
    echo ✅ 後端依賴已安裝
) else (
    echo ❌ 後端依賴未安裝
)

echo 檢查前端依賴...
if exist "frontend\node_modules" (
    echo ✅ 前端依賴已安裝
) else (
    echo ❌ 前端依賴未安裝
)

echo.
echo 檢查服務端口...
netstat -an | findstr ":5000" >nul
if %errorlevel% equ 0 (
    echo ✅ 後端服務 (端口 5000) 正在運行
) else (
    echo ❌ 後端服務 (端口 5000) 未運行
)

netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ✅ 前端服務 (端口 3000) 正在運行
) else (
    echo ❌ 前端服務 (端口 3000) 未運行
)

echo.
pause
goto MAINTENANCE_MENU

:SHOW_HELP
echo.
echo ❓ 使用說明
echo.
echo 📋 系統架構：
echo    這是一個現代化的 React + Node.js 維修管理系統
echo    前端使用 React + TypeScript + Ant Design
echo    後端使用 Node.js + Express + TypeScript + Prisma
echo.
echo 🚀 啟動順序：
echo    1. 先啟動後端服務 (提供 API)
echo    2. 再啟動前端服務 (用戶界面)
echo    3. 在瀏覽器中訪問 http://localhost:3000
echo.
echo 🔧 常見問題：
echo    • 端口被占用：關閉其他服務或重啟電腦
echo    • 依賴安裝失敗：檢查網路連接和 npm 配置
echo    • 登入失敗：確認後端服務正在運行
echo.
echo 💡 偵錯建議：
echo    • 使用偵錯工具檢查服務狀態
echo    • 查看瀏覽器控制台的錯誤訊息
echo    • 檢查 API 文檔確認端點可用性
echo.
pause
goto MAIN_MENU

:CHECK_NODEJS
echo 檢查 Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Node.js
    echo 請從 https://nodejs.org 下載並安裝 Node.js
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js 版本: %NODE_VERSION%
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 npm
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm 版本: %NPM_VERSION%
)
exit /b 0

:INVALID_CHOICE
echo.
echo ❌ 無效的選項，請重新選擇
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo 👋 感謝使用 React 維修管理系統啟動工具
echo.
echo 💡 系統特色：
echo    • 現代化 React 用戶界面
echo    • 強大的 TypeScript 類型安全
echo    • 豐富的 Ant Design 組件
echo    • RESTful API 後端服務
echo    • 完整的認證和權限管理
echo.
echo 🎯 快速啟動：選擇選項 1 即可啟動完整系統
echo 🔍 遇到問題：使用選項 4 開啟偵錯工具
echo.
pause
exit
