#!/bin/bash

# 客退維修品記錄管理系統 - 開發環境設置腳本

set -e

echo "🚀 開始設置客退維修品記錄管理系統開發環境..."

# 檢查 Node.js 版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安裝，請先安裝 Node.js 18 或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本過低，需要 18 或更高版本，當前版本: $(node -v)"
        exit 1
    fi
    
    echo "✅ Node.js 版本檢查通過: $(node -v)"
}

# 檢查 Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "⚠️  Docker 未安裝，將跳過 Docker 相關設置"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        echo "⚠️  Docker 未運行，將跳過 Docker 相關設置"
        return 1
    fi
    
    echo "✅ Docker 檢查通過"
    return 0
}

# 安裝前端依賴
setup_frontend() {
    echo "📦 安裝前端依賴..."
    cd frontend
    
    if [ ! -f ".env" ]; then
        echo "📝 建立前端環境配置文件..."
        cp .env.example .env
    fi
    
    npm install
    echo "✅ 前端依賴安裝完成"
    cd ..
}

# 安裝後端依賴
setup_backend() {
    echo "📦 安裝後端依賴..."
    cd backend
    
    if [ ! -f ".env" ]; then
        echo "📝 建立後端環境配置文件..."
        cp .env.example .env
    fi
    
    npm install
    
    echo "🔧 生成 Prisma 客戶端..."
    npx prisma generate
    
    echo "✅ 後端依賴安裝完成"
    cd ..
}

# 設置資料庫
setup_database() {
    echo "🗄️  設置資料庫..."
    
    if check_docker; then
        echo "🐳 使用 Docker 啟動 MySQL..."
        docker-compose up -d mysql
        
        echo "⏳ 等待 MySQL 啟動..."
        sleep 30
        
        echo "🔧 執行資料庫遷移..."
        cd backend
        npx prisma db push
        
        echo "🌱 執行資料庫種子..."
        npm run db:seed
        
        cd ..
        echo "✅ 資料庫設置完成"
    else
        echo "⚠️  請手動設置 MySQL 資料庫並更新 backend/.env 中的 DATABASE_URL"
    fi
}

# 建立必要目錄
create_directories() {
    echo "📁 建立必要目錄..."
    
    mkdir -p uploads
    mkdir -p logs
    mkdir -p backend/logs
    mkdir -p frontend/dist
    
    echo "✅ 目錄建立完成"
}

# 設置 Git hooks
setup_git_hooks() {
    echo "🔧 設置 Git hooks..."
    
    if [ -d ".git" ]; then
        # 建立 pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "🔍 執行 pre-commit 檢查..."

# 檢查前端程式碼
cd frontend
npm run lint
npm run type-check
cd ..

# 檢查後端程式碼
cd backend
npm run lint
npm run type-check
cd ..

echo "✅ Pre-commit 檢查通過"
EOF
        
        chmod +x .git/hooks/pre-commit
        echo "✅ Git hooks 設置完成"
    else
        echo "⚠️  Git 儲存庫未初始化，跳過 Git hooks 設置"
    fi
}

# 主要設置流程
main() {
    echo "🎯 客退維修品記錄管理系統開發環境設置"
    echo "========================================"
    
    check_node_version
    create_directories
    setup_frontend
    setup_backend
    setup_database
    setup_git_hooks
    
    echo ""
    echo "🎉 開發環境設置完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 更新 backend/.env 中的配置（特別是 Azure AD 和 SharePoint 設定）"
    echo "2. 更新 frontend/.env 中的配置"
    echo "3. 啟動開發服務器："
    echo "   - 前端: cd frontend && npm run dev"
    echo "   - 後端: cd backend && npm run dev"
    echo "   - 或使用 Docker: docker-compose up"
    echo ""
    echo "🌐 應用將在以下地址運行："
    echo "   - 前端: http://localhost:3000"
    echo "   - 後端: http://localhost:5000"
    echo "   - API 文檔: http://localhost:5000/api-docs"
    echo ""
    echo "📚 更多資訊請參考 README.md"
}

# 執行主要流程
main "$@"
