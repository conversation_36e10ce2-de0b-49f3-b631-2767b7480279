import { Router } from 'express';
import CustomerController from '../controllers/customerController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  createCustomerValidation,
  updateCustomerValidation,
  customerListValidation,
  customerIdValidation,
  customerSearchValidation,
  batchOperationValidation,
  emailParamValidation,
  phoneParamValidation,
  customerDetailValidation,
  createCustomerWithContactValidation,
  batchOperationWithPermissionValidation,
} from '../validators/customerValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateCustomerRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 客戶名稱
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 100
 *           description: 電子郵件
 *         phone:
 *           type: string
 *           maxLength: 20
 *           pattern: '^[\d\s\-\+\(\)]+$'
 *           description: 電話號碼
 *         address:
 *           type: string
 *           maxLength: 500
 *           description: 地址
 *         companyName:
 *           type: string
 *           maxLength: 200
 *           description: 公司名稱
 *         contactPerson:
 *           type: string
 *           maxLength: 100
 *           description: 聯絡人
 *         taxId:
 *           type: string
 *           maxLength: 50
 *           pattern: '^[\w\-]+$'
 *           description: 統一編號
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: 備註
 *         isActive:
 *           type: boolean
 *           default: true
 *           description: 是否活躍
 *     
 *     UpdateCustomerRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 客戶名稱
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 100
 *           description: 電子郵件
 *         phone:
 *           type: string
 *           maxLength: 20
 *           pattern: '^[\d\s\-\+\(\)]+$'
 *           description: 電話號碼
 *         address:
 *           type: string
 *           maxLength: 500
 *           description: 地址
 *         companyName:
 *           type: string
 *           maxLength: 200
 *           description: 公司名稱
 *         contactPerson:
 *           type: string
 *           maxLength: 100
 *           description: 聯絡人
 *         taxId:
 *           type: string
 *           maxLength: 50
 *           pattern: '^[\w\-]+$'
 *           description: 統一編號
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: 備註
 *         isActive:
 *           type: boolean
 *           description: 是否活躍
 *     
 *     CustomerInfo:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         email:
 *           type: string
 *         phone:
 *           type: string
 *         address:
 *           type: string
 *         companyName:
 *           type: string
 *         contactPerson:
 *           type: string
 *         taxId:
 *           type: string
 *         notes:
 *           type: string
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     
 *     CustomerListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             customers:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/CustomerInfo'
 *             pagination:
 *               $ref: '#/components/schemas/Pagination'
 *             filters:
 *               type: object
 *             sorting:
 *               type: object
 *     
 *     BatchCustomerOperation:
 *       type: object
 *       required:
 *         - customerIds
 *         - operation
 *       properties:
 *         customerIds:
 *           type: array
 *           items:
 *             type: string
 *           minItems: 1
 *           maxItems: 100
 *           description: 客戶ID列表
 *         operation:
 *           type: string
 *           enum: [activate, deactivate, delete, export]
 *           description: 操作類型
 *         data:
 *           type: object
 *           properties:
 *             isActive:
 *               type: boolean
 *               description: 活躍狀態
 *             exportFormat:
 *               type: string
 *               enum: [csv, excel, json]
 *               description: 匯出格式
 */

/**
 * @swagger
 * /api/v1/customers:
 *   get:
 *     summary: 獲取客戶列表
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *       - in: query
 *         name: hasEmail
 *         schema:
 *           type: boolean
 *         description: 有電子郵件篩選
 *       - in: query
 *         name: hasPhone
 *         schema:
 *           type: boolean
 *         description: 有電話篩選
 *       - in: query
 *         name: hasCompany
 *         schema:
 *           type: boolean
 *         description: 有公司資訊篩選
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, email, phone, companyName, createdAt, updatedAt]
 *           default: createdAt
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取客戶列表成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomerListResponse'
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.TECHNICIAN, Role.ADMIN), customerListValidation, CustomerController.getCustomerList);

/**
 * @swagger
 * /api/v1/customers:
 *   post:
 *     summary: 創建客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerRequest'
 *     responses:
 *       201:
 *         description: 客戶創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       409:
 *         description: 電子郵件或電話已存在
 */
router.post('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), createCustomerWithContactValidation, CustomerController.createCustomer);

/**
 * @swagger
 * /api/v1/customers/search:
 *   get:
 *     summary: 搜尋客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: 結果數量限制
 *     responses:
 *       200:
 *         description: 搜尋成功
 *       400:
 *         description: 搜尋參數錯誤
 *       401:
 *         description: 未認證
 */
router.get('/search', authenticate, customerSearchValidation, CustomerController.searchCustomers);

/**
 * @swagger
 * /api/v1/customers/statistics:
 *   get:
 *     summary: 獲取客戶統計
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), CustomerController.getCustomerStatistics);

/**
 * @swagger
 * /api/v1/customers/contact-statistics:
 *   get:
 *     summary: 獲取客戶聯絡方式統計
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取聯絡方式統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/contact-statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), CustomerController.getContactStatistics);

/**
 * @swagger
 * /api/v1/customers/activity-statistics:
 *   get:
 *     summary: 獲取客戶活動統計
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取活動統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/activity-statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), CustomerController.getActivityStatistics);

/**
 * @swagger
 * /api/v1/customers/batch:
 *   post:
 *     summary: 批量操作客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchCustomerOperation'
 *     responses:
 *       200:
 *         description: 批量操作完成
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/batch', authenticate, authorize(Role.ADMIN), batchOperationWithPermissionValidation, CustomerController.batchOperation);

/**
 * @swagger
 * /api/v1/customers/email/{email}:
 *   get:
 *     summary: 根據電子郵件查找客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: email
 *         required: true
 *         schema:
 *           type: string
 *           format: email
 *         description: 電子郵件地址
 *     responses:
 *       200:
 *         description: 查找成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 客戶不存在
 */
router.get('/email/:email', authenticate, emailParamValidation, CustomerController.findCustomerByEmail);

/**
 * @swagger
 * /api/v1/customers/phone/{phone}:
 *   get:
 *     summary: 根據電話查找客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: phone
 *         required: true
 *         schema:
 *           type: string
 *         description: 電話號碼
 *     responses:
 *       200:
 *         description: 查找成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 客戶不存在
 */
router.get('/phone/:phone', authenticate, phoneParamValidation, CustomerController.findCustomerByPhone);

/**
 * @swagger
 * /api/v1/customers/check-email/{email}:
 *   get:
 *     summary: 檢查電子郵件是否可用
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: email
 *         required: true
 *         schema:
 *           type: string
 *           format: email
 *         description: 要檢查的電子郵件
 *       - in: query
 *         name: excludeId
 *         schema:
 *           type: string
 *         description: 排除的客戶ID
 *     responses:
 *       200:
 *         description: 檢查完成
 *       400:
 *         description: 電子郵件格式錯誤
 *       401:
 *         description: 未認證
 */
router.get('/check-email/:email', authenticate, emailParamValidation, CustomerController.checkEmailAvailability);

/**
 * @swagger
 * /api/v1/customers/check-phone/{phone}:
 *   get:
 *     summary: 檢查電話是否可用
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: phone
 *         required: true
 *         schema:
 *           type: string
 *         description: 要檢查的電話號碼
 *       - in: query
 *         name: excludeId
 *         schema:
 *           type: string
 *         description: 排除的客戶ID
 *     responses:
 *       200:
 *         description: 檢查完成
 *       400:
 *         description: 電話格式錯誤
 *       401:
 *         description: 未認證
 */
router.get('/check-phone/:phone', authenticate, phoneParamValidation, CustomerController.checkPhoneAvailability);

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   get:
 *     summary: 根據ID獲取客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客戶ID
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含詳細資訊
 *     responses:
 *       200:
 *         description: 獲取客戶成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 客戶不存在
 */
router.get('/:id', authenticate, customerDetailValidation, CustomerController.getCustomerById);

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   put:
 *     summary: 更新客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客戶ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCustomerRequest'
 *     responses:
 *       200:
 *         description: 客戶更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 客戶不存在
 */
router.put('/:id', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), updateCustomerValidation, CustomerController.updateCustomer);

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   delete:
 *     summary: 刪除客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客戶ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 客戶刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 客戶不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), customerIdValidation, CustomerController.deleteCustomer);

/**
 * @swagger
 * /api/v1/customers/{id}/activate:
 *   post:
 *     summary: 激活客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客戶ID
 *     responses:
 *       200:
 *         description: 客戶激活成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 客戶不存在
 */
router.post('/:id/activate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), customerIdValidation, CustomerController.activateCustomer);

/**
 * @swagger
 * /api/v1/customers/{id}/deactivate:
 *   post:
 *     summary: 停用客戶
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客戶ID
 *     responses:
 *       200:
 *         description: 客戶停用成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 客戶不存在
 */
router.post('/:id/deactivate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), customerIdValidation, CustomerController.deactivateCustomer);

export default router;
