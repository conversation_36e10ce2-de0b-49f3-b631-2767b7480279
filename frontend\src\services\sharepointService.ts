import { api, ApiResponse } from './api';

// SharePoint整合相關類型定義
export interface SharePointConfig {
  tenantId: string;
  clientId: string;
  clientSecret: string;
  siteUrl: string;
  documentLibrary: string;
  isEnabled: boolean;
  syncInterval: number; // 分鐘
  autoSync: boolean;
  lastSyncAt?: string;
}

export interface SharePointSite {
  id: string;
  name: string;
  displayName: string;
  webUrl: string;
  description?: string;
  createdDateTime: string;
  lastModifiedDateTime: string;
}

export interface SharePointDocumentLibrary {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  webUrl: string;
  itemCount: number;
  createdDateTime: string;
  lastModifiedDateTime: string;
}

export interface SharePointFile {
  id: string;
  name: string;
  displayName: string;
  webUrl: string;
  downloadUrl: string;
  size: number;
  mimeType: string;
  createdDateTime: string;
  lastModifiedDateTime: string;
  createdBy: {
    user: {
      id: string;
      displayName: string;
      email: string;
    };
  };
  lastModifiedBy: {
    user: {
      id: string;
      displayName: string;
      email: string;
    };
  };
  parentReference: {
    driveId: string;
    path: string;
  };
}

export interface SharePointFolder {
  id: string;
  name: string;
  webUrl: string;
  childCount: number;
  createdDateTime: string;
  lastModifiedDateTime: string;
  parentReference: {
    driveId: string;
    path: string;
  };
}

export interface DocumentSyncRecord {
  id: number;
  localFileId: number;
  localFileName: string;
  localFilePath: string;
  sharePointFileId: string;
  sharePointFileName: string;
  sharePointPath: string;
  syncStatus: 'PENDING' | 'SYNCING' | 'SYNCED' | 'CONFLICT' | 'ERROR';
  syncDirection: 'UPLOAD' | 'DOWNLOAD' | 'BIDIRECTIONAL';
  lastSyncAt?: string;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SyncConflict {
  id: number;
  localFileId: number;
  sharePointFileId: string;
  conflictType: 'VERSION_CONFLICT' | 'NAME_CONFLICT' | 'PERMISSION_CONFLICT';
  localVersion: {
    lastModified: string;
    size: number;
    checksum: string;
  };
  sharePointVersion: {
    lastModified: string;
    size: number;
    checksum: string;
  };
  resolution?: 'USE_LOCAL' | 'USE_SHAREPOINT' | 'MERGE' | 'RENAME';
  resolvedAt?: string;
  createdAt: string;
}

export interface Office365Integration {
  isEnabled: boolean;
  tenantId: string;
  applicationId: string;
  permissions: string[];
  connectedServices: {
    outlook: boolean;
    teams: boolean;
    oneDrive: boolean;
    sharePoint: boolean;
  };
  lastConnectionTest?: string;
  connectionStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
}

export interface RepairDocumentTemplate {
  id: number;
  name: string;
  displayName: string;
  description: string;
  templateType: 'REPAIR_ORDER' | 'INVOICE' | 'QUOTE' | 'REPORT' | 'CERTIFICATE';
  sharePointTemplateId?: string;
  sharePointPath?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GeneratedDocument {
  id: number;
  repairRecordId: number;
  templateId: number;
  documentType: string;
  fileName: string;
  localPath?: string;
  sharePointFileId?: string;
  sharePointPath?: string;
  generatedAt: string;
  uploadedAt?: string;
  downloadUrl?: string;
}

// SharePoint服務
export const sharepointService = {
  // SharePoint配置管理
  getConfig: async (): Promise<ApiResponse<SharePointConfig>> => {
    return await api.get<SharePointConfig>('/sharepoint/config');
  },

  updateConfig: async (config: Partial<SharePointConfig>): Promise<ApiResponse<SharePointConfig>> => {
    return await api.put<SharePointConfig>('/sharepoint/config', config);
  },

  testConnection: async (): Promise<ApiResponse<{ success: boolean; message: string }>> => {
    return await api.post<{ success: boolean; message: string }>('/sharepoint/test-connection');
  },

  // SharePoint站點和文檔庫管理
  getSites: async (): Promise<ApiResponse<SharePointSite[]>> => {
    return await api.get<SharePointSite[]>('/sharepoint/sites');
  },

  getDocumentLibraries: async (siteId: string): Promise<ApiResponse<SharePointDocumentLibrary[]>> => {
    return await api.get<SharePointDocumentLibrary[]>(`/sharepoint/sites/${siteId}/libraries`);
  },

  // 文件和文件夾管理
  getFiles: async (libraryId: string, folderPath?: string): Promise<ApiResponse<SharePointFile[]>> => {
    const path = folderPath ? `?folderPath=${encodeURIComponent(folderPath)}` : '';
    return await api.get<SharePointFile[]>(`/sharepoint/libraries/${libraryId}/files${path}`);
  },

  getFolders: async (libraryId: string, folderPath?: string): Promise<ApiResponse<SharePointFolder[]>> => {
    const path = folderPath ? `?folderPath=${encodeURIComponent(folderPath)}` : '';
    return await api.get<SharePointFolder[]>(`/sharepoint/libraries/${libraryId}/folders${path}`);
  },

  createFolder: async (libraryId: string, folderName: string, parentPath?: string): Promise<ApiResponse<SharePointFolder>> => {
    return await api.post<SharePointFolder>(`/sharepoint/libraries/${libraryId}/folders`, {
      name: folderName,
      parentPath,
    });
  },

  uploadFile: async (libraryId: string, file: File, folderPath?: string): Promise<ApiResponse<SharePointFile>> => {
    const formData = new FormData();
    formData.append('file', file);
    if (folderPath) {
      formData.append('folderPath', folderPath);
    }

    return await api.post<SharePointFile>(`/sharepoint/libraries/${libraryId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  downloadFile: async (fileId: string): Promise<ApiResponse<{ downloadUrl: string }>> => {
    return await api.get<{ downloadUrl: string }>(`/sharepoint/files/${fileId}/download`);
  },

  deleteFile: async (fileId: string): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/sharepoint/files/${fileId}`);
  },

  // 文檔同步管理
  getSyncRecords: async (): Promise<ApiResponse<DocumentSyncRecord[]>> => {
    return await api.get<DocumentSyncRecord[]>('/sharepoint/sync/records');
  },

  createSyncRecord: async (syncData: {
    localFileId: number;
    sharePointPath: string;
    syncDirection: 'UPLOAD' | 'DOWNLOAD' | 'BIDIRECTIONAL';
  }): Promise<ApiResponse<DocumentSyncRecord>> => {
    return await api.post<DocumentSyncRecord>('/sharepoint/sync/records', syncData);
  },

  syncFile: async (syncRecordId: number): Promise<ApiResponse<DocumentSyncRecord>> => {
    return await api.post<DocumentSyncRecord>(`/sharepoint/sync/records/${syncRecordId}/sync`);
  },

  syncAllFiles: async (): Promise<ApiResponse<{ synced: number; failed: number; conflicts: number }>> => {
    return await api.post<{ synced: number; failed: number; conflicts: number }>('/sharepoint/sync/all');
  },

  // 同步衝突管理
  getSyncConflicts: async (): Promise<ApiResponse<SyncConflict[]>> => {
    return await api.get<SyncConflict[]>('/sharepoint/sync/conflicts');
  },

  resolveConflict: async (conflictId: number, resolution: 'USE_LOCAL' | 'USE_SHAREPOINT' | 'MERGE' | 'RENAME'): Promise<ApiResponse<SyncConflict>> => {
    return await api.post<SyncConflict>(`/sharepoint/sync/conflicts/${conflictId}/resolve`, {
      resolution,
    });
  },

  // Office 365整合
  getOffice365Config: async (): Promise<ApiResponse<Office365Integration>> => {
    return await api.get<Office365Integration>('/office365/config');
  },

  updateOffice365Config: async (config: Partial<Office365Integration>): Promise<ApiResponse<Office365Integration>> => {
    return await api.put<Office365Integration>('/office365/config', config);
  },

  testOffice365Connection: async (): Promise<ApiResponse<{ success: boolean; message: string; services: Record<string, boolean> }>> => {
    return await api.post<{ success: boolean; message: string; services: Record<string, boolean> }>('/office365/test-connection');
  },

  // 文檔模板管理
  getDocumentTemplates: async (): Promise<ApiResponse<RepairDocumentTemplate[]>> => {
    return await api.get<RepairDocumentTemplate[]>('/sharepoint/templates');
  },

  createDocumentTemplate: async (templateData: Omit<RepairDocumentTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<RepairDocumentTemplate>> => {
    return await api.post<RepairDocumentTemplate>('/sharepoint/templates', templateData);
  },

  updateDocumentTemplate: async (id: number, templateData: Partial<RepairDocumentTemplate>): Promise<ApiResponse<RepairDocumentTemplate>> => {
    return await api.put<RepairDocumentTemplate>(`/sharepoint/templates/${id}`, templateData);
  },

  deleteDocumentTemplate: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/sharepoint/templates/${id}`);
  },

  // 文檔生成
  generateDocument: async (repairRecordId: number, templateId: number, options?: {
    autoUpload?: boolean;
    sharePointPath?: string;
  }): Promise<ApiResponse<GeneratedDocument>> => {
    return await api.post<GeneratedDocument>('/sharepoint/generate-document', {
      repairRecordId,
      templateId,
      ...options,
    });
  },

  getGeneratedDocuments: async (repairRecordId?: number): Promise<ApiResponse<GeneratedDocument[]>> => {
    const query = repairRecordId ? `?repairRecordId=${repairRecordId}` : '';
    return await api.get<GeneratedDocument[]>(`/sharepoint/generated-documents${query}`);
  },

  // 權限管理
  getFilePermissions: async (fileId: string): Promise<ApiResponse<{
    hasRead: boolean;
    hasWrite: boolean;
    hasDelete: boolean;
    hasShare: boolean;
  }>> => {
    return await api.get<{
      hasRead: boolean;
      hasWrite: boolean;
      hasDelete: boolean;
      hasShare: boolean;
    }>(`/sharepoint/files/${fileId}/permissions`);
  },

  shareFile: async (fileId: string, shareData: {
    emails: string[];
    permission: 'read' | 'write';
    message?: string;
  }): Promise<ApiResponse<{ shareUrl: string }>> => {
    return await api.post<{ shareUrl: string }>(`/sharepoint/files/${fileId}/share`, shareData);
  },

  // 搜尋功能
  searchFiles: async (query: string, libraryId?: string): Promise<ApiResponse<SharePointFile[]>> => {
    const params = new URLSearchParams({ query });
    if (libraryId) {
      params.append('libraryId', libraryId);
    }
    return await api.get<SharePointFile[]>(`/sharepoint/search?${params.toString()}`);
  },

  // 版本管理
  getFileVersions: async (fileId: string): Promise<ApiResponse<Array<{
    id: string;
    versionLabel: string;
    size: number;
    lastModifiedDateTime: string;
    lastModifiedBy: {
      user: {
        displayName: string;
        email: string;
      };
    };
  }>>> => {
    return await api.get(`/sharepoint/files/${fileId}/versions`);
  },

  restoreFileVersion: async (fileId: string, versionId: string): Promise<ApiResponse<SharePointFile>> => {
    return await api.post<SharePointFile>(`/sharepoint/files/${fileId}/versions/${versionId}/restore`);
  },
};

export default sharepointService;
