const express = require('express');
const path = require('path');
const cors = require('cors');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// 啟用 CORS
app.use(cors());

// 解析 JSON 請求
app.use(express.json({ limit: '50mb' }));

// 靜態檔案服務
app.use(express.static('.'));

// 數據存儲目錄
const DATA_DIR = './data';
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR);
}

// 主頁路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'complete-system-gui.html'));
});

// API 路由 - 保存數據
app.post('/api/save/:dataType', (req, res) => {
    try {
        const { dataType } = req.params;
        const data = req.body;
        
        const filePath = path.join(DATA_DIR, `${dataType}.json`);
        const saveData = {
            data: data,
            lastUpdated: new Date().toISOString(),
            version: '1.0'
        };
        
        fs.writeFileSync(filePath, JSON.stringify(saveData, null, 2));
        
        res.json({ 
            success: true, 
            message: `${dataType} 數據保存成功`,
            timestamp: saveData.lastUpdated
        });
        
        console.log(`[${new Date().toLocaleString()}] 保存 ${dataType}: ${Array.isArray(data) ? data.length : 1} 筆記錄`);
        
    } catch (error) {
        console.error('保存數據失敗:', error);
        res.status(500).json({ 
            success: false, 
            message: '數據保存失敗',
            error: error.message 
        });
    }
});

// API 路由 - 載入數據
app.get('/api/load/:dataType', (req, res) => {
    try {
        const { dataType } = req.params;
        const filePath = path.join(DATA_DIR, `${dataType}.json`);
        
        if (!fs.existsSync(filePath)) {
            return res.json({ 
                success: true, 
                data: [],
                message: `${dataType} 數據檔案不存在，返回空數據`
            });
        }
        
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const savedData = JSON.parse(fileContent);
        
        res.json({ 
            success: true, 
            data: savedData.data || [],
            lastUpdated: savedData.lastUpdated,
            version: savedData.version
        });
        
        console.log(`[${new Date().toLocaleString()}] 載入 ${dataType}: ${Array.isArray(savedData.data) ? savedData.data.length : 1} 筆記錄`);
        
    } catch (error) {
        console.error('載入數據失敗:', error);
        res.status(500).json({ 
            success: false, 
            message: '數據載入失敗',
            error: error.message 
        });
    }
});

// API 路由 - 獲取系統狀態
app.get('/api/status', (req, res) => {
    try {
        const status = {
            server: 'running',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            dataFiles: []
        };
        
        // 檢查數據檔案
        const dataTypes = ['repairRecords', 'customers', 'parts', 'products'];
        dataTypes.forEach(dataType => {
            const filePath = path.join(DATA_DIR, `${dataType}.json`);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                status.dataFiles.push({
                    type: dataType,
                    size: stats.size,
                    lastModified: stats.mtime,
                    recordCount: Array.isArray(content.data) ? content.data.length : 1
                });
            }
        });
        
        res.json(status);
        
    } catch (error) {
        res.status(500).json({ 
            success: false, 
            message: '獲取系統狀態失敗',
            error: error.message 
        });
    }
});

// API 路由 - 備份數據
app.get('/api/backup', (req, res) => {
    try {
        const backupData = {};
        const dataTypes = ['repairRecords', 'customers', 'parts', 'products'];
        
        dataTypes.forEach(dataType => {
            const filePath = path.join(DATA_DIR, `${dataType}.json`);
            if (fs.existsSync(filePath)) {
                const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                backupData[dataType] = content.data;
            }
        });
        
        backupData.exportDate = new Date().toISOString();
        backupData.serverVersion = '1.0';
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename=backup_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`);
        res.send(JSON.stringify(backupData, null, 2));
        
        console.log(`[${new Date().toLocaleString()}] 建立數據備份`);
        
    } catch (error) {
        console.error('備份失敗:', error);
        res.status(500).json({ 
            success: false, 
            message: '備份失敗',
            error: error.message 
        });
    }
});

// API 路由 - 恢復數據
app.post('/api/restore', (req, res) => {
    try {
        const backupData = req.body;
        const dataTypes = ['repairRecords', 'customers', 'parts', 'products'];
        const results = {};
        
        dataTypes.forEach(dataType => {
            if (backupData[dataType]) {
                const filePath = path.join(DATA_DIR, `${dataType}.json`);
                const saveData = {
                    data: backupData[dataType],
                    lastUpdated: new Date().toISOString(),
                    version: '1.0',
                    restoredFrom: backupData.exportDate || 'unknown'
                };
                
                fs.writeFileSync(filePath, JSON.stringify(saveData, null, 2));
                results[dataType] = Array.isArray(backupData[dataType]) ? backupData[dataType].length : 1;
            }
        });
        
        res.json({ 
            success: true, 
            message: '數據恢復成功',
            results: results,
            timestamp: new Date().toISOString()
        });
        
        console.log(`[${new Date().toLocaleString()}] 恢復數據:`, results);
        
    } catch (error) {
        console.error('恢復數據失敗:', error);
        res.status(500).json({ 
            success: false, 
            message: '數據恢復失敗',
            error: error.message 
        });
    }
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
    console.error('伺服器錯誤:', err);
    res.status(500).json({ 
        success: false, 
        message: '伺服器內部錯誤',
        error: err.message 
    });
});

// 404 處理
app.use((req, res) => {
    res.status(404).json({ 
        success: false, 
        message: '找不到請求的資源' 
    });
});

// 啟動伺服器
app.listen(PORT, () => {
    console.log('========================================');
    console.log('  IACT MIO維保管理系統 伺服器已啟動');
    console.log('========================================');
    console.log(`🌐 本機存取: http://localhost:${PORT}`);
    console.log(`🌐 區域網路存取: http://[您的IP]:${PORT}`);
    console.log(`📁 數據存儲目錄: ${path.resolve(DATA_DIR)}`);
    console.log(`⏰ 啟動時間: ${new Date().toLocaleString()}`);
    console.log('========================================');
    console.log('');
    console.log('💡 提示:');
    console.log('  - 按 Ctrl+C 停止伺服器');
    console.log('  - 數據會自動保存到 data/ 目錄');
    console.log('  - 支援多用戶同時存取');
    console.log('  - API 端點: /api/save, /api/load, /api/status');
    console.log('');
});

// 優雅關閉
process.on('SIGINT', () => {
    console.log('\n正在關閉伺服器...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n正在關閉伺服器...');
    process.exit(0);
});
