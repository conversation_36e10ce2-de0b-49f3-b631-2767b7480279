import request from 'supertest';
import { PrismaClient, UserRole } from '@prisma/client';
import app from '../index';
import { PasswordUtils } from '../utils/password';
import { JWTUtils } from '../utils/jwt';

const prisma = new PrismaClient();

describe('Customer Management API', () => {
  let adminToken: string;
  let customerServiceToken: string;
  let technicianToken: string;
  let viewerToken: string;
  let testUsers: any[] = [];
  let testCustomers: any[] = [];

  beforeAll(async () => {
    // 建立測試用戶
    const adminPassword = await PasswordUtils.hashPassword('AdminPass123!');
    const csPassword = await PasswordUtils.hashPassword('CSPass123!');
    const techPassword = await PasswordUtils.hashPassword('TechPass123!');
    const viewerPassword = await PasswordUtils.hashPassword('ViewerPass123!');

    const admin = await prisma.user.create({
      data: {
        username: 'testadmin_customer',
        email: '<EMAIL>',
        passwordHash: adminPassword,
        fullName: 'Test Admin Customer',
        role: UserRole.ADMIN,
      },
    });

    const customerService = await prisma.user.create({
      data: {
        username: 'testcs_customer',
        email: '<EMAIL>',
        passwordHash: csPassword,
        fullName: 'Test Customer Service',
        role: UserRole.CUSTOMER_SERVICE,
      },
    });

    const technician = await prisma.user.create({
      data: {
        username: 'testtech_customer',
        email: '<EMAIL>',
        passwordHash: techPassword,
        fullName: 'Test Technician Customer',
        role: UserRole.TECHNICIAN,
      },
    });

    const viewer = await prisma.user.create({
      data: {
        username: 'testviewer_customer',
        email: '<EMAIL>',
        passwordHash: viewerPassword,
        fullName: 'Test Viewer Customer',
        role: UserRole.VIEWER,
      },
    });

    testUsers = [admin, customerService, technician, viewer];

    // 生成令牌
    adminToken = JWTUtils.generateAccessToken({
      userId: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: admin.role,
    });

    customerServiceToken = JWTUtils.generateAccessToken({
      userId: customerService.id.toString(),
      username: customerService.username,
      email: customerService.email,
      role: customerService.role,
    });

    technicianToken = JWTUtils.generateAccessToken({
      userId: technician.id.toString(),
      username: technician.username,
      email: technician.email,
      role: technician.role,
    });

    viewerToken = JWTUtils.generateAccessToken({
      userId: viewer.id.toString(),
      username: viewer.username,
      email: viewer.email,
      role: viewer.role,
    });

    // 建立測試客戶
    const customer1 = await prisma.customer.create({
      data: {
        name: 'Test Customer 1',
        email: '<EMAIL>',
        phone: '0912345678',
        address: 'Test Address 1',
        companyName: 'Test Company 1',
        contactPerson: 'Contact Person 1',
        taxId: 'TAX001',
        notes: 'Test notes 1',
      },
    });

    const customer2 = await prisma.customer.create({
      data: {
        name: 'Test Customer 2',
        email: '<EMAIL>',
        phone: '0987654321',
        companyName: 'Test Company 2',
        isActive: false,
      },
    });

    testCustomers = [customer1, customer2];
  });

  afterAll(async () => {
    // 清理測試資料
    for (const customer of testCustomers) {
      await prisma.customer.delete({ where: { id: customer.id } }).catch(() => {});
    }

    for (const user of testUsers) {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }

    // 清理其他測試客戶
    await prisma.customer.deleteMany({
      where: {
        name: { startsWith: 'Test Customer' },
      },
    });

    await prisma.$disconnect();
  });

  describe('GET /api/v1/customers', () => {
    test('should get customer list as admin', async () => {
      const response = await request(app)
        .get('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('customers');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.customers)).toBe(true);
    });

    test('should get customer list as customer service', async () => {
      const response = await request(app)
        .get('/api/v1/customers')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('customers');
    });

    test('should get customer list with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/customers?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.customers.length).toBeLessThanOrEqual(1);
    });

    test('should filter customers by active status', async () => {
      const response = await request(app)
        .get('/api/v1/customers?isActive=true')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.customers.every((customer: any) => customer.isActive === true)).toBe(true);
    });

    test('should search customers', async () => {
      const response = await request(app)
        .get('/api/v1/customers?search=Test Customer 1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.customers.some((customer: any) => 
        customer.name.includes('Test Customer 1')
      )).toBe(true);
    });

    test('should fail without authentication', async () => {
      await request(app)
        .get('/api/v1/customers')
        .expect(401);
    });

    test('should fail with insufficient permissions', async () => {
      await request(app)
        .get('/api/v1/customers')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/customers', () => {
    test('should create customer as admin', async () => {
      const customerData = {
        name: 'New Test Customer',
        email: '<EMAIL>',
        phone: '0911111111',
        address: 'New Test Address',
        companyName: 'New Test Company',
        contactPerson: 'New Contact Person',
        taxId: 'NEWTAX001',
        notes: 'New test notes',
      };

      const response = await request(app)
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(customerData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.name).toBe(customerData.name);
      expect(response.body.data.customer.email).toBe(customerData.email);
    });

    test('should create customer as customer service', async () => {
      const customerData = {
        name: 'CS Test Customer',
        email: '<EMAIL>',
        phone: '0922222222',
      };

      const response = await request(app)
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(customerData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.name).toBe(customerData.name);
    });

    test('should fail with duplicate email', async () => {
      const customerData = {
        name: 'Duplicate Email Customer',
        email: '<EMAIL>', // 已存在的電子郵件
        phone: '0933333333',
      };

      await request(app)
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(customerData)
        .expect(400);
    });

    test('should fail with invalid data', async () => {
      const customerData = {
        name: 'A', // 太短
        email: 'invalid-email',
        phone: 'invalid-phone',
      };

      await request(app)
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(customerData)
        .expect(400);
    });

    test('should fail without permission', async () => {
      const customerData = {
        name: 'Unauthorized Customer',
        email: '<EMAIL>',
      };

      await request(app)
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(customerData)
        .expect(403);
    });
  });

  describe('GET /api/v1/customers/:id', () => {
    test('should get customer by id', async () => {
      const customerId = testCustomers[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/customers/${customerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.id).toBe(customerId);
    });

    test('should get customer with details', async () => {
      const customerId = testCustomers[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/customers/${customerId}?includeDetails=true`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer).toHaveProperty('statistics');
      expect(response.body.data.customer).toHaveProperty('repairRecords');
    });

    test('should fail with non-existent customer', async () => {
      await request(app)
        .get('/api/v1/customers/999999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/v1/customers/:id', () => {
    test('should update customer as admin', async () => {
      const customerId = testCustomers[0].id.toString();
      const updateData = {
        name: 'Updated Customer Name',
        email: '<EMAIL>',
        notes: 'Updated notes',
      };

      const response = await request(app)
        .put(`/api/v1/customers/${customerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.name).toBe(updateData.name);
      expect(response.body.data.customer.email).toBe(updateData.email);
    });

    test('should update customer as customer service', async () => {
      const customerId = testCustomers[1].id.toString();
      const updateData = {
        name: 'CS Updated Customer',
        phone: '0944444444',
      };

      const response = await request(app)
        .put(`/api/v1/customers/${customerId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.name).toBe(updateData.name);
    });

    test('should fail without permission', async () => {
      const customerId = testCustomers[0].id.toString();
      const updateData = {
        name: 'Unauthorized Update',
      };

      await request(app)
        .put(`/api/v1/customers/${customerId}`)
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('GET /api/v1/customers/search', () => {
    test('should search customers', async () => {
      const response = await request(app)
        .get('/api/v1/customers/search?q=Test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(Array.isArray(response.body.data.results)).toBe(true);
    });

    test('should fail with short search query', async () => {
      await request(app)
        .get('/api/v1/customers/search?q=A')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/customers/statistics', () => {
    test('should get customer statistics', async () => {
      const response = await request(app)
        .get('/api/v1/customers/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('active');
      expect(response.body.data.statistics).toHaveProperty('withEmail');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/customers/statistics')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/customers/:id/activate', () => {
    test('should activate customer', async () => {
      const customerId = testCustomers[1].id.toString(); // 這個客戶是停用的

      const response = await request(app)
        .post(`/api/v1/customers/${customerId}/activate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.isActive).toBe(true);
    });
  });

  describe('POST /api/v1/customers/:id/deactivate', () => {
    test('should deactivate customer', async () => {
      const customerId = testCustomers[0].id.toString();

      const response = await request(app)
        .post(`/api/v1/customers/${customerId}/deactivate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.isActive).toBe(false);
    });
  });

  describe('GET /api/v1/customers/email/:email', () => {
    test('should find customer by email', async () => {
      const response = await request(app)
        .get('/api/v1/customers/email/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.email).toBe('<EMAIL>');
    });

    test('should fail with non-existent email', async () => {
      await request(app)
        .get('/api/v1/customers/email/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/customers/phone/:phone', () => {
    test('should find customer by phone', async () => {
      const response = await request(app)
        .get('/api/v1/customers/phone/0912345678')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.customer.phone).toBe('0912345678');
    });

    test('should fail with non-existent phone', async () => {
      await request(app)
        .get('/api/v1/customers/phone/0900000000')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/customers/check-email/:email', () => {
    test('should check email availability', async () => {
      const response = await request(app)
        .get('/api/v1/customers/check-email/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
    });

    test('should detect existing email', async () => {
      const response = await request(app)
        .get('/api/v1/customers/check-email/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(false);
    });
  });

  describe('GET /api/v1/customers/check-phone/:phone', () => {
    test('should check phone availability', async () => {
      const response = await request(app)
        .get('/api/v1/customers/check-phone/0900000000')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
    });

    test('should detect existing phone', async () => {
      const response = await request(app)
        .get('/api/v1/customers/check-phone/0912345678')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(false);
    });
  });

  describe('DELETE /api/v1/customers/:id', () => {
    test('should delete customer as admin', async () => {
      // 先創建一個測試客戶用於刪除
      const testCustomer = await prisma.customer.create({
        data: {
          name: 'Customer to Delete',
          email: '<EMAIL>',
        },
      });

      const response = await request(app)
        .delete(`/api/v1/customers/${testCustomer.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should fail without admin permission', async () => {
      const customerId = testCustomers[0].id.toString();

      await request(app)
        .delete(`/api/v1/customers/${customerId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .expect(403);
    });
  });
});
