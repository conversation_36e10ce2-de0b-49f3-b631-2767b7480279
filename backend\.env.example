# 應用配置
NODE_ENV=development
PORT=5000
APP_NAME=客退維修品記錄管理系統
APP_VERSION=1.0.0

# 資料庫配置
DATABASE_URL="mysql://username:password@localhost:3306/repair_management"

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# 密碼加密
BCRYPT_ROUNDS=12

# CORS配置
CORS_ORIGIN=http://localhost:3000

# 檔案上傳配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Azure AD配置
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret

# SharePoint配置
SHAREPOINT_SITE_ID=your-site-id
SHAREPOINT_SITE_URL=https://yourtenant.sharepoint.com/sites/repair-management
SHAREPOINT_DRIVE_ID=your-drive-id

# 郵件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis配置 (可選)
REDIS_URL=redis://localhost:6379

# 日誌配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 安全配置
HELMET_CSP_DIRECTIVES=default-src 'self'
TRUST_PROXY=false

# 監控配置
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics

# 備份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# 開發配置
DEBUG=repair-management:*
SWAGGER_ENABLED=true
