@echo off
chcp 65001 >nul
echo ========================================
echo   IACT MIO維保管理系統 打包工具
echo ========================================
echo.

echo 正在建立部署包...
echo.

REM 建立部署資料夾
set PACKAGE_NAME=IACT_MIO維保管理系統_v1.0_%date:~0,4%%date:~5,2%%date:~8,2%
mkdir "%PACKAGE_NAME%" 2>nul

echo [1/6] 複製主程式檔案...
copy "complete-system-gui.html" "%PACKAGE_NAME%\" >nul
if exist "%PACKAGE_NAME%\complete-system-gui.html" (
    echo     ✓ complete-system-gui.html 複製成功
) else (
    echo     ✗ complete-system-gui.html 複製失敗
)

echo [2/6] 複製啟動腳本...
copy "run.bat" "%PACKAGE_NAME%\" >nul 2>&1
if exist "%PACKAGE_NAME%\run.bat" (
    echo     ✓ run.bat 複製成功
) else (
    echo     ✗ run.bat 不存在，建立新的啟動腳本
    echo @echo off > "%PACKAGE_NAME%\run.bat"
    echo chcp 65001 ^>nul >> "%PACKAGE_NAME%\run.bat"
    echo echo 啟動 IACT MIO維保管理系統... >> "%PACKAGE_NAME%\run.bat"
    echo start "" "complete-system-gui.html" >> "%PACKAGE_NAME%\run.bat"
    echo     ✓ 新的 run.bat 建立成功
)

copy "啟動維修記錄管理系統.vbs" "%PACKAGE_NAME%\" >nul 2>&1
if exist "%PACKAGE_NAME%\啟動維修記錄管理系統.vbs" (
    echo     ✓ 啟動維修記錄管理系統.vbs 複製成功
) else (
    echo     ✗ 啟動維修記錄管理系統.vbs 不存在，建立新的無視窗啟動腳本
    echo Set objShell = CreateObject("WScript.Shell") > "%PACKAGE_NAME%\啟動維修記錄管理系統.vbs"
    echo objShell.Run "complete-system-gui.html", 0, False >> "%PACKAGE_NAME%\啟動維修記錄管理系統.vbs"
    echo     ✓ 新的 啟動維修記錄管理系統.vbs 建立成功
)

echo [3/6] 複製說明文件...
copy "部署說明.md" "%PACKAGE_NAME%\" >nul 2>&1
if exist "%PACKAGE_NAME%\部署說明.md" (
    echo     ✓ 部署說明.md 複製成功
) else (
    echo     ✗ 部署說明.md 不存在，將建立簡化版說明
)

echo [4/6] 建立使用說明...
(
echo # IACT MIO維保管理系統 使用說明
echo.
echo ## 🚀 快速開始
echo.
echo ### Windows 用戶
echo 1. 雙擊 `run.bat` 啟動系統
echo 2. 或雙擊 `啟動維修記錄管理系統.vbs` ^(無視窗啟動^)
echo 3. 系統會在預設瀏覽器中開啟
echo.
echo ### macOS/Linux 用戶
echo 1. 雙擊 `complete-system-gui.html` 檔案
echo 2. 系統會在預設瀏覽器中開啟
echo.
echo ## 🔑 登入資訊
echo.
echo 測試帳號：
echo - 用戶名：admin
echo - 密碼：admin123
echo.
echo 或
echo - 用戶名：service
echo - 密碼：service123
echo.
echo ## 💾 數據存儲
echo.
echo - 數據自動保存在瀏覽器的 LocalStorage 中
echo - 重啟瀏覽器後數據會自動載入
echo - 建議定期使用系統內建的匯出功能備份數據
echo.
echo ## ⚙️ 系統需求
echo.
echo - 瀏覽器：Chrome 80+, Firefox 75+, Edge 80+, Safari 13+
echo - 作業系統：Windows 7+, macOS 10.12+, Linux
echo - 記憶體：512MB 可用記憶體
echo - 硬碟：^<1MB 程式檔案
echo.
echo ## 🔧 主要功能
echo.
echo - ✅ 維修記錄管理
echo - ✅ 客戶資料管理
echo - ✅ 零件庫存管理
echo - ✅ 統計報表分析
echo - ✅ 數據匯出/匯入
echo - ✅ SharePoint 整合準備
echo.
echo ## 📞 技術支援
echo.
echo 如遇到問題：
echo 1. 確認瀏覽器版本符合需求
echo 2. 嘗試重新整理頁面
echo 3. 檢查瀏覽器控制台錯誤訊息
echo 4. 重啟瀏覽器後再試
echo.
echo ## 🔒 數據安全
echo.
echo - 數據存儲在本地瀏覽器中，不會上傳到網路
echo - 清除瀏覽器數據會導致資料遺失
echo - 建議定期備份重要數據
echo.
echo ---
echo.
echo 版本：v1.0
echo 建立日期：%date% %time:~0,5%
echo 系統名稱：IACT MIO維保管理系統
) > "%PACKAGE_NAME%\使用說明.md"
echo     ✓ 使用說明.md 建立成功

echo [5/6] 建立跨平台啟動腳本...
(
echo #!/bin/bash
echo echo "啟動 IACT MIO維保管理系統..."
echo.
echo # 檢查作業系統
echo if [[ "$OSTYPE" == "darwin"* ]]; then
echo     # macOS
echo     open complete-system-gui.html
echo elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
echo     # Linux
echo     xdg-open complete-system-gui.html
echo else
echo     echo "請手動開啟 complete-system-gui.html"
echo fi
) > "%PACKAGE_NAME%\start.sh"
echo     ✓ start.sh 建立成功 ^(macOS/Linux^)

echo [6/6] 建立版本資訊...
(
echo {
echo   "name": "IACT MIO維保管理系統",
echo   "version": "1.0.0",
echo   "description": "專業的維修記錄管理系統",
echo   "buildDate": "%date% %time:~0,8%",
echo   "features": [
echo     "維修記錄管理",
echo     "客戶資料管理", 
echo     "零件庫存管理",
echo     "統計報表分析",
echo     "數據匯出匯入",
echo     "SharePoint整合準備"
echo   ],
echo   "requirements": {
echo     "browser": "Chrome 80+, Firefox 75+, Edge 80+, Safari 13+",
echo     "os": "Windows 7+, macOS 10.12+, Linux",
echo     "memory": "512MB",
echo     "storage": "^<1MB"
echo   },
echo   "files": [
echo     "complete-system-gui.html",
echo     "run.bat",
echo     "啟動維修記錄管理系統.vbs",
echo     "start.sh",
echo     "使用說明.md",
echo     "version.json"
echo   ]
echo }
) > "%PACKAGE_NAME%\version.json"
echo     ✓ version.json 建立成功

echo.
echo ========================================
echo   打包完成！
echo ========================================
echo.
echo 📦 部署包位置：%PACKAGE_NAME%
echo 📁 包含檔案：
dir "%PACKAGE_NAME%" /b
echo.
echo 🚀 使用方式：
echo   1. 將整個資料夾複製到目標電腦
echo   2. Windows: 雙擊 run.bat 啟動
echo   3. macOS/Linux: 雙擊 complete-system-gui.html
echo.
echo 💡 提示：
echo   - 可將資料夾複製到 USB 隨身碟中使用
echo   - 支援所有主流作業系統和瀏覽器
echo   - 數據會自動保存在瀏覽器中
echo.
echo 按任意鍵關閉...
pause >nul
