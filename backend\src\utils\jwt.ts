import jwt from 'jsonwebtoken';
import { logger } from './logger';

// JWT 配置
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// JWT Payload 介面
export interface JWTPayload {
  userId: string;
  username: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Refresh Token Payload 介面
export interface RefreshTokenPayload {
  userId: string;
  tokenVersion: number;
  iat?: number;
  exp?: number;
}

// Token 對象介面
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// JWT 工具類
export class JWTUtils {
  // 生成訪問令牌
  static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN,
        issuer: 'repair-management-system',
        audience: 'repair-management-users',
      });
    } catch (error) {
      logger.error('生成訪問令牌失敗:', error);
      throw new Error('Token generation failed');
    }
  }

  // 生成刷新令牌
  static generateRefreshToken(payload: Omit<RefreshTokenPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload, JWT_REFRESH_SECRET, {
        expiresIn: JWT_REFRESH_EXPIRES_IN,
        issuer: 'repair-management-system',
        audience: 'repair-management-users',
      });
    } catch (error) {
      logger.error('生成刷新令牌失敗:', error);
      throw new Error('Refresh token generation failed');
    }
  }

  // 生成令牌對
  static generateTokenPair(user: {
    id: string;
    username: string;
    email: string;
    role: string;
  }, tokenVersion: number = 0): TokenPair {
    const accessToken = this.generateAccessToken({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    });

    const refreshToken = this.generateRefreshToken({
      userId: user.id,
      tokenVersion,
    });

    // 計算過期時間（秒）
    const expiresIn = this.getTokenExpirationTime(JWT_EXPIRES_IN);

    return {
      accessToken,
      refreshToken,
      expiresIn,
    };
  }

  // 驗證訪問令牌
  static verifyAccessToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, JWT_SECRET, {
        issuer: 'repair-management-system',
        audience: 'repair-management-users',
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Access token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid access token');
      } else {
        logger.error('訪問令牌驗證失敗:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  // 驗證刷新令牌
  static verifyRefreshToken(token: string): RefreshTokenPayload {
    try {
      const decoded = jwt.verify(token, JWT_REFRESH_SECRET, {
        issuer: 'repair-management-system',
        audience: 'repair-management-users',
      }) as RefreshTokenPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else {
        logger.error('刷新令牌驗證失敗:', error);
        throw new Error('Refresh token verification failed');
      }
    }
  }

  // 解碼令牌（不驗證）
  static decodeToken(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      logger.error('令牌解碼失敗:', error);
      return null;
    }
  }

  // 檢查令牌是否即將過期
  static isTokenExpiringSoon(token: string, thresholdMinutes: number = 5): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return true;
      }

      const now = Math.floor(Date.now() / 1000);
      const threshold = thresholdMinutes * 60;
      
      return (decoded.exp - now) <= threshold;
    } catch (error) {
      return true;
    }
  }

  // 獲取令牌剩餘時間（秒）
  static getTokenRemainingTime(token: string): number {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return 0;
      }

      const now = Math.floor(Date.now() / 1000);
      return Math.max(0, decoded.exp - now);
    } catch (error) {
      return 0;
    }
  }

  // 將時間字符串轉換為秒數
  private static getTokenExpirationTime(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 900; // 默認15分鐘
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 60 * 60 * 24;
      default:
        return 900;
    }
  }

  // 生成安全的隨機字符串
  static generateSecureToken(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  // 驗證JWT配置
  static validateJWTConfig(): boolean {
    const issues: string[] = [];

    if (!JWT_SECRET || JWT_SECRET === 'fallback-secret-key') {
      issues.push('JWT_SECRET 未設置或使用默認值');
    }

    if (!JWT_REFRESH_SECRET || JWT_REFRESH_SECRET === 'fallback-refresh-secret') {
      issues.push('JWT_REFRESH_SECRET 未設置或使用默認值');
    }

    if (JWT_SECRET === JWT_REFRESH_SECRET) {
      issues.push('JWT_SECRET 和 JWT_REFRESH_SECRET 不應相同');
    }

    if (JWT_SECRET.length < 32) {
      issues.push('JWT_SECRET 長度應至少32個字符');
    }

    if (issues.length > 0) {
      logger.warn('JWT 配置問題:', issues);
      return false;
    }

    return true;
  }
}

// 導出常用函數
export const {
  generateAccessToken,
  generateRefreshToken,
  generateTokenPair,
  verifyAccessToken,
  verifyRefreshToken,
  decodeToken,
  isTokenExpiringSoon,
  getTokenRemainingTime,
  generateSecureToken,
  validateJWTConfig,
} = JWTUtils;
