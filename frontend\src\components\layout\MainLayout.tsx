import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Typo<PERSON>, Button, Space, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  ShoppingOutlined,
  ToolOutlined,
  FileTextOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  ProfileOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { logoutAsync } from '../../store/slices/authSlice';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const handleLogout = async () => {
    await dispatch(logoutAsync());
    navigate('/login');
  };

  // 用戶下拉菜單
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '個人資料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '帳戶設定',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '登出',
      onClick: handleLogout,
    },
  ];

  // 側邊欄菜單項目
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '儀表板',
    },
    {
      key: '/repair-records',
      icon: <ToolOutlined />,
      label: '維修記錄',
    },
    {
      key: '/customers',
      icon: <TeamOutlined />,
      label: '客戶管理',
    },
    {
      key: '/products',
      icon: <ShoppingOutlined />,
      label: '產品管理',
    },
    {
      key: '/parts',
      icon: <FileTextOutlined />,
      label: '零件管理',
    },
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: '統計報表',
    },
    ...(user?.role === 'ADMIN' ? [
      {
        key: '/admin',
        icon: <SettingOutlined />,
        label: '系統管理',
        children: [
          {
            key: '/admin/users',
            label: '用戶管理',
          },
          {
            key: '/admin/settings',
            label: '系統設定',
          },
          {
            key: '/admin/logs',
            label: '操作日誌',
          },
        ],
      },
    ] : []),
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      'VIEWER': '查詢用戶',
      'TECHNICIAN': '維修人員',
      'CUSTOMER_SERVICE': '客服人員',
      'ADMIN': '系統管理員',
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0',
          padding: '0 16px'
        }}>
          {!collapsed ? (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              維修管理
            </Title>
          ) : (
            <ToolOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          )}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0, marginTop: 8 }}
        />
      </Sider>

      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          zIndex: 1
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
          </div>

          <Space size="large">
            <Badge count={0} showZero={false}>
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                style={{ fontSize: '16px' }}
              />
            </Badge>

            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: '6px',
                transition: 'background-color 0.3s'
              }}>
                <Avatar 
                  icon={<UserOutlined />} 
                  style={{ marginRight: 8 }}
                />
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Text strong>{user?.name}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {getRoleDisplayName(user?.role || '')}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </Space>
        </Header>

        <Content style={{ 
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          overflow: 'auto'
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
