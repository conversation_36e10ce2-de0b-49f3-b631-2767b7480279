import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Modal, 
  message, 
  Popconfirm,
  Select,
  Row,
  Col,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Customer, CustomerQueryParams } from '../../services/customerService';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬數據 - 實際應用中會從 API 獲取
const mockCustomers: Customer[] = [
  {
    id: 1,
    name: '張先生',
    email: '<EMAIL>',
    phone: '0912-345-678',
    address: '台北市信義區信義路五段7號',
    company: 'ABC科技有限公司',
    contactPerson: '張經理',
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    name: '李小姐',
    email: '<EMAIL>',
    phone: '0923-456-789',
    address: '新北市板橋區中山路一段161號',
    company: '',
    contactPerson: '',
    isActive: true,
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-14T14:20:00Z',
  },
  {
    id: 3,
    name: '王先生',
    email: '<EMAIL>',
    phone: '0934-567-890',
    address: '台中市西屯區台灣大道三段99號',
    company: 'XYZ企業股份有限公司',
    contactPerson: '王總監',
    isActive: false,
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z',
  },
];

interface CustomerListProps {
  onEdit?: (customer: Customer) => void;
  onAdd?: () => void;
}

const CustomerList: React.FC<CustomerListProps> = ({ onEdit, onAdd }) => {
  const [customers, setCustomers] = useState<Customer[]>(mockCustomers);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockCustomers.length,
  });

  // 模擬 API 調用
  const fetchCustomers = async (params?: CustomerQueryParams) => {
    setLoading(true);
    try {
      // 這裡會調用實際的 customerService.getCustomers(params)
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = [...mockCustomers];
      
      // 搜尋過濾
      if (params?.search) {
        filteredData = filteredData.filter(customer =>
          customer.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          customer.email.toLowerCase().includes(params.search!.toLowerCase()) ||
          customer.phone.includes(params.search!) ||
          customer.company?.toLowerCase().includes(params.search!.toLowerCase())
        );
      }
      
      // 狀態過濾
      if (params?.isActive !== undefined) {
        filteredData = filteredData.filter(customer => customer.isActive === params.isActive);
      }
      
      setCustomers(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length,
      }));
    } catch (error) {
      message.error('載入客戶資料失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomers({
      search: searchText,
      isActive: statusFilter,
      page: pagination.current,
      limit: pagination.pageSize,
    });
  }, [searchText, statusFilter, pagination.current, pagination.pageSize]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleStatusFilter = (value: boolean | undefined) => {
    setStatusFilter(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleDelete = async (id: number) => {
    try {
      // 這裡會調用 customerService.deleteCustomer(id)
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('客戶刪除成功');
      fetchCustomers();
    } catch (error) {
      message.error('刪除客戶失敗');
    }
  };

  const handleToggleStatus = async (customer: Customer) => {
    try {
      // 這裡會調用 customerService.activateCustomer 或 deactivateCustomer
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`客戶${customer.isActive ? '停用' : '激活'}成功`);
      fetchCustomers();
    } catch (error) {
      message.error(`${customer.isActive ? '停用' : '激活'}客戶失敗`);
    }
  };

  const columns: ColumnsType<Customer> = [
    {
      title: '客戶姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (text, record) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '聯絡資訊',
      key: 'contact',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <MailOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            <span style={{ fontSize: '12px' }}>{record.email}</span>
          </div>
          <div>
            <PhoneOutlined style={{ marginRight: 4, color: '#52c41a' }} />
            <span style={{ fontSize: '12px' }}>{record.phone}</span>
          </div>
        </div>
      ),
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (address) => (
        <Tooltip placement="topLeft" title={address}>
          <Space>
            <HomeOutlined />
            <span>{address}</span>
          </Space>
        </Tooltip>
      ),
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      width: 150,
      render: (company) => company || '-',
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '正常' : '停用'}
        </Tag>
      ),
    },
    {
      title: '註冊時間',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString('zh-TW'),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '停用' : '激活'}>
            <Button
              type="text"
              onClick={() => handleToggleStatus(record)}
              style={{ color: record.isActive ? '#faad14' : '#52c41a' }}
            >
              {record.isActive ? '停用' : '激活'}
            </Button>
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個客戶嗎？"
            description="刪除後將無法恢復，請謹慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              客戶管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜尋客戶姓名、郵件、電話或公司"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="狀態篩選"
                allowClear
                style={{ width: 120 }}
                onChange={handleStatusFilter}
              >
                <Option value={true}>正常</Option>
                <Option value={false}>停用</Option>
              </Select>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchCustomers()}
                loading={loading}
              >
                重新整理
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('匯出功能開發中')}
              >
                匯出
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                新增客戶
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Table
        columns={columns}
        dataSource={customers}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        scroll={{ x: 1200 }}
        size="small"
      />
    </Card>
  );
};

export default CustomerList;
