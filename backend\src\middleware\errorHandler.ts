import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // 設定默認錯誤狀態碼
  err.statusCode = err.statusCode || 500;
  
  // 記錄錯誤
  logger.error({
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // 開發環境返回詳細錯誤信息
  if (process.env.NODE_ENV === 'development') {
    return res.status(err.statusCode).json({
      success: false,
      error: {
        message: err.message,
        stack: err.stack,
        statusCode: err.statusCode,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // 生產環境返回簡化錯誤信息
  const isOperationalError = err.isOperational || err.statusCode < 500;
  
  res.status(err.statusCode).json({
    success: false,
    error: {
      message: isOperationalError ? err.message : '內部服務器錯誤',
      code: err.statusCode,
    },
    timestamp: new Date().toISOString(),
  });
};

export const createError = (message: string, statusCode: number = 500): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};
