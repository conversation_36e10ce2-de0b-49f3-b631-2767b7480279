import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Progress,
  Tag,
  Space,
  Alert,
  List,
  Avatar,
  Divider
} from 'antd';
import { 
  UserOutlined,
  TeamOutlined,
  ShieldCheckOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ServerOutlined,
  HddOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { SystemStats } from '../../services/systemService';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

// 模擬數據
const mockSystemStats: SystemStats = {
  totalUsers: 24,
  activeUsers: 18,
  totalRoles: 4,
  totalPermissions: 32,
  systemUptime: 2592000, // 30天（秒）
  lastBackup: '2024-01-20T02:00:00Z',
  diskUsage: {
    total: 1000000000000, // 1TB
    used: 450000000000,   // 450GB
    free: 550000000000,   // 550GB
    percentage: 45
  },
  memoryUsage: {
    total: 16000000000,   // 16GB
    used: 8500000000,     // 8.5GB
    free: 7500000000,     // 7.5GB
    percentage: 53
  }
};

const mockRecentActivities = [
  {
    id: 1,
    user: '系統管理員',
    action: '創建新用戶',
    target: '張技師',
    time: '2024-01-20T14:30:00Z',
    type: 'user'
  },
  {
    id: 2,
    user: '系統管理員',
    action: '更新系統設定',
    target: '通知配置',
    time: '2024-01-20T13:15:00Z',
    type: 'setting'
  },
  {
    id: 3,
    user: '客服主管',
    action: '修改角色權限',
    target: '客服人員角色',
    time: '2024-01-20T11:45:00Z',
    type: 'role'
  },
  {
    id: 4,
    user: '系統管理員',
    action: '執行數據備份',
    target: '完整備份',
    time: '2024-01-20T02:00:00Z',
    type: 'backup'
  },
  {
    id: 5,
    user: '維修主管',
    action: '匯出報表',
    target: '月度維修報表',
    time: '2024-01-19T16:20:00Z',
    type: 'export'
  }
];

const mockSystemHealth = [
  { name: '數據庫連接', status: 'healthy', message: '正常運行' },
  { name: '郵件服務', status: 'healthy', message: '正常運行' },
  { name: '文件存儲', status: 'warning', message: '磁碟空間不足' },
  { name: '備份服務', status: 'healthy', message: '最近備份: 今天 02:00' },
  { name: '安全掃描', status: 'healthy', message: '無安全威脅' }
];

const SystemOverview: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<SystemStats>(mockSystemStats);

  useEffect(() => {
    fetchSystemStats();
  }, []);

  const fetchSystemStats = async () => {
    setLoading(true);
    try {
      // 這裡會調用 systemService.getSystemStats()
      await new Promise(resolve => setTimeout(resolve, 500));
      setStats(mockSystemStats);
    } catch (error) {
      console.error('Failed to fetch system stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小時 ${minutes}分鐘`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getActivityIcon = (type: string) => {
    const icons = {
      user: <UserOutlined style={{ color: '#1890ff' }} />,
      setting: <SettingOutlined style={{ color: '#52c41a' }} />,
      role: <ShieldCheckOutlined style={{ color: '#fa8c16' }} />,
      backup: <DatabaseOutlined style={{ color: '#722ed1' }} />,
      export: <HddOutlined style={{ color: '#13c2c2' }} />
    };
    return icons[type as keyof typeof icons] || <ClockCircleOutlined />;
  };

  const getHealthStatus = (status: string) => {
    const statusConfig = {
      healthy: { color: '#52c41a', icon: <CheckCircleOutlined /> },
      warning: { color: '#faad14', icon: <WarningOutlined /> },
      error: { color: '#f5222d', icon: <WarningOutlined /> }
    };
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.healthy;
  };

  return (
    <div>
      {/* 核心統計 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總用戶數"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              suffix={
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  活躍: {stats.activeUsers}
                </Tag>
              }
            />
            <Progress 
              percent={Math.round((stats.activeUsers / stats.totalUsers) * 100)} 
              size="small" 
              status="active"
              format={() => `活躍率 ${Math.round((stats.activeUsers / stats.totalUsers) * 100)}%`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="角色數量"
              value={stats.totalRoles}
              prefix={<TeamOutlined />}
              suffix={
                <Tag color="green" style={{ marginLeft: 8 }}>
                  權限: {stats.totalPermissions}
                </Tag>
              }
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">權限管理正常</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="系統運行時間"
              value={formatUptime(stats.systemUptime)}
              prefix={<ServerOutlined />}
              valueStyle={{ fontSize: '16px' }}
            />
            <div style={{ marginTop: 8 }}>
              <Tag color="green">穩定運行</Tag>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="最近備份"
              value={dayjs(stats.lastBackup).format('MM/DD HH:mm')}
              prefix={<DatabaseOutlined />}
              valueStyle={{ fontSize: '16px' }}
            />
            <div style={{ marginTop: 8 }}>
              <Tag color="blue">自動備份</Tag>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 系統資源使用情況 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="磁碟使用情況" loading={loading}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>已使用</Text>
                <Text strong>{formatBytes(stats.diskUsage.used)} / {formatBytes(stats.diskUsage.total)}</Text>
              </div>
              <Progress 
                percent={stats.diskUsage.percentage} 
                status={stats.diskUsage.percentage > 80 ? 'exception' : 'active'}
                strokeColor={stats.diskUsage.percentage > 80 ? '#f5222d' : '#1890ff'}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text type="secondary">可用空間: {formatBytes(stats.diskUsage.free)}</Text>
              {stats.diskUsage.percentage > 80 && (
                <Tag color="red">空間不足</Tag>
              )}
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="記憶體使用情況" loading={loading}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>已使用</Text>
                <Text strong>{formatBytes(stats.memoryUsage.used)} / {formatBytes(stats.memoryUsage.total)}</Text>
              </div>
              <Progress 
                percent={stats.memoryUsage.percentage} 
                status={stats.memoryUsage.percentage > 90 ? 'exception' : 'active'}
                strokeColor={stats.memoryUsage.percentage > 90 ? '#f5222d' : '#52c41a'}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text type="secondary">可用記憶體: {formatBytes(stats.memoryUsage.free)}</Text>
              {stats.memoryUsage.percentage > 90 && (
                <Tag color="red">記憶體不足</Tag>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 系統健康狀態 */}
        <Col xs={24} lg={12}>
          <Card title="系統健康狀態" loading={loading}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {mockSystemHealth.map((item, index) => {
                const statusConfig = getHealthStatus(item.status);
                return (
                  <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Space>
                      <span style={{ color: statusConfig.color }}>
                        {statusConfig.icon}
                      </span>
                      <Text strong>{item.name}</Text>
                    </Space>
                    <div style={{ textAlign: 'right' }}>
                      <div>
                        <Tag color={item.status === 'healthy' ? 'green' : item.status === 'warning' ? 'orange' : 'red'}>
                          {item.status === 'healthy' ? '正常' : item.status === 'warning' ? '警告' : '錯誤'}
                        </Tag>
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {item.message}
                      </Text>
                    </div>
                  </div>
                );
              })}
            </Space>
          </Card>
        </Col>

        {/* 最近活動 */}
        <Col xs={24} lg={12}>
          <Card title="最近活動" loading={loading}>
            <List
              itemLayout="horizontal"
              dataSource={mockRecentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar icon={getActivityIcon(item.type)} />
                    }
                    title={
                      <Space>
                        <Text strong>{item.user}</Text>
                        <Text>{item.action}</Text>
                        <Text type="secondary">{item.target}</Text>
                      </Space>
                    }
                    description={
                      <Text type="secondary">
                        {dayjs(item.time).format('MM/DD HH:mm')}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 系統警告 */}
      {stats.diskUsage.percentage > 80 && (
        <Alert
          message="磁碟空間警告"
          description={`系統磁碟使用率已達到 ${stats.diskUsage.percentage}%，建議清理不必要的文件或擴展存儲空間。`}
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
          action={
            <Space>
              <Button size="small">清理文件</Button>
              <Button size="small" type="primary">擴展存儲</Button>
            </Space>
          }
        />
      )}
    </div>
  );
};

export default SystemOverview;
