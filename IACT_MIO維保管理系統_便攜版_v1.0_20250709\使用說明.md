# IACT MIO維保管理系統 便攜版 v1.0

## 🚀 快速開始

### Windows 用戶
1. **雙擊 `啟動系統.bat`** 啟動系統
2. **或雙擊 `無視窗啟動.vbs`** (無視窗啟動)
3. 系統會在預設瀏覽器中開啟

### macOS/Linux 用戶
1. **雙擊 `complete-system-gui.html`** 檔案
2. 系統會在預設瀏覽器中開啟

## 🔑 登入資訊

### 管理員帳號
- **用戶名**：admin
- **密碼**：admin123
- **權限**：完整系統管理權限

### 客服帳號
- **用戶名**：service
- **密碼**：service123
- **權限**：維修記錄管理權限

## 💾 數據存儲

### LocalStorage 自動保存
- ✅ 數據自動保存在瀏覽器的 LocalStorage 中
- ✅ 重啟瀏覽器後數據會自動載入
- ✅ 支援離線使用，無需網路連線
- ⚠️ 清除瀏覽器數據會導致資料遺失

### 數據備份建議
- 定期使用系統內建的 **"📤 匯出所有數據"** 功能備份數據
- 將備份檔案保存在安全的位置
- 重要數據建議保留多個版本的備份

## ⚙️ 系統需求

### 瀏覽器需求
- **Chrome** 80+ (推薦)
- **Firefox** 75+
- **Edge** 80+
- **Safari** 13+

### 作業系統支援
- **Windows** 7/8/10/11
- **macOS** 10.12+
- **Linux** (主流發行版)

### 硬體需求
- **記憶體**：512MB 可用記憶體
- **硬碟**：<1MB 程式檔案
- **螢幕**：1024x768 以上解析度

## 🔧 主要功能

### 維修記錄管理
- ✅ **新增維修記錄**：完整的維修資訊登記
- ✅ **編輯記錄**：修改現有維修記錄
- ✅ **查看詳情**：詳細的維修記錄檢視
- ✅ **刪除記錄**：安全的記錄刪除功能
- ✅ **篩選搜尋**：多條件篩選和搜尋

### 客戶資料管理
- ✅ **客戶資訊維護**：完整的客戶資料管理
- ✅ **聯絡資訊**：電話、地址等聯絡方式
- ✅ **維修歷史**：客戶的維修記錄追蹤

### 零件庫存管理
- ✅ **庫存追蹤**：即時的零件庫存管理
- ✅ **導入導出**：批量零件資料處理
- ✅ **低庫存警告**：自動庫存不足提醒
- ✅ **供應商管理**：零件供應商資訊維護

### 統計報表分析
- ✅ **維修統計**：詳細的維修數據分析
- ✅ **趨勢分析**：維修趨勢和模式分析
- ✅ **圖表展示**：直觀的數據可視化
- ✅ **報表匯出**：統計報表匯出功能

### 數據管理
- ✅ **匯出功能**：完整數據匯出為 JSON 格式
- ✅ **匯入功能**：從備份檔案恢復數據
- ✅ **數據同步**：手動觸發數據同步
- ✅ **清除功能**：安全的數據清除選項

### SharePoint 整合準備
- ✅ **雙模式架構**：支援 LocalStorage 和 SharePoint
- ✅ **連線測試**：SharePoint 連線狀態檢測
- ✅ **數據遷移**：LocalStorage 到 SharePoint 遷移
- ✅ **企業級擴展**：為企業級應用做準備

## 📱 行動裝置支援

### 響應式設計
- ✅ 支援手機和平板電腦瀏覽器存取
- ✅ 自動適應不同螢幕大小
- ✅ 觸控操作優化
- ✅ 行動裝置友善的界面設計

### PWA 支援
- 可將系統安裝為手機應用程式
- 支援離線使用
- 類似原生應用的使用體驗

## 🔒 數據安全

### 本地存儲
- ✅ 數據存儲在本地瀏覽器中，不會上傳到網路
- ✅ 完全離線運行，保護數據隱私
- ✅ 符合數據保護法規要求

### 安全建議
- 定期備份重要數據
- 避免在公共電腦上使用
- 使用強密碼保護電腦帳戶
- 定期更新瀏覽器版本

## 📞 技術支援

### 常見問題

#### 1. 系統無法開啟
**解決方案**：
- 檢查瀏覽器版本是否符合需求
- 嘗試使用不同的瀏覽器
- 確認檔案完整性

#### 2. 數據消失
**解決方案**：
- 檢查是否清除了瀏覽器數據
- 從備份檔案恢復數據
- 確認使用相同的瀏覽器和用戶設定檔

#### 3. 功能異常
**解決方案**：
- 重新整理頁面 (F5)
- 重啟瀏覽器
- 清除瀏覽器快取後重新載入

#### 4. 效能問題
**解決方案**：
- 關閉不必要的瀏覽器分頁
- 確保有足夠的可用記憶體
- 定期重啟瀏覽器

### 系統檢查

#### 瀏覽器控制台檢查
1. 在瀏覽器中按 **F12** 開啟開發者工具
2. 點擊 **Console** 標籤
3. 查看是否有紅色錯誤訊息
4. 截圖錯誤訊息以便技術支援

#### 相容性檢查
```javascript
// 在瀏覽器控制台執行以下代碼檢查相容性
console.log('瀏覽器:', navigator.userAgent);
console.log('LocalStorage 支援:', typeof(Storage) !== "undefined");
console.log('螢幕解析度:', screen.width + 'x' + screen.height);
```

## 🎯 使用技巧

### 快速操作
- **Ctrl+F**：快速搜尋功能
- **F5**：重新整理頁面
- **F11**：全螢幕模式
- **Ctrl+Shift+I**：開啟開發者工具

### 數據管理最佳實踐
1. **定期備份**：每週備份一次重要數據
2. **版本控制**：保留多個版本的備份檔案
3. **測試恢復**：定期測試備份檔案的恢復功能
4. **文檔記錄**：記錄重要的系統設定和操作

## 🔄 便攜版特色

### 零安裝部署
- ✅ 無需安裝任何軟體
- ✅ 無需管理員權限
- ✅ 無需網路連線
- ✅ 無需複雜設定

### 跨平台相容
- ✅ 支援所有主流作業系統
- ✅ 支援所有現代瀏覽器
- ✅ 一致的使用體驗
- ✅ 無平台依賴性

### 便攜性優勢
- ✅ 可放在 USB 隨身碟中使用
- ✅ 可複製到任何電腦使用
- ✅ 檔案大小小於 1MB
- ✅ 啟動速度快

### 數據安全性
- ✅ 數據完全在本地存儲
- ✅ 不依賴外部服務
- ✅ 符合隱私保護要求
- ✅ 可完全離線使用

---

## 📋 版本資訊

- **版本**：v1.0 便攜版
- **建立日期**：2025-07-09
- **系統名稱**：IACT MIO維保管理系統
- **部署方式**：便攜式網頁應用
- **檔案大小**：<1MB
- **支援語言**：繁體中文

## 🎉 開始使用

現在您可以：

1. **立即啟動**：雙擊啟動腳本開始使用
2. **登入系統**：使用提供的測試帳號登入
3. **探索功能**：體驗完整的維保管理功能
4. **備份數據**：設定定期數據備份計劃

**祝您使用愉快！** 🚀
