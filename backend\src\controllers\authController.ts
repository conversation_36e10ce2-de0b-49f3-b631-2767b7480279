import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { JWTUtils } from '../utils/jwt';
import { PasswordUtils } from '../utils/password';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';

const prisma = new PrismaClient();

// 登入請求介面
interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// 註冊請求介面
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  fullName: string;
  role?: string;
}

// 刷新令牌請求介面
interface RefreshTokenRequest {
  refreshToken: string;
}

// 修改密碼請求介面
interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export class AuthController {
  // 用戶登入
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 檢查驗證錯誤
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { username, password, rememberMe = false }: LoginRequest = req.body;

      // 查找用戶
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { username },
            { email: username },
          ],
        },
      });

      if (!user) {
        logger.warn('登入失敗 - 用戶不存在:', { username, ip: req.ip });
        throw createError('用戶名或密碼錯誤', 401);
      }

      // 檢查用戶是否為活躍狀態
      if (!user.isActive) {
        logger.warn('登入失敗 - 用戶已停用:', { userId: user.id, username });
        throw createError('用戶帳戶已被停用', 401);
      }

      // 驗證密碼
      const isPasswordValid = await PasswordUtils.verifyPassword(password, user.passwordHash);
      if (!isPasswordValid) {
        logger.warn('登入失敗 - 密碼錯誤:', { userId: user.id, username, ip: req.ip });
        throw createError('用戶名或密碼錯誤', 401);
      }

      // 檢查密碼是否需要更新
      const shouldUpdatePassword = PasswordUtils.shouldUpdatePassword(user.passwordHash);
      
      // 生成令牌對
      const tokenPair = JWTUtils.generateTokenPair({
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
      });

      // 記錄成功登入
      logger.info('用戶登入成功:', {
        userId: user.id,
        username: user.username,
        role: user.role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // 返回響應
      res.json({
        success: true,
        message: '登入成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role,
          },
          tokens: tokenPair,
          shouldUpdatePassword,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 用戶註冊
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 檢查驗證錯誤
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { username, email, password, fullName, role = 'VIEWER' }: RegisterRequest = req.body;

      // 檢查用戶名是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { username },
            { email },
          ],
        },
      });

      if (existingUser) {
        if (existingUser.username === username) {
          throw createError('用戶名已存在', 409);
        } else {
          throw createError('電子郵件已存在', 409);
        }
      }

      // 驗證密碼強度
      const passwordValidation = PasswordUtils.validatePassword(password);
      if (!passwordValidation.isValid) {
        throw createError(`密碼不符合要求: ${passwordValidation.errors.join(', ')}`, 400);
      }

      // 加密密碼
      const hashedPassword = await PasswordUtils.hashPassword(password);

      // 創建用戶
      const newUser = await prisma.user.create({
        data: {
          username,
          email,
          passwordHash: hashedPassword,
          fullName,
          role: role as any,
        },
      });

      // 生成令牌對
      const tokenPair = JWTUtils.generateTokenPair({
        id: newUser.id.toString(),
        username: newUser.username,
        email: newUser.email,
        role: newUser.role,
      });

      // 記錄註冊成功
      logger.info('用戶註冊成功:', {
        userId: newUser.id,
        username: newUser.username,
        email: newUser.email,
        role: newUser.role,
        ip: req.ip,
      });

      // 返回響應（不包含密碼）
      res.status(201).json({
        success: true,
        message: '註冊成功',
        data: {
          user: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            fullName: newUser.fullName,
            role: newUser.role,
          },
          tokens: tokenPair,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刷新令牌
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken }: RefreshTokenRequest = req.body;

      if (!refreshToken) {
        throw createError('未提供刷新令牌', 400);
      }

      // 驗證刷新令牌
      const payload = JWTUtils.verifyRefreshToken(refreshToken);

      // 查找用戶
      const user = await prisma.user.findUnique({
        where: { id: BigInt(payload.userId) },
      });

      if (!user || !user.isActive) {
        throw createError('用戶不存在或已停用', 401);
      }

      // 生成新的令牌對
      const newTokenPair = JWTUtils.generateTokenPair({
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
      });

      logger.debug('令牌刷新成功:', { userId: user.id, username: user.username });

      res.json({
        success: true,
        message: '令牌刷新成功',
        data: {
          tokens: newTokenPair,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 登出
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 在實際應用中，這裡可以將令牌加入黑名單
      // 或者在 Redis 中記錄已登出的令牌

      logger.info('用戶登出:', {
        userId: req.user?.userId,
        username: req.user?.username,
        ip: req.ip,
      });

      res.json({
        success: true,
        message: '登出成功',
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取當前用戶資訊
  static async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const user = await prisma.user.findUnique({
        where: { id: BigInt(req.user.userId) },
        select: {
          id: true,
          username: true,
          email: true,
          fullName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw createError('用戶不存在', 404);
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 修改密碼
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { currentPassword, newPassword }: ChangePasswordRequest = req.body;

      // 查找用戶
      const user = await prisma.user.findUnique({
        where: { id: BigInt(req.user.userId) },
      });

      if (!user) {
        throw createError('用戶不存在', 404);
      }

      // 驗證當前密碼
      const isCurrentPasswordValid = await PasswordUtils.verifyPassword(
        currentPassword,
        user.passwordHash
      );

      if (!isCurrentPasswordValid) {
        throw createError('當前密碼錯誤', 400);
      }

      // 驗證新密碼強度
      const passwordValidation = PasswordUtils.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        throw createError(`新密碼不符合要求: ${passwordValidation.errors.join(', ')}`, 400);
      }

      // 檢查新密碼是否與當前密碼相同
      const isSamePassword = await PasswordUtils.verifyPassword(newPassword, user.passwordHash);
      if (isSamePassword) {
        throw createError('新密碼不能與當前密碼相同', 400);
      }

      // 加密新密碼
      const hashedNewPassword = await PasswordUtils.hashPassword(newPassword);

      // 更新密碼
      await prisma.user.update({
        where: { id: user.id },
        data: { passwordHash: hashedNewPassword },
      });

      logger.info('用戶密碼修改成功:', {
        userId: user.id,
        username: user.username,
        ip: req.ip,
      });

      res.json({
        success: true,
        message: '密碼修改成功',
      });
    } catch (error) {
      next(error);
    }
  }

  // 驗證令牌
  static async verifyToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        throw createError('未提供認證令牌', 400);
      }

      const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
      if (!tokenMatch) {
        throw createError('認證令牌格式錯誤', 400);
      }

      const token = tokenMatch[1];
      const payload = JWTUtils.verifyAccessToken(token);

      // 檢查用戶是否存在且為活躍狀態
      const user = await prisma.user.findUnique({
        where: { id: BigInt(payload.userId) },
        select: { id: true, isActive: true },
      });

      if (!user || !user.isActive) {
        throw createError('用戶不存在或已停用', 401);
      }

      const remainingTime = JWTUtils.getTokenRemainingTime(token);
      const isExpiringSoon = JWTUtils.isTokenExpiringSoon(token);

      res.json({
        success: true,
        message: '令牌有效',
        data: {
          valid: true,
          payload,
          remainingTime,
          isExpiringSoon,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default AuthController;
