# IACT MIO維保管理系統 完整部署指南

## 📦 部署方案總覽

### 方案比較表

| 部署方案 | 適用場景 | 技術需求 | 多用戶支援 | 數據共享 | 部署難度 |
|---------|---------|---------|-----------|---------|---------|
| 便攜式網頁包 | 單機使用 | 僅需瀏覽器 | ❌ | ❌ | ⭐ |
| 本地伺服器 | 區域網路 | Node.js | ✅ | ✅ | ⭐⭐ |
| 雲端部署 | 遠端存取 | 雲端平台 | ✅ | ✅ | ⭐⭐⭐ |

---

## 🎯 方案一：便攜式網頁包 (推薦新手)

### 特點
- ✅ **零安裝**：無需安裝任何軟體
- ✅ **跨平台**：支援 Windows/macOS/Linux
- ✅ **便攜性**：可放在 USB 隨身碟使用
- ✅ **數據安全**：數據存儲在本地

### 快速部署步驟

#### 1. 執行打包腳本
```batch
# Windows 用戶
雙擊 package.bat

# 會自動建立部署包資料夾
```

#### 2. 複製到目標電腦
```
複製整個部署包資料夾到目標電腦任意位置
```

#### 3. 啟動系統
```batch
# Windows
雙擊 run.bat

# macOS/Linux  
雙擊 complete-system-gui.html
```

### 檔案結構
```
IACT_MIO維保管理系統_v1.0_20240120/
├── complete-system-gui.html          # 主程式
├── run.bat                          # Windows 啟動腳本
├── 啟動維修記錄管理系統.vbs          # 無視窗啟動
├── start.sh                         # macOS/Linux 啟動腳本
├── 使用說明.md                      # 使用說明
└── version.json                     # 版本資訊
```

---

## 🌐 方案二：本地伺服器部署 (推薦團隊)

### 特點
- ✅ **多用戶存取**：支援多人同時使用
- ✅ **集中數據**：統一的數據存儲
- ✅ **API 支援**：提供 REST API
- ✅ **自動備份**：伺服器端數據管理

### 部署步驟

#### 1. 安裝 Node.js
```bash
# 前往 https://nodejs.org/ 下載安裝 LTS 版本
# 或使用套件管理器安裝

# Windows (使用 Chocolatey)
choco install nodejs

# macOS (使用 Homebrew)
brew install node

# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm
```

#### 2. 準備檔案
```bash
# 複製以下檔案到伺服器目錄
complete-system-gui.html
server.js
package.json
start-server.bat (Windows)
```

#### 3. 安裝依賴
```bash
# 在專案目錄執行
npm install
```

#### 4. 啟動伺服器
```bash
# Windows
雙擊 start-server.bat

# 或手動執行
npm start

# 或使用 PM2 (生產環境)
npm install -g pm2
pm2 start server.js --name "maintenance-system"
```

#### 5. 存取系統
```
本機存取：http://localhost:3000
區域網路：http://[伺服器IP]:3000
```

### 伺服器 API 端點
```
GET  /                    # 主頁面
GET  /api/status          # 系統狀態
GET  /api/load/:dataType  # 載入數據
POST /api/save/:dataType  # 保存數據
GET  /api/backup          # 備份數據
POST /api/restore         # 恢復數據
```

---

## ☁️ 方案三：雲端部署

### 3.1 Heroku 部署 (免費)

#### 準備步驟
```bash
# 1. 註冊 Heroku 帳號
# 2. 安裝 Heroku CLI
# 3. 登入 Heroku
heroku login
```

#### 部署步驟
```bash
# 1. 初始化 Git 倉庫
git init
git add .
git commit -m "Initial commit"

# 2. 建立 Heroku 應用
heroku create your-app-name

# 3. 部署
git push heroku main

# 4. 開啟應用
heroku open
```

### 3.2 Vercel 部署 (免費)

#### 部署步驟
```bash
# 1. 安裝 Vercel CLI
npm i -g vercel

# 2. 登入 Vercel
vercel login

# 3. 部署
vercel

# 4. 設定環境變數 (如需要)
vercel env add
```

### 3.3 Netlify 部署 (免費)

#### 部署步驟
```bash
# 1. 安裝 Netlify CLI
npm install -g netlify-cli

# 2. 登入 Netlify
netlify login

# 3. 部署
netlify deploy

# 4. 生產部署
netlify deploy --prod
```

---

## 🔧 進階配置

### 環境變數設定
```bash
# .env 檔案
PORT=3000
NODE_ENV=production
DATA_DIR=./data
BACKUP_INTERVAL=3600000
```

### PM2 生產環境配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'maintenance-system',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 80
    }
  }]
};
```

### Nginx 反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 🔒 安全性配置

### HTTPS 設定
```javascript
// server.js 中添加 HTTPS 支援
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('private-key.pem'),
  cert: fs.readFileSync('certificate.pem')
};

https.createServer(options, app).listen(443, () => {
  console.log('HTTPS Server running on port 443');
});
```

### 防火牆設定
```bash
# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

---

## 📊 監控和維護

### 日誌管理
```bash
# 使用 PM2 查看日誌
pm2 logs maintenance-system

# 日誌輪轉
pm2 install pm2-logrotate
```

### 效能監控
```bash
# PM2 監控
pm2 monit

# 系統資源監控
htop
iostat
```

### 自動備份腳本
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
curl -o "backup_$DATE.json" http://localhost:3000/api/backup
echo "Backup completed: backup_$DATE.json"
```

---

## 🧪 測試和驗證

### 功能測試清單
- [ ] 系統啟動正常
- [ ] 登入功能正常
- [ ] 維修記錄 CRUD 操作
- [ ] 數據持久化功能
- [ ] 匯出/匯入功能
- [ ] 多用戶同時存取 (伺服器版)
- [ ] API 端點響應正常 (伺服器版)

### 效能測試
```bash
# 使用 Apache Bench 測試
ab -n 1000 -c 10 http://localhost:3000/

# 使用 curl 測試 API
curl -X GET http://localhost:3000/api/status
```

---

## 🚨 故障排除

### 常見問題

#### 1. 系統無法啟動
```bash
# 檢查瀏覽器版本
# 檢查檔案完整性
# 查看瀏覽器控制台錯誤
```

#### 2. Node.js 伺服器啟動失敗
```bash
# 檢查 Node.js 版本
node --version

# 檢查端口是否被占用
netstat -an | grep 3000

# 查看詳細錯誤
npm start --verbose
```

#### 3. 數據遺失
```bash
# 檢查 LocalStorage
# 檢查數據目錄權限
# 恢復備份數據
```

### 日誌分析
```bash
# 查看系統日誌
tail -f /var/log/syslog

# 查看應用日誌
tail -f logs/app.log
```

---

## 📞 技術支援

### 聯絡資訊
- **技術文檔**：查看 README.md
- **問題回報**：GitHub Issues
- **功能建議**：GitHub Discussions

### 社群資源
- **用戶手冊**：完整的使用說明
- **API 文檔**：詳細的 API 說明
- **最佳實踐**：部署和使用建議

---

## 🎉 總結

選擇最適合您需求的部署方案：

- **個人使用**：便攜式網頁包
- **小團隊**：本地伺服器部署  
- **遠端存取**：雲端部署

每種方案都經過完整測試，提供穩定可靠的維保管理功能！
