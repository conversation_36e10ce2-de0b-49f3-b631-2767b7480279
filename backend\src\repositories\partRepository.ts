import { PrismaClient, Part, StockTransaction } from '@prisma/client';
import { 
  PartInfo, 
  CreatePartRequest, 
  UpdatePartRequest, 
  PartQueryParams,
  PartListResponse,
  PartStatistics,
  PartSearchResult,
  PartDetailInfo,
  StockOperationRequest,
  StockOperationResult,
  BatchStockOperationRequest,
  BatchStockOperationResult,
  StockAlert,
  DEFAULT_PART_PAGINATION
} from '../types/part';
import { logger } from '../utils/logger';

export class PartRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找零件
  async findById(id: string): Promise<PartInfo | null> {
    try {
      const part = await this.prisma.part.findUnique({
        where: { id: BigInt(id) },
      });

      if (!part) return null;

      return this.mapPartToInfo(part);
    } catch (error) {
      logger.error('查找零件失敗:', error);
      throw new Error('查找零件失敗');
    }
  }

  // 根據零件編號查找零件
  async findByPartNumber(partNumber: string): Promise<Part | null> {
    try {
      return await this.prisma.part.findFirst({
        where: { partNumber },
      });
    } catch (error) {
      logger.error('根據零件編號查找零件失敗:', error);
      throw new Error('查找零件失敗');
    }
  }

  // 檢查零件編號是否存在
  async checkPartNumberExists(partNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const whereCondition = excludeId 
        ? { 
            partNumber,
            NOT: { id: BigInt(excludeId) }
          }
        : { partNumber };

      const existingPart = await this.prisma.part.findFirst({
        where: whereCondition,
        select: { id: true },
      });

      return !!existingPart;
    } catch (error) {
      logger.error('檢查零件編號存在性失敗:', error);
      throw new Error('檢查零件編號存在性失敗');
    }
  }

  // 創建零件
  async create(partData: CreatePartRequest): Promise<PartInfo> {
    try {
      const part = await this.prisma.part.create({
        data: {
          name: partData.name,
          partNumber: partData.partNumber,
          description: partData.description,
          category: partData.category,
          brand: partData.brand,
          model: partData.model,
          specifications: partData.specifications,
          unitPrice: partData.unitPrice,
          currency: partData.currency || 'TWD',
          supplier: partData.supplier,
          supplierPartNumber: partData.supplierPartNumber,
          minimumStock: partData.minimumStock || 0,
          currentStock: partData.currentStock || 0,
          reservedStock: 0,
          location: partData.location,
          isActive: partData.isActive ?? true,
        },
      });

      // 如果有初始庫存，記錄庫存交易
      if (partData.currentStock && partData.currentStock > 0) {
        await this.createStockTransaction({
          partId: part.id.toString(),
          type: 'IN',
          quantity: partData.currentStock,
          reason: '初始庫存',
          performedBy: 'SYSTEM',
        });
      }

      logger.info('零件創建成功:', { partId: part.id, name: part.name });

      return this.mapPartToInfo(part);
    } catch (error) {
      logger.error('創建零件失敗:', error);
      throw new Error('創建零件失敗');
    }
  }

  // 更新零件
  async update(id: string, partData: UpdatePartRequest): Promise<PartInfo> {
    try {
      const part = await this.prisma.part.update({
        where: { id: BigInt(id) },
        data: partData,
      });

      logger.info('零件更新成功:', { partId: part.id, name: part.name });

      return this.mapPartToInfo(part);
    } catch (error) {
      logger.error('更新零件失敗:', error);
      throw new Error('更新零件失敗');
    }
  }

  // 刪除零件（軟刪除）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.part.update({
        where: { id: BigInt(id) },
        data: { isActive: false },
      });

      logger.info('零件軟刪除成功:', { partId: id });
    } catch (error) {
      logger.error('軟刪除零件失敗:', error);
      throw new Error('刪除零件失敗');
    }
  }

  // 硬刪除零件
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.part.delete({
        where: { id: BigInt(id) },
      });

      logger.info('零件硬刪除成功:', { partId: id });
    } catch (error) {
      logger.error('硬刪除零件失敗:', error);
      throw new Error('刪除零件失敗');
    }
  }

  // 獲取零件列表
  async findMany(params: PartQueryParams): Promise<PartListResponse> {
    try {
      const {
        page = DEFAULT_PART_PAGINATION.page,
        limit = DEFAULT_PART_PAGINATION.limit,
        search,
        category,
        brand,
        supplier,
        isActive,
        lowStock,
        outOfStock,
        priceMin,
        priceMax,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_PART_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { partNumber: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { category: { contains: search, mode: 'insensitive' } },
          { brand: { contains: search, mode: 'insensitive' } },
          { model: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (category) {
        where.category = { contains: category, mode: 'insensitive' };
      }

      if (brand) {
        where.brand = { contains: brand, mode: 'insensitive' };
      }

      if (supplier) {
        where.supplier = { contains: supplier, mode: 'insensitive' };
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      if (lowStock) {
        where.AND = where.AND || [];
        where.AND.push({
          OR: [
            { currentStock: { lte: { minimumStock: true } } },
            { 
              AND: [
                { minimumStock: { gt: 0 } },
                { currentStock: { lte: { minimumStock: true } } }
              ]
            }
          ]
        });
      }

      if (outOfStock) {
        where.currentStock = { lte: 0 };
      }

      if (priceMin !== undefined || priceMax !== undefined) {
        where.unitPrice = {};
        if (priceMin !== undefined) where.unitPrice.gte = priceMin;
        if (priceMax !== undefined) where.unitPrice.lte = priceMax;
      }

      // 執行查詢
      const [parts, total] = await Promise.all([
        this.prisma.part.findMany({
          where,
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.part.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        parts: parts.map(part => this.mapPartToInfo(part)),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          category,
          brand,
          supplier,
          isActive,
          lowStock,
          outOfStock,
          priceRange: (priceMin !== undefined || priceMax !== undefined) ? { min: priceMin, max: priceMax } : undefined,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取零件列表失敗:', error);
      throw new Error('獲取零件列表失敗');
    }
  }

  // 搜尋零件
  async search(query: string, limit: number = 10): Promise<PartSearchResult[]> {
    try {
      const parts = await this.prisma.part.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { partNumber: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { category: { contains: query, mode: 'insensitive' } },
            { brand: { contains: query, mode: 'insensitive' } },
            { model: { contains: query, mode: 'insensitive' } },
          ],
          isActive: true,
        },
        include: {
          _count: {
            select: {
              usageRecords: true,
            },
          },
        },
        take: limit,
      });

      return parts.map(part => ({
        id: part.id.toString(),
        name: part.name,
        partNumber: part.partNumber,
        category: part.category,
        brand: part.brand,
        unitPrice: part.unitPrice ? parseFloat(part.unitPrice.toString()) : undefined,
        currentStock: part.currentStock,
        availableStock: part.currentStock - part.reservedStock,
        isActive: part.isActive,
        relevanceScore: this.calculateRelevanceScore(part, query),
        usageCount: part._count.usageRecords,
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logger.error('搜尋零件失敗:', error);
      throw new Error('搜尋零件失敗');
    }
  }

  // 獲取零件詳細資訊
  async findByIdWithDetails(id: string): Promise<PartDetailInfo | null> {
    try {
      const part = await this.prisma.part.findUnique({
        where: { id: BigInt(id) },
        include: {
          stockTransactions: {
            orderBy: { createdAt: 'desc' },
            take: 20,
            include: {
              performedByUser: {
                select: {
                  fullName: true,
                },
              },
            },
          },
          usageRecords: {
            orderBy: { usedAt: 'desc' },
            take: 10,
            include: {
              repairRecord: {
                select: {
                  repairNumber: true,
                  product: {
                    select: {
                      model: true,
                    },
                  },
                  customer: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              usageRecords: true,
            },
          },
        },
      });

      if (!part) return null;

      // 計算統計資訊
      const totalUsage = part._count.usageRecords;
      const lastUsedRecord = part.usageRecords[0];
      const lastUsedDate = lastUsedRecord?.usedAt;
      
      // 計算平均每月使用量
      const firstUsageDate = part.usageRecords[part.usageRecords.length - 1]?.usedAt;
      const monthsInUse = firstUsageDate 
        ? Math.max(1, Math.ceil((new Date().getTime() - firstUsageDate.getTime()) / (1000 * 60 * 60 * 24 * 30)))
        : 1;
      const averageUsagePerMonth = totalUsage / monthsInUse;

      // 計算庫存周轉率
      const stockTurnover = part.currentStock > 0 ? totalUsage / part.currentStock : 0;
      
      // 計算在庫天數
      const daysInStock = Math.ceil((new Date().getTime() - part.createdAt.getTime()) / (1000 * 60 * 60 * 24));

      return {
        ...this.mapPartToInfo(part),
        stockHistory: part.stockTransactions.map(transaction => ({
          id: transaction.id.toString(),
          type: transaction.type as any,
          quantity: transaction.quantity,
          reason: transaction.reason,
          referenceId: transaction.referenceId,
          performedBy: transaction.performedByUser?.fullName || 'Unknown',
          createdAt: transaction.createdAt,
        })),
        usageHistory: part.usageRecords.map(usage => ({
          id: usage.id.toString(),
          repairNumber: usage.repairRecord?.repairNumber || 'Unknown',
          productModel: usage.repairRecord?.product?.model || 'Unknown',
          customerName: usage.repairRecord?.customer?.name || 'Unknown',
          quantityUsed: usage.quantity,
          usedAt: usage.usedAt,
        })),
        statistics: {
          totalUsage,
          averageUsagePerMonth: Math.round(averageUsagePerMonth * 100) / 100,
          lastUsedDate,
          stockTurnover: Math.round(stockTurnover * 100) / 100,
          daysInStock,
        },
      };
    } catch (error) {
      logger.error('獲取零件詳細資訊失敗:', error);
      throw new Error('獲取零件詳細資訊失敗');
    }
  }

  // 庫存操作
  async performStockOperation(operation: StockOperationRequest, performedBy: string): Promise<StockOperationResult> {
    try {
      const part = await this.prisma.part.findUnique({
        where: { id: BigInt(operation.partId) },
      });

      if (!part) {
        throw new Error('零件不存在');
      }

      const previousStock = part.currentStock;
      let newStock = previousStock;
      let newReservedStock = part.reservedStock;

      // 根據操作類型計算新庫存
      switch (operation.type) {
        case 'IN':
          newStock = previousStock + operation.quantity;
          break;
        case 'OUT':
          if (previousStock < operation.quantity) {
            throw new Error('庫存不足');
          }
          newStock = previousStock - operation.quantity;
          break;
        case 'ADJUSTMENT':
          newStock = operation.quantity;
          break;
        case 'RESERVED':
          if (previousStock - part.reservedStock < operation.quantity) {
            throw new Error('可用庫存不足');
          }
          newReservedStock = part.reservedStock + operation.quantity;
          break;
        case 'RELEASED':
          if (part.reservedStock < operation.quantity) {
            throw new Error('預留庫存不足');
          }
          newReservedStock = part.reservedStock - operation.quantity;
          break;
      }

      // 使用事務更新庫存和記錄交易
      const result = await this.prisma.$transaction(async (tx) => {
        // 更新零件庫存
        const updatedPart = await tx.part.update({
          where: { id: BigInt(operation.partId) },
          data: {
            currentStock: newStock,
            reservedStock: newReservedStock,
          },
        });

        // 記錄庫存交易
        await this.createStockTransaction({
          partId: operation.partId,
          type: operation.type,
          quantity: operation.quantity,
          reason: operation.reason,
          referenceId: operation.referenceId,
          performedBy,
        }, tx);

        return updatedPart;
      });

      logger.info('庫存操作成功:', { 
        partId: operation.partId, 
        type: operation.type, 
        quantity: operation.quantity,
        previousStock,
        newStock: result.currentStock
      });

      return {
        success: true,
        partId: operation.partId,
        previousStock,
        newStock: result.currentStock,
        operation: {
          type: operation.type,
          quantity: operation.quantity,
          reason: operation.reason,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      logger.error('庫存操作失敗:', error);
      throw error;
    }
  }

  // 批量庫存操作
  async performBatchStockOperation(batchOperation: BatchStockOperationRequest, performedBy: string): Promise<BatchStockOperationResult> {
    const results: StockOperationResult[] = [];
    const errors: Array<{ partId: string; error: string }> = [];

    for (const operation of batchOperation.operations) {
      try {
        const result = await this.performStockOperation(operation, performedBy);
        results.push(result);
      } catch (error) {
        errors.push({
          partId: operation.partId,
          error: error instanceof Error ? error.message : '未知錯誤',
        });
      }
    }

    logger.info('批量庫存操作完成:', { 
      total: batchOperation.operations.length,
      success: results.length,
      failed: errors.length,
      performedBy
    });

    return {
      success: results.length,
      failed: errors.length,
      results,
      errors,
    };
  }

  // 創建庫存交易記錄
  private async createStockTransaction(data: {
    partId: string;
    type: string;
    quantity: number;
    reason: string;
    referenceId?: string;
    performedBy: string;
  }, tx?: any): Promise<void> {
    const prismaClient = tx || this.prisma;
    
    await prismaClient.stockTransaction.create({
      data: {
        partId: BigInt(data.partId),
        type: data.type,
        quantity: data.quantity,
        reason: data.reason,
        referenceId: data.referenceId,
        performedBy: data.performedBy,
      },
    });
  }

  // 計算相關性分數
  private calculateRelevanceScore(part: any, query: string): number {
    const lowerQuery = query.toLowerCase();
    let score = 0;

    // 精確匹配得分更高
    if (part.name.toLowerCase() === lowerQuery) score += 100;
    else if (part.name.toLowerCase().includes(lowerQuery)) score += 50;

    if (part.partNumber.toLowerCase() === lowerQuery) score += 100;
    else if (part.partNumber.toLowerCase().includes(lowerQuery)) score += 75;

    if (part.category.toLowerCase() === lowerQuery) score += 80;
    else if (part.category.toLowerCase().includes(lowerQuery)) score += 40;

    if (part.brand?.toLowerCase() === lowerQuery) score += 70;
    else if (part.brand?.toLowerCase().includes(lowerQuery)) score += 35;

    if (part.model?.toLowerCase() === lowerQuery) score += 60;
    else if (part.model?.toLowerCase().includes(lowerQuery)) score += 30;

    if (part.description?.toLowerCase().includes(lowerQuery)) score += 20;

    // 開頭匹配得分較高
    if (part.name.toLowerCase().startsWith(lowerQuery)) score += 25;
    if (part.partNumber.toLowerCase().startsWith(lowerQuery)) score += 30;
    if (part.category.toLowerCase().startsWith(lowerQuery)) score += 20;

    return score;
  }

  // 獲取零件統計
  async getStatistics(): Promise<PartStatistics> {
    try {
      const [total, active, lowStock, outOfStock, byCategory, byBrand, bySupplier, stockStatus, recentCounts, totalValue, averagePrice] = await Promise.all([
        this.prisma.part.count(),
        this.prisma.part.count({ where: { isActive: true } }),
        this.getLowStockCount(),
        this.prisma.part.count({ where: { currentStock: { lte: 0 } } }),
        this.getPartsByCategory(),
        this.getPartsByBrand(),
        this.getPartsBySupplier(),
        this.getStockStatus(),
        this.getRecentPartCounts(),
        this.getTotalValue(),
        this.getAveragePrice(),
      ]);

      return {
        total,
        active,
        inactive: total - active,
        lowStock,
        outOfStock,
        totalValue,
        averagePrice,
        byCategory,
        byBrand,
        bySupplier,
        stockStatus,
        recentParts: recentCounts,
      };
    } catch (error) {
      logger.error('獲取零件統計失敗:', error);
      throw new Error('獲取零件統計失敗');
    }
  }

  // 獲取庫存警報
  async getStockAlerts(): Promise<StockAlert[]> {
    try {
      const lowStockParts = await this.prisma.part.findMany({
        where: {
          isActive: true,
          OR: [
            { currentStock: { lte: 0 } },
            {
              AND: [
                { minimumStock: { gt: 0 } },
                { currentStock: { lte: { minimumStock: true } } }
              ]
            }
          ]
        },
        orderBy: { currentStock: 'asc' },
      });

      return lowStockParts.map(part => {
        let alertType: 'LOW_STOCK' | 'OUT_OF_STOCK' | 'CRITICAL_STOCK';
        let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

        if (part.currentStock <= 0) {
          alertType = 'OUT_OF_STOCK';
          severity = 'CRITICAL';
        } else if (part.currentStock <= part.minimumStock * 0.1) {
          alertType = 'CRITICAL_STOCK';
          severity = 'HIGH';
        } else {
          alertType = 'LOW_STOCK';
          severity = 'MEDIUM';
        }

        return {
          id: `alert_${part.id}`,
          partId: part.id.toString(),
          partName: part.name,
          partNumber: part.partNumber,
          alertType,
          currentStock: part.currentStock,
          minimumStock: part.minimumStock,
          severity,
          isResolved: false,
          createdAt: new Date(),
        };
      });
    } catch (error) {
      logger.error('獲取庫存警報失敗:', error);
      throw new Error('獲取庫存警報失敗');
    }
  }

  // 獲取低庫存數量
  private async getLowStockCount(): Promise<number> {
    const lowStockParts = await this.prisma.part.count({
      where: {
        isActive: true,
        AND: [
          { minimumStock: { gt: 0 } },
          { currentStock: { lte: { minimumStock: true } } }
        ]
      }
    });
    return lowStockParts;
  }

  // 獲取各類別零件數量
  private async getPartsByCategory() {
    const categoryStats = await this.prisma.part.groupBy({
      by: ['category'],
      _count: true,
      _sum: {
        unitPrice: true,
        currentStock: true,
      },
      where: { isActive: true },
      orderBy: { _count: { _all: 'desc' } },
      take: 10,
    });

    return categoryStats.map(stat => ({
      category: stat.category,
      count: stat._count,
      totalValue: stat._sum.unitPrice ? parseFloat(stat._sum.unitPrice.toString()) * (stat._sum.currentStock || 0) : 0,
    }));
  }

  // 獲取各品牌零件數量
  private async getPartsByBrand() {
    const brandStats = await this.prisma.part.groupBy({
      by: ['brand'],
      _count: true,
      where: {
        isActive: true,
        brand: { not: null }
      },
      orderBy: { _count: { _all: 'desc' } },
      take: 10,
    });

    return brandStats.map(stat => ({
      brand: stat.brand || 'Unknown',
      count: stat._count,
    }));
  }

  // 獲取各供應商零件數量
  private async getPartsBySupplier() {
    const supplierStats = await this.prisma.part.groupBy({
      by: ['supplier'],
      _count: true,
      _sum: {
        unitPrice: true,
        currentStock: true,
      },
      where: {
        isActive: true,
        supplier: { not: null }
      },
      orderBy: { _count: { _all: 'desc' } },
      take: 10,
    });

    return supplierStats.map(stat => ({
      supplier: stat.supplier || 'Unknown',
      count: stat._count,
      totalValue: stat._sum.unitPrice ? parseFloat(stat._sum.unitPrice.toString()) * (stat._sum.currentStock || 0) : 0,
    }));
  }

  // 獲取庫存狀態統計
  private async getStockStatus() {
    const [adequate, low, critical, outOfStock] = await Promise.all([
      this.prisma.part.count({
        where: {
          isActive: true,
          currentStock: { gt: { minimumStock: true } }
        }
      }),
      this.prisma.part.count({
        where: {
          isActive: true,
          AND: [
            { minimumStock: { gt: 0 } },
            { currentStock: { lte: { minimumStock: true } } },
            { currentStock: { gt: { minimumStock: 0.1 } } }
          ]
        }
      }),
      this.prisma.part.count({
        where: {
          isActive: true,
          AND: [
            { minimumStock: { gt: 0 } },
            { currentStock: { lte: { minimumStock: 0.1 } } },
            { currentStock: { gt: 0 } }
          ]
        }
      }),
      this.prisma.part.count({
        where: {
          isActive: true,
          currentStock: { lte: 0 }
        }
      }),
    ]);

    return {
      adequate,
      low,
      critical,
      outOfStock,
    };
  }

  // 獲取最近零件統計
  private async getRecentPartCounts() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [todayCount, weekCount, monthCount] = await Promise.all([
      this.prisma.part.count({ where: { createdAt: { gte: today } } }),
      this.prisma.part.count({ where: { createdAt: { gte: thisWeek } } }),
      this.prisma.part.count({ where: { createdAt: { gte: thisMonth } } }),
    ]);

    return {
      today: todayCount,
      thisWeek: weekCount,
      thisMonth: monthCount,
    };
  }

  // 獲取總價值
  private async getTotalValue(): Promise<number> {
    const result = await this.prisma.part.aggregate({
      _sum: {
        unitPrice: true,
      },
      where: {
        isActive: true,
        unitPrice: { not: null },
      },
    });

    return result._sum.unitPrice ? parseFloat(result._sum.unitPrice.toString()) : 0;
  }

  // 獲取平均價格
  private async getAveragePrice(): Promise<number> {
    const result = await this.prisma.part.aggregate({
      _avg: {
        unitPrice: true,
      },
      where: {
        isActive: true,
        unitPrice: { not: null },
      },
    });

    return result._avg.unitPrice ? parseFloat(result._avg.unitPrice.toString()) : 0;
  }

  // 映射零件資料到介面
  private mapPartToInfo(part: Part): PartInfo {
    return {
      id: part.id.toString(),
      name: part.name,
      partNumber: part.partNumber,
      description: part.description,
      category: part.category,
      brand: part.brand,
      model: part.model,
      specifications: part.specifications,
      unitPrice: part.unitPrice ? parseFloat(part.unitPrice.toString()) : undefined,
      currency: part.currency,
      supplier: part.supplier,
      supplierPartNumber: part.supplierPartNumber,
      minimumStock: part.minimumStock,
      currentStock: part.currentStock,
      reservedStock: part.reservedStock,
      availableStock: part.currentStock - part.reservedStock,
      location: part.location,
      isActive: part.isActive,
      createdAt: part.createdAt,
      updatedAt: part.updatedAt,
    };
  }
}
