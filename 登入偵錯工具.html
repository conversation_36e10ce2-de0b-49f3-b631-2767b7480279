<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入偵錯工具 - IACT MIO維保管理系統</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-ok { color: #52c41a; font-weight: bold; }
        .status-error { color: #f5222d; font-weight: bold; }
        .status-warning { color: #fa8c16; font-weight: bold; }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover { transform: translateY(-2px); }
        .btn-danger { background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%); }
        .btn-success { background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%); }
        
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .test-login {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 登入偵錯工具</h1>
            <p>專門診斷和修復登入按鈕沒反應的問題</p>
        </div>
        
        <div class="content">
            <div class="debug-section">
                <h3>🔍 系統狀態檢查</h3>
                <div class="status-item">
                    <span>瀏覽器支援</span>
                    <span id="browserSupport">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>JavaScript 執行</span>
                    <span id="jsExecution">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>DOM 載入狀態</span>
                    <span id="domStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>主系統檔案</span>
                    <span id="mainFileStatus">檢查中...</span>
                </div>
            </div>
            
            <div class="debug-section">
                <h3>🎯 登入元素檢查</h3>
                <div class="status-item">
                    <span>登入表單 (#loginForm)</span>
                    <span id="loginFormStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>帳號輸入框 (#email)</span>
                    <span id="emailInputStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>密碼輸入框 (#password)</span>
                    <span id="passwordInputStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>登入按鈕</span>
                    <span id="submitButtonStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>帳號數據 (accounts)</span>
                    <span id="accountsStatus">檢查中...</span>
                </div>
            </div>
            
            <div class="test-login">
                <h3>🧪 測試登入功能</h3>
                <p>在這裡測試登入功能是否正常工作：</p>
                <form id="testLoginForm">
                    <div class="form-group">
                        <label>測試帳號</label>
                        <input type="text" id="testEmail" value="admin" placeholder="輸入測試帳號">
                    </div>
                    <div class="form-group">
                        <label>測試密碼</label>
                        <input type="password" id="testPassword" value="admin123" placeholder="輸入測試密碼">
                    </div>
                    <button type="submit" class="btn btn-success">🔐 測試登入</button>
                </form>
            </div>
            
            <div class="debug-section">
                <h3>🛠️ 偵錯工具</h3>
                <button class="btn" onclick="runFullDiagnostic()">🔍 執行完整診斷</button>
                <button class="btn" onclick="checkMainSystem()">📋 檢查主系統</button>
                <button class="btn" onclick="testLoginEvents()">🎯 測試登入事件</button>
                <button class="btn btn-success" onclick="openMainSystem()">🚀 開啟主系統</button>
                <button class="btn btn-danger" onclick="clearBrowserCache()">🗑️ 清除快取</button>
            </div>
            
            <div class="debug-section">
                <h3>📊 偵錯日誌</h3>
                <div class="log-area" id="debugLog">
                    正在初始化偵錯工具...\n
                </div>
                <button class="btn" onclick="clearLog()">清除日誌</button>
                <button class="btn" onclick="exportLog()">匯出日誌</button>
            </div>
        </div>
    </div>

    <script>
        let logArea;
        
        function log(message, type = 'info') {
            if (!logArea) {
                logArea = document.getElementById('debugLog');
            }
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = status === 'ok' ? 'status-ok' : 
                                   status === 'error' ? 'status-error' : 'status-warning';
            }
        }
        
        function checkBrowserSupport() {
            log('檢查瀏覽器支援...');
            
            const hasLocalStorage = typeof(Storage) !== "undefined";
            const hasConsole = typeof console !== "undefined";
            const hasEventListener = typeof document.addEventListener !== "undefined";
            
            if (hasLocalStorage && hasConsole && hasEventListener) {
                setStatus('browserSupport', 'ok', '✅ 完全支援');
                log('瀏覽器支援檢查通過', 'success');
            } else {
                setStatus('browserSupport', 'error', '❌ 部分不支援');
                log('瀏覽器支援檢查失敗', 'error');
            }
            
            setStatus('jsExecution', 'ok', '✅ 正常');
            log('JavaScript 執行正常', 'success');
            
            setStatus('domStatus', 'ok', `✅ ${document.readyState}`);
            log(`DOM 狀態: ${document.readyState}`, 'success');
        }
        
        function checkMainSystem() {
            log('檢查主系統檔案...');
            
            fetch('complete-system-gui.html')
                .then(response => {
                    if (response.ok) {
                        setStatus('mainFileStatus', 'ok', '✅ 檔案存在');
                        log('主系統檔案存在', 'success');
                        return response.text();
                    } else {
                        throw new Error('檔案不存在');
                    }
                })
                .then(content => {
                    // 檢查關鍵元素
                    const hasLoginForm = content.includes('id="loginForm"');
                    const hasAccounts = content.includes('const accounts');
                    const hasLoginFunction = content.includes('function login(');
                    
                    if (hasLoginForm && hasAccounts && hasLoginFunction) {
                        log('主系統關鍵功能完整', 'success');
                    } else {
                        log('主系統可能缺少關鍵功能', 'warning');
                        log(`登入表單: ${hasLoginForm ? '✅' : '❌'}`, hasLoginForm ? 'success' : 'error');
                        log(`帳號數據: ${hasAccounts ? '✅' : '❌'}`, hasAccounts ? 'success' : 'error');
                        log(`登入函數: ${hasLoginFunction ? '✅' : '❌'}`, hasLoginFunction ? 'success' : 'error');
                    }
                })
                .catch(error => {
                    setStatus('mainFileStatus', 'error', '❌ 檔案遺失');
                    log(`主系統檔案檢查失敗: ${error.message}`, 'error');
                });
        }
        
        function testLoginEvents() {
            log('測試登入事件綁定...');
            
            // 嘗試在新視窗中載入主系統並檢查
            const testWindow = window.open('complete-system-gui.html', '_blank', 'width=800,height=600');
            
            if (testWindow) {
                log('已開啟測試視窗，請在該視窗中：', 'info');
                log('1. 按 F12 開啟開發者工具', 'info');
                log('2. 在控制台輸入: debugLoginSystem()', 'info');
                log('3. 查看詳細的偵錯信息', 'info');
                log('4. 嘗試輸入: forceLogin()', 'info');
            } else {
                log('無法開啟測試視窗，可能被瀏覽器阻擋', 'warning');
            }
        }
        
        function runFullDiagnostic() {
            log('=== 開始完整診斷 ===');
            document.getElementById('debugLog').textContent = '';
            
            checkBrowserSupport();
            checkMainSystem();
            
            // 模擬檢查登入元素（因為不在同一頁面）
            setStatus('loginFormStatus', 'warning', '⚠️ 需在主系統檢查');
            setStatus('emailInputStatus', 'warning', '⚠️ 需在主系統檢查');
            setStatus('passwordInputStatus', 'warning', '⚠️ 需在主系統檢查');
            setStatus('submitButtonStatus', 'warning', '⚠️ 需在主系統檢查');
            setStatus('accountsStatus', 'warning', '⚠️ 需在主系統檢查');
            
            log('=== 診斷完成 ===');
            log('');
            log('🔧 修復建議：');
            log('1. 確保使用最新版本的瀏覽器');
            log('2. 清除瀏覽器快取和 Cookie');
            log('3. 停用瀏覽器擴充功能');
            log('4. 嘗試無痕模式');
            log('5. 檢查是否有 JavaScript 錯誤');
        }
        
        function openMainSystem() {
            log('開啟主系統...');
            window.open('complete-system-gui.html', '_blank');
        }
        
        function clearBrowserCache() {
            if (confirm('⚠️ 這將清除瀏覽器的本地存儲數據。\n確定要繼續嗎？')) {
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                    log('瀏覽器快取已清除', 'success');
                    alert('✅ 快取已清除！請重新載入主系統。');
                } catch (error) {
                    log(`清除快取失敗: ${error.message}`, 'error');
                }
            }
        }
        
        function clearLog() {
            document.getElementById('debugLog').textContent = '';
            log('日誌已清除');
        }
        
        function exportLog() {
            const logContent = document.getElementById('debugLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `登入偵錯日誌_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('日誌已匯出', 'success');
        }
        
        // 測試登入表單
        document.getElementById('testLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log(`測試登入: ${email} / ${password}`);
            
            // 模擬帳號驗證
            const testAccounts = {
                'admin': { password: 'admin123', name: '系統管理員' },
                'service': { password: 'service123', name: '客服人員' },
                'tech': { password: 'tech123', name: '維修技師' },
                'viewer': { password: 'viewer123', name: '查詢用戶' }
            };
            
            if (testAccounts[email] && testAccounts[email].password === password) {
                log(`✅ 測試登入成功！歡迎 ${testAccounts[email].name}`, 'success');
                alert(`✅ 測試登入成功！\n\n帳號: ${email}\n角色: ${testAccounts[email].name}\n\n登入功能正常，問題可能在主系統的事件綁定。`);
            } else {
                log('❌ 測試登入失敗：帳號或密碼錯誤', 'error');
                alert('❌ 測試登入失敗\n\n請檢查帳號密碼是否正確：\n• admin / admin123\n• service / service123\n• tech / tech123\n• viewer / viewer123');
            }
        });
        
        // 頁面載入時自動執行診斷
        window.addEventListener('load', function() {
            setTimeout(runFullDiagnostic, 500);
        });
    </script>
</body>
</html>
