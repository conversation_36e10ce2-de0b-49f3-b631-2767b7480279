# SharePoint 快速開始指南

## 🚀 **5分鐘快速整合 SharePoint**

### 前置準備
- ✅ SharePoint 網站存在
- ✅ 您有網站權限 (參與者或更高)
- ✅ 知道 SharePoint 網站 URL

## 📋 **步驟一：確認 SharePoint URL**

### URL 格式範例
```
Office 365: https://yourcompany.sharepoint.com/sites/maintenance
企業內部: https://sharepoint.company.com/sites/maintenance
```

### 如何取得 URL
1. **開啟您的 SharePoint 網站**
2. **複製瀏覽器地址欄的 URL**
3. **移除頁面路徑，保留到網站根目錄**

```
完整 URL: https://contoso.sharepoint.com/sites/maintenance/SitePages/Home.aspx
網站 URL: https://contoso.sharepoint.com/sites/maintenance
```

## 🛠️ **步驟二：建立 SharePoint 清單**

### 方法一：使用自動化腳本 (推薦)

#### 執行腳本
1. **以管理員身分開啟 PowerShell**
2. **進入 SharePoint整合 資料夾**
3. **執行腳本**：
   ```powershell
   .\SharePoint清單建立腳本.ps1 -SiteUrl "https://yourcompany.sharepoint.com/sites/maintenance"
   ```

#### 腳本會自動建立
- ✅ 維修記錄清單
- ✅ 客戶資料清單
- ✅ 零件資料清單
- ✅ 產品資料清單
- ✅ 系統設定清單

### 方法二：手動建立清單

#### 建立維修記錄清單
1. **進入 SharePoint 網站**
2. **點擊 "新增" → "清單"**
3. **選擇 "空白清單"**
4. **名稱**：`維修記錄`
5. **新增以下欄位**：

| 欄位名稱 | 類型 | 必填 | 說明 |
|----------|------|------|------|
| 維修編號 | 文字 | ✅ | 唯一識別碼 |
| 客戶姓名 | 文字 | ✅ | 客戶名稱 |
| 聯絡電話 | 文字 | ❌ | 客戶電話 |
| 產品序號 | 文字 | ❌ | 產品序號 |
| 產品名稱 | 文字 | ✅ | 產品名稱 |
| 產品型號 | 文字 | ❌ | 產品型號 |
| 問題描述 | 多行文字 | ❌ | 問題說明 |
| 維保狀態 | 選擇 | ❌ | 客訴/維保 |
| 維修記錄 | 選擇 | ❌ | 調整/換馬達 |
| 測試結果 | 選擇 | ❌ | 正常/異常/待測試 |
| 維保員 | 文字 | ❌ | 負責技師 |
| 維修日期 | 日期時間 | ❌ | 維修日期 |

#### 重複建立其他清單
- 客戶資料清單
- 零件資料清單
- 產品資料清單

## ⚙️ **步驟三：在系統中配置 SharePoint**

### 配置步驟
1. **啟動維保管理系統**：雙擊 `啟動系統.bat`
2. **登入系統**：使用 admin / admin123
3. **進入系統設定**：點擊左側導航 "系統設定"
4. **選擇存儲模式**：選擇 "SharePoint 整合"
5. **輸入網站 URL**：貼上您的 SharePoint 網站 URL
6. **測試連線**：點擊 "🔗 測試連線"
7. **確認成功**：看到綠色成功訊息

### 配置畫面
```
┌─────────────────────────────────────┐
│           系統設定                   │
├─────────────────────────────────────┤
│ 存儲模式：                          │
│ ○ LocalStorage                     │
│ ● SharePoint 整合                   │
│                                     │
│ SharePoint 網站 URL：               │
│ ┌─────────────────────────────────┐ │
│ │ https://contoso.sharepoint.com  │ │
│ │ /sites/maintenance              │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [ 🔗 測試連線 ] [ 💾 儲存設定 ]      │
└─────────────────────────────────────┘
```

## 🧪 **步驟四：測試整合**

### 連線測試
1. **點擊 "🔗 測試連線"**
2. **等待測試結果**
3. **確認顯示**：✅ SharePoint 連線成功

### 功能測試
1. **新增維修記錄**：測試數據寫入
2. **查看記錄列表**：測試數據讀取
3. **編輯記錄**：測試數據更新
4. **檢查 SharePoint**：確認數據已同步

## 🔄 **步驟五：數據遷移 (可選)**

### 從 LocalStorage 遷移
如果您之前使用 LocalStorage 存儲數據：

1. **確保 SharePoint 連線成功**
2. **點擊 "📤 遷移數據"**
3. **等待遷移完成**
4. **確認數據已上傳到 SharePoint**

### 手動遷移
1. **匯出現有數據**：點擊 "📤 匯出所有數據"
2. **下載 JSON 檔案**
3. **切換到 SharePoint 模式**
4. **匯入數據**：點擊 "📥 匯入數據"

## ✅ **完成檢查清單**

### 環境檢查
- [ ] SharePoint 網站可以正常存取
- [ ] 您有適當的權限 (參與者或更高)
- [ ] 網站 URL 格式正確

### 清單檢查
- [ ] 維修記錄清單已建立
- [ ] 客戶資料清單已建立
- [ ] 零件資料清單已建立
- [ ] 產品資料清單已建立
- [ ] 所有欄位設定正確

### 系統檢查
- [ ] SharePoint URL 已配置
- [ ] 連線測試成功
- [ ] 可以新增維修記錄
- [ ] 數據正確同步到 SharePoint

## 🚨 **常見問題**

### 問題 1：連線失敗
**錯誤**：無法連線到 SharePoint

**解決方案**：
1. 檢查 URL 格式是否正確
2. 確認網站存在且可存取
3. 檢查權限設定
4. 測試網路連線

### 問題 2：清單不存在
**錯誤**：List 'XXX' does not exist

**解決方案**：
1. 確認清單名稱正確 (區分大小寫)
2. 檢查清單是否已建立
3. 確認權限可以存取清單

### 問題 3：權限被拒絕
**錯誤**：Access denied

**解決方案**：
1. 檢查使用者權限
2. 確認至少有參與者權限
3. 聯絡 SharePoint 管理員

## 📞 **取得協助**

### 自助資源
- **詳細指導**：`SharePoint整合指導手冊.md`
- **URL 配置**：`SharePoint_URL配置指南.md`
- **自動化腳本**：`SharePoint清單建立腳本.ps1`

### 聯絡支援
- **IT 部門**：SharePoint 環境和權限
- **系統管理員**：清單建立和配置
- **技術支援**：系統整合問題

## 🎉 **整合完成**

完成 SharePoint 整合後，您將享有：

### 企業級功能
- ✅ **多用戶協作**：團隊成員同時使用
- ✅ **數據安全**：企業級安全保護
- ✅ **版本控制**：自動追蹤數據變更
- ✅ **備份恢復**：SharePoint 自動備份

### 進階功能
- ✅ **工作流程**：自動化業務流程
- ✅ **通知提醒**：重要事件通知
- ✅ **報表分析**：Power BI 整合
- ✅ **行動存取**：SharePoint 行動應用

**🚀 立即開始您的 SharePoint 整合之旅！**

---

**💡 提示**：如果您是第一次使用 SharePoint，建議先聯絡您的 IT 部門確認環境設定和權限配置。
