/* 全局樣式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具類 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.full-width {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 24px 16px !important;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
}

/* 自定義 Ant Design 樣式 */
.ant-layout-header {
  padding: 0 24px;
}

.ant-layout-sider-collapsed .ant-layout-sider-trigger {
  border-top: 1px solid #434343;
}

/* 卡片樣式 */
.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

/* 表格樣式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 表單樣式 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 按鈕樣式 */
.ant-btn {
  border-radius: 6px;
}

/* 狀態標籤樣式 */
.status-tag {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-processing {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-cancelled {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

/* 優先級樣式 */
.priority-low {
  color: #52c41a;
}

.priority-medium {
  color: #fa8c16;
}

.priority-high {
  color: #ff4d4f;
}

.priority-urgent {
  color: #722ed1;
  font-weight: bold;
}

/* 載入動畫 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 空狀態樣式 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: rgba(0, 0, 0, 0.45);
}

.empty-state .ant-empty-description {
  margin-top: 16px;
}

/* 錯誤樣式 */
.error-boundary {
  text-align: center;
  padding: 48px 24px;
}

.error-boundary h2 {
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-boundary p {
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 24px;
}
