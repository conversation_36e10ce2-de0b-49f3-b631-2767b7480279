// 零件基本資訊介面
export interface PartInfo {
  id: string;
  name: string;
  partNumber: string;
  description?: string;
  category: string;
  brand?: string;
  model?: string;
  specifications?: string;
  unitPrice?: number;
  currency?: string;
  supplier?: string;
  supplierPartNumber?: string;
  minimumStock: number;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  location?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 創建零件請求介面
export interface CreatePartRequest {
  name: string;
  partNumber: string;
  description?: string;
  category: string;
  brand?: string;
  model?: string;
  specifications?: string;
  unitPrice?: number;
  currency?: string;
  supplier?: string;
  supplierPartNumber?: string;
  minimumStock?: number;
  currentStock?: number;
  location?: string;
  isActive?: boolean;
}

// 更新零件請求介面
export interface UpdatePartRequest {
  name?: string;
  partNumber?: string;
  description?: string;
  category?: string;
  brand?: string;
  model?: string;
  specifications?: string;
  unitPrice?: number;
  currency?: string;
  supplier?: string;
  supplierPartNumber?: string;
  minimumStock?: number;
  location?: string;
  isActive?: boolean;
}

// 零件查詢參數介面
export interface PartQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  brand?: string;
  supplier?: string;
  isActive?: boolean;
  lowStock?: boolean;
  outOfStock?: boolean;
  priceMin?: number;
  priceMax?: number;
  sortBy?: 'name' | 'partNumber' | 'category' | 'brand' | 'unitPrice' | 'currentStock' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 零件列表響應介面
export interface PartListResponse {
  parts: PartInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    category?: string;
    brand?: string;
    supplier?: string;
    isActive?: boolean;
    lowStock?: boolean;
    outOfStock?: boolean;
    priceRange?: { min?: number; max?: number };
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 零件統計介面
export interface PartStatistics {
  total: number;
  active: number;
  inactive: number;
  lowStock: number;
  outOfStock: number;
  totalValue: number;
  averagePrice: number;
  byCategory: Array<{
    category: string;
    count: number;
    totalValue: number;
  }>;
  byBrand: Array<{
    brand: string;
    count: number;
  }>;
  bySupplier: Array<{
    supplier: string;
    count: number;
    totalValue: number;
  }>;
  stockStatus: {
    adequate: number;
    low: number;
    critical: number;
    outOfStock: number;
  };
  recentParts: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}

// 零件搜尋結果介面
export interface PartSearchResult {
  id: string;
  name: string;
  partNumber: string;
  category: string;
  brand?: string;
  unitPrice?: number;
  currentStock: number;
  availableStock: number;
  isActive: boolean;
  relevanceScore: number;
  usageCount?: number;
}

// 零件詳細資訊介面（包含關聯資料）
export interface PartDetailInfo extends PartInfo {
  stockHistory: Array<{
    id: string;
    type: 'IN' | 'OUT' | 'ADJUSTMENT' | 'RESERVED' | 'RELEASED';
    quantity: number;
    reason: string;
    referenceId?: string;
    performedBy: string;
    createdAt: Date;
  }>;
  usageHistory: Array<{
    id: string;
    repairNumber: string;
    productModel: string;
    customerName: string;
    quantityUsed: number;
    usedAt: Date;
  }>;
  statistics: {
    totalUsage: number;
    averageUsagePerMonth: number;
    lastUsedDate?: Date;
    stockTurnover: number;
    daysInStock: number;
  };
}

// 庫存操作請求介面
export interface StockOperationRequest {
  partId: string;
  type: 'IN' | 'OUT' | 'ADJUSTMENT' | 'RESERVED' | 'RELEASED';
  quantity: number;
  reason: string;
  referenceId?: string;
  notes?: string;
}

// 庫存操作結果介面
export interface StockOperationResult {
  success: boolean;
  partId: string;
  previousStock: number;
  newStock: number;
  operation: {
    type: string;
    quantity: number;
    reason: string;
  };
  timestamp: Date;
}

// 批量庫存操作請求介面
export interface BatchStockOperationRequest {
  operations: StockOperationRequest[];
  batchReason?: string;
}

// 批量庫存操作結果介面
export interface BatchStockOperationResult {
  success: number;
  failed: number;
  results: StockOperationResult[];
  errors: Array<{
    partId: string;
    error: string;
  }>;
}

// 零件驗證結果介面
export interface PartValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 零件匯入請求介面
export interface PartImportRequest {
  parts: CreatePartRequest[];
  options: {
    skipDuplicates: boolean;
    updateExisting: boolean;
    validateOnly: boolean;
  };
}

// 零件匯入結果介面
export interface PartImportResult {
  total: number;
  created: number;
  updated: number;
  skipped: number;
  failed: number;
  errors: Array<{
    row: number;
    part: CreatePartRequest;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    part: CreatePartRequest;
    warning: string;
  }>;
}

// 庫存警報介面
export interface StockAlert {
  id: string;
  partId: string;
  partName: string;
  partNumber: string;
  alertType: 'LOW_STOCK' | 'OUT_OF_STOCK' | 'CRITICAL_STOCK';
  currentStock: number;
  minimumStock: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  isResolved: boolean;
  createdAt: Date;
  resolvedAt?: Date;
}

// 零件使用記錄介面
export interface PartUsageRecord {
  id: string;
  partId: string;
  repairRecordId: string;
  quantity: number;
  unitPrice?: number;
  totalCost?: number;
  usedBy: string;
  usedAt: Date;
  notes?: string;
}

// 零件狀態枚舉
export enum PartStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DISCONTINUED = 'DISCONTINUED',
  OBSOLETE = 'OBSOLETE',
}

// 庫存操作類型枚舉
export enum StockOperationType {
  IN = 'IN',                    // 入庫
  OUT = 'OUT',                  // 出庫
  ADJUSTMENT = 'ADJUSTMENT',    // 調整
  RESERVED = 'RESERVED',        // 預留
  RELEASED = 'RELEASED',        // 釋放預留
}

// 零件類別枚舉
export enum PartCategory {
  ELECTRONIC = 'ELECTRONIC',           // 電子零件
  MECHANICAL = 'MECHANICAL',           // 機械零件
  OPTICAL = 'OPTICAL',                 // 光學零件
  BATTERY = 'BATTERY',                 // 電池
  CABLE = 'CABLE',                     // 線材
  CONNECTOR = 'CONNECTOR',             // 連接器
  HOUSING = 'HOUSING',                 // 外殼
  SCREEN = 'SCREEN',                   // 螢幕
  SENSOR = 'SENSOR',                   // 感測器
  SPEAKER = 'SPEAKER',                 // 揚聲器
  MICROPHONE = 'MICROPHONE',           // 麥克風
  CAMERA = 'CAMERA',                   // 攝影機
  BUTTON = 'BUTTON',                   // 按鍵
  SWITCH = 'SWITCH',                   // 開關
  ADHESIVE = 'ADHESIVE',               // 膠材
  SCREW = 'SCREW',                     // 螺絲
  TOOL = 'TOOL',                       // 工具
  CONSUMABLE = 'CONSUMABLE',           // 耗材
  OTHER = 'OTHER',                     // 其他
}

// 零件排序選項
export const PART_SORT_OPTIONS = [
  { value: 'name', label: '零件名稱' },
  { value: 'partNumber', label: '零件編號' },
  { value: 'category', label: '類別' },
  { value: 'brand', label: '品牌' },
  { value: 'unitPrice', label: '單價' },
  { value: 'currentStock', label: '庫存數量' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 零件篩選選項
export const PART_FILTER_OPTIONS = {
  status: [
    { value: true, label: '活躍' },
    { value: false, label: '停用' },
  ],
  stockStatus: [
    { value: 'lowStock', label: '庫存不足' },
    { value: 'outOfStock', label: '缺貨' },
    { value: 'adequate', label: '庫存充足' },
  ],
  priceRange: [
    { value: { min: 0, max: 100 }, label: '100元以下' },
    { value: { min: 100, max: 500 }, label: '100-500元' },
    { value: { min: 500, max: 1000 }, label: '500-1000元' },
    { value: { min: 1000 }, label: '1000元以上' },
  ],
} as const;

// 預設分頁設定
export const DEFAULT_PART_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const;

// 零件搜尋配置
export const PART_SEARCH_CONFIG = {
  minSearchLength: 2,
  maxSearchLength: 100,
  searchFields: ['name', 'partNumber', 'description', 'category', 'brand', 'model'],
  fuzzySearchThreshold: 0.6,
} as const;

// 零件驗證規則
export const PART_VALIDATION_RULES = {
  name: {
    minLength: 2,
    maxLength: 200,
    required: true,
  },
  partNumber: {
    minLength: 1,
    maxLength: 100,
    required: true,
    pattern: /^[A-Za-z0-9\-_]+$/,
  },
  description: {
    maxLength: 1000,
    required: false,
  },
  category: {
    minLength: 1,
    maxLength: 100,
    required: true,
  },
  brand: {
    maxLength: 100,
    required: false,
  },
  model: {
    maxLength: 100,
    required: false,
  },
  specifications: {
    maxLength: 2000,
    required: false,
  },
  unitPrice: {
    min: 0,
    max: 999999999,
    required: false,
  },
  currency: {
    maxLength: 10,
    required: false,
    pattern: /^[A-Z]{3}$/,
  },
  supplier: {
    maxLength: 200,
    required: false,
  },
  supplierPartNumber: {
    maxLength: 100,
    required: false,
  },
  minimumStock: {
    min: 0,
    max: 999999,
    required: false,
  },
  currentStock: {
    min: 0,
    max: 999999,
    required: false,
  },
  location: {
    maxLength: 200,
    required: false,
  },
} as const;

// 庫存警報設定
export const STOCK_ALERT_CONFIG = {
  lowStockThreshold: 0.2,      // 低於最小庫存的20%
  criticalStockThreshold: 0.1,  // 低於最小庫存的10%
  checkInterval: 3600000,       // 檢查間隔：1小時
} as const;

// 常見零件類別
export const COMMON_PART_CATEGORIES = [
  'Electronic Components',
  'Mechanical Parts',
  'Optical Components',
  'Batteries',
  'Cables & Connectors',
  'Housing & Enclosures',
  'Displays & Screens',
  'Sensors',
  'Audio Components',
  'Camera Modules',
  'Input Devices',
  'Adhesives & Materials',
  'Fasteners',
  'Tools & Equipment',
  'Consumables',
] as const;

// 常見供應商
export const COMMON_SUPPLIERS = [
  'Foxconn',
  'Samsung',
  'LG',
  'Sony',
  'Panasonic',
  'Murata',
  'TDK',
  'Vishay',
  'Texas Instruments',
  'Qualcomm',
  'Broadcom',
  'Intel',
  'AMD',
  'NVIDIA',
  'Infineon',
] as const;

// 零件匯出格式
export const PART_EXPORT_FORMATS = {
  csv: {
    mimeType: 'text/csv',
    extension: 'csv',
    headers: [
      'ID', '零件名稱', '零件編號', '描述', '類別', '品牌', '型號',
      '規格', '單價', '幣別', '供應商', '供應商編號', '最小庫存',
      '目前庫存', '預留庫存', '可用庫存', '位置', '狀態', '創建時間'
    ],
  },
  excel: {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xlsx',
    sheetName: '零件資料',
  },
  json: {
    mimeType: 'application/json',
    extension: 'json',
  },
} as const;
