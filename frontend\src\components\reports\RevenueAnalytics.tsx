import React from 'react';
import { Card, Typography } from 'antd';
import { StatisticsQueryParams } from '../../services/statisticsService';

const { Title } = Typography;

interface RevenueAnalyticsProps {
  filters: StatisticsQueryParams;
}

const RevenueAnalytics: React.FC<RevenueAnalyticsProps> = ({ filters }) => {
  return (
    <Card>
      <Title level={3}>營收分析</Title>
      <p>營收分析功能開發中...</p>
      <ul>
        <li>營收趨勢分析</li>
        <li>利潤率分析</li>
        <li>成本結構分析</li>
        <li>營收預測</li>
      </ul>
    </Card>
  );
};

export default RevenueAnalytics;
