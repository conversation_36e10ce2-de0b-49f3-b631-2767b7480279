{"name": "repair-management-frontend", "version": "1.0.0", "description": "客退維修品記錄管理系統 - 前端應用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@reduxjs/toolkit": "^1.9.1", "react-redux": "^8.0.5", "antd": "^5.2.0", "react-hook-form": "^7.43.0", "@hookform/resolvers": "^2.9.10", "yup": "^0.32.11", "recharts": "^2.5.0", "axios": "^1.3.0", "dayjs": "^1.11.7", "@ant-design/icons": "^5.0.1", "styled-components": "^5.3.6", "@azure/msal-browser": "^2.35.0", "@azure/msal-react": "^1.5.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "prettier": "^2.8.4", "typescript": "^4.9.5", "vite": "^4.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "jest": "^29.4.0", "jest-environment-jsdom": "^29.4.0", "@types/jest": "^29.4.0", "ts-jest": "^29.0.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["repair-management", "react", "typescript", "antd", "sharepoint"], "author": "維修管理系統開發團隊", "license": "MIT"}