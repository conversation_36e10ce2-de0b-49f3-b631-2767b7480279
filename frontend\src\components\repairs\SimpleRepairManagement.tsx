import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Card,
  Typography,
  Modal,
  Form,
  Row,
  Col,
  message,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬維修記錄數據
const mockRepairData = [
  {
    id: 'R2024001',
    customer: '台積電',
    customerContact: '張經理',
    product: 'MIO-X1000',
    productSerial: 'SN20240001',
    faultDescription: '主板無法啟動，疑似電源模組故障',
    status: 'REPAIRING',
    priority: 'HIGH',
    receivedDate: '2024-07-08',
    estimatedCompletion: '2024-07-15',
    assignedTechnician: '李技師',
    repairCost: 15000,
  },
  {
    id: 'R2024002',
    customer: '聯發科',
    customerContact: '王主任',
    product: 'MIO-S500',
    productSerial: 'SN20240002',
    faultDescription: '顯示器閃爍，可能是背光模組問題',
    status: 'PENDING_TEST',
    priority: 'MEDIUM',
    receivedDate: '2024-07-07',
    estimatedCompletion: '2024-07-12',
    assignedTechnician: '陳技師',
    repairCost: 8000,
  },
  {
    id: 'R2024003',
    customer: '鴻海精密',
    customerContact: '林工程師',
    product: 'MIO-Pro',
    productSerial: 'SN20240003',
    faultDescription: '軟體異常，需要重新安裝系統',
    status: 'COMPLETED',
    priority: 'LOW',
    receivedDate: '2024-07-06',
    estimatedCompletion: '2024-07-10',
    assignedTechnician: '黃技師',
    repairCost: 3000,
  },
];

const SimpleRepairManagement: React.FC = () => {
  const [data, setData] = useState(mockRepairData);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING_INSPECTION': 'orange',
      'REPAIRING': 'blue',
      'PENDING_TEST': 'purple',
      'COMPLETED': 'green',
      'DELIVERED': 'cyan',
      'CANCELLED': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'PENDING_INSPECTION': '待檢查',
      'REPAIRING': '維修中',
      'PENDING_TEST': '待測試',
      'COMPLETED': '已完成',
      'DELIVERED': '已交付',
      'CANCELLED': '已取消',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'HIGH': 'red',
      'MEDIUM': 'orange',
      'LOW': 'green',
      'URGENT': 'purple',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: '維修編號',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: '客戶',
      dataIndex: 'customer',
      key: 'customer',
      width: 120,
    },
    {
      title: '產品型號',
      dataIndex: 'product',
      key: 'product',
      width: 120,
    },
    {
      title: '序號',
      dataIndex: 'productSerial',
      key: 'productSerial',
      width: 120,
    },
    {
      title: '故障描述',
      dataIndex: 'faultDescription',
      key: 'faultDescription',
      width: 200,
      ellipsis: true,
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '優先級',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '接收日期',
      dataIndex: 'receivedDate',
      key: 'receivedDate',
      width: 100,
    },
    {
      title: '負責技師',
      dataIndex: 'assignedTechnician',
      key: 'assignedTechnician',
      width: 100,
    },
    {
      title: '維修費用',
      dataIndex: 'repairCost',
      key: 'repairCost',
      width: 100,
      render: (cost: number) => `NT$ ${cost.toLocaleString()}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];

  const handleView = (record: any) => {
    Modal.info({
      title: '維修記錄詳情',
      width: 600,
      content: (
        <div style={{ marginTop: '16px' }}>
          <p><strong>維修編號：</strong>{record.id}</p>
          <p><strong>客戶：</strong>{record.customer}</p>
          <p><strong>聯絡人：</strong>{record.customerContact}</p>
          <p><strong>產品型號：</strong>{record.product}</p>
          <p><strong>產品序號：</strong>{record.productSerial}</p>
          <p><strong>故障描述：</strong>{record.faultDescription}</p>
          <p><strong>狀態：</strong>
            <Tag color={getStatusColor(record.status)} style={{ marginLeft: '8px' }}>
              {getStatusText(record.status)}
            </Tag>
          </p>
          <p><strong>優先級：</strong>
            <Tag color={getPriorityColor(record.priority)} style={{ marginLeft: '8px' }}>
              {record.priority}
            </Tag>
          </p>
          <p><strong>接收日期：</strong>{record.receivedDate}</p>
          <p><strong>預計完成：</strong>{record.estimatedCompletion}</p>
          <p><strong>負責技師：</strong>{record.assignedTechnician}</p>
          <p><strong>維修費用：</strong>NT$ {record.repairCost.toLocaleString()}</p>
        </div>
      ),
    });
  };

  const handleEdit = (record: any) => {
    setEditingRecord(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      console.log('表單數據:', values);
      
      if (editingRecord) {
        // 編輯模式
        const updatedData = data.map(item => 
          item.id === editingRecord.id ? { ...item, ...values } : item
        );
        setData(updatedData);
        message.success('維修記錄更新成功！');
      } else {
        // 新增模式
        const newRecord = {
          ...values,
          id: `R${Date.now()}`,
          receivedDate: new Date().toISOString().split('T')[0],
          repairCost: 0,
        };
        setData([newRecord, ...data]);
        message.success('維修記錄新增成功！');
      }
      
      setModalVisible(false);
      setEditingRecord(null);
    } catch (error) {
      console.error('表單驗證失敗:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingRecord(null);
  };

  return (
    <div>
      {/* 頁面標題 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>維修記錄管理</Title>
      </div>

      {/* 搜尋和操作區域 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜尋維修編號、客戶或產品"
              allowClear
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select placeholder="狀態篩選" allowClear style={{ width: '100%' }}>
              <Option value="PENDING_INSPECTION">待檢查</Option>
              <Option value="REPAIRING">維修中</Option>
              <Option value="PENDING_TEST">待測試</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="DELIVERED">已交付</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select placeholder="優先級篩選" allowClear style={{ width: '100%' }}>
              <Option value="HIGH">高</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="LOW">低</Option>
              <Option value="URGENT">緊急</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ width: '100%' }}
            >
              新增維修記錄
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 維修記錄表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: data.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          }}
        />
      </Card>

      {/* 新增/編輯模態框 */}
      <Modal
        title={editingRecord ? '編輯維修記錄' : '新增維修記錄'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="確定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'PENDING_INSPECTION',
            priority: 'MEDIUM',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customer"
                label="客戶名稱"
                rules={[{ required: true, message: '請輸入客戶名稱' }]}
              >
                <Input placeholder="請輸入客戶名稱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="customerContact"
                label="聯絡人"
                rules={[{ required: true, message: '請輸入聯絡人' }]}
              >
                <Input placeholder="請輸入聯絡人" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="product"
                label="產品型號"
                rules={[{ required: true, message: '請輸入產品型號' }]}
              >
                <Input placeholder="請輸入產品型號" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="productSerial"
                label="產品序號"
                rules={[{ required: true, message: '請輸入產品序號' }]}
              >
                <Input placeholder="請輸入產品序號" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="faultDescription"
            label="故障描述"
            rules={[{ required: true, message: '請輸入故障描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="請詳細描述故障現象和問題"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="status" label="維修狀態">
                <Select>
                  <Option value="PENDING_INSPECTION">待檢查</Option>
                  <Option value="REPAIRING">維修中</Option>
                  <Option value="PENDING_TEST">待測試</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="DELIVERED">已交付</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="priority" label="優先級">
                <Select>
                  <Option value="LOW">低</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="HIGH">高</Option>
                  <Option value="URGENT">緊急</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="assignedTechnician" label="負責技師">
                <Select placeholder="選擇負責技師">
                  <Option value="李技師">李技師</Option>
                  <Option value="陳技師">陳技師</Option>
                  <Option value="黃技師">黃技師</Option>
                  <Option value="王技師">王技師</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default SimpleRepairManagement;
