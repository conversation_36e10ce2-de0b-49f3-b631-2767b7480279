import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Typography, Spin, Alert, Progress, List, Avatar, Tag } from 'antd';
import {
  ToolOutlined,
  TeamOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  TrendingUpOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const { Title, Text } = Typography;

// 模擬數據 - 實際應用中會從 API 獲取
const mockDashboardData = {
  statistics: {
    totalRepairs: 1248,
    activeRepairs: 89,
    completedRepairs: 1159,
    totalCustomers: 456,
    totalProducts: 234,
    totalParts: 1567,
  },
  repairTrend: [
    { month: '1月', repairs: 98 },
    { month: '2月', repairs: 112 },
    { month: '3月', repairs: 89 },
    { month: '4月', repairs: 134 },
    { month: '5月', repairs: 156 },
    { month: '6月', repairs: 142 },
  ],
  repairStatus: [
    { name: '待檢測', value: 12, color: '#faad14' },
    { name: '檢測中', value: 8, color: '#1890ff' },
    { name: '維修中', value: 45, color: '#722ed1' },
    { name: '完成', value: 24, color: '#52c41a' },
  ],
  recentRepairs: [
    {
      id: 'R001',
      customerName: '張先生',
      productName: 'iPhone 14',
      status: '維修中',
      createdAt: '2024-01-15',
    },
    {
      id: 'R002',
      customerName: '李小姐',
      productName: 'MacBook Pro',
      status: '檢測中',
      createdAt: '2024-01-14',
    },
    {
      id: 'R003',
      customerName: '王先生',
      productName: 'iPad Air',
      status: '完成',
      createdAt: '2024-01-13',
    },
  ],
  lowStockParts: [
    { name: '螢幕總成', currentStock: 5, minStock: 10 },
    { name: '電池', currentStock: 8, minStock: 15 },
    { name: '主機板', currentStock: 2, minStock: 5 },
  ],
};

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(mockDashboardData);

  useEffect(() => {
    // 模擬 API 調用
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        // 這裡會調用實際的 API
        await new Promise(resolve => setTimeout(resolve, 1000));
        setData(mockDashboardData);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    const colors = {
      '待檢測': 'orange',
      '檢測中': 'blue',
      '維修中': 'purple',
      '完成': 'green',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        儀表板
      </Title>

      {/* 統計卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總維修記錄"
              value={data.statistics.totalRepairs}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="進行中維修"
              value={data.statistics.activeRepairs}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="客戶總數"
              value={data.statistics.totalCustomers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="零件總數"
              value={data.statistics.totalParts}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 圖表區域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="維修趨勢" extra={<TrendingUpOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.repairTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="repairs" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  dot={{ fill: '#1890ff', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="維修狀態分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data.repairStatus}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {data.repairStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div style={{ marginTop: 16 }}>
              {data.repairStatus.map((item, index) => (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <span>
                    <span style={{ 
                      display: 'inline-block', 
                      width: 12, 
                      height: 12, 
                      backgroundColor: item.color, 
                      marginRight: 8,
                      borderRadius: 2
                    }} />
                    {item.name}
                  </span>
                  <span>{item.value}</span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 列表區域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="最近維修記錄" extra={<CheckCircleOutlined />}>
            <List
              itemLayout="horizontal"
              dataSource={data.recentRepairs}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<ToolOutlined />} />}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{item.id} - {item.customerName}</span>
                        <Tag color={getStatusColor(item.status)}>{item.status}</Tag>
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary">{item.productName}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>{item.createdAt}</Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="庫存警報" 
            extra={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
          >
            {data.lowStockParts.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={data.lowStockParts}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<FileTextOutlined />} style={{ backgroundColor: '#faad14' }} />}
                      title={item.name}
                      description={
                        <div>
                          <Text type="secondary">
                            庫存: {item.currentStock} / 最小: {item.minStock}
                          </Text>
                          <Progress
                            percent={Math.round((item.currentStock / item.minStock) * 100)}
                            size="small"
                            status={item.currentStock < item.minStock ? 'exception' : 'normal'}
                            style={{ marginTop: 4 }}
                          />
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Alert
                message="庫存充足"
                description="所有零件庫存都在安全範圍內"
                type="success"
                showIcon
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
