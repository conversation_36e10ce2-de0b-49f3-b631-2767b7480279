import React, { useState, useEffect } from 'react';
import { Typography, message } from 'antd';
import ProductList from './ProductList';
import ProductForm from './ProductForm';
import CategoryManagement from './CategoryManagement';
import { 
  Product, 
  ProductCategory, 
  CreateProductRequest, 
  UpdateProductRequest 
} from '../../services/productService';

const { Title } = Typography;

const ProductManagement: React.FC = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isCategoryManagementVisible, setIsCategoryManagementVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [formLoading, setFormLoading] = useState(false);

  // 模擬分類數據
  const mockCategories: ProductCategory[] = [
    {
      id: 1,
      name: '智慧型手機',
      description: '各品牌智慧型手機產品',
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 2,
      name: '筆記型電腦',
      description: '筆記型電腦及相關配件',
      isActive: true,
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-14T14:20:00Z',
    },
    {
      id: 3,
      name: '平板電腦',
      description: '平板電腦產品',
      isActive: true,
      createdAt: '2024-01-13T09:15:00Z',
      updatedAt: '2024-01-13T09:15:00Z',
    },
  ];

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      // 這裡會調用 productCategoryService.getCategories({ isActive: true })
      await new Promise(resolve => setTimeout(resolve, 300));
      setCategories(mockCategories);
    } catch (error) {
      message.error('載入分類資料失敗');
    }
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setIsFormVisible(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setIsFormVisible(true);
  };

  const handleFormCancel = () => {
    setIsFormVisible(false);
    setEditingProduct(null);
  };

  const handleFormSubmit = async (data: CreateProductRequest | UpdateProductRequest) => {
    setFormLoading(true);
    try {
      if (editingProduct) {
        // 更新產品
        // await productService.updateProduct(editingProduct.id, data as UpdateProductRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('產品資料更新成功');
      } else {
        // 新增產品
        // await productService.createProduct(data as CreateProductRequest);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模擬 API 調用
        message.success('產品新增成功');
      }
      
      setIsFormVisible(false);
      setEditingProduct(null);
      // 這裡可以觸發產品列表重新載入
    } catch (error: any) {
      message.error(error.message || `${editingProduct ? '更新' : '新增'}產品失敗`);
    } finally {
      setFormLoading(false);
    }
  };

  const handleManageCategories = () => {
    setIsCategoryManagementVisible(true);
  };

  const handleCategoryManagementCancel = () => {
    setIsCategoryManagementVisible(false);
    // 重新載入分類數據
    fetchCategories();
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        產品管理
      </Title>

      <ProductList
        onAdd={handleAddProduct}
        onEdit={handleEditProduct}
        onManageCategories={handleManageCategories}
      />

      <ProductForm
        visible={isFormVisible}
        product={editingProduct}
        categories={categories}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

      <CategoryManagement
        visible={isCategoryManagementVisible}
        onCancel={handleCategoryManagementCancel}
      />
    </div>
  );
};

export default ProductManagement;
