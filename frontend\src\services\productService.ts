import { api, ApiResponse, PaginatedResponse } from './api';

// 產品相關類型定義
export interface Product {
  id: number;
  name: string;
  model: string;
  brand: string;
  categoryId: number;
  category?: ProductCategory;
  description?: string;
  specifications?: string;
  warrantyPeriod: number; // 保固期限（月）
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategory {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductRequest {
  name: string;
  model: string;
  brand: string;
  categoryId: number;
  description?: string;
  specifications?: string;
  warrantyPeriod: number;
}

export interface UpdateProductRequest {
  name?: string;
  model?: string;
  brand?: string;
  categoryId?: number;
  description?: string;
  specifications?: string;
  warrantyPeriod?: number;
  isActive?: boolean;
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: number;
  brand?: string;
  isActive?: boolean;
  sortBy?: 'name' | 'model' | 'brand' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductStatistics {
  total: number;
  active: number;
  inactive: number;
  byCategory: Array<{
    categoryId: number;
    categoryName: string;
    count: number;
  }>;
  byBrand: Array<{
    brand: string;
    count: number;
  }>;
  recentProducts: number;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  isActive?: boolean;
}

// 產品服務
export const productService = {
  // 獲取產品列表
  getProducts: async (params?: ProductQueryParams): Promise<ApiResponse<PaginatedResponse<Product>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<Product>>(url);
  },

  // 獲取單個產品
  getProduct: async (id: number): Promise<ApiResponse<Product>> => {
    return await api.get<Product>(`/products/${id}`);
  },

  // 創建產品
  createProduct: async (productData: CreateProductRequest): Promise<ApiResponse<Product>> => {
    return await api.post<Product>('/products', productData);
  },

  // 更新產品
  updateProduct: async (id: number, productData: UpdateProductRequest): Promise<ApiResponse<Product>> => {
    return await api.put<Product>(`/products/${id}`, productData);
  },

  // 刪除產品
  deleteProduct: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/products/${id}`);
  },

  // 激活產品
  activateProduct: async (id: number): Promise<ApiResponse<Product>> => {
    return await api.post<Product>(`/products/${id}/activate`);
  },

  // 停用產品
  deactivateProduct: async (id: number): Promise<ApiResponse<Product>> => {
    return await api.post<Product>(`/products/${id}/deactivate`);
  },

  // 搜尋產品
  searchProducts: async (query: string): Promise<ApiResponse<Product[]>> => {
    return await api.get<Product[]>(`/products/search?q=${encodeURIComponent(query)}`);
  },

  // 獲取產品統計
  getProductStatistics: async (): Promise<ApiResponse<ProductStatistics>> => {
    return await api.get<ProductStatistics>('/products/statistics');
  },

  // 檢查產品型號是否可用
  checkModelAvailability: async (model: string, excludeId?: number): Promise<ApiResponse<{ available: boolean }>> => {
    const params = new URLSearchParams({ model });
    if (excludeId) {
      params.append('excludeId', excludeId.toString());
    }
    return await api.get<{ available: boolean }>(`/products/check-model?${params.toString()}`);
  },

  // 批量操作產品
  batchUpdateProducts: async (ids: number[], action: 'activate' | 'deactivate' | 'delete'): Promise<ApiResponse<{ success: number; failed: number }>> => {
    return await api.post<{ success: number; failed: number }>('/products/batch', {
      ids,
      action,
    });
  },
};

// 產品分類服務
export const productCategoryService = {
  // 獲取分類列表
  getCategories: async (params?: { isActive?: boolean }): Promise<ApiResponse<ProductCategory[]>> => {
    const queryParams = new URLSearchParams();
    if (params?.isActive !== undefined) {
      queryParams.append('isActive', params.isActive.toString());
    }
    
    const url = `/product-categories${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<ProductCategory[]>(url);
  },

  // 獲取單個分類
  getCategory: async (id: number): Promise<ApiResponse<ProductCategory>> => {
    return await api.get<ProductCategory>(`/product-categories/${id}`);
  },

  // 創建分類
  createCategory: async (categoryData: CreateCategoryRequest): Promise<ApiResponse<ProductCategory>> => {
    return await api.post<ProductCategory>('/product-categories', categoryData);
  },

  // 更新分類
  updateCategory: async (id: number, categoryData: UpdateCategoryRequest): Promise<ApiResponse<ProductCategory>> => {
    return await api.put<ProductCategory>(`/product-categories/${id}`, categoryData);
  },

  // 刪除分類
  deleteCategory: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/product-categories/${id}`);
  },

  // 激活分類
  activateCategory: async (id: number): Promise<ApiResponse<ProductCategory>> => {
    return await api.post<ProductCategory>(`/product-categories/${id}/activate`);
  },

  // 停用分類
  deactivateCategory: async (id: number): Promise<ApiResponse<ProductCategory>> => {
    return await api.post<ProductCategory>(`/product-categories/${id}/deactivate`);
  },
};

export default productService;
