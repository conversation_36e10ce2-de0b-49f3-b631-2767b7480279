# 資料夾清理選項

## 🗂️ 當前狀況

已清理的文件：
- ✅ 多餘的批次檔（保留 run.bat）
- ✅ 重複的GUI文件（保留 complete-system-gui.html）
- ✅ 部分開發文檔
- ✅ Docker配置文件

## 📁 剩餘資料夾說明

### 🎯 核心文件（必須保留）
- `complete-system-gui.html` - 主要系統界面
- `run.bat` - 啟動批次檔
- `README.md` - 專案說明
- `使用指南.md` - 簡化使用說明

### 📚 文檔資料夾 (docs)
**保留的重要文檔**：
- 業務需求規格書
- 技術架構設計
- 資料庫設計
- 合規檢查清單
- 法律合規檢查報告
- 各功能完成報告

**建議**：如果您不需要詳細的技術文檔，可以刪除整個 `docs` 資料夾

### 💻 開發資料夾
**backend** - 後端開發代碼
**frontend** - 前端開發代碼  
**scripts** - 開發腳本

**選擇**：
- **保留** - 如果您需要修改或擴展系統功能
- **刪除** - 如果您只需要使用現有的HTML系統

## 🎯 建議的清理方案

### 方案A：僅保留使用文件（最簡潔）
保留：
- `complete-system-gui.html`
- `run.bat`
- `使用指南.md`

刪除：
- `docs` 資料夾
- `backend` 資料夾
- `frontend` 資料夾
- `scripts` 資料夾
- `README.md`

### 方案B：保留重要文檔（推薦）
保留：
- `complete-system-gui.html`
- `run.bat`
- `使用指南.md`
- `docs` 資料夾（重要文檔）

刪除：
- `backend` 資料夾
- `frontend` 資料夾
- `scripts` 資料夾

### 方案C：保留所有（開發用）
保留所有文件，以備將來開發需要

## 🤔 您的選擇

請告訴我您希望：
1. **方案A** - 最簡潔，只保留使用必需文件
2. **方案B** - 保留重要文檔，刪除開發代碼
3. **方案C** - 保留所有文件
4. **自定義** - 告訴我具體要保留什麼

我會根據您的選擇進行最終清理。
