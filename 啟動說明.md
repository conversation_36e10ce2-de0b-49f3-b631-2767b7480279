# IACT MIO維保管理系統 啟動說明

## 🚀 啟動方式

### 方式一：無終端視窗啟動（推薦）
**檔案**：`啟動維修記錄管理系統.vbs`
**特點**：
- ✅ 不會顯示終端視窗
- ✅ 直接開啟瀏覽器
- ✅ 乾淨的啟動體驗

**使用方法**：
1. 雙擊 `啟動維修記錄管理系統.vbs`
2. 系統會自動在預設瀏覽器中開啟

### 方式二：批次檔案啟動
**檔案**：`run.bat`
**特點**：
- ✅ 快速啟動
- ✅ 終端視窗會自動關閉
- ✅ 相容性好

**使用方法**：
1. 雙擊 `run.bat`
2. 終端視窗會短暫出現後自動關閉
3. 瀏覽器會開啟系統頁面

### 方式三：直接開啟HTML檔案
**檔案**：`complete-system-gui.html`
**特點**：
- ✅ 最直接的方式
- ✅ 不會有任何額外視窗

**使用方法**：
1. 雙擊 `complete-system-gui.html`
2. 直接在瀏覽器中開啟

## 📋 檔案說明

### 主要檔案
- **complete-system-gui.html**：主系統檔案
- **啟動維修記錄管理系統.vbs**：VBScript啟動檔案（推薦）
- **run.bat**：批次檔案啟動檔案

### 文件檔案
- **啟動說明.md**：本說明檔案
- **維修記錄查看編輯功能完成報告.md**：功能說明文件

## 🎯 推薦使用方式

**建議使用**：`啟動維修記錄管理系統.vbs`

**原因**：
1. **無終端干擾**：不會顯示任何終端視窗
2. **啟動速度快**：直接啟動瀏覽器
3. **用戶體驗佳**：乾淨簡潔的啟動過程
4. **專業外觀**：適合正式使用環境

## 🔧 技術說明

### VBScript啟動原理
```vbscript
Set objShell = CreateObject("WScript.Shell")
objShell.Run "complete-system-gui.html", 1, False
```

- **CreateObject("WScript.Shell")**：建立Shell物件
- **Run方法**：執行指定檔案
- **參數1**：正常視窗顯示
- **參數False**：不等待程式結束

### 批次檔案原理
```batch
@echo off
start "" complete-system-gui.html
exit /b
```

- **@echo off**：隱藏命令回顯
- **start ""**：在新視窗中啟動檔案
- **exit /b**：退出批次檔案但不關閉父視窗

## 🛠️ 故障排除

### 如果VBScript無法執行
1. **檢查安全設定**：確認Windows允許執行VBScript
2. **使用批次檔案**：改用 `run.bat`
3. **直接開啟HTML**：雙擊 `complete-system-gui.html`

### 如果瀏覽器沒有開啟
1. **檢查預設瀏覽器**：確認系統有設定預設瀏覽器
2. **手動開啟**：直接雙擊HTML檔案
3. **指定瀏覽器**：右鍵選擇"開啟方式"

### 如果系統無法載入
1. **檢查檔案完整性**：確認HTML檔案沒有損壞
2. **檢查瀏覽器相容性**：建議使用Chrome、Edge或Firefox
3. **清除瀏覽器快取**：按Ctrl+Shift+R強制重新載入

## 📱 系統需求

### 瀏覽器支援
- ✅ Google Chrome (推薦)
- ✅ Microsoft Edge (推薦)
- ✅ Mozilla Firefox
- ✅ Safari (Mac)

### 作業系統支援
- ✅ Windows 10/11
- ✅ macOS
- ✅ Linux

### 功能需求
- ✅ JavaScript支援
- ✅ HTML5支援
- ✅ CSS3支援

## 🎉 使用建議

### 日常使用
1. **建立桌面捷徑**：將 `啟動維修記錄管理系統.vbs` 拖曳到桌面
2. **釘選到工作列**：右鍵選擇"釘選到工作列"
3. **設定開機啟動**：將檔案放入啟動資料夾（可選）

### 團隊部署
1. **統一啟動方式**：建議全團隊使用VBScript啟動
2. **建立說明文件**：提供團隊成員使用說明
3. **定期備份**：備份系統檔案和數據

**🚀 現在您可以享受無終端干擾的乾淨啟動體驗！**
