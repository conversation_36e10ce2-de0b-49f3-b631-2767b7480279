import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Progress,
  Tag,
  Space,
  List,
  Avatar,
  Timeline,
  Button
} from 'antd';
import { 
  CloudOutlined,
  FileOutlined,
  SyncOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
  FolderOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface SharePointOverviewProps {
  connectionStatus: 'connected' | 'disconnected' | 'testing';
}

// 模擬數據
const mockSharePointStats = {
  totalFiles: 1247,
  syncedFiles: 1189,
  pendingSync: 58,
  totalSize: 15.6, // GB
  lastSyncAt: '2024-01-20T14:30:00Z',
  documentsGenerated: 342,
  templatesActive: 8,
  office365Connected: true,
};

const mockRecentFiles = [
  {
    id: '1',
    name: '維修報告_R2024-001.pdf',
    type: 'pdf',
    size: '2.3 MB',
    lastModified: '2024-01-20T14:30:00Z',
    modifiedBy: '維修技師',
    action: 'uploaded'
  },
  {
    id: '2',
    name: '客戶合約_張先生.docx',
    type: 'docx',
    size: '156 KB',
    lastModified: '2024-01-20T13:45:00Z',
    modifiedBy: '客服主管',
    action: 'synced'
  },
  {
    id: '3',
    name: '零件清單_2024Q1.xlsx',
    type: 'xlsx',
    size: '890 KB',
    lastModified: '2024-01-20T12:20:00Z',
    modifiedBy: '倉庫管理員',
    action: 'downloaded'
  },
  {
    id: '4',
    name: '維修流程圖.vsdx',
    type: 'vsdx',
    size: '1.2 MB',
    lastModified: '2024-01-20T11:15:00Z',
    modifiedBy: '系統管理員',
    action: 'uploaded'
  }
];

const mockSyncActivity = [
  {
    time: '2024-01-20T14:30:00Z',
    action: '上傳文檔',
    description: '維修報告_R2024-001.pdf 已上傳到 SharePoint',
    status: 'success'
  },
  {
    time: '2024-01-20T13:45:00Z',
    action: '同步完成',
    description: '客戶合約文件夾同步完成，共處理 15 個文件',
    status: 'success'
  },
  {
    time: '2024-01-20T12:20:00Z',
    action: '下載文檔',
    description: '零件清單_2024Q1.xlsx 已從 SharePoint 下載',
    status: 'success'
  },
  {
    time: '2024-01-20T11:15:00Z',
    action: '模板更新',
    description: '維修報告模板已更新到最新版本',
    status: 'info'
  },
  {
    time: '2024-01-20T10:30:00Z',
    action: '同步錯誤',
    description: '文件 "舊版報告.pdf" 同步失敗，權限不足',
    status: 'error'
  }
];

const SharePointOverview: React.FC<SharePointOverviewProps> = ({ connectionStatus }) => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (connectionStatus === 'connected') {
      fetchOverviewData();
    }
  }, [connectionStatus]);

  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      // 這裡會調用相關的SharePoint統計API
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Failed to fetch SharePoint overview data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (type: string) => {
    const icons = {
      pdf: '📄',
      docx: '📝',
      xlsx: '📊',
      vsdx: '📋',
      pptx: '📊',
      default: '📄'
    };
    return icons[type as keyof typeof icons] || icons.default;
  };

  const getActionIcon = (action: string) => {
    const icons = {
      uploaded: <UploadOutlined style={{ color: '#52c41a' }} />,
      downloaded: <DownloadOutlined style={{ color: '#1890ff' }} />,
      synced: <SyncOutlined style={{ color: '#722ed1' }} />,
      default: <FileOutlined />
    };
    return icons[action as keyof typeof icons] || icons.default;
  };

  const getActivityIcon = (status: string) => {
    const icons = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      info: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#f5222d' }} />,
    };
    return icons[status as keyof typeof icons] || icons.success;
  };

  if (connectionStatus !== 'connected') {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <CloudOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3} type="secondary">SharePoint未連接</Title>
          <Text type="secondary">
            請先在「SharePoint設定」頁面配置連接資訊，然後測試連接。
          </Text>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 核心統計 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總文件數"
              value={mockSharePointStats.totalFiles}
              prefix={<FileOutlined />}
              suffix={
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  已同步: {mockSharePointStats.syncedFiles}
                </Tag>
              }
            />
            <Progress 
              percent={Math.round((mockSharePointStats.syncedFiles / mockSharePointStats.totalFiles) * 100)} 
              size="small" 
              status="active"
              format={() => `同步率 ${Math.round((mockSharePointStats.syncedFiles / mockSharePointStats.totalFiles) * 100)}%`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待同步文件"
              value={mockSharePointStats.pendingSync}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                最後同步: {dayjs(mockSharePointStats.lastSyncAt).format('MM/DD HH:mm')}
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="存儲使用"
              value={mockSharePointStats.totalSize}
              precision={1}
              suffix="GB"
              prefix={<CloudOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Tag color="green">雲端存儲</Tag>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="生成文檔"
              value={mockSharePointStats.documentsGenerated}
              prefix={<TemplateOutlined />}
              suffix={
                <Tag color="purple" style={{ marginLeft: 8 }}>
                  模板: {mockSharePointStats.templatesActive}
                </Tag>
              }
            />
            <div style={{ marginTop: 8 }}>
              <Tag color={mockSharePointStats.office365Connected ? 'green' : 'red'}>
                Office 365 {mockSharePointStats.office365Connected ? '已連接' : '未連接'}
              </Tag>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近文件活動 */}
        <Col xs={24} lg={12}>
          <Card title="最近文件活動" loading={loading}>
            <List
              itemLayout="horizontal"
              dataSource={mockRecentFiles}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar style={{ backgroundColor: '#f6f6f6' }}>
                        {getFileIcon(item.type)}
                      </Avatar>
                    }
                    title={
                      <Space>
                        {getActionIcon(item.action)}
                        <Text strong>{item.name}</Text>
                      </Space>
                    }
                    description={
                      <Space split={<span style={{ color: '#d9d9d9' }}>•</span>}>
                        <Text type="secondary">{item.size}</Text>
                        <Text type="secondary">{item.modifiedBy}</Text>
                        <Text type="secondary">
                          {dayjs(item.lastModified).format('MM/DD HH:mm')}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 同步活動時間軸 */}
        <Col xs={24} lg={12}>
          <Card title="同步活動" loading={loading}>
            <Timeline>
              {mockSyncActivity.map((activity, index) => (
                <Timeline.Item
                  key={index}
                  dot={getActivityIcon(activity.status)}
                >
                  <div>
                    <Space>
                      <Text strong>{activity.action}</Text>
                      <Text type="secondary">
                        {dayjs(activity.time).format('MM/DD HH:mm')}
                      </Text>
                    </Space>
                    <div style={{ marginTop: 4, color: '#666' }}>
                      {activity.description}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <FolderOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: 8 }} />
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>文件管理</div>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: 12 }}>
                  瀏覽和管理SharePoint文件
                </div>
                <Button type="primary" size="small">
                  開啟文件管理
                </Button>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <SyncOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: 8 }} />
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>文檔同步</div>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: 12 }}>
                  管理本地和雲端文檔同步
                </div>
                <Button type="primary" size="small">
                  開啟同步管理
                </Button>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <TemplateOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: 8 }} />
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>文檔模板</div>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: 12 }}>
                  管理維修文檔模板
                </div>
                <Button type="primary" size="small">
                  管理模板
                </Button>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SharePointOverview;
