import { Router } from 'express';
import Auth<PERSON>ontroller from '../controllers/authController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  loginValidation,
  registerValidation,
  refreshTokenValidation,
  changePasswordValidation,
  resetPasswordRequestValidation,
  resetPasswordValidation,
  updateProfileValidation,
} from '../validators/authValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - username
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用戶名或電子郵件
 *         password:
 *           type: string
 *           description: 密碼
 *         rememberMe:
 *           type: boolean
 *           description: 記住我
 *     
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - password
 *         - fullName
 *       properties:
 *         username:
 *           type: string
 *           description: 用戶名
 *         email:
 *           type: string
 *           format: email
 *           description: 電子郵件
 *         password:
 *           type: string
 *           description: 密碼
 *         fullName:
 *           type: string
 *           description: 姓名
 *         role:
 *           type: string
 *           enum: [<PERSON>MI<PERSON>, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *           description: 角色
 *     
 *     AuthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             tokens:
 *               $ref: '#/components/schemas/TokenPair'
 *     
 *     TokenPair:
 *       type: object
 *       properties:
 *         accessToken:
 *           type: string
 *         refreshToken:
 *           type: string
 *         expiresIn:
 *           type: number
 *     
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         username:
 *           type: string
 *         email:
 *           type: string
 *         fullName:
 *           type: string
 *         role:
 *           type: string
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: 用戶登入
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: 登入成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       401:
 *         description: 認證失敗
 *       400:
 *         description: 請求參數錯誤
 */
router.post('/login', loginValidation, AuthController.login);

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     summary: 用戶註冊
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: 註冊成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       409:
 *         description: 用戶名或電子郵件已存在
 *       400:
 *         description: 請求參數錯誤
 */
router.post('/register', registerValidation, AuthController.register);

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     summary: 刷新訪問令牌
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: 刷新令牌
 *     responses:
 *       200:
 *         description: 令牌刷新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     tokens:
 *                       $ref: '#/components/schemas/TokenPair'
 *       401:
 *         description: 刷新令牌無效或已過期
 */
router.post('/refresh', refreshTokenValidation, AuthController.refreshToken);

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: 用戶登出
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 *       401:
 *         description: 未認證
 */
router.post('/logout', authenticate, AuthController.logout);

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     summary: 獲取當前用戶資訊
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取用戶資訊成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: 未認證
 *       404:
 *         description: 用戶不存在
 */
router.get('/me', authenticate, AuthController.getCurrentUser);

/**
 * @swagger
 * /api/v1/auth/change-password:
 *   put:
 *     summary: 修改密碼
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: 當前密碼
 *               newPassword:
 *                 type: string
 *                 description: 新密碼
 *     responses:
 *       200:
 *         description: 密碼修改成功
 *       400:
 *         description: 請求參數錯誤或當前密碼錯誤
 *       401:
 *         description: 未認證
 */
router.put('/change-password', authenticate, changePasswordValidation, AuthController.changePassword);

/**
 * @swagger
 * /api/v1/auth/verify:
 *   post:
 *     summary: 驗證訪問令牌
 *     tags: [Authentication]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 令牌有效
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     valid:
 *                       type: boolean
 *                     payload:
 *                       type: object
 *                     remainingTime:
 *                       type: number
 *                     isExpiringSoon:
 *                       type: boolean
 *       401:
 *         description: 令牌無效或已過期
 */
router.post('/verify', AuthController.verifyToken);

// 管理員專用路由
/**
 * @swagger
 * /api/v1/auth/admin/users:
 *   get:
 *     summary: 獲取所有用戶列表（管理員專用）
 *     tags: [Authentication, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER]
 *         description: 角色篩選
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *     responses:
 *       200:
 *         description: 獲取用戶列表成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/admin/users', authenticate, authorize(Role.ADMIN), (req, res) => {
  // TODO: 實作獲取用戶列表功能
  res.json({ message: '管理員用戶列表功能待實作' });
});

export default router;
