#!/bin/bash

# 資料庫測試腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 MySQL 是否運行
check_mysql() {
    log_info "檢查 MySQL 服務狀態..."
    
    if command -v docker &> /dev/null && docker ps | grep -q mysql; then
        log_success "MySQL Docker 容器正在運行"
        return 0
    elif command -v mysql &> /dev/null; then
        if mysql -e "SELECT 1" &> /dev/null; then
            log_success "MySQL 服務正在運行"
            return 0
        fi
    fi
    
    log_error "MySQL 服務未運行"
    return 1
}

# 啟動 MySQL (如果使用 Docker)
start_mysql() {
    log_info "啟動 MySQL Docker 容器..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d mysql
        log_info "等待 MySQL 啟動..."
        sleep 30
    else
        log_error "Docker Compose 未安裝"
        return 1
    fi
}

# 測試資料庫連接
test_connection() {
    log_info "測試資料庫連接..."
    
    cd backend
    
    if npm run db:generate &> /dev/null; then
        log_success "Prisma 客戶端生成成功"
    else
        log_error "Prisma 客戶端生成失敗"
        return 1
    fi
    
    # 測試連接
    if npx prisma db pull --force &> /dev/null; then
        log_success "資料庫連接測試成功"
    else
        log_error "資料庫連接測試失敗"
        return 1
    fi
    
    cd ..
}

# 執行資料庫遷移
run_migrations() {
    log_info "執行資料庫遷移..."
    
    cd backend
    
    if npm run db:push; then
        log_success "資料庫遷移完成"
    else
        log_error "資料庫遷移失敗"
        return 1
    fi
    
    cd ..
}

# 執行種子資料
run_seed() {
    log_info "執行種子資料..."
    
    cd backend
    
    if npm run db:seed; then
        log_success "種子資料執行完成"
    else
        log_error "種子資料執行失敗"
        return 1
    fi
    
    cd ..
}

# 驗證資料
verify_data() {
    log_info "驗證資料完整性..."
    
    cd backend
    
    # 檢查表是否存在和有資料
    local tables=("users" "customers" "products" "repair_records")
    
    for table in "${tables[@]}"; do
        local count=$(npx prisma db execute --stdin <<< "SELECT COUNT(*) as count FROM $table;" 2>/dev/null | grep -o '[0-9]\+' | tail -1)
        
        if [ "$count" -gt 0 ]; then
            log_success "$table 表: $count 筆記錄"
        else
            log_warning "$table 表: 無資料"
        fi
    done
    
    cd ..
}

# 測試 API 連接
test_api() {
    log_info "測試 API 連接..."
    
    # 啟動後端服務 (背景執行)
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # 等待服務啟動
    log_info "等待 API 服務啟動..."
    sleep 10
    
    # 測試健康檢查端點
    if curl -f http://localhost:5000/health &> /dev/null; then
        log_success "API 健康檢查通過"
    else
        log_error "API 健康檢查失敗"
        kill $BACKEND_PID 2>/dev/null || true
        return 1
    fi
    
    # 測試資料庫健康檢查
    if curl -f http://localhost:5000/api/v1/health/database &> /dev/null; then
        log_success "資料庫健康檢查通過"
    else
        log_warning "資料庫健康檢查端點未實作"
    fi
    
    # 關閉後端服務
    kill $BACKEND_PID 2>/dev/null || true
    sleep 2
}

# 清理測試環境
cleanup() {
    log_info "清理測試環境..."
    
    # 停止可能運行的進程
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "tsx watch" 2>/dev/null || true
    
    log_success "清理完成"
}

# 顯示幫助信息
show_help() {
    echo "資料庫測試腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  -s, --setup          執行完整設置 (啟動MySQL + 遷移 + 種子)"
    echo "  -m, --migrate        僅執行遷移"
    echo "  -d, --seed           僅執行種子資料"
    echo "  -t, --test           僅測試連接"
    echo "  -a, --api            測試 API 連接"
    echo "  -v, --verify         驗證資料"
    echo "  -c, --cleanup        清理環境"
    echo "  -h, --help           顯示此幫助信息"
    echo ""
    echo "默認行為: 執行完整測試流程"
}

# 主要函數
main() {
    local setup_only=false
    local migrate_only=false
    local seed_only=false
    local test_only=false
    local api_only=false
    local verify_only=false
    local cleanup_only=false
    
    # 解析命令行參數
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--setup)
                setup_only=true
                shift
                ;;
            -m|--migrate)
                migrate_only=true
                shift
                ;;
            -d|--seed)
                seed_only=true
                shift
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            -a|--api)
                api_only=true
                shift
                ;;
            -v|--verify)
                verify_only=true
                shift
                ;;
            -c|--cleanup)
                cleanup_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知選項: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 設置錯誤處理
    trap cleanup EXIT
    
    log_info "🗄️ 資料庫測試開始"
    echo "========================"
    
    # 執行指定操作
    if [ "$cleanup_only" = true ]; then
        cleanup
        exit 0
    fi
    
    if [ "$setup_only" = true ]; then
        if ! check_mysql; then
            start_mysql
        fi
        test_connection
        run_migrations
        run_seed
        verify_data
        exit 0
    fi
    
    if [ "$migrate_only" = true ]; then
        test_connection
        run_migrations
        exit 0
    fi
    
    if [ "$seed_only" = true ]; then
        test_connection
        run_seed
        exit 0
    fi
    
    if [ "$test_only" = true ]; then
        test_connection
        exit 0
    fi
    
    if [ "$api_only" = true ]; then
        test_api
        exit 0
    fi
    
    if [ "$verify_only" = true ]; then
        verify_data
        exit 0
    fi
    
    # 默認執行完整流程
    log_info "執行完整資料庫測試流程..."
    
    # 1. 檢查 MySQL
    if ! check_mysql; then
        log_info "嘗試啟動 MySQL..."
        start_mysql
    fi
    
    # 2. 測試連接
    test_connection
    
    # 3. 執行遷移
    run_migrations
    
    # 4. 執行種子資料
    run_seed
    
    # 5. 驗證資料
    verify_data
    
    # 6. 測試 API (可選)
    if command -v curl &> /dev/null; then
        test_api
    else
        log_warning "curl 未安裝，跳過 API 測試"
    fi
    
    echo ""
    log_success "🎉 資料庫測試完成！"
    echo ""
    echo "📋 測試結果:"
    echo "   ✅ MySQL 服務運行正常"
    echo "   ✅ 資料庫連接成功"
    echo "   ✅ 資料表遷移完成"
    echo "   ✅ 種子資料載入完成"
    echo "   ✅ 資料完整性驗證通過"
    echo ""
    echo "🌐 可用服務:"
    echo "   - 資料庫: localhost:3306"
    echo "   - API: http://localhost:5000 (需手動啟動)"
    echo ""
    echo "💡 下一步:"
    echo "   - 啟動開發服務器: ./scripts/dev.sh"
    echo "   - 查看 Prisma Studio: cd backend && npx prisma studio"
}

# 執行主函數
main "$@"
