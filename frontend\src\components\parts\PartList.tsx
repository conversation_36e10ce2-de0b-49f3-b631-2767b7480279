import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Modal, 
  message, 
  Popconfirm,
  Select,
  Row,
  Col,
  Tooltip,
  Badge,
  Progress
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  FileTextOutlined,
  DollarOutlined,
  WarningOutlined,
  ReloadOutlined,
  ExportOutlined,
  InboxOutlined,
  OutboxOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Part, PartQueryParams } from '../../services/partService';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬數據
const mockParts: Part[] = [
  {
    id: 1,
    name: '螢幕總成',
    partNumber: 'SCR-001',
    category: 'Display',
    brand: 'Samsung',
    model: 'Galaxy S21',
    description: 'OLED螢幕總成含觸控',
    unitPrice: 2500,
    currency: 'TWD',
    supplier: 'ABC零件商',
    supplierPartNumber: 'ABC-SCR-001',
    currentStock: 15,
    reservedStock: 3,
    availableStock: 12,
    minimumStock: 10,
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    name: '電池',
    partNumber: 'BAT-002',
    category: 'Battery',
    brand: 'Apple',
    model: 'iPhone 14',
    description: '原廠電池 3279mAh',
    unitPrice: 1200,
    currency: 'TWD',
    supplier: 'XYZ供應商',
    supplierPartNumber: 'XYZ-BAT-002',
    currentStock: 5,
    reservedStock: 2,
    availableStock: 3,
    minimumStock: 15,
    isActive: true,
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-14T14:20:00Z',
  },
  {
    id: 3,
    name: '主機板',
    partNumber: 'MB-003',
    category: 'Motherboard',
    brand: 'Apple',
    model: 'MacBook Pro',
    description: 'M2晶片主機板',
    unitPrice: 15000,
    currency: 'TWD',
    supplier: 'DEF電子',
    supplierPartNumber: 'DEF-MB-003',
    currentStock: 0,
    reservedStock: 0,
    availableStock: 0,
    minimumStock: 5,
    isActive: false,
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z',
  },
];

interface PartListProps {
  onEdit?: (part: Part) => void;
  onAdd?: () => void;
  onStockOperation?: (part: Part, operation: 'in' | 'out' | 'adjust') => void;
}

const PartList: React.FC<PartListProps> = ({ onEdit, onAdd, onStockOperation }) => {
  const [parts, setParts] = useState<Part[]>(mockParts);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string | undefined>(undefined);
  const [brandFilter, setBrandFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [stockFilter, setStockFilter] = useState<string | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockParts.length,
  });

  // 獲取分類和品牌列表
  const categories = Array.from(new Set(mockParts.map(p => p.category)));
  const brands = Array.from(new Set(mockParts.map(p => p.brand).filter(Boolean)));

  const fetchParts = async (params?: PartQueryParams) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = [...mockParts];
      
      if (params?.search) {
        filteredData = filteredData.filter(part =>
          part.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          part.partNumber.toLowerCase().includes(params.search!.toLowerCase()) ||
          part.brand?.toLowerCase().includes(params.search!.toLowerCase()) ||
          part.supplier?.toLowerCase().includes(params.search!.toLowerCase())
        );
      }
      
      if (params?.category) {
        filteredData = filteredData.filter(part => part.category === params.category);
      }
      
      if (params?.brand) {
        filteredData = filteredData.filter(part => part.brand === params.brand);
      }
      
      if (params?.isActive !== undefined) {
        filteredData = filteredData.filter(part => part.isActive === params.isActive);
      }
      
      if (stockFilter === 'low') {
        filteredData = filteredData.filter(part => part.currentStock < part.minimumStock && part.currentStock > 0);
      } else if (stockFilter === 'out') {
        filteredData = filteredData.filter(part => part.currentStock === 0);
      }
      
      setParts(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length,
      }));
    } catch (error) {
      message.error('載入零件資料失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchParts({
      search: searchText,
      category: categoryFilter,
      brand: brandFilter,
      isActive: statusFilter,
      lowStock: stockFilter === 'low',
      outOfStock: stockFilter === 'out',
      page: pagination.current,
      limit: pagination.pageSize,
    });
  }, [searchText, categoryFilter, brandFilter, statusFilter, stockFilter, pagination.current, pagination.pageSize]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('零件刪除成功');
      fetchParts();
    } catch (error) {
      message.error('刪除零件失敗');
    }
  };

  const handleToggleStatus = async (part: Part) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`零件${part.isActive ? '停用' : '激活'}成功`);
      fetchParts();
    } catch (error) {
      message.error(`${part.isActive ? '停用' : '激活'}零件失敗`);
    }
  };

  const getStockStatus = (part: Part) => {
    if (part.currentStock === 0) {
      return { status: 'error', text: '缺貨', color: '#ff4d4f' };
    } else if (part.currentStock < part.minimumStock * 0.1) {
      return { status: 'error', text: '危險', color: '#ff7a45' };
    } else if (part.currentStock < part.minimumStock) {
      return { status: 'warning', text: '不足', color: '#faad14' };
    } else {
      return { status: 'success', text: '充足', color: '#52c41a' };
    }
  };

  const getStockProgress = (part: Part) => {
    const percentage = Math.min((part.currentStock / part.minimumStock) * 100, 100);
    const stockStatus = getStockStatus(part);
    return (
      <Progress
        percent={percentage}
        size="small"
        status={stockStatus.status as any}
        showInfo={false}
        strokeColor={stockStatus.color}
      />
    );
  };

  const columns: ColumnsType<Part> = [
    {
      title: '零件資訊',
      key: 'partInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            <FileTextOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {record.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            編號：{record.partNumber}
          </div>
          {record.brand && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              品牌：{record.brand}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '分類',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '庫存狀況',
      key: 'stock',
      width: 150,
      render: (_, record) => {
        const stockStatus = getStockStatus(record);
        return (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text style={{ fontSize: '12px' }}>
                現有：{record.currentStock}
              </Text>
              <Tag color={stockStatus.color} style={{ fontSize: '10px', padding: '0 4px' }}>
                {stockStatus.text}
              </Tag>
            </div>
            {getStockProgress(record)}
            <div style={{ fontSize: '10px', color: '#999', marginTop: 2 }}>
              預留：{record.reservedStock} | 最小：{record.minimumStock}
            </div>
          </div>
        );
      },
    },
    {
      title: '單價',
      key: 'price',
      width: 100,
      render: (_, record) => (
        <div>
          <DollarOutlined style={{ marginRight: 4, color: '#52c41a' }} />
          <span>{record.unitPrice.toLocaleString()}</span>
          <div style={{ fontSize: '10px', color: '#999' }}>
            {record.currency}
          </div>
        </div>
      ),
    },
    {
      title: '供應商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 120,
      render: (supplier) => supplier || '-',
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '正常' : '停用'} 
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="編輯">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Tooltip title="入庫">
            <Button
              type="text"
              icon={<InboxOutlined />}
              style={{ color: '#52c41a' }}
              onClick={() => onStockOperation?.(record, 'in')}
            />
          </Tooltip>
          <Tooltip title="出庫">
            <Button
              type="text"
              icon={<OutboxOutlined />}
              style={{ color: '#faad14' }}
              onClick={() => onStockOperation?.(record, 'out')}
              disabled={record.availableStock === 0}
            />
          </Tooltip>
          <Tooltip title="調整">
            <Button
              type="text"
              icon={<SettingOutlined />}
              style={{ color: '#722ed1' }}
              onClick={() => onStockOperation?.(record, 'adjust')}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '停用' : '激活'}>
            <Button
              type="text"
              onClick={() => handleToggleStatus(record)}
              style={{ color: record.isActive ? '#faad14' : '#52c41a' }}
            >
              {record.isActive ? '停用' : '激活'}
            </Button>
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個零件嗎？"
            description="刪除後將無法恢復，請謹慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              零件管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Search
                placeholder="搜尋零件名稱、編號、品牌或供應商"
                allowClear
                style={{ width: 280 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="分類篩選"
                allowClear
                style={{ width: 120 }}
                onChange={setCategoryFilter}
              >
                {categories.map(category => (
                  <Option key={category} value={category}>
                    {category}
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="品牌篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setBrandFilter}
              >
                {brands.map(brand => (
                  <Option key={brand} value={brand}>
                    {brand}
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="庫存篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setStockFilter}
              >
                <Option value="low">
                  <WarningOutlined style={{ color: '#faad14' }} /> 不足
                </Option>
                <Option value="out">
                  <WarningOutlined style={{ color: '#ff4d4f' }} /> 缺貨
                </Option>
              </Select>
              <Select
                placeholder="狀態篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setStatusFilter}
              >
                <Option value={true}>正常</Option>
                <Option value={false}>停用</Option>
              </Select>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchParts()}
                loading={loading}
              >
                重新整理
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('匯出功能開發中')}
              >
                匯出
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                新增零件
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Table
        columns={columns}
        dataSource={parts}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        scroll={{ x: 1400 }}
        size="small"
      />
    </Card>
  );
};

export default PartList;
