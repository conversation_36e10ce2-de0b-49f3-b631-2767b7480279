import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space,
  Button,
  message,
  Progress,
  Alert
} from 'antd';
import { 
  BugOutlined,
  ThunderboltOutlined,
  ShieldCheckOutlined,
  DatabaseOutlined,
  ApiOutlined,
  MobileOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import FunctionalTesting from './FunctionalTesting';
import PerformanceTesting from './PerformanceTesting';
import SecurityTesting from './SecurityTesting';
import APITesting from './APITesting';
import DatabaseTesting from './DatabaseTesting';
import ResponsiveTesting from './ResponsiveTesting';
import TestResults from './TestResults';

const { Title } = Typography;
const { TabPane } = Tabs;

interface TestSuite {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  progress: number;
  testCount: number;
  passedCount: number;
  failedCount: number;
}

const SystemTesting: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [testSuites, setTestSuites] = useState<TestSuite[]>([
    {
      id: 'functional',
      name: '功能測試',
      description: '測試所有業務功能的正確性',
      status: 'pending',
      progress: 0,
      testCount: 45,
      passedCount: 0,
      failedCount: 0
    },
    {
      id: 'performance',
      name: '性能測試',
      description: '測試系統性能和響應時間',
      status: 'pending',
      progress: 0,
      testCount: 20,
      passedCount: 0,
      failedCount: 0
    },
    {
      id: 'security',
      name: '安全測試',
      description: '測試系統安全性和權限控制',
      status: 'pending',
      progress: 0,
      testCount: 15,
      passedCount: 0,
      failedCount: 0
    },
    {
      id: 'api',
      name: 'API測試',
      description: '測試所有API接口的功能和性能',
      status: 'pending',
      progress: 0,
      testCount: 35,
      passedCount: 0,
      failedCount: 0
    },
    {
      id: 'database',
      name: '數據庫測試',
      description: '測試數據庫操作和數據完整性',
      status: 'pending',
      progress: 0,
      testCount: 25,
      passedCount: 0,
      failedCount: 0
    },
    {
      id: 'responsive',
      name: '響應式測試',
      description: '測試不同設備和瀏覽器的兼容性',
      status: 'pending',
      progress: 0,
      testCount: 12,
      passedCount: 0,
      failedCount: 0
    }
  ]);

  const runAllTests = async () => {
    setLoading(true);
    message.info('開始執行完整測試套件...');

    try {
      for (let i = 0; i < testSuites.length; i++) {
        const suite = testSuites[i];
        
        // 更新測試狀態為運行中
        setTestSuites(prev => prev.map(s => 
          s.id === suite.id 
            ? { ...s, status: 'running' as const, progress: 0 }
            : s
        ));

        // 模擬測試執行
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 200));
          setTestSuites(prev => prev.map(s => 
            s.id === suite.id 
              ? { ...s, progress }
              : s
          ));
        }

        // 模擬測試結果
        const passed = Math.floor(suite.testCount * (0.85 + Math.random() * 0.1));
        const failed = suite.testCount - passed;
        const status = failed === 0 ? 'passed' : 'failed';

        setTestSuites(prev => prev.map(s => 
          s.id === suite.id 
            ? { ...s, status: status as const, passedCount: passed, failedCount: failed }
            : s
        ));

        message.success(`${suite.name}完成: ${passed}/${suite.testCount} 通過`);
      }

      message.success('所有測試套件執行完成！');
    } catch (error) {
      message.error('測試執行過程中發生錯誤');
    } finally {
      setLoading(false);
    }
  };

  const runSingleTest = async (suiteId: string) => {
    const suite = testSuites.find(s => s.id === suiteId);
    if (!suite) return;

    setTestSuites(prev => prev.map(s => 
      s.id === suiteId 
        ? { ...s, status: 'running' as const, progress: 0 }
        : s
    ));

    try {
      // 模擬測試執行
      for (let progress = 0; progress <= 100; progress += 5) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setTestSuites(prev => prev.map(s => 
          s.id === suiteId 
            ? { ...s, progress }
            : s
        ));
      }

      // 模擬測試結果
      const passed = Math.floor(suite.testCount * (0.85 + Math.random() * 0.1));
      const failed = suite.testCount - passed;
      const status = failed === 0 ? 'passed' : 'failed';

      setTestSuites(prev => prev.map(s => 
        s.id === suiteId 
          ? { ...s, status: status as const, passedCount: passed, failedCount: failed }
          : s
      ));

      message.success(`${suite.name}完成: ${passed}/${suite.testCount} 通過`);
    } catch (error) {
      message.error(`${suite.name}執行失敗`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'passed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <PlayCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getOverallStatus = () => {
    const completed = testSuites.filter(s => s.status === 'passed' || s.status === 'failed');
    const passed = testSuites.filter(s => s.status === 'passed');
    const failed = testSuites.filter(s => s.status === 'failed');

    if (completed.length === 0) {
      return { type: 'info', message: '尚未開始測試' };
    } else if (completed.length < testSuites.length) {
      return { type: 'warning', message: '測試進行中...' };
    } else if (failed.length === 0) {
      return { type: 'success', message: '所有測試通過！' };
    } else {
      return { type: 'error', message: `${failed.length} 個測試套件失敗` };
    }
  };

  const overallStatus = getOverallStatus();

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BugOutlined />
          測試總覽
        </span>
      ),
      children: (
        <div>
          <Alert
            message="系統測試總覽"
            description={overallStatus.message}
            type={overallStatus.type as any}
            showIcon
            style={{ marginBottom: 24 }}
          />

          <div style={{ marginBottom: 24 }}>
            <Space>
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={runAllTests}
                loading={loading}
                size="large"
              >
                執行所有測試
              </Button>
            </Space>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
            {testSuites.map(suite => (
              <Card key={suite.id} size="small">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                  <Space>
                    {getStatusIcon(suite.status)}
                    <span style={{ fontWeight: 'bold' }}>{suite.name}</span>
                  </Space>
                  <Button 
                    size="small" 
                    onClick={() => runSingleTest(suite.id)}
                    disabled={suite.status === 'running'}
                  >
                    執行
                  </Button>
                </div>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: 12 }}>
                  {suite.description}
                </div>
                {suite.status === 'running' && (
                  <Progress percent={suite.progress} size="small" />
                )}
                {(suite.status === 'passed' || suite.status === 'failed') && (
                  <div style={{ fontSize: '12px' }}>
                    <span style={{ color: '#52c41a' }}>通過: {suite.passedCount}</span>
                    <span style={{ margin: '0 8px', color: '#d9d9d9' }}>|</span>
                    <span style={{ color: '#f5222d' }}>失敗: {suite.failedCount}</span>
                    <span style={{ margin: '0 8px', color: '#d9d9d9' }}>|</span>
                    <span>總計: {suite.testCount}</span>
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>
      ),
    },
    {
      key: 'functional',
      label: (
        <span>
          <BugOutlined />
          功能測試
        </span>
      ),
      children: <FunctionalTesting />,
    },
    {
      key: 'performance',
      label: (
        <span>
          <ThunderboltOutlined />
          性能測試
        </span>
      ),
      children: <PerformanceTesting />,
    },
    {
      key: 'security',
      label: (
        <span>
          <ShieldCheckOutlined />
          安全測試
        </span>
      ),
      children: <SecurityTesting />,
    },
    {
      key: 'api',
      label: (
        <span>
          <ApiOutlined />
          API測試
        </span>
      ),
      children: <APITesting />,
    },
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          數據庫測試
        </span>
      ),
      children: <DatabaseTesting />,
    },
    {
      key: 'responsive',
      label: (
        <span>
          <MobileOutlined />
          響應式測試
        </span>
      ),
      children: <ResponsiveTesting />,
    },
    {
      key: 'results',
      label: (
        <span>
          <CheckCircleOutlined />
          測試結果
        </span>
      ),
      children: <TestResults testSuites={testSuites} />,
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          🧪 系統測試與優化
        </Title>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </div>
  );
};

export default SystemTesting;
