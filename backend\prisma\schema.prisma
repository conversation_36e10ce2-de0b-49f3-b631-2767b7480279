// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        BigInt   @id @default(autoincrement())
  username  String   @unique @db.VarChar(50)
  email     String   @unique @db.VarChar(100)
  passwordHash String @map("password_hash") @db.VarChar(255)
  fullName  String   @map("full_name") @db.VarChar(100)
  role      UserRole
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  createdRepairRecords    RepairRecord[] @relation("CreatedBy")
  assignedRepairRecords   RepairRecord[] @relation("AssignedTechnician")
  statusHistoryChanges    RepairStatusHistory[]
  progressRecords         RepairProgress[]
  uploadedFiles           SharePointFile[]
  syncLogRecords          SharePointSyncLog[]
  systemSettingsUpdates  SystemSetting[]

  @@map("users")
}

model Customer {
  id        BigInt   @id @default(autoincrement())
  name      String   @db.VarChar(100)
  phone     String?  @db.VarChar(20)
  email     String?  @db.VarChar(100)
  address   String?  @db.Text
  company   String?  @db.VarChar(100)
  notes     String?  @db.Text
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  repairRecords RepairRecord[]

  @@index([name])
  @@index([phone])
  @@index([email])
  @@map("customers")
}

model ProductCategory {
  id          BigInt   @id @default(autoincrement())
  name        String   @db.VarChar(100)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  products Product[]

  @@map("product_categories")
}

model Product {
  id              BigInt    @id @default(autoincrement())
  categoryId      BigInt?   @map("category_id")
  model           String    @db.VarChar(100)
  brand           String?   @db.VarChar(50)
  description     String?   @db.Text
  warrantyMonths  Int       @default(12) @map("warranty_months")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  category      ProductCategory? @relation(fields: [categoryId], references: [id])
  repairRecords RepairRecord[]

  @@index([model])
  @@index([brand])
  @@map("products")
}

model RepairRecord {
  id                      BigInt            @id @default(autoincrement())
  repairNumber            String            @unique @map("repair_number") @db.VarChar(20)
  customerId              BigInt            @map("customer_id")
  productId               BigInt            @map("product_id")
  productSerial           String?           @map("product_serial") @db.VarChar(100)
  purchaseDate            DateTime?         @map("purchase_date") @db.Date
  warrantyStatus          WarrantyStatus    @default(IN_WARRANTY) @map("warranty_status")
  faultDescription        String            @map("fault_description") @db.Text
  receivedDate            DateTime          @map("received_date") @db.Date
  estimatedCompletionDate DateTime?         @map("estimated_completion_date") @db.Date
  actualCompletionDate    DateTime?         @map("actual_completion_date") @db.Date
  priority                Priority          @default(MEDIUM)
  status                  RepairStatus      @default(PENDING_INSPECTION)
  assignedTechnicianId    BigInt?           @map("assigned_technician_id")
  repairCost              Decimal           @default(0.00) @map("repair_cost") @db.Decimal(10, 2)
  partsCost               Decimal           @default(0.00) @map("parts_cost") @db.Decimal(10, 2)
  totalCost               Decimal           @default(0.00) @map("total_cost") @db.Decimal(10, 2)
  notes                   String?           @db.Text
  createdBy               BigInt            @map("created_by")
  createdAt               DateTime          @default(now()) @map("created_at")
  updatedAt               DateTime          @updatedAt @map("updated_at")

  // Relations
  customer            Customer                @relation(fields: [customerId], references: [id])
  product             Product                 @relation(fields: [productId], references: [id])
  assignedTechnician  User?                   @relation("AssignedTechnician", fields: [assignedTechnicianId], references: [id])
  createdByUser       User                    @relation("CreatedBy", fields: [createdBy], references: [id])
  statusHistory       RepairStatusHistory[]
  repairParts         RepairPart[]
  progressRecords     RepairProgress[]
  sharepointFiles     SharePointFile[]

  @@index([repairNumber])
  @@index([status])
  @@index([receivedDate])
  @@index([customerId])
  @@index([assignedTechnicianId])
  @@map("repair_records")
}

model RepairStatusHistory {
  id              BigInt   @id @default(autoincrement())
  repairRecordId  BigInt   @map("repair_record_id")
  oldStatus       String?  @map("old_status") @db.VarChar(50)
  newStatus       String   @map("new_status") @db.VarChar(50)
  changedBy       BigInt   @map("changed_by")
  changeReason    String?  @map("change_reason") @db.Text
  changedAt       DateTime @default(now()) @map("changed_at")

  // Relations
  repairRecord RepairRecord @relation(fields: [repairRecordId], references: [id], onDelete: Cascade)
  changedByUser User        @relation(fields: [changedBy], references: [id])

  @@index([repairRecordId])
  @@index([changedAt])
  @@map("repair_status_history")
}

model Part {
  id            BigInt   @id @default(autoincrement())
  partNumber    String   @unique @map("part_number") @db.VarChar(50)
  name          String   @db.VarChar(100)
  description   String?  @db.Text
  unitPrice     Decimal  @default(0.00) @map("unit_price") @db.Decimal(10, 2)
  stockQuantity Int      @default(0) @map("stock_quantity")
  minimumStock  Int      @default(0) @map("minimum_stock")
  supplier      String?  @db.VarChar(100)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  repairParts RepairPart[]

  @@index([partNumber])
  @@index([name])
  @@map("parts")
}

model RepairPart {
  id             BigInt      @id @default(autoincrement())
  repairRecordId BigInt      @map("repair_record_id")
  partId         BigInt      @map("part_id")
  quantity       Int         @default(1)
  unitPrice      Decimal     @map("unit_price") @db.Decimal(10, 2)
  totalPrice     Decimal     @map("total_price") @db.Decimal(10, 2)
  notes          String?     @db.Text
  createdAt      DateTime    @default(now()) @map("created_at")

  // Relations
  repairRecord RepairRecord @relation(fields: [repairRecordId], references: [id], onDelete: Cascade)
  part         Part         @relation(fields: [partId], references: [id])

  @@index([repairRecordId])
  @@index([partId])
  @@map("repair_parts")
}

model RepairProgress {
  id             BigInt      @id @default(autoincrement())
  repairRecordId BigInt      @map("repair_record_id")
  progressDate   DateTime    @map("progress_date") @db.Date
  description    String      @db.Text
  technicianId   BigInt      @map("technician_id")
  hoursSpent     Decimal     @default(0.00) @map("hours_spent") @db.Decimal(4, 2)
  createdAt      DateTime    @default(now()) @map("created_at")

  // Relations
  repairRecord RepairRecord @relation(fields: [repairRecordId], references: [id], onDelete: Cascade)
  technician   User         @relation(fields: [technicianId], references: [id])

  @@index([repairRecordId])
  @@index([progressDate])
  @@map("repair_progress")
}

model SharePointFile {
  id                BigInt     @id @default(autoincrement())
  repairRecordId    BigInt?    @map("repair_record_id")
  sharepointFileId  String     @map("sharepoint_file_id") @db.VarChar(255)
  fileName          String     @map("file_name") @db.VarChar(255)
  filePath          String     @map("file_path") @db.VarChar(500)
  fileType          String?    @map("file_type") @db.VarChar(50)
  fileSize          BigInt?    @map("file_size")
  uploadDate        DateTime   @default(now()) @map("upload_date")
  uploadedBy        BigInt     @map("uploaded_by")
  syncStatus        SyncStatus @default(PENDING) @map("sync_status")
  lastSyncDate      DateTime?  @map("last_sync_date")
  createdAt         DateTime   @default(now()) @map("created_at")
  updatedAt         DateTime   @updatedAt @map("updated_at")

  // Relations
  repairRecord  RepairRecord?       @relation(fields: [repairRecordId], references: [id])
  uploadedByUser User               @relation(fields: [uploadedBy], references: [id])
  syncLogs      SharePointSyncLog[]

  @@index([repairRecordId])
  @@index([sharepointFileId])
  @@index([syncStatus])
  @@map("sharepoint_files")
}

model SharePointSyncLog {
  id                BigInt        @id @default(autoincrement())
  operationType     OperationType @map("operation_type")
  fileId            BigInt?       @map("file_id")
  sharepointFileId  String?       @map("sharepoint_file_id") @db.VarChar(255)
  status            SyncLogStatus
  errorMessage      String?       @map("error_message") @db.Text
  syncDate          DateTime      @default(now()) @map("sync_date")
  performedBy       BigInt        @map("performed_by")

  // Relations
  file          SharePointFile? @relation(fields: [fileId], references: [id])
  performedByUser User          @relation(fields: [performedBy], references: [id])

  @@index([syncDate])
  @@index([status])
  @@index([operationType])
  @@map("sharepoint_sync_log")
}

model SystemSetting {
  id           BigInt   @id @default(autoincrement())
  settingKey   String   @unique @map("setting_key") @db.VarChar(100)
  settingValue String?  @map("setting_value") @db.Text
  description  String?  @db.Text
  updatedBy    BigInt?  @map("updated_by")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  updatedByUser User? @relation(fields: [updatedBy], references: [id])

  @@map("system_settings")
}

// Enums
enum UserRole {
  ADMIN
  TECHNICIAN
  CUSTOMER_SERVICE
  VIEWER

  @@map("user_role")
}

enum WarrantyStatus {
  IN_WARRANTY
  OUT_OF_WARRANTY
  EXTENDED

  @@map("warranty_status")
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT

  @@map("priority")
}

enum RepairStatus {
  PENDING_INSPECTION
  INSPECTING
  PENDING_QUOTE
  REPAIRING
  PENDING_TEST
  TESTING
  COMPLETED
  DELIVERED
  PAUSED
  CANCELLED

  @@map("repair_status")
}

enum SyncStatus {
  PENDING
  SYNCED
  FAILED

  @@map("sync_status")
}

enum OperationType {
  UPLOAD
  DOWNLOAD
  UPDATE
  DELETE
  SYNC

  @@map("operation_type")
}

enum SyncLogStatus {
  SUCCESS
  FAILED

  @@map("sync_log_status")
}
