import { Router } from 'express';
import ProductController from '../controllers/productController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  createCategoryValidation,
  updateCategoryValidation,
  categoryListValidation,
  categoryIdValidation,
} from '../validators/productValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     UpdateProductCategoryRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: 類別名稱
 *         description:
 *           type: string
 *           maxLength: 500
 *           description: 類別描述
 *         parentId:
 *           type: string
 *           description: 父類別ID
 *         isActive:
 *           type: boolean
 *           description: 是否活躍
 */

/**
 * @swagger
 * /api/v1/product-categories:
 *   get:
 *     summary: 獲取產品類別列表
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: parentId
 *         schema:
 *           type: string
 *         description: 父類別ID篩選（空值表示根類別）
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 活躍狀態篩選
 *       - in: query
 *         name: includeChildren
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含子類別
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, createdAt, updatedAt]
 *           default: name
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取產品類別列表成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), categoryListValidation, ProductController.getCategoryList);

/**
 * @swagger
 * /api/v1/product-categories:
 *   post:
 *     summary: 創建產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProductCategoryRequest'
 *     responses:
 *       201:
 *         description: 產品類別創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       409:
 *         description: 類別名稱已存在
 */
router.post('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), createCategoryValidation, ProductController.createCategory);

/**
 * @swagger
 * /api/v1/product-categories/tree:
 *   get:
 *     summary: 獲取產品類別樹狀結構
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取類別樹狀結構成功
 *       401:
 *         description: 未認證
 */
router.get('/tree', authenticate, ProductController.getCategoryTree);

/**
 * @swagger
 * /api/v1/product-categories/statistics:
 *   get:
 *     summary: 獲取產品類別統計
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), ProductController.getCategoryStatistics);

/**
 * @swagger
 * /api/v1/product-categories/{id}:
 *   get:
 *     summary: 根據ID獲取產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 類別ID
 *     responses:
 *       200:
 *         description: 獲取產品類別成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 產品類別不存在
 */
router.get('/:id', authenticate, categoryIdValidation, ProductController.getCategoryById);

/**
 * @swagger
 * /api/v1/product-categories/{id}:
 *   put:
 *     summary: 更新產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 類別ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductCategoryRequest'
 *     responses:
 *       200:
 *         description: 產品類別更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品類別不存在
 */
router.put('/:id', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), updateCategoryValidation, ProductController.updateCategory);

/**
 * @swagger
 * /api/v1/product-categories/{id}:
 *   delete:
 *     summary: 刪除產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 類別ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 產品類別刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品類別不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), categoryIdValidation, ProductController.deleteCategory);

/**
 * @swagger
 * /api/v1/product-categories/{id}/activate:
 *   post:
 *     summary: 激活產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 類別ID
 *     responses:
 *       200:
 *         description: 產品類別激活成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品類別不存在
 */
router.post('/:id/activate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), categoryIdValidation, ProductController.activateCategory);

/**
 * @swagger
 * /api/v1/product-categories/{id}/deactivate:
 *   post:
 *     summary: 停用產品類別
 *     tags: [Product Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 類別ID
 *     responses:
 *       200:
 *         description: 產品類別停用成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 產品類別不存在
 */
router.post('/:id/deactivate', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), categoryIdValidation, ProductController.deactivateCategory);

export default router;
