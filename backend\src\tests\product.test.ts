import request from 'supertest';
import { PrismaClient, UserRole } from '@prisma/client';
import app from '../index';
import { PasswordUtils } from '../utils/password';
import { JWTUtils } from '../utils/jwt';

const prisma = new PrismaClient();

describe('Product Management API', () => {
  let adminToken: string;
  let customerServiceToken: string;
  let technicianToken: string;
  let viewerToken: string;
  let testUsers: any[] = [];
  let testCategories: any[] = [];
  let testProducts: any[] = [];

  beforeAll(async () => {
    // 建立測試用戶
    const adminPassword = await PasswordUtils.hashPassword('AdminPass123!');
    const csPassword = await PasswordUtils.hashPassword('CSPass123!');
    const techPassword = await PasswordUtils.hashPassword('TechPass123!');
    const viewerPassword = await PasswordUtils.hashPassword('ViewerPass123!');

    const admin = await prisma.user.create({
      data: {
        username: 'testadmin_product',
        email: '<EMAIL>',
        passwordHash: adminPassword,
        fullName: 'Test Admin Product',
        role: UserRole.ADMIN,
      },
    });

    const customerService = await prisma.user.create({
      data: {
        username: 'testcs_product',
        email: '<EMAIL>',
        passwordHash: csPassword,
        fullName: 'Test Customer Service Product',
        role: UserRole.CUSTOMER_SERVICE,
      },
    });

    const technician = await prisma.user.create({
      data: {
        username: 'testtech_product',
        email: '<EMAIL>',
        passwordHash: techPassword,
        fullName: 'Test Technician Product',
        role: UserRole.TECHNICIAN,
      },
    });

    const viewer = await prisma.user.create({
      data: {
        username: 'testviewer_product',
        email: '<EMAIL>',
        passwordHash: viewerPassword,
        fullName: 'Test Viewer Product',
        role: UserRole.VIEWER,
      },
    });

    testUsers = [admin, customerService, technician, viewer];

    // 生成令牌
    adminToken = JWTUtils.generateAccessToken({
      userId: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: admin.role,
    });

    customerServiceToken = JWTUtils.generateAccessToken({
      userId: customerService.id.toString(),
      username: customerService.username,
      email: customerService.email,
      role: customerService.role,
    });

    technicianToken = JWTUtils.generateAccessToken({
      userId: technician.id.toString(),
      username: technician.username,
      email: technician.email,
      role: technician.role,
    });

    viewerToken = JWTUtils.generateAccessToken({
      userId: viewer.id.toString(),
      username: viewer.username,
      email: viewer.email,
      role: viewer.role,
    });

    // 建立測試產品類別
    const category1 = await prisma.productCategory.create({
      data: {
        name: 'Test Category 1',
        description: 'Test category description 1',
      },
    });

    const category2 = await prisma.productCategory.create({
      data: {
        name: 'Test Category 2',
        description: 'Test category description 2',
        parentId: category1.id,
      },
    });

    testCategories = [category1, category2];

    // 建立測試產品
    const product1 = await prisma.product.create({
      data: {
        name: 'Test Product 1',
        model: 'TP001',
        brand: 'Test Brand',
        categoryId: category1.id,
        description: 'Test product description 1',
        specifications: 'Test specifications 1',
        warrantyPeriod: 12,
        price: 1000,
      },
    });

    const product2 = await prisma.product.create({
      data: {
        name: 'Test Product 2',
        model: 'TP002',
        brand: 'Another Brand',
        categoryId: category2.id,
        description: 'Test product description 2',
        price: 2000,
        isActive: false,
      },
    });

    testProducts = [product1, product2];
  });

  afterAll(async () => {
    // 清理測試資料
    for (const product of testProducts) {
      await prisma.product.delete({ where: { id: product.id } }).catch(() => {});
    }

    for (const category of testCategories) {
      await prisma.productCategory.delete({ where: { id: category.id } }).catch(() => {});
    }

    for (const user of testUsers) {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }

    // 清理其他測試產品和類別
    await prisma.product.deleteMany({
      where: {
        name: { startsWith: 'Test Product' },
      },
    });

    await prisma.productCategory.deleteMany({
      where: {
        name: { startsWith: 'Test Category' },
      },
    });

    await prisma.$disconnect();
  });

  describe('GET /api/v1/products', () => {
    test('should get product list as admin', async () => {
      const response = await request(app)
        .get('/api/v1/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('products');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.products)).toBe(true);
    });

    test('should get product list as technician', async () => {
      const response = await request(app)
        .get('/api/v1/products')
        .set('Authorization', `Bearer ${technicianToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('products');
    });

    test('should get product list with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/products?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.products.length).toBeLessThanOrEqual(1);
    });

    test('should filter products by category', async () => {
      const categoryId = testCategories[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/products?categoryId=${categoryId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.products.every((product: any) => 
        product.categoryId === categoryId
      )).toBe(true);
    });

    test('should filter products by brand', async () => {
      const response = await request(app)
        .get('/api/v1/products?brand=Test Brand')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.products.some((product: any) => 
        product.brand.includes('Test Brand')
      )).toBe(true);
    });

    test('should fail without authentication', async () => {
      await request(app)
        .get('/api/v1/products')
        .expect(401);
    });

    test('should fail with insufficient permissions', async () => {
      await request(app)
        .get('/api/v1/products')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/products', () => {
    test('should create product as admin', async () => {
      const productData = {
        name: 'New Test Product',
        model: 'NTP001',
        brand: 'New Test Brand',
        categoryId: testCategories[0].id.toString(),
        description: 'New test product description',
        specifications: 'New test specifications',
        warrantyPeriod: 24,
        price: 1500,
      };

      const response = await request(app)
        .post('/api/v1/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(productData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(productData.name);
      expect(response.body.data.product.model).toBe(productData.model);
    });

    test('should create product as customer service', async () => {
      const productData = {
        name: 'CS Test Product',
        model: 'CSTP001',
        brand: 'CS Test Brand',
        categoryId: testCategories[0].id.toString(),
        price: 800,
      };

      const response = await request(app)
        .post('/api/v1/products')
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(productData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(productData.name);
    });

    test('should fail with duplicate model', async () => {
      const productData = {
        name: 'Duplicate Model Product',
        model: 'TP001', // 已存在的型號
        brand: 'Test Brand',
        categoryId: testCategories[0].id.toString(),
      };

      await request(app)
        .post('/api/v1/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(productData)
        .expect(400);
    });

    test('should fail with invalid data', async () => {
      const productData = {
        name: 'A', // 太短
        model: '',
        brand: '',
        categoryId: 'invalid',
      };

      await request(app)
        .post('/api/v1/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(productData)
        .expect(400);
    });

    test('should fail without permission', async () => {
      const productData = {
        name: 'Unauthorized Product',
        model: 'UP001',
        brand: 'Unauthorized Brand',
        categoryId: testCategories[0].id.toString(),
      };

      await request(app)
        .post('/api/v1/products')
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(productData)
        .expect(403);
    });
  });

  describe('GET /api/v1/products/:id', () => {
    test('should get product by id', async () => {
      const productId = testProducts[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/products/${productId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.id).toBe(productId);
    });

    test('should get product with details', async () => {
      const productId = testProducts[0].id.toString();

      const response = await request(app)
        .get(`/api/v1/products/${productId}?includeDetails=true`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product).toHaveProperty('statistics');
      expect(response.body.data.product).toHaveProperty('repairRecords');
    });

    test('should fail with non-existent product', async () => {
      await request(app)
        .get('/api/v1/products/999999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/v1/products/:id', () => {
    test('should update product as admin', async () => {
      const productId = testProducts[0].id.toString();
      const updateData = {
        name: 'Updated Product Name',
        price: 1200,
        warrantyPeriod: 18,
      };

      const response = await request(app)
        .put(`/api/v1/products/${productId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(updateData.name);
      expect(response.body.data.product.price).toBe(updateData.price);
    });

    test('should update product as customer service', async () => {
      const productId = testProducts[1].id.toString();
      const updateData = {
        name: 'CS Updated Product',
        brand: 'Updated Brand',
      };

      const response = await request(app)
        .put(`/api/v1/products/${productId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(updateData.name);
    });

    test('should fail without permission', async () => {
      const productId = testProducts[0].id.toString();
      const updateData = {
        name: 'Unauthorized Update',
      };

      await request(app)
        .put(`/api/v1/products/${productId}`)
        .set('Authorization', `Bearer ${viewerToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('GET /api/v1/products/search', () => {
    test('should search products', async () => {
      const response = await request(app)
        .get('/api/v1/products/search?q=Test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(Array.isArray(response.body.data.results)).toBe(true);
    });

    test('should fail with short search query', async () => {
      await request(app)
        .get('/api/v1/products/search?q=A')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/products/statistics', () => {
    test('should get product statistics', async () => {
      const response = await request(app)
        .get('/api/v1/products/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('active');
      expect(response.body.data.statistics).toHaveProperty('byCategory');
    });

    test('should fail without permission', async () => {
      await request(app)
        .get('/api/v1/products/statistics')
        .set('Authorization', `Bearer ${viewerToken}`)
        .expect(403);
    });
  });

  describe('GET /api/v1/products/model/:model', () => {
    test('should find product by model', async () => {
      const response = await request(app)
        .get('/api/v1/products/model/TP001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.model).toBe('TP001');
    });

    test('should fail with non-existent model', async () => {
      await request(app)
        .get('/api/v1/products/model/NONEXISTENT')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/products/check-model/:model', () => {
    test('should check model availability', async () => {
      const response = await request(app)
        .get('/api/v1/products/check-model/AVAILABLE001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
    });

    test('should detect existing model', async () => {
      const response = await request(app)
        .get('/api/v1/products/check-model/TP001')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(false);
    });
  });

  describe('POST /api/v1/products/:id/activate', () => {
    test('should activate product', async () => {
      const productId = testProducts[1].id.toString(); // 這個產品是停用的

      const response = await request(app)
        .post(`/api/v1/products/${productId}/activate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.isActive).toBe(true);
    });
  });

  describe('POST /api/v1/products/:id/deactivate', () => {
    test('should deactivate product', async () => {
      const productId = testProducts[0].id.toString();

      const response = await request(app)
        .post(`/api/v1/products/${productId}/deactivate`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.isActive).toBe(false);
    });
  });

  describe('DELETE /api/v1/products/:id', () => {
    test('should delete product as admin', async () => {
      // 先創建一個測試產品用於刪除
      const testProduct = await prisma.product.create({
        data: {
          name: 'Product to Delete',
          model: 'PTD001',
          brand: 'Delete Brand',
          categoryId: testCategories[0].id,
        },
      });

      const response = await request(app)
        .delete(`/api/v1/products/${testProduct.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should fail without admin permission', async () => {
      const productId = testProducts[0].id.toString();

      await request(app)
        .delete(`/api/v1/products/${productId}`)
        .set('Authorization', `Bearer ${customerServiceToken}`)
        .expect(403);
    });
  });

  // === 產品類別測試 ===

  describe('GET /api/v1/product-categories', () => {
    test('should get category list', async () => {
      const response = await request(app)
        .get('/api/v1/product-categories')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('categories');
      expect(Array.isArray(response.body.data.categories)).toBe(true);
    });
  });

  describe('POST /api/v1/product-categories', () => {
    test('should create category as admin', async () => {
      const categoryData = {
        name: 'New Test Category',
        description: 'New test category description',
      };

      const response = await request(app)
        .post('/api/v1/product-categories')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(categoryData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.category.name).toBe(categoryData.name);
    });
  });

  describe('GET /api/v1/product-categories/tree', () => {
    test('should get category tree', async () => {
      const response = await request(app)
        .get('/api/v1/product-categories/tree')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tree');
      expect(Array.isArray(response.body.data.tree)).toBe(true);
    });
  });

  describe('GET /api/v1/product-categories/statistics', () => {
    test('should get category statistics', async () => {
      const response = await request(app)
        .get('/api/v1/product-categories/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toHaveProperty('total');
      expect(response.body.data.statistics).toHaveProperty('rootCategories');
    });
  });
});
