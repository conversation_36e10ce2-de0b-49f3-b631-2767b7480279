import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PartService } from '../services/partService';
import { PartRepository } from '../repositories/partRepository';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { 
  CreatePartRequest, 
  UpdatePartRequest, 
  PartQueryParams,
  StockOperationRequest,
  BatchStockOperationRequest 
} from '../types/part';

// 初始化服務
const partRepository = new PartRepository(prisma);
const partService = new PartService(partRepository);

export class PartController {
  // === 零件相關方法 ===

  // 獲取零件列表
  static async getPartList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: PartQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        category: req.query.category as string,
        brand: req.query.brand as string,
        supplier: req.query.supplier as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        lowStock: req.query.lowStock === 'true',
        outOfStock: req.query.outOfStock === 'true',
        priceMin: req.query.priceMin ? parseFloat(req.query.priceMin as string) : undefined,
        priceMax: req.query.priceMax ? parseFloat(req.query.priceMax as string) : undefined,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await partService.getPartList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取零件
  static async getPartById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeDetails = false } = req.query;

      let part;
      if (includeDetails === 'true') {
        part = await partService.getPartDetailById(id);
      } else {
        part = await partService.getPartById(id);
      }

      if (!part) {
        throw createError('零件不存在', 404);
      }

      res.json({
        success: true,
        data: { part },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建零件
  static async createPart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const partData: CreatePartRequest = req.body;
      const createdBy = req.user?.userId;

      const newPart = await partService.createPart(partData, createdBy);

      res.status(201).json({
        success: true,
        message: '零件創建成功',
        data: { part: newPart },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新零件
  static async updatePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const partData: UpdatePartRequest = req.body;
      const updatedBy = req.user?.userId;

      const updatedPart = await partService.updatePart(id, partData, updatedBy);

      res.json({
        success: true,
        message: '零件更新成功',
        data: { part: updatedPart },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除零件
  static async deletePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId;

      await partService.deletePart(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '零件已永久刪除' : '零件已停用',
      });
    } catch (error) {
      next(error);
    }
  }

  // 搜尋零件
  static async searchParts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw createError('搜尋關鍵字不能為空', 400);
      }

      const results = await partService.searchParts(query, parseInt(limit as string));

      res.json({
        success: true,
        data: { 
          query,
          results,
          count: results.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取零件統計
  static async getPartStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await partService.getPartStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 激活零件
  static async activatePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const activatedBy = req.user?.userId;

      const part = await partService.activatePart(id, activatedBy);

      res.json({
        success: true,
        message: '零件已激活',
        data: { part },
      });
    } catch (error) {
      next(error);
    }
  }

  // 停用零件
  static async deactivatePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const deactivatedBy = req.user?.userId;

      const part = await partService.deactivatePart(id, deactivatedBy);

      res.json({
        success: true,
        message: '零件已停用',
        data: { part },
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據零件編號查找零件
  static async findPartByPartNumber(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { partNumber } = req.params;

      if (!partNumber) {
        throw createError('零件編號不能為空', 400);
      }

      const part = await partService.findPartByPartNumber(partNumber);

      if (!part) {
        throw createError('零件不存在', 404);
      }

      res.json({
        success: true,
        data: { part },
      });
    } catch (error) {
      next(error);
    }
  }

  // 檢查零件編號是否可用
  static async checkPartNumberAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { partNumber } = req.params;
      const { excludeId } = req.query;

      if (!partNumber) {
        throw createError('零件編號不能為空', 400);
      }

      const isAvailable = await partService.checkPartNumberAvailability(partNumber, excludeId as string);

      res.json({
        success: true,
        data: { 
          partNumber,
          available: isAvailable,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // === 庫存管理相關方法 ===

  // 執行庫存操作
  static async performStockOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const operation: StockOperationRequest = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      const result = await partService.performStockOperation(operation, performedBy);

      res.json({
        success: true,
        message: '庫存操作成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量庫存操作
  static async performBatchStockOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const batchOperation: BatchStockOperationRequest = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      const result = await partService.performBatchStockOperation(batchOperation, performedBy);

      res.json({
        success: true,
        message: '批量庫存操作完成',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 入庫操作
  static async stockIn(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, reason, referenceId } = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      if (!quantity || quantity <= 0) {
        throw createError('入庫數量必須大於0', 400);
      }

      if (!reason) {
        throw createError('入庫原因不能為空', 400);
      }

      const result = await partService.stockIn(id, quantity, reason, performedBy, referenceId);

      res.json({
        success: true,
        message: '入庫操作成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 出庫操作
  static async stockOut(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, reason, referenceId } = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      if (!quantity || quantity <= 0) {
        throw createError('出庫數量必須大於0', 400);
      }

      if (!reason) {
        throw createError('出庫原因不能為空', 400);
      }

      const result = await partService.stockOut(id, quantity, reason, performedBy, referenceId);

      res.json({
        success: true,
        message: '出庫操作成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 庫存調整
  static async adjustStock(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, reason } = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      if (quantity === undefined || quantity === null || quantity < 0) {
        throw createError('調整後的庫存數量不能小於0', 400);
      }

      if (!reason) {
        throw createError('調整原因不能為空', 400);
      }

      const result = await partService.adjustStock(id, quantity, reason, performedBy);

      res.json({
        success: true,
        message: '庫存調整成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 預留庫存
  static async reserveStock(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, reason, referenceId } = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      if (!quantity || quantity <= 0) {
        throw createError('預留數量必須大於0', 400);
      }

      if (!reason) {
        throw createError('預留原因不能為空', 400);
      }

      const result = await partService.reserveStock(id, quantity, reason, performedBy, referenceId);

      res.json({
        success: true,
        message: '庫存預留成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 釋放預留庫存
  static async releaseReservedStock(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity, reason, referenceId } = req.body;
      const performedBy = req.user?.userId || 'UNKNOWN';

      if (!quantity || quantity <= 0) {
        throw createError('釋放數量必須大於0', 400);
      }

      if (!reason) {
        throw createError('釋放原因不能為空', 400);
      }

      const result = await partService.releaseReservedStock(id, quantity, reason, performedBy, referenceId);

      res.json({
        success: true,
        message: '預留庫存釋放成功',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取庫存警報
  static async getStockAlerts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const alerts = await partService.getStockAlerts();

      res.json({
        success: true,
        data: { 
          alerts,
          count: alerts.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default PartController;
