import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { ProductService } from '../services/productService';
import { ProductRepository } from '../repositories/productRepository';
import { ProductCategoryRepository } from '../repositories/productCategoryRepository';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { 
  CreateProductRequest, 
  UpdateProductRequest, 
  CreateProductCategoryRequest,
  UpdateProductCategoryRequest,
  ProductQueryParams,
  ProductCategoryQueryParams,
  BatchProductOperation 
} from '../types/product';

// 初始化服務
const productRepository = new ProductRepository(prisma);
const categoryRepository = new ProductCategoryRepository(prisma);
const productService = new ProductService(productRepository, categoryRepository);

export class ProductController {
  // === 產品相關方法 ===

  // 獲取產品列表
  static async getProductList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: ProductQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        categoryId: req.query.categoryId as string,
        brand: req.query.brand as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        hasPrice: req.query.hasPrice ? req.query.hasPrice === 'true' : undefined,
        priceMin: req.query.priceMin ? parseFloat(req.query.priceMin as string) : undefined,
        priceMax: req.query.priceMax ? parseFloat(req.query.priceMax as string) : undefined,
        warrantyMin: req.query.warrantyMin ? parseInt(req.query.warrantyMin as string) : undefined,
        warrantyMax: req.query.warrantyMax ? parseInt(req.query.warrantyMax as string) : undefined,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await productService.getProductList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取產品
  static async getProductById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeDetails = false } = req.query;

      let product;
      if (includeDetails === 'true') {
        product = await productService.getProductDetailById(id);
      } else {
        product = await productService.getProductById(id);
      }

      if (!product) {
        throw createError('產品不存在', 404);
      }

      res.json({
        success: true,
        data: { product },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建產品
  static async createProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const productData: CreateProductRequest = req.body;
      const createdBy = req.user?.userId;

      const newProduct = await productService.createProduct(productData, createdBy);

      res.status(201).json({
        success: true,
        message: '產品創建成功',
        data: { product: newProduct },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新產品
  static async updateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const productData: UpdateProductRequest = req.body;
      const updatedBy = req.user?.userId;

      const updatedProduct = await productService.updateProduct(id, productData, updatedBy);

      res.json({
        success: true,
        message: '產品更新成功',
        data: { product: updatedProduct },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除產品
  static async deleteProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId;

      await productService.deleteProduct(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '產品已永久刪除' : '產品已停用',
      });
    } catch (error) {
      next(error);
    }
  }

  // 搜尋產品
  static async searchProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw createError('搜尋關鍵字不能為空', 400);
      }

      const results = await productService.searchProducts(query, parseInt(limit as string));

      res.json({
        success: true,
        data: { 
          query,
          results,
          count: results.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取產品統計
  static async getProductStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await productService.getProductStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量操作產品
  static async batchOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const operation: BatchProductOperation = req.body;
      const operatedBy = req.user?.userId;

      const result = await productService.batchOperation(operation, operatedBy);

      res.json({
        success: true,
        message: '批量操作完成',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 激活產品
  static async activateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const activatedBy = req.user?.userId;

      const product = await productService.activateProduct(id, activatedBy);

      res.json({
        success: true,
        message: '產品已激活',
        data: { product },
      });
    } catch (error) {
      next(error);
    }
  }

  // 停用產品
  static async deactivateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const deactivatedBy = req.user?.userId;

      const product = await productService.deactivateProduct(id, deactivatedBy);

      res.json({
        success: true,
        message: '產品已停用',
        data: { product },
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據型號查找產品
  static async findProductByModel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { model } = req.params;

      if (!model) {
        throw createError('產品型號不能為空', 400);
      }

      const product = await productService.findProductByModel(model);

      if (!product) {
        throw createError('產品不存在', 404);
      }

      res.json({
        success: true,
        data: { product },
      });
    } catch (error) {
      next(error);
    }
  }

  // 檢查型號是否可用
  static async checkModelAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { model } = req.params;
      const { excludeId } = req.query;

      if (!model) {
        throw createError('產品型號不能為空', 400);
      }

      const product = await productService.findProductByModel(model);
      const isAvailable = !product || (excludeId && product.id === excludeId);

      res.json({
        success: true,
        data: { 
          model,
          available: isAvailable,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // === 產品類別相關方法 ===

  // 獲取產品類別列表
  static async getCategoryList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: ProductCategoryQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        parentId: req.query.parentId as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        includeChildren: req.query.includeChildren === 'true',
        sortBy: req.query.sortBy as any || 'name',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'asc',
      };

      const result = await productService.getCategoryList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取產品類別
  static async getCategoryById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const category = await productService.getCategoryById(id);

      if (!category) {
        throw createError('產品類別不存在', 404);
      }

      res.json({
        success: true,
        data: { category },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建產品類別
  static async createCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const categoryData: CreateProductCategoryRequest = req.body;
      const createdBy = req.user?.userId;

      const newCategory = await productService.createCategory(categoryData, createdBy);

      res.status(201).json({
        success: true,
        message: '產品類別創建成功',
        data: { category: newCategory },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新產品類別
  static async updateCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const categoryData: UpdateProductCategoryRequest = req.body;
      const updatedBy = req.user?.userId;

      const updatedCategory = await productService.updateCategory(id, categoryData, updatedBy);

      res.json({
        success: true,
        message: '產品類別更新成功',
        data: { category: updatedCategory },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除產品類別
  static async deleteCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId;

      await productService.deleteCategory(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '產品類別已永久刪除' : '產品類別已停用',
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取產品類別樹狀結構
  static async getCategoryTree(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const tree = await productService.getCategoryTree();

      res.json({
        success: true,
        data: { tree },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取產品類別統計
  static async getCategoryStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await productService.getCategoryStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 激活產品類別
  static async activateCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const activatedBy = req.user?.userId;

      const category = await productService.activateCategory(id, activatedBy);

      res.json({
        success: true,
        message: '產品類別已激活',
        data: { category },
      });
    } catch (error) {
      next(error);
    }
  }

  // 停用產品類別
  static async deactivateCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const deactivatedBy = req.user?.userId;

      const category = await productService.deactivateCategory(id, deactivatedBy);

      res.json({
        success: true,
        message: '產品類別已停用',
        data: { category },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default ProductController;
