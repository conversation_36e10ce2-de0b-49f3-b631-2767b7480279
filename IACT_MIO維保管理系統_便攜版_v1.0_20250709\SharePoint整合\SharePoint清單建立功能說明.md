# SharePoint 清單建立功能說明

## 🎯 **功能概述**

系統現在提供了三種方式來建立 SharePoint 清單，讓您可以根據環境和權限選擇最適合的方式。

## 🚀 **使用方式**

### 步驟 1：進入系統設定
1. **啟動系統**：雙擊 `啟動系統.bat`
2. **登入系統**：使用 admin / admin123
3. **進入系統設定**：點擊左側導航 "系統設定"
4. **選擇存儲模式**：選擇 "SharePoint 整合"

### 步驟 2：輸入 SharePoint URL
1. **輸入網站 URL**：在 "SharePoint 網站 URL" 欄位輸入您的網站地址
2. **URL 格式範例**：
   ```
   https://yourcompany.sharepoint.com/sites/maintenance
   https://contoso.sharepoint.com/sites/repair-service
   ```

### 步驟 3：建立清單
1. **點擊按鈕**：點擊 "📋 建立清單結構" 按鈕
2. **選擇方式**：從三種建立方式中選擇一種

## 🛠️ **三種建立方式**

### 方式一：🚀 自動建立 (推薦)

#### 特點
- ✅ **完全自動化**：一鍵建立所有清單和欄位
- ✅ **即時反饋**：顯示建立進度和結果
- ✅ **錯誤處理**：自動處理常見錯誤
- ✅ **最快速度**：幾分鐘內完成

#### 適用情況
- 您有 SharePoint 網站的參與者權限或更高
- 網站允許透過 REST API 建立清單
- 想要最快速的建立方式

#### 使用步驟
1. **選擇自動建立**
2. **等待處理**：系統會自動建立所有清單
3. **查看結果**：顯示成功和失敗的清單數量
4. **測試連線**：建立完成後測試 SharePoint 連線

#### 建立的清單
- ✅ **維修記錄清單** (14個欄位)
- ✅ **客戶資料清單** (9個欄位)
- ✅ **零件資料清單** (11個欄位)
- ✅ **產品資料清單** (8個欄位)

### 方式二：⚡ PowerShell 腳本

#### 特點
- ✅ **專業工具**：使用 PnP PowerShell 模組
- ✅ **可下載**：生成完整的 .ps1 腳本檔案
- ✅ **可重複使用**：腳本可用於多個環境
- ✅ **詳細日誌**：完整的執行日誌

#### 適用情況
- 自動建立失敗時的備用方案
- 需要在多個 SharePoint 網站建立相同結構
- IT 管理員偏好使用 PowerShell
- 需要更多控制權的情況

#### 使用步驟
1. **選擇 PowerShell 腳本**
2. **下載腳本**：點擊 "💾 下載腳本" 或 "📋 複製腳本"
3. **儲存檔案**：將腳本儲存為 .ps1 檔案
4. **執行腳本**：
   ```powershell
   # 以管理員身分執行 PowerShell
   .\CreateSharePointLists.ps1
   ```
5. **按照提示**：完成 SharePoint 登入驗證

#### 腳本功能
- 自動安裝 PnP PowerShell 模組
- 互動式 SharePoint 登入
- 建立所有必要清單和欄位
- 詳細的進度顯示
- 錯誤處理和報告

### 方式三：📋 手動建立

#### 特點
- ✅ **完全控制**：手動控制每個步驟
- ✅ **詳細指導**：逐步建立說明
- ✅ **適用所有環境**：不受 API 限制
- ✅ **學習機會**：了解 SharePoint 清單結構

#### 適用情況
- 自動方式都無法使用時
- 需要自訂清單結構
- 學習 SharePoint 清單建立
- 環境有特殊限制

#### 使用步驟
1. **選擇手動建立**
2. **查看說明**：詳細的建立指導會顯示
3. **複製說明**：可複製說明到其他地方參考
4. **按步驟建立**：在 SharePoint 中手動建立清單

#### 建立指導包含
- 📋 **清單名稱**：每個清單的正確名稱
- 🔧 **欄位詳情**：每個欄位的類型和設定
- 📝 **建立步驟**：詳細的操作步驟
- ⚙️ **設定說明**：特殊設定的說明

## 📊 **清單結構詳情**

### 維修記錄清單
```
清單名稱：維修記錄
欄位數量：14個
主要欄位：
- RepairId (文字) - 維修編號 *必填
- CustomerName (文字) - 客戶姓名 *必填
- ProductName (文字) - 產品名稱 *必填
- ServiceStatus (選擇) - 維保狀態 [客訴, 維保]
- RepairRecord (選擇) - 維修記錄 [調整, 換馬達, 清潔, 校正, 更換零件, 軟體更新]
- TestResult (選擇) - 測試結果 [正常, 異常, 待測試]
```

### 客戶資料清單
```
清單名稱：客戶資料
欄位數量：9個
主要欄位：
- CustomerId (文字) - 客戶編號 *必填
- CustomerName (文字) - 客戶姓名 *必填
- Status (選擇) - 客戶狀態 [活躍, 非活躍, VIP, 黑名單]
```

### 零件資料清單
```
清單名稱：零件資料
欄位數量：11個
主要欄位：
- PartId (文字) - 零件編號 *必填
- PartName (文字) - 零件名稱 *必填
- Category (選擇) - 零件分類 [電子零件, 機械零件, 消耗品, 工具, 配件]
- Stock (數字) - 庫存數量
- Price (貨幣) - 單價
```

### 產品資料清單
```
清單名稱：產品資料
欄位數量：8個
主要欄位：
- ProductId (文字) - 產品編號 *必填
- ProductName (文字) - 產品名稱 *必填
- Category (選擇) - 產品分類 [手機, 平板, 筆電, 桌機, 印表機, 其他]
```

## 🔧 **故障排除**

### 自動建立失敗
**可能原因**：
- 權限不足
- CORS 設定問題
- 網路連線問題
- SharePoint 版本不支援

**解決方案**：
1. 檢查權限設定 (至少需要參與者權限)
2. 嘗試使用 PowerShell 腳本
3. 聯絡 SharePoint 管理員
4. 使用手動建立方式

### PowerShell 腳本執行失敗
**可能原因**：
- PowerShell 執行原則限制
- PnP PowerShell 模組安裝失敗
- SharePoint 登入失敗

**解決方案**：
```powershell
# 設定執行原則
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 手動安裝模組
Install-Module -Name PnP.PowerShell -Force -AllowClobber

# 檢查模組
Get-Module -ListAvailable -Name PnP.PowerShell
```

### 清單已存在錯誤
**錯誤訊息**：`List already exists`

**解決方案**：
1. 檢查 SharePoint 網站中是否已有同名清單
2. 刪除現有清單或重新命名
3. 修改腳本中的清單名稱

## 💡 **最佳實踐**

### 建立前準備
1. **確認權限**：確保有適當的 SharePoint 權限
2. **備份現有數據**：如果網站已有數據，先備份
3. **測試環境**：先在測試環境中嘗試
4. **網路連線**：確保網路連線穩定

### 建立後驗證
1. **檢查清單**：確認所有清單都已建立
2. **驗證欄位**：檢查欄位類型和設定
3. **測試權限**：確認可以新增、編輯、刪除項目
4. **系統測試**：在維保管理系統中測試連線

### 維護建議
1. **定期備份**：定期備份 SharePoint 數據
2. **權限管理**：定期檢查和更新權限設定
3. **效能監控**：監控清單效能，避免項目過多
4. **版本控制**：啟用清單版本控制功能

## 🎯 **成功指標**

### 建立成功的標誌
- ✅ 所有 4 個清單都已建立
- ✅ 每個清單的欄位都正確設定
- ✅ 選擇欄位的選項都已配置
- ✅ 系統連線測試成功
- ✅ 可以新增和編輯維修記錄

### 下一步行動
1. **測試連線**：在系統設定中測試 SharePoint 連線
2. **遷移數據**：如有現有數據，執行數據遷移
3. **培訓用戶**：培訓團隊成員使用新功能
4. **建立備份策略**：設定定期備份計劃

---

**🎉 現在您可以輕鬆建立 SharePoint 清單，享受企業級的維保管理功能！**

**💡 提示**：建議先嘗試自動建立，如果失敗再使用 PowerShell 腳本或手動建立方式。
