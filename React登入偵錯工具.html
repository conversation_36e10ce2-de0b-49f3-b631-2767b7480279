<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React 登入偵錯工具 - IACT MIO維保管理系統</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #d9d9d9;
        }
        
        .status-ok { border-left-color: #52c41a; }
        .status-error { border-left-color: #f5222d; }
        .status-warning { border-left-color: #fa8c16; }
        
        .status-text {
            font-weight: bold;
        }
        .status-text.ok { color: #52c41a; }
        .status-text.error { color: #f5222d; }
        .status-text.warning { color: #fa8c16; }
        
        .btn {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover { transform: translateY(-2px); }
        .btn-success { background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%); }
        .btn-danger { background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%); }
        .btn-warning { background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%); }
        
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .test-section {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .service-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .service-running { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .service-stopped { background: #fff2f0; color: #f5222d; border: 1px solid #ffccc7; }
        .service-unknown { background: #f0f0f0; color: #666; border: 1px solid #d9d9d9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚛️ React 登入偵錯工具</h1>
            <p>專門診斷 React/TypeScript 維修管理系統的登入問題</p>
        </div>
        
        <div class="content">
            <div class="debug-section">
                <h3>🚀 服務狀態檢查</h3>
                <div class="status-grid">
                    <div class="status-item" id="frontendStatus">
                        <span>前端服務 (React)</span>
                        <span class="service-status service-unknown" id="frontendStatusText">檢查中...</span>
                    </div>
                    <div class="status-item" id="backendStatus">
                        <span>後端服務 (API)</span>
                        <span class="service-status service-unknown" id="backendStatusText">檢查中...</span>
                    </div>
                    <div class="status-item" id="databaseStatus">
                        <span>資料庫連接</span>
                        <span class="service-status service-unknown" id="databaseStatusText">檢查中...</span>
                    </div>
                    <div class="status-item" id="authStatus">
                        <span>認證服務</span>
                        <span class="service-status service-unknown" id="authStatusText">檢查中...</span>
                    </div>
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn" onclick="checkAllServices()">🔄 重新檢查所有服務</button>
                    <button class="btn btn-success" onclick="startServices()">▶️ 啟動服務</button>
                    <button class="btn btn-warning" onclick="restartServices()">🔄 重啟服務</button>
                </div>
            </div>
            
            <div class="debug-section">
                <h3>🔍 React 應用檢查</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span>React 應用載入</span>
                        <span class="status-text" id="reactAppStatus">檢查中...</span>
                    </div>
                    <div class="status-item">
                        <span>Redux Store</span>
                        <span class="status-text" id="reduxStatus">檢查中...</span>
                    </div>
                    <div class="status-item">
                        <span>路由系統</span>
                        <span class="status-text" id="routerStatus">檢查中...</span>
                    </div>
                    <div class="status-item">
                        <span>Ant Design 組件</span>
                        <span class="status-text" id="antdStatus">檢查中...</span>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 API 連接測試</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <div class="form-group">
                            <label>API 基礎 URL</label>
                            <input type="text" id="apiBaseUrl" value="http://localhost:5000/api/v1" placeholder="API 基礎 URL">
                        </div>
                        <div class="form-group">
                            <label>測試端點</label>
                            <select id="testEndpoint">
                                <option value="/health">健康檢查 (/health)</option>
                                <option value="/auth/login">登入端點 (/auth/login)</option>
                                <option value="/auth/profile">用戶資料 (/auth/profile)</option>
                            </select>
                        </div>
                        <button class="btn" onclick="testApiConnection()">🔗 測試 API 連接</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label>測試帳號</label>
                            <input type="email" id="testEmail" value="<EMAIL>" placeholder="測試電子郵件">
                        </div>
                        <div class="form-group">
                            <label>測試密碼</label>
                            <input type="password" id="testPassword" value="admin123" placeholder="測試密碼">
                        </div>
                        <button class="btn btn-success" onclick="testLogin()">🔐 測試登入</button>
                    </div>
                </div>
            </div>
            
            <div class="debug-section">
                <h3>🛠️ 快速修復工具</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn" onclick="openReactApp()">🚀 開啟 React 應用</button>
                    <button class="btn" onclick="openApiDocs()">📚 開啟 API 文檔</button>
                    <button class="btn btn-warning" onclick="clearLocalStorage()">🗑️ 清除本地存儲</button>
                    <button class="btn btn-warning" onclick="resetReduxState()">🔄 重置 Redux 狀態</button>
                    <button class="btn btn-danger" onclick="emergencyReset()">🆘 緊急重置</button>
                    <button class="btn" onclick="downloadLogs()">📥 下載偵錯日誌</button>
                </div>
            </div>
            
            <div class="debug-section">
                <h3>📊 即時偵錯日誌</h3>
                <div class="log-area" id="debugLog">
                    正在初始化 React 登入偵錯工具...\n
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn" onclick="clearLog()">清除日誌</button>
                    <button class="btn" onclick="exportLog()">匯出日誌</button>
                    <button class="btn btn-success" onclick="runFullDiagnostic()">🔍 執行完整診斷</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logArea;
        
        function log(message, type = 'info') {
            if (!logArea) {
                logArea = document.getElementById('debugLog');
            }
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `status-text ${status}`;
            }
        }
        
        function setServiceStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `service-status service-${status}`;
                element.textContent = status === 'running' ? '運行中' : 
                                   status === 'stopped' ? '已停止' : '未知';
            }
        }
        
        async function checkService(url, name) {
            try {
                const response = await fetch(url, { 
                    method: 'GET',
                    mode: 'cors',
                    timeout: 5000 
                });
                
                if (response.ok) {
                    log(`${name} 服務正常運行`, 'success');
                    return 'running';
                } else {
                    log(`${name} 服務回應異常: ${response.status}`, 'warning');
                    return 'stopped';
                }
            } catch (error) {
                log(`${name} 服務連接失敗: ${error.message}`, 'error');
                return 'stopped';
            }
        }
        
        async function checkAllServices() {
            log('開始檢查所有服務狀態...');
            
            // 檢查前端服務
            const frontendStatus = await checkService('http://localhost:3000', '前端');
            setServiceStatus('frontendStatusText', frontendStatus);
            
            // 檢查後端服務
            const backendStatus = await checkService('http://localhost:5000/api/v1/health', '後端');
            setServiceStatus('backendStatusText', backendStatus);
            
            // 檢查認證服務
            const authStatus = await checkService('http://localhost:5000/api/v1/auth/profile', '認證');
            setServiceStatus('authStatusText', authStatus);
            
            // 模擬資料庫檢查
            setServiceStatus('databaseStatusText', backendStatus);
            
            log('服務狀態檢查完成');
        }
        
        async function testApiConnection() {
            const baseUrl = document.getElementById('apiBaseUrl').value;
            const endpoint = document.getElementById('testEndpoint').value;
            const fullUrl = baseUrl + endpoint;
            
            log(`測試 API 連接: ${fullUrl}`);
            
            try {
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    log(`API 連接成功: ${response.status}`, 'success');
                    log(`回應內容: ${data.substring(0, 200)}...`);
                } else {
                    log(`API 連接失敗: ${response.status}`, 'error');
                    log(`錯誤內容: ${data}`);
                }
            } catch (error) {
                log(`API 連接錯誤: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            const baseUrl = document.getElementById('apiBaseUrl').value;
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log(`測試登入: ${email}`);
            
            try {
                const response = await fetch(`${baseUrl}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`登入測試成功! 用戶: ${data.data.user.name}`, 'success');
                    log(`Token: ${data.data.token.substring(0, 20)}...`);
                } else {
                    log(`登入測試失敗: ${data.error || '未知錯誤'}`, 'error');
                }
            } catch (error) {
                log(`登入測試錯誤: ${error.message}`, 'error');
                log('可能原因: 後端服務未啟動或網路連接問題', 'warning');
            }
        }
        
        function openReactApp() {
            log('開啟 React 應用...');
            window.open('http://localhost:3000', '_blank');
        }
        
        function openApiDocs() {
            log('開啟 API 文檔...');
            window.open('http://localhost:5000/api-docs', '_blank');
        }
        
        function startServices() {
            log('啟動服務指令...');
            alert('請在終端中執行以下命令:\n\n後端: cd backend && npm run dev\n前端: cd frontend && npm run dev');
        }
        
        function restartServices() {
            log('重啟服務指令...');
            alert('請按 Ctrl+C 停止現有服務，然後重新執行:\n\n後端: cd backend && npm run dev\n前端: cd frontend && npm run dev');
        }
        
        function clearLocalStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                log('本地存儲已清除', 'success');
                alert('✅ 本地存儲已清除！\n請重新載入 React 應用。');
            } catch (error) {
                log(`清除本地存儲失敗: ${error.message}`, 'error');
            }
        }
        
        function resetReduxState() {
            log('Redux 狀態重置指令...');
            alert('請在 React 應用的瀏覽器控制台中執行:\n\nstore.dispatch({type: "auth/clearAuth"})');
        }
        
        function emergencyReset() {
            if (confirm('⚠️ 這將清除所有本地數據並重置系統。\n確定要繼續嗎？')) {
                clearLocalStorage();
                log('執行緊急重置...', 'warning');
                alert('🆘 緊急重置完成！\n\n請執行以下步驟:\n1. 重新啟動前後端服務\n2. 清除瀏覽器快取\n3. 重新載入應用');
            }
        }
        
        function runFullDiagnostic() {
            log('=== 開始完整診斷 ===');
            document.getElementById('debugLog').textContent = '';
            
            log('🔍 React 登入系統完整診斷');
            log('');
            
            // 檢查瀏覽器環境
            log('📱 瀏覽器環境檢查:');
            log(`  瀏覽器: ${navigator.userAgent.split(' ').pop()}`);
            log(`  LocalStorage: ${typeof Storage !== 'undefined' ? '✅ 支援' : '❌ 不支援'}`);
            log(`  Fetch API: ${typeof fetch !== 'undefined' ? '✅ 支援' : '❌ 不支援'}`);
            log('');
            
            // 檢查服務
            log('🚀 開始檢查服務狀態...');
            checkAllServices();
            
            setTimeout(() => {
                log('');
                log('📋 診斷建議:');
                log('1. 確保 Node.js 版本 >= 18');
                log('2. 檢查 npm 依賴是否正確安裝');
                log('3. 確認前後端服務都在運行');
                log('4. 檢查防火牆和端口設定');
                log('5. 查看瀏覽器控制台的錯誤訊息');
                log('');
                log('=== 診斷完成 ===');
            }, 3000);
        }
        
        function clearLog() {
            document.getElementById('debugLog').textContent = '';
            log('日誌已清除');
        }
        
        function exportLog() {
            const logContent = document.getElementById('debugLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `React登入偵錯日誌_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('偵錯日誌已匯出', 'success');
        }
        
        function downloadLogs() {
            log('準備下載完整偵錯日誌...');
            const diagnosticInfo = {
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                url: window.location.href,
                localStorage: Object.keys(localStorage),
                sessionStorage: Object.keys(sessionStorage),
                log: document.getElementById('debugLog').textContent
            };
            
            const blob = new Blob([JSON.stringify(diagnosticInfo, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `React系統診斷報告_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
            log('完整診斷報告已下載', 'success');
        }
        
        // 頁面載入時自動執行診斷
        window.addEventListener('load', function() {
            setTimeout(runFullDiagnostic, 500);
        });
    </script>
</body>
</html>
