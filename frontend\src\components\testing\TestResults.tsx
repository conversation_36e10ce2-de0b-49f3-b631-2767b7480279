import React from 'react';
import { Card, Typography, Table, Tag, Progress } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface TestSuite {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  progress: number;
  testCount: number;
  passedCount: number;
  failedCount: number;
}

interface TestResultsProps {
  testSuites: TestSuite[];
}

const TestResults: React.FC<TestResultsProps> = ({ testSuites }) => {
  const columns = [
    {
      title: '測試套件',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TestSuite) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
        </div>
      ),
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', icon: <ClockCircleOutlined />, text: '待執行' },
          running: { color: 'processing', icon: <ClockCircleOutlined />, text: '執行中' },
          passed: { color: 'success', icon: <CheckCircleOutlined />, text: '通過' },
          failed: { color: 'error', icon: <CloseCircleOutlined />, text: '失敗' },
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '進度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: TestSuite) => {
        if (record.status === 'pending') return '-';
        if (record.status === 'running') {
          return <Progress percent={progress} size="small" />;
        }
        return <Progress percent={100} size="small" status={record.status === 'passed' ? 'success' : 'exception'} />;
      },
    },
    {
      title: '測試結果',
      key: 'results',
      render: (record: TestSuite) => {
        if (record.status === 'pending' || record.status === 'running') {
          return `${record.testCount} 個測試`;
        }
        return (
          <div>
            <span style={{ color: '#52c41a' }}>通過: {record.passedCount}</span>
            <span style={{ margin: '0 8px', color: '#d9d9d9' }}>|</span>
            <span style={{ color: '#f5222d' }}>失敗: {record.failedCount}</span>
            <span style={{ margin: '0 8px', color: '#d9d9d9' }}>|</span>
            <span>總計: {record.testCount}</span>
          </div>
        );
      },
    },
  ];

  const totalTests = testSuites.reduce((sum, suite) => sum + suite.testCount, 0);
  const totalPassed = testSuites.reduce((sum, suite) => sum + suite.passedCount, 0);
  const totalFailed = testSuites.reduce((sum, suite) => sum + suite.failedCount, 0);
  const completedSuites = testSuites.filter(s => s.status === 'passed' || s.status === 'failed').length;

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Title level={4}>測試總結</Title>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 16 }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>{completedSuites}/{testSuites.length}</div>
            <div style={{ color: '#666' }}>完成套件</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{totalPassed}</div>
            <div style={{ color: '#666' }}>通過測試</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>{totalFailed}</div>
            <div style={{ color: '#666' }}>失敗測試</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalTests}</div>
            <div style={{ color: '#666' }}>總測試數</div>
          </div>
        </div>
      </Card>

      <Card title="詳細測試結果">
        <Table
          columns={columns}
          dataSource={testSuites}
          rowKey="id"
          pagination={false}
          size="middle"
        />
      </Card>
    </div>
  );
};

export default TestResults;
