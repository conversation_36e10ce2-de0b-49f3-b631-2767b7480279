# API配置
VITE_API_BASE_URL=http://localhost:5000/api/v1
VITE_API_TIMEOUT=10000

# Azure AD配置
VITE_AZURE_CLIENT_ID=your-client-id
VITE_AZURE_TENANT_ID=your-tenant-id
VITE_AZURE_REDIRECT_URI=http://localhost:3000/auth/callback

# SharePoint配置
VITE_SHAREPOINT_SITE_URL=https://yourtenant.sharepoint.com/sites/repair-management

# 應用配置
VITE_APP_NAME=客退維修品記錄管理系統
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=專業的維修記錄管理解決方案

# 功能開關
VITE_ENABLE_SHAREPOINT=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=false

# 開發配置
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_DEBUG=true

# 上傳配置
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.zip

# 分頁配置
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# 主題配置
VITE_DEFAULT_THEME=light
VITE_PRIMARY_COLOR=#1890ff

# 地區配置
VITE_DEFAULT_LOCALE=zh-TW
VITE_TIMEZONE=Asia/Taipei
