import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Modal, 
  message, 
  Popconfirm,
  Select,
  Row,
  Col,
  Tooltip,
  Badge,
  Progress,
  DatePicker
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ToolOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { RepairRecord, RepairQueryParams } from '../../services/repairService';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 模擬數據
const mockRepairRecords: RepairRecord[] = [
  {
    id: 1,
    recordNumber: 'R2024-001',
    customerId: 1,
    customer: { id: 1, name: '張先生', email: '<EMAIL>', phone: '0912-345-678' },
    productId: 1,
    product: { id: 1, name: 'iPhone 14', model: 'A2882', brand: 'Apple' },
    issueDescription: '螢幕破裂，觸控異常',
    symptoms: ['螢幕破裂', '觸控失靈', '顯示異常'],
    priority: 'HIGH',
    status: 'REPAIRING',
    assignedTechnicianId: 3,
    assignedTechnician: { id: 3, name: '維修技師', email: '<EMAIL>' },
    estimatedCost: 3500,
    actualCost: 3200,
    estimatedCompletionDate: '2024-01-20T00:00:00Z',
    warrantyStatus: 'OUT_OF_WARRANTY',
    repairNotes: '已更換螢幕總成，測試正常',
    usedParts: [],
    statusHistory: [],
    attachments: [],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-18T14:20:00Z',
    createdBy: 2,
    updatedBy: 3,
  },
  {
    id: 2,
    recordNumber: 'R2024-002',
    customerId: 2,
    customer: { id: 2, name: '李小姐', email: '<EMAIL>', phone: '0923-456-789' },
    productId: 2,
    product: { id: 2, name: 'MacBook Pro', model: 'MBP-M2', brand: 'Apple' },
    issueDescription: '電池續航力下降，充電異常',
    symptoms: ['電池續航短', '充電慢', '發熱'],
    priority: 'MEDIUM',
    status: 'PENDING_PARTS',
    assignedTechnicianId: 3,
    assignedTechnician: { id: 3, name: '維修技師', email: '<EMAIL>' },
    estimatedCost: 4500,
    estimatedCompletionDate: '2024-01-25T00:00:00Z',
    warrantyStatus: 'IN_WARRANTY',
    repairNotes: '需要更換電池，等待零件到貨',
    usedParts: [],
    statusHistory: [],
    attachments: [],
    createdAt: '2024-01-16T09:15:00Z',
    updatedAt: '2024-01-17T11:30:00Z',
    createdBy: 2,
    updatedBy: 3,
  },
  {
    id: 3,
    recordNumber: 'R2024-003',
    customerId: 3,
    customer: { id: 3, name: '王先生', email: '<EMAIL>', phone: '0934-567-890' },
    productId: 3,
    product: { id: 3, name: 'iPad Air', model: 'IPAD-AIR-5', brand: 'Apple' },
    issueDescription: '無法開機，疑似主機板問題',
    symptoms: ['無法開機', '無反應', '充電無效'],
    priority: 'URGENT',
    status: 'COMPLETED',
    assignedTechnicianId: 3,
    assignedTechnician: { id: 3, name: '維修技師', email: '<EMAIL>' },
    estimatedCost: 8000,
    actualCost: 7500,
    estimatedCompletionDate: '2024-01-22T00:00:00Z',
    actualCompletionDate: '2024-01-21T16:00:00Z',
    warrantyStatus: 'EXTENDED_WARRANTY',
    repairNotes: '更換主機板，已完成測試',
    usedParts: [],
    statusHistory: [],
    attachments: [],
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-21T16:00:00Z',
    createdBy: 2,
    updatedBy: 3,
  },
];

interface RepairListProps {
  onEdit?: (repair: RepairRecord) => void;
  onAdd?: () => void;
  onView?: (repair: RepairRecord) => void;
  onStatusUpdate?: (repair: RepairRecord) => void;
}

const RepairList: React.FC<RepairListProps> = ({ onEdit, onAdd, onView, onStatusUpdate }) => {
  const [repairs, setRepairs] = useState<RepairRecord[]>(mockRepairRecords);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [priorityFilter, setPriorityFilter] = useState<string | undefined>(undefined);
  const [technicianFilter, setTechnicianFilter] = useState<number | undefined>(undefined);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockRepairRecords.length,
  });

  const fetchRepairs = async (params?: RepairQueryParams) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = [...mockRepairRecords];
      
      if (params?.search) {
        filteredData = filteredData.filter(repair =>
          repair.recordNumber.toLowerCase().includes(params.search!.toLowerCase()) ||
          repair.customer?.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          repair.product?.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          repair.issueDescription.toLowerCase().includes(params.search!.toLowerCase())
        );
      }
      
      if (params?.status) {
        filteredData = filteredData.filter(repair => repair.status === params.status);
      }
      
      if (params?.priority) {
        filteredData = filteredData.filter(repair => repair.priority === params.priority);
      }
      
      if (params?.assignedTechnicianId) {
        filteredData = filteredData.filter(repair => repair.assignedTechnicianId === params.assignedTechnicianId);
      }
      
      setRepairs(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length,
      }));
    } catch (error) {
      message.error('載入維修記錄失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRepairs({
      search: searchText,
      status: statusFilter,
      priority: priorityFilter,
      assignedTechnicianId: technicianFilter,
      dateFrom: dateRange?.[0]?.format('YYYY-MM-DD'),
      dateTo: dateRange?.[1]?.format('YYYY-MM-DD'),
      page: pagination.current,
      limit: pagination.pageSize,
    });
  }, [searchText, statusFilter, priorityFilter, technicianFilter, dateRange, pagination.current, pagination.pageSize]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('維修記錄刪除成功');
      fetchRepairs();
    } catch (error) {
      message.error('刪除維修記錄失敗');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING_INSPECTION': 'orange',
      'INSPECTING': 'blue',
      'PENDING_REPAIR': 'purple',
      'REPAIRING': 'cyan',
      'PENDING_PARTS': 'gold',
      'COMPLETED': 'green',
      'DELIVERED': 'lime',
      'CANCELLED': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'PENDING_INSPECTION': '待檢測',
      'INSPECTING': '檢測中',
      'PENDING_REPAIR': '待維修',
      'REPAIRING': '維修中',
      'PENDING_PARTS': '待零件',
      'COMPLETED': '已完成',
      'DELIVERED': '已交付',
      'CANCELLED': '已取消',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'LOW': 'green',
      'MEDIUM': 'blue',
      'HIGH': 'orange',
      'URGENT': 'red',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const getPriorityText = (priority: string) => {
    const texts = {
      'LOW': '低',
      'MEDIUM': '中',
      'HIGH': '高',
      'URGENT': '緊急',
    };
    return texts[priority as keyof typeof texts] || priority;
  };

  const getWarrantyColor = (warranty: string) => {
    const colors = {
      'IN_WARRANTY': 'green',
      'OUT_OF_WARRANTY': 'red',
      'EXTENDED_WARRANTY': 'blue',
    };
    return colors[warranty as keyof typeof colors] || 'default';
  };

  const getWarrantyText = (warranty: string) => {
    const texts = {
      'IN_WARRANTY': '保固內',
      'OUT_OF_WARRANTY': '保固外',
      'EXTENDED_WARRANTY': '延長保固',
    };
    return texts[warranty as keyof typeof texts] || warranty;
  };

  const columns: ColumnsType<RepairRecord> = [
    {
      title: '維修編號',
      dataIndex: 'recordNumber',
      key: 'recordNumber',
      width: 120,
      fixed: 'left',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
            {text}
          </div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            {dayjs(record.createdAt).format('MM/DD')}
          </div>
        </div>
      ),
    },
    {
      title: '客戶資訊',
      key: 'customer',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>
            <UserOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {record.customer?.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.customer?.phone}
          </div>
        </div>
      ),
    },
    {
      title: '產品資訊',
      key: 'product',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>
            {record.product?.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.product?.brand} {record.product?.model}
          </div>
        </div>
      ),
    },
    {
      title: '問題描述',
      dataIndex: 'issueDescription',
      key: 'issueDescription',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          <span>{description}</span>
        </Tooltip>
      ),
    },
    {
      title: '優先級',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '指派技師',
      key: 'technician',
      width: 120,
      render: (_, record) => (
        record.assignedTechnician ? (
          <div>
            <ToolOutlined style={{ marginRight: 4, color: '#52c41a' }} />
            <span style={{ fontSize: '12px' }}>
              {record.assignedTechnician.name}
            </span>
          </div>
        ) : (
          <Text type="secondary">未指派</Text>
        )
      ),
    },
    {
      title: '保固狀態',
      dataIndex: 'warrantyStatus',
      key: 'warrantyStatus',
      width: 100,
      render: (warranty) => (
        <Tag color={getWarrantyColor(warranty)}>
          {getWarrantyText(warranty)}
        </Tag>
      ),
    },
    {
      title: '費用',
      key: 'cost',
      width: 100,
      render: (_, record) => (
        <div>
          <DollarOutlined style={{ marginRight: 4, color: '#52c41a' }} />
          <div style={{ fontSize: '12px' }}>
            {record.actualCost ? (
              <span style={{ color: '#52c41a' }}>
                實際: ${record.actualCost.toLocaleString()}
              </span>
            ) : record.estimatedCost ? (
              <span style={{ color: '#faad14' }}>
                預估: ${record.estimatedCost.toLocaleString()}
              </span>
            ) : (
              <span style={{ color: '#999' }}>未估價</span>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '預計完成',
      dataIndex: 'estimatedCompletionDate',
      key: 'estimatedCompletionDate',
      width: 120,
      render: (date, record) => (
        <div>
          <CalendarOutlined style={{ marginRight: 4, color: '#1890ff' }} />
          <div style={{ fontSize: '12px' }}>
            {date ? dayjs(date).format('MM/DD') : '-'}
          </div>
          {record.actualCompletionDate && (
            <div style={{ fontSize: '10px', color: '#52c41a' }}>
              實際: {dayjs(record.actualCompletionDate).format('MM/DD')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="查看詳情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => onView?.(record)}
            />
          </Tooltip>
          <Tooltip title="編輯">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Tooltip title="更新狀態">
            <Button
              type="text"
              icon={<HistoryOutlined />}
              onClick={() => onStatusUpdate?.(record)}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個維修記錄嗎？"
            description="刪除後將無法恢復，請謹慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              維修記錄管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Search
                placeholder="搜尋維修編號、客戶、產品或問題描述"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="狀態篩選"
                allowClear
                style={{ width: 120 }}
                onChange={setStatusFilter}
              >
                <Option value="PENDING_INSPECTION">待檢測</Option>
                <Option value="INSPECTING">檢測中</Option>
                <Option value="PENDING_REPAIR">待維修</Option>
                <Option value="REPAIRING">維修中</Option>
                <Option value="PENDING_PARTS">待零件</Option>
                <Option value="COMPLETED">已完成</Option>
                <Option value="DELIVERED">已交付</Option>
                <Option value="CANCELLED">已取消</Option>
              </Select>
              <Select
                placeholder="優先級篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setPriorityFilter}
              >
                <Option value="LOW">低</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="HIGH">高</Option>
                <Option value="URGENT">緊急</Option>
              </Select>
              <RangePicker
                placeholder={['開始日期', '結束日期']}
                style={{ width: 240 }}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchRepairs()}
                loading={loading}
              >
                重新整理
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('匯出功能開發中')}
              >
                匯出
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                新增維修記錄
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Table
        columns={columns}
        dataSource={repairs}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        scroll={{ x: 1600 }}
        size="small"
      />
    </Card>
  );
};

export default RepairList;
