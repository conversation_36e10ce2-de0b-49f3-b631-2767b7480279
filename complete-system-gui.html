<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IACT MIO維保管理系統 - 完整版GUI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 400px;
        }
        
        .main-layout {
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            background: #fff;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #f0f0f0;
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 24px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background-color: #f0f0f0;
        }
        
        .menu-item.active {
            background-color: #e6f7ff;
            color: #1890ff;
            border-right: 3px solid #1890ff;
        }
        
        .menu-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f5f5f5;
        }
        
        .header {
            height: 64px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow: auto;
        }
        
        .page-content {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .hidden {
            display: none;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        
        .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .table th {
            background: #fafafa;
            font-weight: 600;
        }
        
        .table tr:hover {
            background: #f5f5f5;
        }
        
        .tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag-green {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .tag-blue {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .tag-orange {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .tag-red {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .tag-gray {
            background: #f5f5f5;
            color: #8c8c8c;
            border: 1px solid #d9d9d9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-card[onclick] {
            cursor: pointer;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .toolbar {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .search-input {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            transition: border-color 0.3s;
            cursor: pointer;
        }

        .filter-select:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 模態框樣式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-display {
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            color: #495057;
            min-height: 20px;
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 0 24px 24px;
            border-top: 1px solid #f0f0f0;
            margin-top: 16px;
            padding-top: 20px;
        }

        .tab-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            color: #666;
        }

        .tab-item:hover {
            color: #1890ff;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }

        .tab-content {
            display: block;
        }

        .tab-content.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登入頁面 -->
        <div id="loginView" class="login-container">
            <div class="login-box">
                <div style="text-align: center; margin-bottom: 32px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">🔧</div>
                    <h2 style="margin: 0; color: #262626;">系統登入</h2>
                    <p style="color: #666; margin: 8px 0 0 0;">IACT MIO維保管理系統</p>
                </div>

                <form id="loginForm">
                    <div class="form-group">
                        <label>用戶名</label>
                        <input type="text" id="email" value="admin" required placeholder="請輸入用戶名">
                    </div>
                    <div class="form-group">
                        <label>密碼</label>
                        <input type="password" id="password" value="admin123" required placeholder="請輸入密碼">
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px;">
                        登入系統
                    </button>
                </form>

                <div style="margin-top: 24px; padding: 16px; background: #f6f6f6; border-radius: 6px;">
                    <h4 style="margin: 0 0 12px 0; font-size: 14px;">測試帳號：</h4>
                    <div style="font-size: 12px; line-height: 1.6;">
                        <div><strong>管理員:</strong> admin / admin123</div>
                        <div><strong>客服:</strong> service / service123</div>
                        <div><strong>技師:</strong> tech / tech123</div>
                        <div><strong>查詢:</strong> viewer / viewer123</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主應用界面 -->
        <div id="mainView" class="main-layout hidden">
            <div class="sidebar">
                <div class="sidebar-header">
                    🔧 維修管理
                </div>
                <div class="sidebar-menu">
                    <div class="menu-item active" data-page="dashboard">
                        <span class="menu-icon">📊</span>
                        儀表板
                    </div>
                    <div class="menu-item" data-page="repairs">
                        <span class="menu-icon">🛠️</span>
                        維修記錄
                    </div>
                    <div class="menu-item" data-page="customers">
                        <span class="menu-icon">👥</span>
                        客戶管理
                    </div>
                    <div class="menu-item" data-page="products">
                        <span class="menu-icon">📦</span>
                        產品管理
                    </div>
                    <div class="menu-item" data-page="parts">
                        <span class="menu-icon">🔧</span>
                        零件管理
                    </div>
                    <div class="menu-item" data-page="reports">
                        <span class="menu-icon">📈</span>
                        統計報表
                    </div>
                    <div class="menu-item" data-page="settings">
                        <span class="menu-icon">⚙️</span>
                        系統設定
                    </div>
                    <div class="menu-item" data-page="sharepoint">
                        <span class="menu-icon">☁️</span>
                        SharePoint
                    </div>
                    <div class="menu-item" data-page="testing">
                        <span class="menu-icon">🧪</span>
                        系統測試
                    </div>
                </div>
            </div>
            <div class="main-content">
                <div class="header">
                    <h3 style="margin: 0;">IACT MIO維保管理系統</h3>
                    <div class="user-info" onclick="logout()">
                        <div class="user-avatar" id="userAvatar">A</div>
                        <div>
                            <div style="font-size: 14px; font-weight: 500;" id="userName">系統管理員</div>
                            <div style="font-size: 12px; color: #666;" id="userRole">ADMIN</div>
                        </div>
                    </div>
                </div>
                <div class="content">
                    <!-- 儀表板頁面 -->
                    <div id="dashboardPage" class="page-content">
                        <div class="page-header">
                            <h2 class="page-title">📊 系統儀表板</h2>
                            <button class="btn btn-primary" onclick="refreshDashboard()">重新整理</button>
                        </div>

                        <!-- 核心統計卡片 -->
                        <div class="stats-grid">
                            <div class="stat-card" onclick="showPage('repairs')" style="cursor: pointer;">
                                <div class="stat-number">1,248</div>
                                <div class="stat-label">總維修記錄</div>
                                <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">+12% 本月</div>
                            </div>
                            <div class="stat-card" onclick="showPage('repairs')" style="cursor: pointer;">
                                <div class="stat-number">89</div>
                                <div class="stat-label">進行中維修</div>
                                <div style="font-size: 12px; color: #fa8c16; margin-top: 4px;">需要關注</div>
                            </div>
                            <div class="stat-card" onclick="showPage('customers')" style="cursor: pointer;">
                                <div class="stat-number">456</div>
                                <div class="stat-label">客戶總數</div>
                                <div style="font-size: 12px; color: #1890ff; margin-top: 4px;">+5 新客戶</div>
                            </div>
                            <div class="stat-card" onclick="showPage('parts')" style="cursor: pointer;">
                                <div class="stat-number">1,567</div>
                                <div class="stat-label">零件總數</div>
                                <div style="font-size: 12px; color: #f5222d; margin-top: 4px;">23 缺貨</div>
                            </div>
                        </div>

                        <!-- 快速操作區 -->
                        <div style="background: white; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 24px;">
                            <h3>⚡ 快速操作</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-top: 16px;">
                                <button class="btn btn-primary" onclick="showPage('repairs'); setTimeout(showAddRepairForm, 100);" style="padding: 16px; height: auto;">
                                    <div style="font-size: 20px; margin-bottom: 8px;">🛠️</div>
                                    <div>新增維修記錄</div>
                                </button>
                                <button class="btn" onclick="showPage('customers')" style="padding: 16px; height: auto;">
                                    <div style="font-size: 20px; margin-bottom: 8px;">👥</div>
                                    <div>管理客戶</div>
                                </button>
                                <button class="btn" onclick="showPage('parts')" style="padding: 16px; height: auto;">
                                    <div style="font-size: 20px; margin-bottom: 8px;">🔧</div>
                                    <div>查看庫存</div>
                                </button>
                                <button class="btn" onclick="showPage('reports')" style="padding: 16px; height: auto;">
                                    <div style="font-size: 20px; margin-bottom: 8px;">📈</div>
                                    <div>查看報表</div>
                                </button>
                            </div>
                        </div>

                        <!-- 最近活動 -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                            <div style="background: white; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h3>📋 最近維修記錄</h3>
                                <div style="margin-top: 16px;">
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('repairs')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">R2024-001 - iPhone 14維修</div>
                                                <div style="font-size: 12px; color: #666;">張先生 • 螢幕破裂</div>
                                            </div>
                                            <span class="tag tag-blue">維修中</span>
                                        </div>
                                    </div>
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('repairs')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">R2024-002 - MacBook Pro維修</div>
                                                <div style="font-size: 12px; color: #666;">李小姐 • 電池問題</div>
                                            </div>
                                            <span class="tag tag-orange">待零件</span>
                                        </div>
                                    </div>
                                    <div style="padding: 12px 0; cursor: pointer;" onclick="showPage('repairs')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">R2024-003 - iPad Air維修</div>
                                                <div style="font-size: 12px; color: #666;">王先生 • 主機板問題</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 16px;">
                                    <button class="btn" onclick="showPage('repairs')">查看所有維修記錄</button>
                                </div>
                            </div>

                            <div style="background: white; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h3>🎯 系統功能模組</h3>
                                <div style="margin-top: 16px;">
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('repairs')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">🛠️ 維修記錄管理</div>
                                                <div style="font-size: 12px; color: #666;">完整的維修流程管理</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('customers')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">👥 客戶管理</div>
                                                <div style="font-size: 12px; color: #666;">客戶資料維護和查詢</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('products')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">📦 產品管理</div>
                                                <div style="font-size: 12px; color: #666;">產品分類和規格管理</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                    <div style="border-bottom: 1px solid #f0f0f0; padding: 12px 0; cursor: pointer;" onclick="showPage('parts')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">🔧 零件管理</div>
                                                <div style="font-size: 12px; color: #666;">庫存監控和警報系統</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                    <div style="padding: 12px 0; cursor: pointer;" onclick="showPage('reports')">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: bold;">📈 統計報表</div>
                                                <div style="font-size: 12px; color: #666;">數據分析和報表生成</div>
                                            </div>
                                            <span class="tag tag-green">已完成</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 維修記錄頁面 -->
                    <div id="repairsPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">🛠️ 維修記錄管理</h2>
                            <button class="btn btn-primary" onclick="showAddRepairForm()">新增維修記錄</button>
                        </div>
                        <div class="toolbar">
                            <input type="text" id="repairSearchInput" class="search-input" placeholder="搜尋維修編號、客戶、產品序號或產品..." onkeyup="searchRepairs()">
                            <select id="testResultFilter" class="filter-select" onchange="filterRepairs()">
                                <option value="">所有測試結果</option>
                                <option value="pending">待測試</option>
                                <option value="testing">測試中</option>
                                <option value="pass">Pass</option>
                                <option value="fail">Fail</option>
                            </select>

                            <select id="serviceStatusFilter" class="filter-select" onchange="filterRepairs()">
                                <option value="">所有維保狀態</option>
                                <option value="complaint">客訴</option>
                                <option value="maintenance">維保</option>
                            </select>
                            <button class="btn" onclick="clearFilters()">清除篩選</button>
                        </div>

                        <!-- 篩選結果顯示 -->
                        <div id="filterStats" style="margin-bottom: 16px; color: #666; font-size: 14px;"></div>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>維修編號</th>
                                    <th>客戶</th>
                                    <th>產品序號</th>
                                    <th>產品</th>
                                    <th>問題描述</th>
                                    <th>維保狀態</th>
                                    <th>維修記錄</th>
                                    <th>維保測試</th>
                                    <th>維保員</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="repairTableBody">

                            </tbody>
                        </table>

                        <!-- 新增維修記錄表單 -->
                        <div id="addRepairForm" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>🛠️ 新增維修記錄</h3>
                                    <button class="btn" onclick="hideAddRepairForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="repairForm" onsubmit="submitRepairForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>客戶姓名 *</label>
                                            <input type="text" id="customerName" required placeholder="請輸入客戶姓名">
                                        </div>
                                        <div class="form-group">
                                            <label>聯絡電話</label>
                                            <input type="tel" id="customerPhone" placeholder="請輸入聯絡電話">
                                        </div>
                                        <div class="form-group">
                                            <label>產品序號 *</label>
                                            <input type="text" id="productSerial" required placeholder="例如：SN240120001" style="text-transform: uppercase;">
                                        </div>
                                        <div class="form-group">
                                            <label>產品名稱 *</label>
                                            <input type="text" id="productName" required placeholder="例如：iPhone 14">
                                        </div>
                                        <div class="form-group">
                                            <label>產品型號</label>
                                            <input type="text" id="productModel" placeholder="例如：Apple A2882">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>問題描述 *</label>
                                            <textarea id="issueDescription" required placeholder="請詳細描述產品問題" rows="3"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>維保狀態 *</label>
                                            <select id="serviceStatus" required onchange="updateRepairRecordOptions()">
                                                <option value="">請選擇維保狀態</option>
                                                <option value="complaint">客訴</option>
                                                <option value="maintenance">維保</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維修記錄 *</label>
                                            <select id="repairRecord" required>
                                                <option value="">請先選擇維保狀態</option>
                                                <option value="adjustment">調整</option>
                                                <option value="motor_replacement">換馬達</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維保測試</label>
                                            <select id="testResult">
                                                <option value="pending">待測試</option>
                                                <option value="testing">測試中</option>
                                                <option value="pass">Pass</option>
                                                <option value="fail">Fail</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維保員</label>
                                            <select id="technician">
                                                <option value="">請選擇維保員</option>
                                                <option value="維保員A">維保員A</option>
                                                <option value="維保員B">維保員B</option>
                                                <option value="維保員C">維保員C</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>預計完成日期</label>
                                            <input type="date" id="expectedDate">
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideAddRepairForm()">取消</button>
                                        <button type="button" class="btn" onclick="clearLastInputData()" style="background: #f0f0f0; color: #666;">清除記憶</button>
                                        <button type="submit" class="btn btn-primary">建立維修記錄</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 查看維修記錄模態框 -->
                        <div id="viewRepairModal" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>🔍 查看維修記錄</h3>
                                    <button class="btn" onclick="hideViewRepairModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <div class="modal-body">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>維修編號</label>
                                            <div id="viewRepairId" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>建立日期</label>
                                            <div id="viewRepairDate" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>客戶姓名</label>
                                            <div id="viewCustomerName" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>聯絡電話</label>
                                            <div id="viewCustomerPhone" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>產品序號</label>
                                            <div id="viewProductSerial" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>產品名稱</label>
                                            <div id="viewProductName" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>產品型號</label>
                                            <div id="viewProductModel" class="form-display"></div>
                                        </div>
                                        <div class="form-group full-width">
                                            <label>問題描述</label>
                                            <div id="viewIssueDescription" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>維保狀態</label>
                                            <div id="viewServiceStatus" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>維修記錄</label>
                                            <div id="viewRepairRecord" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>維保測試</label>
                                            <div id="viewTestResult" class="form-display"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>維保員</label>
                                            <div id="viewTechnician" class="form-display"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="button" class="btn" onclick="hideViewRepairModal()">關閉</button>
                                    <button type="button" class="btn btn-primary" onclick="editFromView()">編輯</button>
                                </div>
                            </div>
                        </div>
                        <!-- 編輯維修記錄模態框 -->
                        <div id="editRepairModal" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>✏️ 編輯維修記錄</h3>
                                    <button class="btn" onclick="hideEditRepairModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="editRepairForm" onsubmit="submitEditRepairForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>維修編號</label>
                                            <input type="text" id="editRepairId" readonly style="background: #f5f5f5;">
                                        </div>
                                        <div class="form-group">
                                            <label>建立日期</label>
                                            <input type="text" id="editRepairDate" readonly style="background: #f5f5f5;">
                                        </div>
                                        <div class="form-group">
                                            <label>客戶姓名 *</label>
                                            <input type="text" id="editCustomerName" required placeholder="請輸入客戶姓名">
                                        </div>
                                        <div class="form-group">
                                            <label>聯絡電話 *</label>
                                            <input type="tel" id="editCustomerPhone" required placeholder="請輸入聯絡電話">
                                        </div>
                                        <div class="form-group">
                                            <label>產品序號 *</label>
                                            <input type="text" id="editProductSerial" required placeholder="例如：SN240120001" style="text-transform: uppercase;">
                                        </div>
                                        <div class="form-group">
                                            <label>產品名稱 *</label>
                                            <input type="text" id="editProductName" required placeholder="例如：iPhone 14">
                                        </div>
                                        <div class="form-group">
                                            <label>產品型號</label>
                                            <input type="text" id="editProductModel" placeholder="例如：Apple A2882">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>問題描述 *</label>
                                            <textarea id="editIssueDescription" required placeholder="請詳細描述產品問題" rows="3"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>維保狀態 *</label>
                                            <select id="editServiceStatus" required onchange="updateEditRepairRecordOptions()">
                                                <option value="">請選擇維保狀態</option>
                                                <option value="complaint">客訴</option>
                                                <option value="maintenance">維保</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維修記錄 *</label>
                                            <select id="editRepairRecord" required>
                                                <option value="">請先選擇維保狀態</option>
                                                <option value="adjustment">調整</option>
                                                <option value="motor_replacement">換馬達</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維保測試</label>
                                            <select id="editTestResult">
                                                <option value="pending">待測試</option>
                                                <option value="testing">測試中</option>
                                                <option value="pass">Pass</option>
                                                <option value="fail">Fail</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>維保員</label>
                                            <select id="editTechnician">
                                                <option value="">請選擇維保員</option>
                                                <option value="維保員A">維保員A</option>
                                                <option value="維保員B">維保員B</option>
                                                <option value="維保員C">維保員C</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideEditRepairModal()">取消</button>
                                        <button type="submit" class="btn btn-primary">儲存變更</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 其他頁面佔位符 -->
                    <div id="customersPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">👥 客戶管理</h2>
                            <button class="btn btn-primary" onclick="showAddCustomerForm()">新增客戶</button>
                        </div>

                        <div class="toolbar">
                            <input type="text" id="customerSearchInput" class="search-input" placeholder="搜尋客戶姓名、電話或電子郵件..." onkeyup="searchCustomers()">
                            <select id="customerStatusFilter" class="filter-select" onchange="filterCustomers()">
                                <option value="">所有狀態</option>
                                <option value="active">活躍</option>
                                <option value="inactive">非活躍</option>
                                <option value="vip">VIP客戶</option>
                            </select>
                            <button class="btn" onclick="clearCustomerFilters()">清除篩選</button>
                        </div>

                        <!-- 篩選結果顯示 -->
                        <div id="customerFilterStats" style="margin-bottom: 16px; color: #666; font-size: 14px;"></div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>客戶編號</th>
                                    <th>客戶姓名</th>
                                    <th>聯絡電話</th>
                                    <th>電子郵件</th>
                                    <th>地址</th>
                                    <th>狀態</th>
                                    <th>註冊日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="customerTableBody">
                            </tbody>
                        </table>

                        <!-- 新增客戶表單 -->
                        <div id="addCustomerForm" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>👥 新增客戶</h3>
                                    <button class="btn" onclick="hideAddCustomerForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="customerForm" onsubmit="submitCustomerForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>客戶姓名 *</label>
                                            <input type="text" id="customerNameInput" required placeholder="請輸入客戶姓名">
                                        </div>
                                        <div class="form-group">
                                            <label>聯絡電話 *</label>
                                            <input type="tel" id="customerPhoneInput" required placeholder="請輸入聯絡電話">
                                        </div>
                                        <div class="form-group">
                                            <label>電子郵件</label>
                                            <input type="email" id="customerEmailInput" placeholder="請輸入電子郵件">
                                        </div>
                                        <div class="form-group">
                                            <label>客戶狀態</label>
                                            <select id="customerStatusInput">
                                                <option value="active">活躍</option>
                                                <option value="inactive">非活躍</option>
                                                <option value="vip">VIP客戶</option>
                                            </select>
                                        </div>
                                        <div class="form-group full-width">
                                            <label>地址</label>
                                            <textarea id="customerAddressInput" placeholder="請輸入客戶地址" rows="2"></textarea>
                                        </div>
                                        <div class="form-group full-width">
                                            <label>備註</label>
                                            <textarea id="customerNotesInput" placeholder="客戶備註資訊" rows="2"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideAddCustomerForm()">取消</button>
                                        <button type="submit" class="btn btn-primary">建立客戶</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div id="productsPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">📦 產品管理</h2>
                            <button class="btn btn-primary" onclick="showAddProductForm()">新增產品</button>
                        </div>

                        <div class="toolbar">
                            <input type="text" id="productSearchInput" class="search-input" placeholder="搜尋產品名稱、品牌或型號..." onkeyup="searchProducts()">
                            <select id="productBrandFilter" class="filter-select" onchange="filterProducts()">
                                <option value="">所有品牌</option>
                                <option value="Apple">Apple</option>
                                <option value="Samsung">Samsung</option>
                                <option value="ASUS">ASUS</option>
                                <option value="Sony">Sony</option>
                                <option value="Xiaomi">Xiaomi</option>
                                <option value="Other">其他</option>
                            </select>
                            <select id="productCategoryFilter" class="filter-select" onchange="filterProducts()">
                                <option value="">所有分類</option>
                                <option value="smartphone">智慧型手機</option>
                                <option value="laptop">筆記型電腦</option>
                                <option value="tablet">平板電腦</option>
                                <option value="desktop">桌上型電腦</option>
                                <option value="accessory">配件</option>
                                <option value="other">其他</option>
                            </select>
                            <button class="btn" onclick="clearProductFilters()">清除篩選</button>
                        </div>

                        <!-- 篩選結果顯示 -->
                        <div id="productFilterStats" style="margin-bottom: 16px; color: #666; font-size: 14px;"></div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>產品編號</th>
                                    <th>產品名稱</th>
                                    <th>品牌</th>
                                    <th>型號</th>
                                    <th>分類</th>
                                    <th>保固期限</th>
                                    <th>建議售價</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="productTableBody">
                            </tbody>
                        </table>

                        <!-- 新增產品表單 -->
                        <div id="addProductForm" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>📦 新增產品</h3>
                                    <button class="btn" onclick="hideAddProductForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="productForm" onsubmit="submitProductForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>產品名稱 *</label>
                                            <input type="text" id="productNameInput" required placeholder="請輸入產品名稱">
                                        </div>
                                        <div class="form-group">
                                            <label>品牌 *</label>
                                            <select id="productBrandInput" required>
                                                <option value="">請選擇品牌</option>
                                                <option value="Apple">Apple</option>
                                                <option value="Samsung">Samsung</option>
                                                <option value="ASUS">ASUS</option>
                                                <option value="Sony">Sony</option>
                                                <option value="Xiaomi">Xiaomi</option>
                                                <option value="Other">其他</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>型號</label>
                                            <input type="text" id="productModelInput" placeholder="請輸入型號">
                                        </div>
                                        <div class="form-group">
                                            <label>產品分類 *</label>
                                            <select id="productCategoryInput" required>
                                                <option value="">請選擇分類</option>
                                                <option value="smartphone">智慧型手機</option>
                                                <option value="laptop">筆記型電腦</option>
                                                <option value="tablet">平板電腦</option>
                                                <option value="desktop">桌上型電腦</option>
                                                <option value="accessory">配件</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>保固期限 (月)</label>
                                            <input type="number" id="productWarrantyInput" min="0" placeholder="請輸入保固月數">
                                        </div>
                                        <div class="form-group">
                                            <label>建議售價 (NT$)</label>
                                            <input type="number" id="productPriceInput" min="0" step="0.01" placeholder="請輸入建議售價">
                                        </div>
                                        <div class="form-group">
                                            <label>產品狀態</label>
                                            <select id="productStatusInput">
                                                <option value="active">上架中</option>
                                                <option value="discontinued">停產</option>
                                                <option value="coming_soon">即將推出</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>發布日期</label>
                                            <input type="date" id="productReleaseDateInput">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>產品描述</label>
                                            <textarea id="productDescriptionInput" placeholder="產品詳細描述" rows="3"></textarea>
                                        </div>
                                        <div class="form-group full-width">
                                            <label>備註</label>
                                            <textarea id="productNotesInput" placeholder="產品備註資訊" rows="2"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideAddProductForm()">取消</button>
                                        <button type="submit" class="btn btn-primary">建立產品</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div id="partsPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">🔧 零件管理</h2>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-primary" onclick="showAddPartForm()">新增零件</button>
                                <button class="btn" onclick="showImportPartForm()" style="background: #52c41a; color: white;">📥 導入資料</button>
                                <button class="btn" onclick="exportPartData()" style="background: #1890ff; color: white;">📤 匯出資料</button>
                            </div>
                        </div>

                        <div class="toolbar">
                            <input type="text" id="partSearchInput" class="search-input" placeholder="搜尋零件名稱、型號或供應商..." onkeyup="searchParts()">
                            <select id="partCategoryFilter" class="filter-select" onchange="filterParts()">
                                <option value="">所有分類</option>
                                <option value="screen">螢幕</option>
                                <option value="battery">電池</option>
                                <option value="motherboard">主機板</option>
                                <option value="camera">相機</option>
                                <option value="speaker">喇叭</option>
                                <option value="other">其他</option>
                            </select>
                            <select id="partStockFilter" class="filter-select" onchange="filterParts()">
                                <option value="">所有庫存</option>
                                <option value="in_stock">有庫存</option>
                                <option value="low_stock">庫存不足</option>
                                <option value="out_of_stock">缺貨</option>
                            </select>
                            <button class="btn" onclick="clearPartFilters()">清除篩選</button>
                        </div>

                        <!-- 篩選結果顯示 -->
                        <div id="partFilterStats" style="margin-bottom: 16px; color: #666; font-size: 14px;"></div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>零件編號</th>
                                    <th>零件名稱</th>
                                    <th>分類</th>
                                    <th>型號</th>
                                    <th>供應商</th>
                                    <th>庫存數量</th>
                                    <th>單價</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="partTableBody">
                            </tbody>
                        </table>

                        <!-- 新增零件表單 -->
                        <div id="addPartForm" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>🔧 新增零件</h3>
                                    <button class="btn" onclick="hideAddPartForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="partForm" onsubmit="submitPartForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>零件編號 *</label>
                                            <input type="text" id="partIdInput" required placeholder="請輸入零件編號" onblur="lookupPartName()">
                                        </div>
                                        <div class="form-group">
                                            <label>零件名稱</label>
                                            <input type="text" id="partNameInput" readonly style="background: #f5f5f5;" placeholder="將根據零件編號自動查詢">
                                        </div>
                                        <div class="form-group">
                                            <label>零件分類 *</label>
                                            <select id="partCategoryInput" required>
                                                <option value="">請選擇分類</option>
                                                <option value="screen">螢幕</option>
                                                <option value="battery">電池</option>
                                                <option value="motherboard">主機板</option>
                                                <option value="camera">相機</option>
                                                <option value="speaker">喇叭</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>型號</label>
                                            <input type="text" id="partModelInput" placeholder="請輸入型號">
                                        </div>
                                        <div class="form-group">
                                            <label>供應商</label>
                                            <input type="text" id="partSupplierInput" placeholder="請輸入供應商">
                                        </div>
                                        <div class="form-group">
                                            <label>庫存數量 *</label>
                                            <input type="number" id="partStockInput" required min="0" placeholder="請輸入庫存數量">
                                        </div>
                                        <div class="form-group">
                                            <label>單價 (NT$)</label>
                                            <input type="number" id="partPriceInput" min="0" step="0.01" placeholder="請輸入單價">
                                        </div>
                                        <div class="form-group">
                                            <label>最低庫存警告</label>
                                            <input type="number" id="partMinStockInput" min="0" placeholder="庫存低於此數量時警告">
                                        </div>
                                        <div class="form-group">
                                            <label>存放位置</label>
                                            <input type="text" id="partLocationInput" placeholder="請輸入存放位置">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>備註</label>
                                            <textarea id="partNotesInput" placeholder="零件備註資訊" rows="2"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideAddPartForm()">取消</button>
                                        <button type="submit" class="btn btn-primary">建立零件</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 編輯零件表單 -->
                        <div id="editPartForm" class="modal hidden">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>✏️ 編輯零件</h3>
                                    <button class="btn" onclick="hideEditPartForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <form id="editPartFormElement" onsubmit="submitEditPartForm(event)">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>零件編號</label>
                                            <input type="text" id="editPartId" readonly style="background: #f5f5f5;">
                                        </div>
                                        <div class="form-group">
                                            <label>零件名稱 *</label>
                                            <input type="text" id="editPartName" required placeholder="請輸入零件名稱">
                                        </div>
                                        <div class="form-group">
                                            <label>零件分類 *</label>
                                            <select id="editPartCategory" required>
                                                <option value="">請選擇分類</option>
                                                <option value="screen">螢幕</option>
                                                <option value="battery">電池</option>
                                                <option value="motherboard">主機板</option>
                                                <option value="camera">相機</option>
                                                <option value="speaker">喇叭</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>型號</label>
                                            <input type="text" id="editPartModel" placeholder="請輸入型號">
                                        </div>
                                        <div class="form-group">
                                            <label>供應商</label>
                                            <input type="text" id="editPartSupplier" placeholder="請輸入供應商">
                                        </div>
                                        <div class="form-group">
                                            <label>庫存數量 *</label>
                                            <input type="number" id="editPartStock" required min="0" placeholder="請輸入庫存數量">
                                        </div>
                                        <div class="form-group">
                                            <label>單價 (NT$)</label>
                                            <input type="number" id="editPartPrice" min="0" step="0.01" placeholder="請輸入單價">
                                        </div>
                                        <div class="form-group">
                                            <label>最低庫存警告</label>
                                            <input type="number" id="editPartMinStock" min="0" placeholder="庫存低於此數量時警告">
                                        </div>
                                        <div class="form-group">
                                            <label>存放位置</label>
                                            <input type="text" id="editPartLocation" placeholder="請輸入存放位置">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>備註</label>
                                            <textarea id="editPartNotes" placeholder="零件備註資訊" rows="2"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn" onclick="hideEditPartForm()">取消</button>
                                        <button type="submit" class="btn btn-primary">儲存變更</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 導入零件資料表單 -->
                        <div id="importPartForm" class="modal hidden">
                            <div class="modal-content" style="max-width: 600px;">
                                <div class="modal-header">
                                    <h3>📥 導入零件資料</h3>
                                    <button class="btn" onclick="hideImportPartForm()" style="background: none; border: none; font-size: 20px; cursor: pointer;">✕</button>
                                </div>
                                <div style="padding: 24px;">
                                    <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                                        <h4 style="color: #52c41a; margin: 0 0 8px 0;">📋 支援的檔案格式</h4>
                                        <ul style="margin: 0; padding-left: 20px; color: #666;">
                                            <li>CSV檔案 (.csv)</li>
                                            <li>Excel檔案 (.xlsx, .xls)</li>
                                            <li>JSON檔案 (.json)</li>
                                        </ul>
                                    </div>

                                    <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                                        <h4 style="color: #fa8c16; margin: 0 0 8px 0;">📝 檔案格式要求</h4>
                                        <p style="margin: 0 0 8px 0; color: #666;">請確保檔案包含以下欄位（欄位名稱需完全一致）：</p>
                                        <div style="font-family: monospace; background: #f5f5f5; padding: 8px; border-radius: 4px; font-size: 12px;">
                                            零件編號, 零件名稱, 零件分類, 型號, 供應商, 庫存數量, 單價, 最低庫存警告, 存放位置, 備註
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 24px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">選擇檔案</label>
                                        <input type="file" id="importFileInput" accept=".csv,.xlsx,.xls,.json"
                                               style="width: 100%; padding: 8px; border: 2px dashed #d9d9d9; border-radius: 6px; background: #fafafa;">
                                    </div>

                                    <div style="margin-bottom: 24px;">
                                        <label style="display: flex; align-items: center; gap: 8px;">
                                            <input type="checkbox" id="replaceExistingData" style="margin: 0;">
                                            <span>覆蓋現有資料（如果零件編號重複）</span>
                                        </label>
                                    </div>

                                    <div style="display: flex; gap: 12px; justify-content: flex-end;">
                                        <button type="button" class="btn" onclick="hideImportPartForm()">取消</button>
                                        <button type="button" class="btn" onclick="downloadTemplate()" style="background: #1890ff; color: white;">下載範本</button>
                                        <button type="button" class="btn btn-primary" onclick="importPartData()">開始導入</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="reportsPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">📈 統計報表</h2>
                            <button class="btn btn-primary">匯出報表</button>
                        </div>

                        <!-- 統計卡片 -->
                        <div class="stats-grid" style="margin-bottom: 24px;">
                            <div class="stat-card">
                                <div class="stat-number">92%</div>
                                <div class="stat-label">完成率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3.1</div>
                                <div class="stat-label">平均處理時間(天)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">$2.4M</div>
                                <div class="stat-label">總營收</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">4.6</div>
                                <div class="stat-label">客戶滿意度</div>
                            </div>
                        </div>

                        <!-- 報表選項卡 -->
                        <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="border-bottom: 1px solid #f0f0f0; margin-bottom: 24px;">
                                <div style="display: flex; gap: 24px;">
                                    <div class="tab-item active" data-tab="overview" onclick="showReportTab('overview')">總覽報表</div>
                                    <div class="tab-item" data-tab="repairs" onclick="showReportTab('repairs')">維修分析</div>
                                    <div class="tab-item" data-tab="customers" onclick="showReportTab('customers')">客戶分析</div>
                                    <div class="tab-item" data-tab="revenue" onclick="showReportTab('revenue')">營收分析</div>
                                    <div class="tab-item" data-tab="statistics" onclick="showReportTab('statistics')">維修統計</div>
                                </div>
                            </div>

                            <!-- 總覽報表內容 -->
                            <div id="overviewTab" class="tab-content">
                                <h3>📊 總覽報表</h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 16px;">
                                    <div>
                                        <h4>維修狀態分布</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>已完成</span>
                                                <span><span class="tag tag-green">65%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>維修中</span>
                                                <span><span class="tag tag-blue">15%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>待檢測</span>
                                                <span><span class="tag tag-orange">12%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>其他</span>
                                                <span><span class="tag tag-red">8%</span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4>優先級分布</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>低優先級</span>
                                                <span>45件</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>中優先級</span>
                                                <span>32件</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>高優先級</span>
                                                <span>18件</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>緊急</span>
                                                <span>8件</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 維修分析內容 -->
                            <div id="repairsTab" class="tab-content hidden">
                                <h3>🛠️ 維修分析</h3>
                                <div style="margin-top: 16px;">
                                    <h4>常見問題統計</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>問題類型</th>
                                                <th>發生次數</th>
                                                <th>平均費用</th>
                                                <th>平均時間</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>螢幕破裂</td>
                                                <td>234</td>
                                                <td>$2,500</td>
                                                <td>2.1天</td>
                                            </tr>
                                            <tr>
                                                <td>電池問題</td>
                                                <td>189</td>
                                                <td>$1,800</td>
                                                <td>1.8天</td>
                                            </tr>
                                            <tr>
                                                <td>充電異常</td>
                                                <td>156</td>
                                                <td>$1,200</td>
                                                <td>1.5天</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 客戶分析內容 -->
                            <div id="customersTab" class="tab-content hidden">
                                <h3>👥 客戶分析</h3>

                                <!-- 客戶統計卡片 -->
                                <div class="stats-grid" style="margin-bottom: 24px;">
                                    <div class="stat-card">
                                        <div class="stat-number">456</div>
                                        <div class="stat-label">總客戶數</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">89</div>
                                        <div class="stat-label">VIP客戶</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">2.3</div>
                                        <div class="stat-label">平均維修次數</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">4.2</div>
                                        <div class="stat-label">客戶滿意度</div>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                    <!-- 客戶價值分析 -->
                                    <div>
                                        <h4>💎 客戶價值分析</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>高價值客戶 (>$10,000)</span>
                                                <span><span class="tag tag-red">45位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>中價值客戶 ($5,000-$10,000)</span>
                                                <span><span class="tag tag-orange">128位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>一般客戶 ($1,000-$5,000)</span>
                                                <span><span class="tag tag-blue">203位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>新客戶 (<$1,000)</span>
                                                <span><span class="tag tag-green">80位</span></span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 客戶忠誠度統計 -->
                                    <div>
                                        <h4>🏆 客戶忠誠度統計</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>超級忠誠 (>10次維修)</span>
                                                <span><span class="tag tag-red">23位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>高忠誠 (5-10次維修)</span>
                                                <span><span class="tag tag-orange">67位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>中忠誠 (2-4次維修)</span>
                                                <span><span class="tag tag-blue">156位</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>新客戶 (1次維修)</span>
                                                <span><span class="tag tag-green">210位</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 客戶維修頻率分析 -->
                                <div style="margin-top: 24px;">
                                    <h4>📊 客戶維修頻率分析</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>客戶類型</th>
                                                <th>平均維修間隔</th>
                                                <th>最常見問題</th>
                                                <th>平均消費</th>
                                                <th>滿意度</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="tag tag-red">VIP客戶</span></td>
                                                <td>45天</td>
                                                <td>螢幕更換</td>
                                                <td>$3,200</td>
                                                <td><span class="tag tag-green">4.8</span></td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-orange">企業客戶</span></td>
                                                <td>60天</td>
                                                <td>系統維護</td>
                                                <td>$2,800</td>
                                                <td><span class="tag tag-green">4.5</span></td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-blue">一般客戶</span></td>
                                                <td>120天</td>
                                                <td>電池更換</td>
                                                <td>$1,500</td>
                                                <td><span class="tag tag-blue">4.2</span></td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-green">新客戶</span></td>
                                                <td>-</td>
                                                <td>螢幕破裂</td>
                                                <td>$2,100</td>
                                                <td><span class="tag tag-orange">3.9</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 客戶滿意度趨勢 -->
                                <div style="margin-top: 24px;">
                                    <h4>📈 客戶滿意度趨勢</h4>
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 16px; text-align: center;">
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">1月</div>
                                                <div style="font-size: 24px; color: #1890ff;">4.1</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">2月</div>
                                                <div style="font-size: 24px; color: #1890ff;">4.3</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">3月</div>
                                                <div style="font-size: 24px; color: #1890ff;">4.2</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">4月</div>
                                                <div style="font-size: 24px; color: #52c41a;">4.5</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">5月</div>
                                                <div style="font-size: 24px; color: #52c41a;">4.4</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">6月</div>
                                                <div style="font-size: 24px; color: #52c41a;">4.6</div>
                                            </div>
                                        </div>
                                        <div style="text-align: center; margin-top: 16px; color: #52c41a; font-weight: bold;">
                                            ↗ 滿意度持續提升中
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 營收分析內容 -->
                            <div id="revenueTab" class="tab-content hidden">
                                <h3>💰 營收分析</h3>

                                <!-- 營收統計卡片 -->
                                <div class="stats-grid" style="margin-bottom: 24px;">
                                    <div class="stat-card">
                                        <div class="stat-number">$2.4M</div>
                                        <div class="stat-label">總營收</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">$1.8M</div>
                                        <div class="stat-label">淨利潤</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">75%</div>
                                        <div class="stat-label">利潤率</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">+12%</div>
                                        <div class="stat-label">月成長率</div>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                    <!-- 營收趨勢分析 -->
                                    <div>
                                        <h4>📈 營收趨勢分析 (近6個月)</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>1月</span>
                                                <span style="color: #1890ff; font-weight: bold;">$320,000</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>2月</span>
                                                <span style="color: #1890ff; font-weight: bold;">$350,000</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>3月</span>
                                                <span style="color: #1890ff; font-weight: bold;">$380,000</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>4月</span>
                                                <span style="color: #52c41a; font-weight: bold;">$420,000</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>5月</span>
                                                <span style="color: #52c41a; font-weight: bold;">$450,000</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>6月</span>
                                                <span style="color: #52c41a; font-weight: bold;">$480,000</span>
                                            </div>
                                            <div style="text-align: center; margin-top: 16px; color: #52c41a; font-weight: bold;">
                                                ↗ 營收持續成長 (+50% YoY)
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 利潤率分析 -->
                                    <div>
                                        <h4>💹 利潤率分析</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>維修服務</span>
                                                <span><span class="tag tag-green">78%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>零件銷售</span>
                                                <span><span class="tag tag-blue">65%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>保固服務</span>
                                                <span><span class="tag tag-orange">45%</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>其他服務</span>
                                                <span><span class="tag tag-red">35%</span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 成本結構分析 -->
                                <div style="margin-top: 24px;">
                                    <h4>📊 成本結構分析</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>成本項目</th>
                                                <th>金額</th>
                                                <th>佔比</th>
                                                <th>月變化</th>
                                                <th>狀態</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>零件採購</td>
                                                <td>$180,000</td>
                                                <td>37.5%</td>
                                                <td><span style="color: #f5222d;">+5%</span></td>
                                                <td><span class="tag tag-orange">需關注</span></td>
                                            </tr>
                                            <tr>
                                                <td>人力成本</td>
                                                <td>$120,000</td>
                                                <td>25%</td>
                                                <td><span style="color: #52c41a;">-2%</span></td>
                                                <td><span class="tag tag-green">正常</span></td>
                                            </tr>
                                            <tr>
                                                <td>租金水電</td>
                                                <td>$60,000</td>
                                                <td>12.5%</td>
                                                <td><span style="color: #666;">0%</span></td>
                                                <td><span class="tag tag-blue">穩定</span></td>
                                            </tr>
                                            <tr>
                                                <td>設備折舊</td>
                                                <td>$30,000</td>
                                                <td>6.25%</td>
                                                <td><span style="color: #666;">0%</span></td>
                                                <td><span class="tag tag-blue">穩定</span></td>
                                            </tr>
                                            <tr>
                                                <td>其他費用</td>
                                                <td>$90,000</td>
                                                <td>18.75%</td>
                                                <td><span style="color: #52c41a;">-3%</span></td>
                                                <td><span class="tag tag-green">優化中</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 營收預測 -->
                                <div style="margin-top: 24px;">
                                    <h4>🔮 營收預測 (未來3個月)</h4>
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; text-align: center;">
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">7月預測</div>
                                                <div style="font-size: 24px; color: #1890ff; margin-bottom: 8px;">$510,000</div>
                                                <div style="font-size: 12px; color: #52c41a;">+6.25% 預期成長</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">8月預測</div>
                                                <div style="font-size: 24px; color: #1890ff; margin-bottom: 8px;">$540,000</div>
                                                <div style="font-size: 12px; color: #52c41a;">+5.88% 預期成長</div>
                                            </div>
                                            <div>
                                                <div style="font-weight: bold; margin-bottom: 8px;">9月預測</div>
                                                <div style="font-size: 24px; color: #1890ff; margin-bottom: 8px;">$570,000</div>
                                                <div style="font-size: 12px; color: #52c41a;">+5.56% 預期成長</div>
                                            </div>
                                        </div>
                                        <div style="text-align: center; margin-top: 16px;">
                                            <div style="color: #666; font-size: 14px;">基於歷史數據和市場趨勢分析</div>
                                            <div style="color: #52c41a; font-weight: bold; margin-top: 8px;">
                                                預計Q3總營收: $1,620,000 (+18% QoQ)
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 維修統計內容 -->
                            <div id="statisticsTab" class="tab-content hidden">
                                <h3>📊 維修統計</h3>

                                <!-- 時間範圍選擇器 -->
                                <div style="background: #f6f6f6; padding: 16px; border-radius: 6px; margin-bottom: 24px;">
                                    <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="font-weight: 500;">統計範圍：</label>
                                            <select id="statisticsTimeRange" onchange="updateStatistics()" style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                <option value="year">年度統計</option>
                                                <option value="month">月度統計</option>
                                                <option value="week">週度統計</option>
                                                <option value="day">日度統計</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="font-weight: 500;">年份：</label>
                                            <select id="statisticsYear" onchange="updateStatistics()" style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                <option value="2024">2024年</option>
                                                <option value="2023">2023年</option>
                                                <option value="2022">2022年</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;" id="monthSelector">
                                            <label style="font-weight: 500;">月份：</label>
                                            <select id="statisticsMonth" onchange="updateStatistics()" style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                <option value="1">1月</option>
                                                <option value="2">2月</option>
                                                <option value="3">3月</option>
                                                <option value="4">4月</option>
                                                <option value="5">5月</option>
                                                <option value="6">6月</option>
                                                <option value="7">7月</option>
                                                <option value="8">8月</option>
                                                <option value="9">9月</option>
                                                <option value="10">10月</option>
                                                <option value="11">11月</option>
                                                <option value="12">12月</option>
                                            </select>
                                        </div>
                                        <button class="btn btn-primary" onclick="updateStatistics()">🔄 更新統計</button>
                                    </div>
                                </div>

                                <!-- 維修統計卡片 -->
                                <div class="stats-grid" style="margin-bottom: 24px;">
                                    <div class="stat-card">
                                        <div class="stat-number" id="totalRepairsCount">1,247</div>
                                        <div class="stat-label">總維修數量</div>
                                        <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">+15% 較上期</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="avgRepairsPerDay">4.2</div>
                                        <div class="stat-label">日均維修量</div>
                                        <div style="font-size: 12px; color: #1890ff; margin-top: 4px;">穩定成長</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="completionRate">92%</div>
                                        <div class="stat-label">完成率</div>
                                        <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">+3% 較上期</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="avgRepairTime">2.3天</div>
                                        <div class="stat-label">平均維修時間</div>
                                        <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">-0.5天 較上期</div>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                                    <!-- 維修數量趨勢圖 -->
                                    <div>
                                        <h4>📈 維修數量趨勢</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div id="repairTrendChart" style="height: 200px; display: flex; align-items: end; justify-content: space-between; gap: 8px;">
                                                <!-- 模擬柱狀圖 -->
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #1890ff; width: 100%; height: 120px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">1月</div>
                                                    <div style="font-size: 10px; color: #666;">98</div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #1890ff; width: 100%; height: 140px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">2月</div>
                                                    <div style="font-size: 10px; color: #666;">115</div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #1890ff; width: 100%; height: 160px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">3月</div>
                                                    <div style="font-size: 10px; color: #666;">132</div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #52c41a; width: 100%; height: 180px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">4月</div>
                                                    <div style="font-size: 10px; color: #666;">148</div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #52c41a; width: 100%; height: 200px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">5月</div>
                                                    <div style="font-size: 10px; color: #666;">165</div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                                    <div style="background: #52c41a; width: 100%; height: 185px; border-radius: 4px 4px 0 0;"></div>
                                                    <div style="margin-top: 8px; font-size: 12px;">6月</div>
                                                    <div style="font-size: 10px; color: #666;">152</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 維保狀態分布 -->
                                    <div>
                                        <h4>🔧 維保狀態分布</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>客訴維修</span>
                                                <span><span class="tag tag-red">342筆 (27%)</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                <span>維保維修</span>
                                                <span><span class="tag tag-blue">905筆 (73%)</span></span>
                                            </div>
                                            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e8e8e8;">
                                                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">客訴主要原因：</div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                    <span style="font-size: 12px;">螢幕問題</span>
                                                    <span style="font-size: 12px; color: #f5222d;">45%</span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                    <span style="font-size: 12px;">電池問題</span>
                                                    <span style="font-size: 12px; color: #fa8c16;">28%</span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span style="font-size: 12px;">系統問題</span>
                                                    <span style="font-size: 12px; color: #1890ff;">27%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 維修記錄類型分析 -->
                                <div style="margin-bottom: 24px;">
                                    <h4>🔨 維修記錄類型分析</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                        <div>
                                            <h5>按維修類型統計</h5>
                                            <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>調整維修</span>
                                                    <span><span class="tag tag-blue">789筆 (63%)</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>換馬達</span>
                                                    <span><span class="tag tag-orange">458筆 (37%)</span></span>
                                                </div>
                                                <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e8e8e8;">
                                                    <div style="font-size: 12px; color: #666; margin-bottom: 8px;">調整維修細分：</div>
                                                    <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                                                        <span style="font-size: 12px;">軟體調整</span>
                                                        <span style="font-size: 12px; color: #1890ff;">45%</span>
                                                    </div>
                                                    <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                                                        <span style="font-size: 12px;">硬體調整</span>
                                                        <span style="font-size: 12px; color: #52c41a;">35%</span>
                                                    </div>
                                                    <div style="display: flex; justify-content: space-between;">
                                                        <span style="font-size: 12px;">系統優化</span>
                                                        <span style="font-size: 12px; color: #fa8c16;">20%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h5>按產品類型統計</h5>
                                            <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>iPhone系列</span>
                                                    <span><span class="tag tag-red">523筆 (42%)</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>MacBook系列</span>
                                                    <span><span class="tag tag-blue">312筆 (25%)</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>iPad系列</span>
                                                    <span><span class="tag tag-green">234筆 (19%)</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>其他品牌</span>
                                                    <span><span class="tag tag-orange">178筆 (14%)</span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 維修效率分析 -->
                                <div style="margin-bottom: 24px;">
                                    <h4>⚡ 維修效率分析</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>時間範圍</th>
                                                <th>維修數量</th>
                                                <th>平均處理時間</th>
                                                <th>完成率</th>
                                                <th>客戶滿意度</th>
                                                <th>趨勢</th>
                                            </tr>
                                        </thead>
                                        <tbody id="efficiencyTableBody">
                                            <tr>
                                                <td><span class="tag tag-blue">本週</span></td>
                                                <td>42筆</td>
                                                <td>1.8天</td>
                                                <td><span class="tag tag-green">95%</span></td>
                                                <td><span class="tag tag-green">4.7</span></td>
                                                <td><span style="color: #52c41a;">↗ +8%</span></td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-orange">本月</span></td>
                                                <td>165筆</td>
                                                <td>2.1天</td>
                                                <td><span class="tag tag-green">92%</span></td>
                                                <td><span class="tag tag-green">4.5</span></td>
                                                <td><span style="color: #52c41a;">↗ +5%</span></td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-red">本年</span></td>
                                                <td>1,247筆</td>
                                                <td>2.3天</td>
                                                <td><span class="tag tag-green">89%</span></td>
                                                <td><span class="tag tag-blue">4.3</span></td>
                                                <td><span style="color: #52c41a;">↗ +12%</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 問題熱點分析 -->
                                <div style="margin-bottom: 24px;">
                                    <h4>🔥 問題熱點分析</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                        <div>
                                            <h5>最常見問題 TOP 10</h5>
                                            <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                                    <span style="font-size: 14px;">1. 螢幕破裂</span>
                                                    <span><span class="tag tag-red">156筆</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                                    <span style="font-size: 14px;">2. 電池續航問題</span>
                                                    <span><span class="tag tag-orange">134筆</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                                    <span style="font-size: 14px;">3. 系統卡頓</span>
                                                    <span><span class="tag tag-blue">98筆</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                                    <span style="font-size: 14px;">4. 充電問題</span>
                                                    <span><span class="tag tag-green">87筆</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                                    <span style="font-size: 14px;">5. 相機故障</span>
                                                    <span><span class="tag tag-purple">76筆</span></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h5>維修成功率分析</h5>
                                            <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>一次修復成功</span>
                                                    <span><span class="tag tag-green">78%</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>二次修復成功</span>
                                                    <span><span class="tag tag-blue">15%</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                                    <span>三次以上修復</span>
                                                    <span><span class="tag tag-orange">5%</span></span>
                                                </div>
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>無法修復</span>
                                                    <span><span class="tag tag-red">2%</span></span>
                                                </div>
                                                <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e8e8e8; text-align: center;">
                                                    <div style="font-size: 24px; color: #52c41a; font-weight: bold;">93%</div>
                                                    <div style="font-size: 12px; color: #666;">總體修復成功率</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 維保員績效分析 -->
                                <div style="margin-bottom: 24px;">
                                    <h4>👨‍🔧 維保員績效分析</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>維保員</th>
                                                <th>處理數量</th>
                                                <th>平均處理時間</th>
                                                <th>成功率</th>
                                                <th>客戶評分</th>
                                                <th>專長領域</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="tag tag-blue">維保員A</span></td>
                                                <td>342筆</td>
                                                <td>2.1天</td>
                                                <td><span class="tag tag-green">96%</span></td>
                                                <td><span class="tag tag-green">4.8</span></td>
                                                <td>iPhone維修</td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-green">維保員B</span></td>
                                                <td>298筆</td>
                                                <td>1.9天</td>
                                                <td><span class="tag tag-green">94%</span></td>
                                                <td><span class="tag tag-green">4.6</span></td>
                                                <td>MacBook維修</td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-orange">維保員C</span></td>
                                                <td>267筆</td>
                                                <td>2.4天</td>
                                                <td><span class="tag tag-blue">91%</span></td>
                                                <td><span class="tag tag-blue">4.4</span></td>
                                                <td>iPad維修</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系統設定頁面 -->
                    <div id="settingsPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">⚙️ 系統設定</h2>
                            <button class="btn btn-primary">保存設定</button>
                        </div>

                        <!-- 系統統計 -->
                        <div class="stats-grid" style="margin-bottom: 24px;">
                            <div class="stat-card">
                                <div class="stat-number">24</div>
                                <div class="stat-label">總用戶數</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">18</div>
                                <div class="stat-label">活躍用戶</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">4</div>
                                <div class="stat-label">角色數量</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">30天</div>
                                <div class="stat-label">系統運行</div>
                            </div>
                        </div>

                        <!-- 設定選項卡 -->
                        <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="border-bottom: 1px solid #f0f0f0; margin-bottom: 24px;">
                                <div style="display: flex; gap: 24px;">
                                    <div class="tab-item active" data-tab="overview" onclick="showSettingsTab('overview')">系統總覽</div>
                                    <div class="tab-item" data-tab="users" onclick="showSettingsTab('users')">用戶管理</div>
                                    <div class="tab-item" data-tab="roles" onclick="showSettingsTab('roles')">角色權限</div>
                                    <div class="tab-item" data-tab="system" onclick="showSettingsTab('system')">系統設定</div>
                                </div>
                            </div>

                            <!-- 系統總覽內容 -->
                            <div id="overviewTab" class="tab-content">
                                <h3>📊 系統總覽</h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 16px;">
                                    <div>
                                        <h4>系統健康狀態</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>數據庫連接</span>
                                                <span><span class="tag tag-green">正常</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>郵件服務</span>
                                                <span><span class="tag tag-green">正常</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>文件存儲</span>
                                                <span><span class="tag tag-orange">警告</span></span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>備份服務</span>
                                                <span><span class="tag tag-green">正常</span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4>資源使用情況</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                                    <span>磁碟使用</span>
                                                    <span>45%</span>
                                                </div>
                                                <div style="background: #e6f7ff; height: 8px; border-radius: 4px;">
                                                    <div style="background: #1890ff; height: 8px; width: 45%; border-radius: 4px;"></div>
                                                </div>
                                            </div>
                                            <div>
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                                    <span>記憶體使用</span>
                                                    <span>53%</span>
                                                </div>
                                                <div style="background: #f6ffed; height: 8px; border-radius: 4px;">
                                                    <div style="background: #52c41a; height: 8px; width: 53%; border-radius: 4px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 用戶管理內容 -->
                            <div id="usersTab" class="tab-content hidden">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3>👥 用戶管理</h3>
                                    <button class="btn btn-primary" onclick="showAddUserForm()">新增用戶</button>
                                </div>
                                <div style="margin-top: 16px;">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>用戶</th>
                                                <th>角色</th>
                                                <th>部門</th>
                                                <th>狀態</th>
                                                <th>最後登入</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <strong>系統管理員</strong><br>
                                                    <small><EMAIL></small>
                                                </td>
                                                <td><span class="tag tag-red">系統管理員</span></td>
                                                <td>IT部門</td>
                                                <td><span class="tag tag-green">啟用</span></td>
                                                <td>今天 14:30</td>
                                                <td>
                                                    <button class="btn" style="margin-right: 8px;" onclick="editUser('admin')">編輯</button>
                                                    <button class="btn" onclick="resetPassword('admin')">重置密碼</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>客服主管</strong><br>
                                                    <small><EMAIL></small>
                                                </td>
                                                <td><span class="tag tag-orange">客服主管</span></td>
                                                <td>客服部門</td>
                                                <td><span class="tag tag-green">啟用</span></td>
                                                <td>今天 13:15</td>
                                                <td>
                                                    <button class="btn" style="margin-right: 8px;" onclick="editUser('service')">編輯</button>
                                                    <button class="btn" onclick="resetPassword('service')">重置密碼</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>維修技師</strong><br>
                                                    <small><EMAIL></small>
                                                </td>
                                                <td><span class="tag tag-blue">維修技師</span></td>
                                                <td>維修部門</td>
                                                <td><span class="tag tag-green">啟用</span></td>
                                                <td>今天 12:45</td>
                                                <td>
                                                    <button class="btn" style="margin-right: 8px;" onclick="editUser('tech')">編輯</button>
                                                    <button class="btn" onclick="resetPassword('tech')">重置密碼</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 新增用戶表單 -->
                                <div id="addUserForm" class="modal hidden">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3>👤 新增用戶</h3>
                                            <button class="close-btn" onclick="hideAddUserForm()">×</button>
                                        </div>
                                        <form id="userForm" onsubmit="submitUserForm(event)">
                                            <div class="form-group">
                                                <label for="userNameInput">用戶名稱 *</label>
                                                <input type="text" id="userNameInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="userEmailInput">電子郵箱 *</label>
                                                <input type="email" id="userEmailInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="userRoleInput">角色 *</label>
                                                <select id="userRoleInput" required>
                                                    <option value="">請選擇角色</option>
                                                    <option value="admin">系統管理員</option>
                                                    <option value="service">客服主管</option>
                                                    <option value="tech">維修技師</option>
                                                    <option value="viewer">查詢用戶</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="userDepartmentInput">部門</label>
                                                <select id="userDepartmentInput">
                                                    <option value="">請選擇部門</option>
                                                    <option value="IT部門">IT部門</option>
                                                    <option value="客服部門">客服部門</option>
                                                    <option value="維修部門">維修部門</option>
                                                    <option value="財務部門">財務部門</option>
                                                    <option value="管理部門">管理部門</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="userPasswordInput">初始密碼 *</label>
                                                <input type="password" id="userPasswordInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="userStatusInput">狀態</label>
                                                <select id="userStatusInput">
                                                    <option value="active">啟用</option>
                                                    <option value="inactive">停用</option>
                                                </select>
                                            </div>
                                            <div class="form-actions">
                                                <button type="button" class="btn" onclick="hideAddUserForm()">取消</button>
                                                <button type="submit" class="btn btn-primary">新增用戶</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <!-- 編輯用戶表單 -->
                                <div id="editUserForm" class="modal hidden">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3>✏️ 編輯用戶</h3>
                                            <button class="close-btn" onclick="hideEditUserForm()">×</button>
                                        </div>
                                        <form id="editUserFormData" onsubmit="submitEditUserForm(event)">
                                            <input type="hidden" id="editUserId">
                                            <div class="form-group">
                                                <label for="editUserNameInput">用戶名稱 *</label>
                                                <input type="text" id="editUserNameInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="editUserEmailInput">電子郵箱 *</label>
                                                <input type="email" id="editUserEmailInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="editUserRoleInput">角色 *</label>
                                                <select id="editUserRoleInput" required>
                                                    <option value="admin">系統管理員</option>
                                                    <option value="service">客服主管</option>
                                                    <option value="tech">維修技師</option>
                                                    <option value="viewer">查詢用戶</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="editUserDepartmentInput">部門</label>
                                                <select id="editUserDepartmentInput">
                                                    <option value="IT部門">IT部門</option>
                                                    <option value="客服部門">客服部門</option>
                                                    <option value="維修部門">維修部門</option>
                                                    <option value="財務部門">財務部門</option>
                                                    <option value="管理部門">管理部門</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="editUserStatusInput">狀態</label>
                                                <select id="editUserStatusInput">
                                                    <option value="active">啟用</option>
                                                    <option value="inactive">停用</option>
                                                </select>
                                            </div>
                                            <div class="form-actions">
                                                <button type="button" class="btn" onclick="hideEditUserForm()">取消</button>
                                                <button type="submit" class="btn btn-primary">保存更改</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- 角色權限內容 -->
                            <div id="rolesTab" class="tab-content hidden">
                                <h3>🛡️ 角色權限</h3>

                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <div>管理系統角色和權限設定</div>
                                    <button class="btn btn-primary" onclick="showAddRoleForm()">新增角色</button>
                                </div>

                                <!-- 角色列表 -->
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>角色名稱</th>
                                            <th>描述</th>
                                            <th>用戶數量</th>
                                            <th>權限範圍</th>
                                            <th>狀態</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="roleTableBody">
                                        <tr>
                                            <td><span class="tag tag-red">系統管理員</span></td>
                                            <td>擁有系統所有權限</td>
                                            <td>1</td>
                                            <td><span class="tag tag-green">完整權限</span></td>
                                            <td><span class="tag tag-green">啟用</span></td>
                                            <td>
                                                <button class="btn" style="margin-right: 8px;" onclick="editRole('admin')">編輯</button>
                                                <button class="btn" onclick="viewRolePermissions('admin')">權限</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><span class="tag tag-orange">客服主管</span></td>
                                            <td>客戶服務和維修管理</td>
                                            <td>3</td>
                                            <td><span class="tag tag-blue">業務權限</span></td>
                                            <td><span class="tag tag-green">啟用</span></td>
                                            <td>
                                                <button class="btn" style="margin-right: 8px;" onclick="editRole('service')">編輯</button>
                                                <button class="btn" onclick="viewRolePermissions('service')">權限</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><span class="tag tag-blue">維修技師</span></td>
                                            <td>維修記錄和零件管理</td>
                                            <td>8</td>
                                            <td><span class="tag tag-blue">操作權限</span></td>
                                            <td><span class="tag tag-green">啟用</span></td>
                                            <td>
                                                <button class="btn" style="margin-right: 8px;" onclick="editRole('tech')">編輯</button>
                                                <button class="btn" onclick="viewRolePermissions('tech')">權限</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><span class="tag tag-green">查詢用戶</span></td>
                                            <td>僅查看權限</td>
                                            <td>12</td>
                                            <td><span class="tag tag-orange">查看權限</span></td>
                                            <td><span class="tag tag-green">啟用</span></td>
                                            <td>
                                                <button class="btn" style="margin-right: 8px;" onclick="editRole('viewer')">編輯</button>
                                                <button class="btn" onclick="viewRolePermissions('viewer')">權限</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <!-- 權限矩陣 -->
                                <div style="margin-top: 24px;">
                                    <h4>📋 權限矩陣</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>功能模組</th>
                                                <th>系統管理員</th>
                                                <th>客服主管</th>
                                                <th>維修技師</th>
                                                <th>查詢用戶</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>維修記錄管理</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-blue">編輯</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>客戶管理</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>產品管理</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-blue">編輯</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>零件管理</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-blue">編輯</span></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>統計報表</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                                <td><span class="tag tag-orange">查看</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>系統設定</strong></td>
                                                <td><span class="tag tag-green">完整</span></td>
                                                <td><span class="tag tag-red">無權限</span></td>
                                                <td><span class="tag tag-red">無權限</span></td>
                                                <td><span class="tag tag-red">無權限</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 新增角色表單 -->
                                <div id="addRoleForm" class="modal hidden">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3>🛡️ 新增角色</h3>
                                            <button class="close-btn" onclick="hideAddRoleForm()">×</button>
                                        </div>
                                        <form id="roleForm" onsubmit="submitRoleForm(event)">
                                            <div class="form-group">
                                                <label for="roleNameInput">角色名稱 *</label>
                                                <input type="text" id="roleNameInput" required placeholder="例如：客服專員">
                                            </div>
                                            <div class="form-group">
                                                <label for="roleDescriptionInput">角色描述 *</label>
                                                <textarea id="roleDescriptionInput" required placeholder="描述角色的職責和功能"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="roleColorInput">角色標籤顏色</label>
                                                <select id="roleColorInput">
                                                    <option value="blue">藍色</option>
                                                    <option value="green">綠色</option>
                                                    <option value="orange">橙色</option>
                                                    <option value="purple">紫色</option>
                                                    <option value="red">紅色</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>權限設定</label>
                                                <div style="border: 1px solid #d9d9d9; border-radius: 4px; padding: 12px;">
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_repair" style="margin-right: 8px;">
                                                            維修記錄管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_customer" style="margin-right: 8px;">
                                                            客戶管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_product" style="margin-right: 8px;">
                                                            產品管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_parts" style="margin-right: 8px;">
                                                            零件管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_reports" style="margin-right: 8px;">
                                                            統計報表
                                                        </label>
                                                    </div>
                                                    <div>
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="perm_system" style="margin-right: 8px;">
                                                            系統設定
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button type="button" class="btn" onclick="hideAddRoleForm()">取消</button>
                                                <button type="submit" class="btn btn-primary">新增角色</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <!-- 編輯角色表單 -->
                                <div id="editRoleForm" class="modal hidden">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3>✏️ 編輯角色</h3>
                                            <button class="close-btn" onclick="hideEditRoleForm()">×</button>
                                        </div>
                                        <form id="editRoleFormData" onsubmit="submitEditRoleForm(event)">
                                            <input type="hidden" id="editRoleId">
                                            <div class="form-group">
                                                <label for="editRoleNameInput">角色名稱 *</label>
                                                <input type="text" id="editRoleNameInput" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="editRoleDescriptionInput">角色描述 *</label>
                                                <textarea id="editRoleDescriptionInput" required></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="editRoleColorInput">角色標籤顏色</label>
                                                <select id="editRoleColorInput">
                                                    <option value="blue">藍色</option>
                                                    <option value="green">綠色</option>
                                                    <option value="orange">橙色</option>
                                                    <option value="purple">紫色</option>
                                                    <option value="red">紅色</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>權限設定</label>
                                                <div style="border: 1px solid #d9d9d9; border-radius: 4px; padding: 12px;">
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_repair" style="margin-right: 8px;">
                                                            維修記錄管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_customer" style="margin-right: 8px;">
                                                            客戶管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_product" style="margin-right: 8px;">
                                                            產品管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_parts" style="margin-right: 8px;">
                                                            零件管理
                                                        </label>
                                                    </div>
                                                    <div style="margin-bottom: 8px;">
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_reports" style="margin-right: 8px;">
                                                            統計報表
                                                        </label>
                                                    </div>
                                                    <div>
                                                        <label style="display: flex; align-items: center;">
                                                            <input type="checkbox" id="edit_perm_system" style="margin-right: 8px;">
                                                            系統設定
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button type="button" class="btn" onclick="hideEditRoleForm()">取消</button>
                                                <button type="submit" class="btn btn-primary">保存更改</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <!-- 權限詳情表單 -->
                                <div id="rolePermissionsForm" class="modal hidden">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3>🔍 角色權限詳情</h3>
                                            <button class="close-btn" onclick="hideRolePermissionsForm()">×</button>
                                        </div>
                                        <div style="padding: 20px;">
                                            <div id="rolePermissionsContent">
                                                <!-- 權限詳情內容將由JavaScript動態生成 -->
                                            </div>
                                            <div class="form-actions">
                                                <button type="button" class="btn" onclick="hideRolePermissionsForm()">關閉</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 系統設定內容 -->
                            <div id="systemTab" class="tab-content hidden">
                                <h3>⚙️ 系統設定</h3>

                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <div>配置系統參數和業務規則</div>
                                    <button class="btn btn-primary" onclick="saveSystemSettings()">保存設定</button>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                    <!-- 一般設定 -->
                                    <div>
                                        <h4>🔧 一般設定</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">系統名稱</label>
                                                <input type="text" value="IACT MIO維保管理系統" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">系統版本</label>
                                                <input type="text" value="v1.0.0" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">時區設定</label>
                                                <select style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                    <option value="Asia/Taipei" selected>台北時間 (UTC+8)</option>
                                                    <option value="UTC">世界標準時間 (UTC)</option>
                                                </select>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">數據存儲模式</label>
                                                <select id="storageMode" onchange="onStorageModeChange()" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                    <option value="localStorage">本地存儲 (LocalStorage)</option>
                                                    <option value="sharepoint">SharePoint 整合</option>
                                                </select>
                                                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                                    選擇數據存儲方式，可隨時切換
                                                </div>
                                            </div>
                                            <div>
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    啟用系統維護模式
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 通知設定 -->
                                    <div>
                                        <h4>📧 通知設定</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">SMTP伺服器</label>
                                                <input type="text" value="smtp.gmail.com" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">發送者郵箱</label>
                                                <input type="email" value="<EMAIL>" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    啟用郵件通知
                                                </label>
                                            </div>
                                            <div>
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    啟用SMS通知
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 24px;">
                                    <!-- 安全設定 -->
                                    <div>
                                        <h4>🔒 安全設定</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">密碼最小長度</label>
                                                <input type="number" value="8" min="6" max="20" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">會話超時 (分鐘)</label>
                                                <input type="number" value="30" min="5" max="480" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    啟用雙因子認證
                                                </label>
                                            </div>
                                            <div>
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    記錄登入日誌
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 業務設定 -->
                                    <div>
                                        <h4>💼 業務設定</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">預設保固期 (月)</label>
                                                <input type="number" value="12" min="1" max="60" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">維修編號前綴</label>
                                                <input type="text" value="R" maxlength="5" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">庫存警告閾值</label>
                                                <input type="number" value="5" min="1" max="100" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div>
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    自動備份數據
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 系統日誌 -->
                                <div style="margin-top: 24px;">
                                    <h4>📋 系統日誌</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>時間</th>
                                                <th>操作類型</th>
                                                <th>用戶</th>
                                                <th>描述</th>
                                                <th>狀態</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>2024-01-20 14:30</td>
                                                <td><span class="tag tag-blue">系統設定</span></td>
                                                <td>系統管理員</td>
                                                <td>更新系統名稱設定</td>
                                                <td><span class="tag tag-green">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>2024-01-20 13:15</td>
                                                <td><span class="tag tag-orange">用戶管理</span></td>
                                                <td>系統管理員</td>
                                                <td>新增用戶：客服專員</td>
                                                <td><span class="tag tag-green">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>2024-01-20 12:45</td>
                                                <td><span class="tag tag-red">安全設定</span></td>
                                                <td>系統管理員</td>
                                                <td>修改密碼策略</td>
                                                <td><span class="tag tag-green">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>2024-01-20 11:20</td>
                                                <td><span class="tag tag-green">備份</span></td>
                                                <td>系統</td>
                                                <td>自動數據備份</td>
                                                <td><span class="tag tag-green">成功</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- SharePoint 配置區域 -->
                                <div id="sharepointConfig" style="margin-top: 24px; display: none;">
                                    <h4>☁️ SharePoint 整合配置</h4>
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <div style="background: #e6f7ff; border: 1px solid #91d5ff; border-radius: 4px; padding: 12px; margin-bottom: 16px;">
                                            <div style="color: #1890ff; font-weight: 500; margin-bottom: 4px;">📋 配置說明</div>
                                            <div style="font-size: 12px; color: #666;">
                                                請確保您有 SharePoint 網站的存取權限，並已建立相應的清單結構。
                                            </div>
                                        </div>

                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                            <div>
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">SharePoint 網站 URL *</label>
                                                <input type="url" id="sharepointSiteUrl" placeholder="https://yourcompany.sharepoint.com/sites/maintenance"
                                                       style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                            <div>
                                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">維修記錄清單名稱</label>
                                                <input type="text" id="sharepointListName" value="維修記錄"
                                                       style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            </div>
                                        </div>

                                        <div style="margin-top: 16px; display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                                            <button class="btn" onclick="testSharePointConnection()" style="background: #1890ff; color: white;">
                                                🔗 測試連線
                                            </button>
                                            <button class="btn" onclick="createSharePointLists()" style="background: #52c41a; color: white;">
                                                📋 建立清單結構
                                            </button>
                                            <button class="btn" onclick="migrateToSharePoint()" style="background: #fa8c16; color: white;">
                                                📤 遷移數據
                                            </button>
                                            <div id="connectionStatus" style="margin-left: auto;">
                                                <span class="tag tag-gray">未測試</span>
                                            </div>
                                        </div>

                                        <div style="margin-top: 16px;">
                                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">清單結構預覽：</div>
                                            <div style="background: white; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 11px; color: #666;">
                                                維修記錄: RepairId, CustomerName, Phone, SerialNumber, ProductName, Issue, ServiceStatus...<br>
                                                客戶資料: CustomerId, CustomerName, Phone, Email, Address<br>
                                                零件資料: PartId, PartName, Category, Stock, Price, Supplier<br>
                                                產品資料: ProductId, ProductName, Brand, Model, Category
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 數據管理區域 -->
                                <div style="margin-top: 24px;">
                                    <h4>💾 數據管理</h4>
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px;">
                                            <button class="btn" onclick="exportAllData()" style="background: #1890ff; color: white;">
                                                📤 匯出所有數據
                                            </button>
                                            <button class="btn" onclick="importAllData()" style="background: #52c41a; color: white;">
                                                📥 匯入數據
                                            </button>
                                            <button class="btn" onclick="clearAllData()" style="background: #f5222d; color: white;">
                                                🗑️清除所有數據
                                            </button>
                                            <button class="btn" onclick="syncData()" style="background: #722ed1; color: white;">
                                                🔄 同步數據
                                            </button>
                                        </div>

                                        <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e8e8e8;">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <div>
                                                    <div style="font-weight: 500; margin-bottom: 4px;">當前存儲狀態</div>
                                                    <div style="font-size: 12px; color: #666;" id="storageStatus">
                                                        模式: LocalStorage | 維修記錄: 0 筆 | 最後更新: --
                                                    </div>
                                                </div>
                                                <button class="btn" onclick="refreshStorageStatus()" style="background: #13c2c2; color: white;">
                                                    🔄 刷新狀態
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SharePoint頁面 -->
                    <div id="sharepointPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">☁️ SharePoint整合</h2>
                            <button class="btn btn-primary" onclick="testSharePointConnection()">測試連接</button>
                        </div>

                        <!-- SharePoint統計 -->
                        <div class="stats-grid" style="margin-bottom: 24px;">
                            <div class="stat-card">
                                <div class="stat-number">1,247</div>
                                <div class="stat-label">總文件數</div>
                                <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">+23 本週</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">1,189</div>
                                <div class="stat-label">已同步</div>
                                <div style="font-size: 12px; color: #1890ff; margin-top: 4px;">95.3%</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">58</div>
                                <div class="stat-label">待同步</div>
                                <div style="font-size: 12px; color: #fa8c16; margin-top: 4px;">需處理</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">15.6GB</div>
                                <div class="stat-label">存儲使用</div>
                                <div style="font-size: 12px; color: #52c41a; margin-top: 4px;">充足</div>
                            </div>
                        </div>

                        <!-- SharePoint選項卡 -->
                        <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 24px;">
                            <div style="border-bottom: 1px solid #f0f0f0; padding: 16px 24px;">
                                <div style="display: flex; gap: 24px;">
                                    <div class="tab-item active" data-tab="files" onclick="showSharePointTab('files')">文件管理</div>
                                    <div class="tab-item" data-tab="sync" onclick="showSharePointTab('sync')">文檔同步</div>
                                    <div class="tab-item" data-tab="templates" onclick="showSharePointTab('templates')">文檔模板</div>
                                    <div class="tab-item" data-tab="office365" onclick="showSharePointTab('office365')">Office 365整合</div>
                                </div>
                            </div>

                            <!-- 文件管理內容 -->
                            <div id="filesTab" class="tab-content" style="padding: 24px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3>📁 文件管理</h3>
                                    <div>
                                        <button class="btn" onclick="uploadFile()" style="margin-right: 8px;">上傳文件</button>
                                        <button class="btn btn-primary" onclick="createFolder()">新建文件夾</button>
                                    </div>
                                </div>

                                <!-- 文件瀏覽器 -->
                                <div style="border: 1px solid #f0f0f0; border-radius: 6px; margin-bottom: 16px;">
                                    <div style="background: #fafafa; padding: 12px 16px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center;">
                                        <span style="margin-right: 16px;">📂 /維修文檔/2024年</span>
                                        <input type="text" placeholder="搜尋文件..." style="flex: 1; padding: 6px 12px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                    </div>
                                    <div style="padding: 16px;">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>名稱</th>
                                                    <th>類型</th>
                                                    <th>大小</th>
                                                    <th>修改時間</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="fileTableBody">
                                                <tr>
                                                    <td>📁 維修報告</td>
                                                    <td>文件夾</td>
                                                    <td>-</td>
                                                    <td>2024-01-20</td>
                                                    <td>
                                                        <button class="btn" onclick="openFolder('維修報告')">開啟</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>📄 維修記錄_R2024-001.pdf</td>
                                                    <td>PDF</td>
                                                    <td>2.3 MB</td>
                                                    <td>2024-01-20 14:30</td>
                                                    <td>
                                                        <button class="btn" onclick="downloadFile('R2024-001.pdf')" style="margin-right: 8px;">下載</button>
                                                        <button class="btn" onclick="shareFile('R2024-001.pdf')">分享</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>📄 客戶合約_張先生.docx</td>
                                                    <td>Word</td>
                                                    <td>156 KB</td>
                                                    <td>2024-01-19 16:45</td>
                                                    <td>
                                                        <button class="btn" onclick="downloadFile('客戶合約_張先生.docx')" style="margin-right: 8px;">下載</button>
                                                        <button class="btn" onclick="shareFile('客戶合約_張先生.docx')">分享</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>📊 零件清單_2024Q1.xlsx</td>
                                                    <td>Excel</td>
                                                    <td>890 KB</td>
                                                    <td>2024-01-18 10:20</td>
                                                    <td>
                                                        <button class="btn" onclick="downloadFile('零件清單_2024Q1.xlsx')" style="margin-right: 8px;">下載</button>
                                                        <button class="btn" onclick="shareFile('零件清單_2024Q1.xlsx')">分享</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- 文件操作統計 -->
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                                    <div style="text-align: center; padding: 16px; background: #f6f6f6; border-radius: 6px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #1890ff;">342</div>
                                        <div style="font-size: 12px; color: #666;">PDF文件</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; background: #f6f6f6; border-radius: 6px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #52c41a;">156</div>
                                        <div style="font-size: 12px; color: #666;">Word文檔</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; background: #f6f6f6; border-radius: 6px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">89</div>
                                        <div style="font-size: 12px; color: #666;">Excel表格</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; background: #f6f6f6; border-radius: 6px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #722ed1;">45</div>
                                        <div style="font-size: 12px; color: #666;">其他文件</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 文檔同步內容 -->
                            <div id="syncTab" class="tab-content hidden" style="padding: 24px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3>🔄 文檔同步</h3>
                                    <div>
                                        <button class="btn" onclick="pauseSync()" style="margin-right: 8px;">暫停同步</button>
                                        <button class="btn btn-primary" onclick="startSync()">開始同步</button>
                                    </div>
                                </div>

                                <!-- 同步狀態 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <h4>📊 同步統計</h4>
                                        <div style="margin-top: 12px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>同步進度</span>
                                                <span style="font-weight: bold; color: #52c41a;">95.3%</span>
                                            </div>
                                            <div style="background: #f0f0f0; height: 8px; border-radius: 4px; margin-bottom: 12px;">
                                                <div style="background: #52c41a; height: 8px; width: 95.3%; border-radius: 4px;"></div>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>已同步文件</span>
                                                <span style="font-weight: bold;">1,189</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>待同步文件</span>
                                                <span style="font-weight: bold; color: #fa8c16;">58</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>同步錯誤</span>
                                                <span style="font-weight: bold; color: #f5222d;">3</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <h4>⚙️ 同步設定</h4>
                                        <div style="margin-top: 12px;">
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    自動同步
                                                </label>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    雙向同步
                                                </label>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: block; margin-bottom: 4px;">同步間隔</label>
                                                <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                    <option value="5">5分鐘</option>
                                                    <option value="15" selected>15分鐘</option>
                                                    <option value="30">30分鐘</option>
                                                    <option value="60">1小時</option>
                                                </select>
                                            </div>
                                            <button class="btn" onclick="saveSyncSettings()">保存設定</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 同步活動 -->
                                <div>
                                    <h4>📋 同步活動</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>時間</th>
                                                <th>文件名稱</th>
                                                <th>操作</th>
                                                <th>狀態</th>
                                                <th>詳情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>14:30</td>
                                                <td>維修記錄_R2024-001.pdf</td>
                                                <td><span class="tag tag-blue">上傳</span></td>
                                                <td><span class="tag tag-green">成功</span></td>
                                                <td>同步到SharePoint</td>
                                            </tr>
                                            <tr>
                                                <td>14:25</td>
                                                <td>客戶合約_張先生.docx</td>
                                                <td><span class="tag tag-orange">更新</span></td>
                                                <td><span class="tag tag-green">成功</span></td>
                                                <td>版本更新</td>
                                            </tr>
                                            <tr>
                                                <td>14:20</td>
                                                <td>零件清單_2024Q1.xlsx</td>
                                                <td><span class="tag tag-blue">下載</span></td>
                                                <td><span class="tag tag-red">失敗</span></td>
                                                <td>網路連接錯誤</td>
                                            </tr>
                                            <tr>
                                                <td>14:15</td>
                                                <td>維修報告模板.docx</td>
                                                <td><span class="tag tag-green">同步</span></td>
                                                <td><span class="tag tag-green">成功</span></td>
                                                <td>雙向同步完成</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 文檔模板內容 -->
                            <div id="templatesTab" class="tab-content hidden" style="padding: 24px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3>📄 文檔模板</h3>
                                    <button class="btn btn-primary" onclick="createTemplate()">新建模板</button>
                                </div>

                                <!-- 模板分類 -->
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-bottom: 24px;">
                                    <div style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px;">
                                        <h4>🔧 維修報告模板</h4>
                                        <p style="color: #666; font-size: 14px; margin-bottom: 16px;">標準化維修報告格式，包含檢測結果、維修過程和建議。</p>
                                        <div style="margin-bottom: 16px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>可用模板</span>
                                                <span style="font-weight: bold;">8個</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>本月使用</span>
                                                <span style="font-weight: bold; color: #52c41a;">156次</span>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn" onclick="viewTemplates('repair')" style="margin-right: 8px;">查看模板</button>
                                            <button class="btn btn-primary" onclick="useTemplate('repair')">使用模板</button>
                                        </div>
                                    </div>

                                    <div style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px;">
                                        <h4>💰 發票模板</h4>
                                        <p style="color: #666; font-size: 14px; margin-bottom: 16px;">標準發票格式，自動計算金額和稅額，符合財務規範。</p>
                                        <div style="margin-bottom: 16px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>可用模板</span>
                                                <span style="font-weight: bold;">5個</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>本月使用</span>
                                                <span style="font-weight: bold; color: #52c41a;">89次</span>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn" onclick="viewTemplates('invoice')" style="margin-right: 8px;">查看模板</button>
                                            <button class="btn btn-primary" onclick="useTemplate('invoice')">使用模板</button>
                                        </div>
                                    </div>

                                    <div style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px;">
                                        <h4>📋 報價單模板</h4>
                                        <p style="color: #666; font-size: 14px; margin-bottom: 16px;">維修服務報價單，包含零件費用、工時費用和總計。</p>
                                        <div style="margin-bottom: 16px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>可用模板</span>
                                                <span style="font-weight: bold;">6個</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>本月使用</span>
                                                <span style="font-weight: bold; color: #52c41a;">234次</span>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn" onclick="viewTemplates('quote')" style="margin-right: 8px;">查看模板</button>
                                            <button class="btn btn-primary" onclick="useTemplate('quote')">使用模板</button>
                                        </div>
                                    </div>

                                    <div style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px;">
                                        <h4>🏆 證書模板</h4>
                                        <p style="color: #666; font-size: 14px; margin-bottom: 16px;">維修完成證書和保固證書，提升服務專業度。</p>
                                        <div style="margin-bottom: 16px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>可用模板</span>
                                                <span style="font-weight: bold;">4個</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                <span>本月使用</span>
                                                <span style="font-weight: bold; color: #52c41a;">67次</span>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn" onclick="viewTemplates('certificate')" style="margin-right: 8px;">查看模板</button>
                                            <button class="btn btn-primary" onclick="useTemplate('certificate')">使用模板</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 最近使用的模板 -->
                                <div>
                                    <h4>📋 最近使用的模板</h4>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>模板名稱</th>
                                                <th>類型</th>
                                                <th>使用時間</th>
                                                <th>使用者</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>標準維修報告_v2.1</td>
                                                <td><span class="tag tag-blue">維修報告</span></td>
                                                <td>2024-01-20 14:30</td>
                                                <td>維修技師A</td>
                                                <td>
                                                    <button class="btn" onclick="useTemplate('repair', 'standard_v2.1')">再次使用</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>服務發票_標準版</td>
                                                <td><span class="tag tag-green">發票</span></td>
                                                <td>2024-01-20 13:45</td>
                                                <td>客服主管</td>
                                                <td>
                                                    <button class="btn" onclick="useTemplate('invoice', 'standard')">再次使用</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>維修報價單_詳細版</td>
                                                <td><span class="tag tag-orange">報價單</span></td>
                                                <td>2024-01-20 11:20</td>
                                                <td>客服專員</td>
                                                <td>
                                                    <button class="btn" onclick="useTemplate('quote', 'detailed')">再次使用</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 模板查看對話框 -->
                                <div id="viewTemplatesModal" class="modal hidden">
                                    <div class="modal-content" style="max-width: 900px;">
                                        <div class="modal-header">
                                            <h3 id="viewTemplatesTitle">📋 查看模板</h3>
                                            <button class="close-btn" onclick="hideViewTemplatesModal()">×</button>
                                        </div>
                                        <div style="padding: 20px;">
                                            <!-- 模板列表 -->
                                            <div id="templatesList">
                                                <!-- 模板內容將由JavaScript動態生成 -->
                                            </div>

                                            <!-- 模板預覽 -->
                                            <div id="templatePreview" class="hidden" style="margin-top: 20px;">
                                                <h4>📄 模板預覽</h4>
                                                <div id="templatePreviewContent" style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 16px; background: #fafafa; min-height: 300px;">
                                                    <!-- 預覽內容將由JavaScript動態生成 -->
                                                </div>
                                                <div style="margin-top: 16px;">
                                                    <button class="btn" onclick="hideTemplatePreview()">返回列表</button>
                                                    <button class="btn btn-primary" onclick="useCurrentTemplate()" style="margin-left: 8px;">使用此模板</button>
                                                    <button class="btn" onclick="downloadTemplate()" style="margin-left: 8px;">下載模板</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-actions">
                                            <button type="button" class="btn" onclick="hideViewTemplatesModal()">關閉</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Office 365整合內容 -->
                            <div id="office365Tab" class="tab-content hidden" style="padding: 24px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                    <h3>🏢 Office 365整合</h3>
                                    <button class="btn btn-primary" onclick="configureOffice365()">配置整合</button>
                                </div>

                                <!-- 整合狀態 -->
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; margin-bottom: 24px;">
                                    <div style="text-align: center; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🟢</div>
                                        <div style="font-weight: bold;">Outlook</div>
                                        <div style="color: #52c41a; font-size: 12px;">已連接</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🟢</div>
                                        <div style="font-weight: bold;">Teams</div>
                                        <div style="color: #52c41a; font-size: 12px;">已連接</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🟢</div>
                                        <div style="font-weight: bold;">OneDrive</div>
                                        <div style="color: #52c41a; font-size: 12px;">已連接</div>
                                    </div>
                                    <div style="text-align: center; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px;">
                                        <div style="font-size: 24px; margin-bottom: 8px;">🟡</div>
                                        <div style="font-weight: bold;">Power BI</div>
                                        <div style="color: #fa8c16; font-size: 12px;">設定中</div>
                                    </div>
                                </div>

                                <!-- 功能詳情 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                    <div>
                                        <h4>📧 Outlook整合</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px; margin-bottom: 16px;">
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    自動發送維修通知
                                                </label>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" checked style="margin-right: 8px;">
                                                    維修完成提醒
                                                </label>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <label style="display: flex; align-items: center;">
                                                    <input type="checkbox" style="margin-right: 8px;">
                                                    客戶滿意度調查
                                                </label>
                                            </div>
                                            <button class="btn" onclick="configureOutlook()">配置Outlook</button>
                                        </div>

                                        <h4>👥 Teams協作</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>維修團隊頻道</span>
                                                    <span style="color: #52c41a; font-weight: bold;">活躍</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>客服支援頻道</span>
                                                    <span style="color: #52c41a; font-weight: bold;">活躍</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>管理層頻道</span>
                                                    <span style="color: #1890ff; font-weight: bold;">正常</span>
                                                </div>
                                            </div>
                                            <button class="btn" onclick="openTeams()">開啟Teams</button>
                                        </div>
                                    </div>

                                    <div>
                                        <h4>☁️ OneDrive存儲</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px; margin-bottom: 16px;">
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>已使用空間</span>
                                                    <span style="font-weight: bold;">15.6 GB</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>可用空間</span>
                                                    <span style="font-weight: bold;">984.4 GB</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>同步狀態</span>
                                                    <span style="color: #52c41a; font-weight: bold;">最新</span>
                                                </div>
                                            </div>
                                            <button class="btn" onclick="openOneDrive()">開啟OneDrive</button>
                                        </div>

                                        <h4>📊 Power BI報表</h4>
                                        <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>維修儀表板</span>
                                                    <span style="color: #fa8c16; font-weight: bold;">設定中</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>財務報表</span>
                                                    <span style="color: #fa8c16; font-weight: bold;">設定中</span>
                                                </div>
                                            </div>
                                            <div style="margin-bottom: 12px;">
                                                <div style="display: flex; justify-content: space-between;">
                                                    <span>客戶分析</span>
                                                    <span style="color: #fa8c16; font-weight: bold;">設定中</span>
                                                </div>
                                            </div>
                                            <button class="btn" onclick="configurePowerBI()">配置Power BI</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 單點登入設定 -->
                                <div style="margin-top: 24px;">
                                    <h4>🔐 單點登入 (SSO)</h4>
                                    <div style="background: #f6f6f6; padding: 16px; border-radius: 6px;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                            <div>
                                                <div style="margin-bottom: 12px;">
                                                    <label style="display: block; margin-bottom: 4px;">Azure AD租戶ID</label>
                                                    <input type="text" value="12345678-1234-1234-1234-123456789012" readonly style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; background: #f9f9f9;">
                                                </div>
                                                <div style="margin-bottom: 12px;">
                                                    <label style="display: block; margin-bottom: 4px;">應用程式ID</label>
                                                    <input type="text" value="87654321-4321-4321-4321-210987654321" readonly style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; background: #f9f9f9;">
                                                </div>
                                            </div>
                                            <div>
                                                <div style="margin-bottom: 12px;">
                                                    <label style="display: flex; align-items: center;">
                                                        <input type="checkbox" checked style="margin-right: 8px;">
                                                        啟用SSO登入
                                                    </label>
                                                </div>
                                                <div style="margin-bottom: 12px;">
                                                    <label style="display: flex; align-items: center;">
                                                        <input type="checkbox" checked style="margin-right: 8px;">
                                                        自動用戶佈建
                                                    </label>
                                                </div>
                                                <button class="btn btn-primary" onclick="testSSO()">測試SSO連接</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系統測試頁面 -->
                    <div id="testingPage" class="page-content hidden">
                        <div class="page-header">
                            <h2 class="page-title">🧪 系統測試與優化</h2>
                            <button class="btn btn-primary">執行所有測試</button>
                        </div>

                        <!-- 測試統計 -->
                        <div class="stats-grid" style="margin-bottom: 24px;">
                            <div class="stat-card">
                                <div class="stat-number">152</div>
                                <div class="stat-label">總測試數</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">142</div>
                                <div class="stat-label">通過測試</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">10</div>
                                <div class="stat-label">失敗測試</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">93.4%</div>
                                <div class="stat-label">通過率</div>
                            </div>
                        </div>

                        <!-- 測試套件 -->
                        <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3>🔬 測試套件</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-top: 16px;">
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>🐛 功能測試</h4>
                                        <span class="tag tag-green">通過</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試所有業務功能的正確性</p>
                                    <div style="background: #e6f7ff; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #1890ff; height: 8px; width: 95%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 43</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 2</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 45</span>
                                    </div>
                                </div>
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>⚡ 性能測試</h4>
                                        <span class="tag tag-green">通過</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試系統性能和響應時間</p>
                                    <div style="background: #f6ffed; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #52c41a; height: 8px; width: 90%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 18</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 2</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 20</span>
                                    </div>
                                </div>
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>🛡️ 安全測試</h4>
                                        <span class="tag tag-green">通過</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試系統安全性和權限控制</p>
                                    <div style="background: #f6ffed; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #52c41a; height: 8px; width: 100%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 15</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 0</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 15</span>
                                    </div>
                                </div>
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>🔌 API測試</h4>
                                        <span class="tag tag-orange">部分失敗</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試所有API接口的功能和性能</p>
                                    <div style="background: #fff7e6; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #fa8c16; height: 8px; width: 85%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 30</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 5</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 35</span>
                                    </div>
                                </div>
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>🗄️ 數據庫測試</h4>
                                        <span class="tag tag-green">通過</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試數據庫操作和數據完整性</p>
                                    <div style="background: #f6ffed; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #52c41a; height: 8px; width: 96%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 24</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 1</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 25</span>
                                    </div>
                                </div>
                                <div style="border: 1px solid #f0f0f0; border-radius: 8px; padding: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <h4>📱 響應式測試</h4>
                                        <span class="tag tag-green">通過</span>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">測試不同設備和瀏覽器的兼容性</p>
                                    <div style="background: #f6ffed; height: 8px; border-radius: 4px; margin-bottom: 8px;">
                                        <div style="background: #52c41a; height: 8px; width: 100%; border-radius: 4px;"></div>
                                    </div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #52c41a;">通過: 12</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span style="color: #f5222d;">失敗: 0</span>
                                        <span style="margin: 0 8px; color: #d9d9d9;">|</span>
                                        <span>總計: 12</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 偵錯：全域錯誤捕獲
        window.addEventListener('error', function(e) {
            console.error('JavaScript 錯誤:', e.error);
            console.error('錯誤訊息:', e.message);
            console.error('錯誤位置:', e.filename, '行號:', e.lineno);
            alert('發生 JavaScript 錯誤：' + e.message);
        });

        // 偵錯：未處理的 Promise 錯誤
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未處理的 Promise 錯誤:', e.reason);
            alert('發生 Promise 錯誤：' + e.reason);
        });

        console.log('偵錯模式啟動');

        // 偵錯：手動測試函數
        window.debugLogin = function() {
            console.log('=== 手動登入測試 ===');
            const loginForm = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            console.log('表單:', loginForm);
            console.log('帳號輸入框:', emailInput);
            console.log('密碼輸入框:', passwordInput);

            if (emailInput) emailInput.value = 'admin';
            if (passwordInput) passwordInput.value = 'admin123';

            if (loginForm) {
                console.log('嘗試觸發表單提交');
                const event = new Event('submit', { bubbles: true, cancelable: true });
                loginForm.dispatchEvent(event);
            }
        };

        // 偵錯：檢查元素存在
        window.checkElements = function() {
            console.log('=== 元素檢查 ===');
            console.log('loginForm:', document.getElementById('loginForm'));
            console.log('email:', document.getElementById('email'));
            console.log('password:', document.getElementById('password'));
            console.log('accounts:', typeof accounts !== 'undefined' ? accounts : 'undefined');
        };

        // 測試帳號數據
        const accounts = {
            'admin': { password: 'admin123', name: '系統管理員', role: 'ADMIN' },
            'service': { password: 'service123', name: '客服人員', role: 'CUSTOMER_SERVICE' },
            'tech': { password: 'tech123', name: '維修技師', role: 'TECHNICIAN' },
            'viewer': { password: 'viewer123', name: '查詢用戶', role: 'VIEWER' },
            // 保持向後相容性，同時支援電子郵件格式
            '<EMAIL>': { password: 'admin123', name: '系統管理員', role: 'ADMIN' },
            '<EMAIL>': { password: 'service123', name: '客服人員', role: 'CUSTOMER_SERVICE' },
            '<EMAIL>': { password: 'tech123', name: '維修技師', role: 'TECHNICIAN' },
            '<EMAIL>': { password: 'viewer123', name: '查詢用戶', role: 'VIEWER' }
        };

        let currentUser = null;
        let currentPage = 'dashboard';

        // 偵錯：檢查頁面載入狀態
        console.log('JavaScript 開始執行');
        console.log('document.readyState:', document.readyState);

        // 確保 DOM 載入完成後再綁定事件
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded 事件觸發');

            // 偵錯：檢查所有相關元素
            const loginForm = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            console.log('登入表單元素:', loginForm);
            console.log('帳號輸入框:', emailInput);
            console.log('密碼輸入框:', passwordInput);
            console.log('accounts 物件:', accounts);

            if (loginForm) {
                console.log('找到登入表單，開始綁定事件');

                loginForm.addEventListener('submit', function(e) {
                    console.log('表單提交事件觸發');
                    e.preventDefault();

                    const email = emailInput ? emailInput.value : '';
                    const password = passwordInput ? passwordInput.value : '';

                    console.log('登入嘗試 - 帳號:', email, '密碼:', password);
                    console.log('帳號是否存在:', !!accounts[email]);

                    if (accounts[email] && accounts[email].password === password) {
                        console.log('登入驗證成功，呼叫 login 函數');
                        login(accounts[email]);
                    } else {
                        console.log('登入驗證失敗');
                        alert('帳號或密碼錯誤！請使用提供的測試帳號。');
                    }
                });

                // 偵錯：也為按鈕添加直接點擊事件
                const submitButton = loginForm.querySelector('button[type="submit"]');
                if (submitButton) {
                    console.log('找到提交按鈕，添加點擊事件');
                    submitButton.addEventListener('click', function(e) {
                        console.log('按鈕直接點擊事件觸發');
                        // 讓表單的 submit 事件處理
                    });
                } else {
                    console.error('找不到提交按鈕');
                }

                console.log('事件綁定完成');
            } else {
                console.error('找不到登入表單元素 #loginForm');
            }
        });

        // 偵錯：如果 DOM 已經載入完成
        if (document.readyState === 'loading') {
            console.log('DOM 正在載入中，等待 DOMContentLoaded');
        } else {
            console.log('DOM 已載入完成，立即執行初始化');
            // 如果 DOM 已經載入完成，立即執行
            setTimeout(function() {
                const loginForm = document.getElementById('loginForm');
                console.log('延遲檢查 - 登入表單:', loginForm);
            }, 100);
        }

        // 登入成功
        function login(user) {
            currentUser = user;
            document.getElementById('loginView').classList.add('hidden');
            document.getElementById('mainView').classList.remove('hidden');
            
            // 更新用戶資訊
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userRole').textContent = user.role;
            document.getElementById('userAvatar').textContent = user.name.charAt(0);
            
            // 顯示儀表板
            showPage('dashboard');
            
            alert(`登入成功！歡迎 ${user.name}`);
        }

        // 登出
        function logout() {
            if (confirm('確定要登出嗎？')) {
                currentUser = null;
                document.getElementById('mainView').classList.add('hidden');
                document.getElementById('loginView').classList.remove('hidden');
                
                // 重置表單
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = 'admin123';
            }
        }

        // 頁面切換
        function showPage(pageKey) {
            // 隱藏所有頁面
            const pages = document.querySelectorAll('[id$="Page"]');
            pages.forEach(page => page.classList.add('hidden'));
            
            // 顯示目標頁面
            const targetPage = document.getElementById(pageKey + 'Page');
            if (targetPage) {
                targetPage.classList.remove('hidden');
            }
            
            // 更新菜單狀態
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.page === pageKey) {
                    item.classList.add('active');
                }
            });
            
            currentPage = pageKey;
        }

        // 菜單點擊事件
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    const pageKey = this.dataset.page;
                    showPage(pageKey);
                });
            });

            // 報表選項卡切換事件
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabKey = this.dataset.tab;
                    showReportTab(tabKey);
                });
            });
        });

        // 報表選項卡切換
        function showReportTab(tabKey) {
            // 隱藏所有選項卡內容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.add('hidden'));

            // 顯示目標選項卡內容
            const targetTab = document.getElementById(tabKey + 'Tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
            }

            // 更新選項卡狀態
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.tab === tabKey) {
                    item.classList.add('active');
                }
            });
        }

        // 系統設定選項卡切換
        function showSettingsTab(tabKey) {
            // 隱藏所有選項卡內容
            const tabContents = document.querySelectorAll('#settingsPage .tab-content');
            tabContents.forEach(content => content.classList.add('hidden'));

            // 顯示目標選項卡內容
            const targetTab = document.getElementById(tabKey + 'Tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
            }

            // 更新選項卡狀態
            const tabItems = document.querySelectorAll('#settingsPage .tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.tab === tabKey) {
                    item.classList.add('active');
                }
            });
        }

        // 系統設定相關功能
        function showAddRoleForm() {
            alert('新增角色功能\n(這裡會開啟新增角色表單)');
        }

        function editRole(roleId) {
            alert(`編輯角色：${roleId}\n(這裡會開啟編輯角色表單)`);
        }

        function viewRolePermissions(roleId) {
            const roleNames = {
                admin: '系統管理員',
                service: '客服主管',
                tech: '維修技師',
                viewer: '查詢用戶'
            };
            alert(`查看 ${roleNames[roleId]} 權限\n(這裡會顯示詳細權限設定)`);
        }

        function saveSystemSettings() {
            alert('系統設定已保存！\n所有配置更改已生效。');
        }

        // SharePoint選項卡切換
        function showSharePointTab(tabKey) {
            // 隱藏所有選項卡內容
            const tabContents = document.querySelectorAll('#sharepointPage .tab-content');
            tabContents.forEach(content => content.classList.add('hidden'));

            // 顯示目標選項卡內容
            const targetTab = document.getElementById(tabKey + 'Tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
            }

            // 更新選項卡狀態
            const tabItems = document.querySelectorAll('#sharepointPage .tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.tab === tabKey) {
                    item.classList.add('active');
                }
            });
        }

        // SharePoint連接測試
        function testSharePointConnection() {
            alert('🔗 SharePoint連接測試\n\n✅ 連接成功！\n• 伺服器響應時間：245ms\n• 認證狀態：已驗證\n• 權限檢查：通過');
        }

        // 文件管理功能
        function uploadFile() {
            alert('📤 上傳文件\n\n選擇要上傳到SharePoint的文件...\n(這裡會開啟文件選擇對話框)');
        }

        function createFolder() {
            const folderName = prompt('📁 新建文件夾\n\n請輸入文件夾名稱：');
            if (folderName) {
                alert(`文件夾 "${folderName}" 已創建成功！`);
            }
        }

        function openFolder(folderName) {
            alert(`📂 開啟文件夾：${folderName}\n\n正在載入文件夾內容...`);
        }

        function downloadFile(fileName) {
            alert(`📥 下載文件：${fileName}\n\n文件下載已開始...`);
        }

        function shareFile(fileName) {
            alert(`🔗 分享文件：${fileName}\n\n分享連結已生成：\nhttps://sharepoint.com/shared/${fileName}\n\n連結已複製到剪貼板！`);
        }

        // 文檔同步功能
        function startSync() {
            alert('🔄 開始同步\n\n正在啟動文檔同步...\n• 檢查待同步文件\n• 建立連接\n• 開始同步作業');
        }

        function pauseSync() {
            alert('⏸️ 暫停同步\n\n同步作業已暫停\n當前進度已保存');
        }

        function saveSyncSettings() {
            alert('💾 同步設定已保存\n\n新的同步設定將在下次同步時生效');
        }

        // 文檔模板功能
        function createTemplate() {
            alert('📄 新建模板\n\n(這裡會開啟模板編輯器)\n• 選擇模板類型\n• 設計模板佈局\n• 配置動態欄位');
        }

        function viewTemplates(category) {
            const categoryNames = {
                repair: '維修報告',
                invoice: '發票',
                quote: '報價單',
                certificate: '證書'
            };

            // 模板數據
            const templates = {
                repair: [
                    {
                        id: 'repair_standard_v21',
                        name: '標準維修報告_v2.1',
                        description: '標準維修報告格式，包含完整的檢測流程和維修記錄',
                        version: 'v2.1',
                        lastModified: '2024-01-15',
                        author: '系統管理員',
                        downloads: 156,
                        size: '45 KB',
                        format: 'Word (.docx)'
                    },
                    {
                        id: 'repair_detailed_v10',
                        name: '詳細維修報告_v1.0',
                        description: '詳細維修報告，包含故障分析和解決方案建議',
                        version: 'v1.0',
                        lastModified: '2024-01-10',
                        author: '維修主管',
                        downloads: 89,
                        size: '52 KB',
                        format: 'Word (.docx)'
                    },
                    {
                        id: 'repair_quick_v15',
                        name: '快速維修報告_v1.5',
                        description: '簡化版維修報告，適用於簡單維修作業',
                        version: 'v1.5',
                        lastModified: '2024-01-08',
                        author: '維修技師',
                        downloads: 234,
                        size: '28 KB',
                        format: 'Word (.docx)'
                    }
                ],
                invoice: [
                    {
                        id: 'invoice_standard_v20',
                        name: '服務發票_標準版',
                        description: '標準服務發票格式，符合財務規範',
                        version: 'v2.0',
                        lastModified: '2024-01-12',
                        author: '財務主管',
                        downloads: 89,
                        size: '35 KB',
                        format: 'Excel (.xlsx)'
                    },
                    {
                        id: 'invoice_detailed_v11',
                        name: '詳細發票_含稅版',
                        description: '詳細發票格式，包含稅額計算和明細',
                        version: 'v1.1',
                        lastModified: '2024-01-05',
                        author: '會計專員',
                        downloads: 67,
                        size: '42 KB',
                        format: 'Excel (.xlsx)'
                    }
                ],
                quote: [
                    {
                        id: 'quote_standard_v30',
                        name: '標準報價單_v3.0',
                        description: '標準維修服務報價單格式',
                        version: 'v3.0',
                        lastModified: '2024-01-18',
                        author: '客服主管',
                        downloads: 234,
                        size: '38 KB',
                        format: 'Word (.docx)'
                    },
                    {
                        id: 'quote_detailed_v22',
                        name: '詳細報價單_含工時',
                        description: '詳細報價單，包含工時費用和零件明細',
                        version: 'v2.2',
                        lastModified: '2024-01-14',
                        author: '業務專員',
                        downloads: 145,
                        size: '48 KB',
                        format: 'Excel (.xlsx)'
                    }
                ],
                certificate: [
                    {
                        id: 'cert_completion_v10',
                        name: '維修完成證書',
                        description: '維修完成證書，證明設備已完成維修',
                        version: 'v1.0',
                        lastModified: '2024-01-16',
                        author: '品質主管',
                        downloads: 67,
                        size: '25 KB',
                        format: 'Word (.docx)'
                    },
                    {
                        id: 'cert_warranty_v12',
                        name: '保固證書_標準版',
                        description: '標準保固證書格式',
                        version: 'v1.2',
                        lastModified: '2024-01-11',
                        author: '客服主管',
                        downloads: 45,
                        size: '22 KB',
                        format: 'PDF (.pdf)'
                    }
                ]
            };

            // 設定標題
            document.getElementById('viewTemplatesTitle').textContent = `📋 查看${categoryNames[category]}模板`;

            // 生成模板列表
            const templatesList = document.getElementById('templatesList');
            const categoryTemplates = templates[category] || [];

            let listHTML = `
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4>${categoryNames[category]}模板 (${categoryTemplates.length}個)</h4>
                        <div>
                            <button class="btn" onclick="sortTemplates('name')">按名稱排序</button>
                            <button class="btn" onclick="sortTemplates('downloads')" style="margin-left: 8px;">按使用量排序</button>
                        </div>
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>模板名稱</th>
                            <th>版本</th>
                            <th>格式</th>
                            <th>大小</th>
                            <th>使用次數</th>
                            <th>最後修改</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            categoryTemplates.forEach(template => {
                listHTML += `
                    <tr>
                        <td>
                            <div>
                                <strong>${template.name}</strong>
                                <div style="font-size: 12px; color: #666; margin-top: 4px;">${template.description}</div>
                            </div>
                        </td>
                        <td><span class="tag tag-blue">${template.version}</span></td>
                        <td>${template.format}</td>
                        <td>${template.size}</td>
                        <td><span style="color: #52c41a; font-weight: bold;">${template.downloads}</span></td>
                        <td>${template.lastModified}</td>
                        <td>
                            <button class="btn" onclick="previewTemplate('${template.id}')" style="margin-right: 8px;">預覽</button>
                            <button class="btn btn-primary" onclick="useTemplateById('${template.id}')">使用</button>
                        </td>
                    </tr>
                `;
            });

            listHTML += `
                    </tbody>
                </table>
            `;

            templatesList.innerHTML = listHTML;

            // 顯示模態對話框
            document.getElementById('viewTemplatesModal').classList.remove('hidden');

            // 隱藏預覽區域
            document.getElementById('templatePreview').classList.add('hidden');
        }

        function hideViewTemplatesModal() {
            document.getElementById('viewTemplatesModal').classList.add('hidden');
            document.getElementById('templatePreview').classList.add('hidden');
        }

        function previewTemplate(templateId) {
            // 模板預覽內容
            const templatePreviews = {
                'repair_standard_v21': {
                    name: '標準維修報告_v2.1',
                    content: `
                        <h3 style="text-align: center; color: #1890ff;">維修記錄報告</h3>
                        <hr>
                        <div style="margin: 20px 0;">
                            <p><strong>維修編號：</strong> [自動填入]</p>
                            <p><strong>客戶姓名：</strong> [自動填入]</p>
                            <p><strong>聯絡電話：</strong> [自動填入]</p>
                            <p><strong>設備型號：</strong> [自動填入]</p>
                            <p><strong>維修日期：</strong> [自動填入]</p>
                        </div>
                        <h4>故障描述</h4>
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 60px;">
                            [客戶描述的故障現象]
                        </div>
                        <h4>檢測結果</h4>
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 60px;">
                            [技師檢測發現的問題]
                        </div>
                        <h4>維修過程</h4>
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 80px;">
                            [詳細的維修步驟和更換零件]
                        </div>
                        <h4>測試結果</h4>
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 40px;">
                            [維修後的測試結果]
                        </div>
                        <div style="margin-top: 30px;">
                            <p><strong>維修技師：</strong> ________________</p>
                            <p><strong>客戶簽名：</strong> ________________</p>
                            <p><strong>完成日期：</strong> ________________</p>
                        </div>
                    `
                },
                'invoice_standard_v20': {
                    name: '服務發票_標準版',
                    content: `
                        <h3 style="text-align: center; color: #52c41a;">服務發票</h3>
                        <hr>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                            <div>
                                <p><strong>發票編號：</strong> [自動生成]</p>
                                <p><strong>開立日期：</strong> [自動填入]</p>
                                <p><strong>客戶編號：</strong> [自動填入]</p>
                            </div>
                            <div>
                                <p><strong>客戶名稱：</strong> [自動填入]</p>
                                <p><strong>聯絡地址：</strong> [自動填入]</p>
                                <p><strong>統一編號：</strong> [自動填入]</p>
                            </div>
                        </div>
                        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                            <thead>
                                <tr style="background: #f0f0f0;">
                                    <th style="border: 1px solid #ddd; padding: 8px;">項目</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">數量</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">單價</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">金額</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[服務項目]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[數量]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[單價]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[小計]</td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="text-align: right; margin: 20px 0;">
                            <p><strong>小計：</strong> NT$ [自動計算]</p>
                            <p><strong>稅額：</strong> NT$ [自動計算]</p>
                            <p><strong>總計：</strong> NT$ [自動計算]</p>
                        </div>
                    `
                },
                'quote_standard_v30': {
                    name: '標準報價單_v3.0',
                    content: `
                        <h3 style="text-align: center; color: #fa8c16;">維修服務報價單</h3>
                        <hr>
                        <div style="margin: 20px 0;">
                            <p><strong>報價編號：</strong> [自動生成]</p>
                            <p><strong>報價日期：</strong> [自動填入]</p>
                            <p><strong>客戶名稱：</strong> [自動填入]</p>
                            <p><strong>設備型號：</strong> [自動填入]</p>
                            <p><strong>有效期限：</strong> [報價後30天]</p>
                        </div>
                        <h4>維修項目明細</h4>
                        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                            <thead>
                                <tr style="background: #f0f0f0;">
                                    <th style="border: 1px solid #ddd; padding: 8px;">維修項目</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">工時</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">工時費</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">零件費</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">小計</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[維修項目描述]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[預估工時]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[工時費用]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[零件費用]</td>
                                    <td style="border: 1px solid #ddd; padding: 8px;">[項目小計]</td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="text-align: right; margin: 20px 0;">
                            <p><strong>總工時：</strong> [總計工時] 小時</p>
                            <p><strong>總費用：</strong> NT$ [總計金額]</p>
                        </div>
                        <div style="margin-top: 30px; font-size: 12px; color: #666;">
                            <p>※ 本報價單有效期限為30天</p>
                            <p>※ 實際費用可能因現場狀況而有所調整</p>
                        </div>
                    `
                },
                'cert_completion_v10': {
                    name: '維修完成證書',
                    content: `
                        <div style="text-align: center; border: 2px solid #1890ff; padding: 30px; margin: 20px;">
                            <h2 style="color: #1890ff; margin-bottom: 30px;">維修完成證書</h2>
                            <div style="margin: 30px 0; text-align: left;">
                                <p><strong>證書編號：</strong> [自動生成]</p>
                                <p><strong>客戶姓名：</strong> [自動填入]</p>
                                <p><strong>設備型號：</strong> [自動填入]</p>
                                <p><strong>序列號碼：</strong> [自動填入]</p>
                                <p><strong>維修項目：</strong> [維修內容]</p>
                                <p><strong>完成日期：</strong> [自動填入]</p>
                            </div>
                            <div style="margin: 40px 0;">
                                <h4 style="color: #52c41a;">茲證明上述設備已完成維修作業</h4>
                                <p>經本公司專業技師檢測，設備功能已恢復正常</p>
                                <p>並提供 <strong>[保固期限]</strong> 保固服務</p>
                            </div>
                            <div style="margin-top: 50px;">
                                <p><strong>維修技師：</strong> ________________</p>
                                <p><strong>品質主管：</strong> ________________</p>
                                <p><strong>公司印章：</strong> ________________</p>
                            </div>
                        </div>
                    `
                }
            };

            const preview = templatePreviews[templateId];
            if (preview) {
                document.getElementById('templatePreviewContent').innerHTML = preview.content;
                document.getElementById('templatePreview').classList.remove('hidden');

                // 設定當前預覽的模板ID
                window.currentPreviewTemplateId = templateId;
            }
        }

        function hideTemplatePreview() {
            document.getElementById('templatePreview').classList.add('hidden');
        }

        function useCurrentTemplate() {
            if (window.currentPreviewTemplateId) {
                useTemplateById(window.currentPreviewTemplateId);
            }
        }

        function useTemplateById(templateId) {
            alert(`📄 使用模板：${templateId}\n\n正在基於模板創建新文檔...\n• 載入模板格式\n• 填入預設數據\n• 開啟編輯器`);
            hideViewTemplatesModal();
        }

        function downloadTemplate() {
            if (window.currentPreviewTemplateId) {
                alert(`📥 下載模板：${window.currentPreviewTemplateId}\n\n模板文件下載已開始...\n文件將保存到下載文件夾`);
            }
        }

        function sortTemplates(sortBy) {
            alert(`📊 排序模板\n\n按${sortBy === 'name' ? '名稱' : '使用量'}排序模板列表...`);
        }

        function useTemplate(category, templateId = null) {
            const categoryNames = {
                repair: '維修報告',
                invoice: '發票',
                quote: '報價單',
                certificate: '證書'
            };

            if (templateId) {
                alert(`📄 使用模板：${templateId}\n\n正在基於模板創建新文檔...`);
            } else {
                alert(`📄 使用${categoryNames[category]}模板\n\n請選擇要使用的模板：\n(這裡會顯示模板選擇對話框)`);
            }
        }

        // Office 365整合功能
        function configureOffice365() {
            alert('🏢 配置Office 365整合\n\n(這裡會開啟配置嚮導)\n• Azure AD設定\n• 應用程式註冊\n• 權限配置');
        }

        function configureOutlook() {
            alert('📧 配置Outlook整合\n\n• 設定SMTP伺服器\n• 配置郵件模板\n• 設定自動通知規則');
        }

        function openTeams() {
            alert('👥 開啟Microsoft Teams\n\n正在啟動Teams應用...\n(實際環境中會開啟Teams應用)');
        }

        function openOneDrive() {
            alert('☁️ 開啟OneDrive\n\n正在開啟OneDrive網頁版...\n(實際環境中會開啟OneDrive)');
        }

        function configurePowerBI() {
            alert('📊 配置Power BI\n\n• 連接數據源\n• 設計報表\n• 配置自動刷新');
        }

        function testSSO() {
            alert('🔐 測試SSO連接\n\n✅ SSO測試成功！\n• Azure AD連接：正常\n• 用戶認證：通過\n• 權限同步：完成');
        }

        // 用戶管理功能
        function showAddUserForm() {
            document.getElementById('addUserForm').classList.remove('hidden');
        }

        function hideAddUserForm() {
            document.getElementById('addUserForm').classList.add('hidden');
            document.getElementById('userForm').reset();
        }

        function submitUserForm(event) {
            event.preventDefault();

            const formData = {
                name: document.getElementById('userNameInput').value,
                email: document.getElementById('userEmailInput').value,
                role: document.getElementById('userRoleInput').value,
                department: document.getElementById('userDepartmentInput').value,
                password: document.getElementById('userPasswordInput').value,
                status: document.getElementById('userStatusInput').value
            };

            // 驗證必填欄位
            if (!formData.name || !formData.email || !formData.role || !formData.password) {
                alert('請填寫所有必填欄位！');
                return;
            }

            // 驗證郵箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formData.email)) {
                alert('請輸入有效的電子郵箱地址！');
                return;
            }

            // 驗證密碼長度
            if (formData.password.length < 6) {
                alert('密碼長度至少需要6個字符！');
                return;
            }

            const roleNames = {
                admin: '系統管理員',
                service: '客服主管',
                tech: '維修技師',
                viewer: '查詢用戶'
            };

            alert(`✅ 用戶新增成功！\n\n用戶名稱：${formData.name}\n電子郵箱：${formData.email}\n角色：${roleNames[formData.role]}\n部門：${formData.department || '未指定'}\n狀態：${formData.status === 'active' ? '啟用' : '停用'}\n\n初始密碼已設定，請提醒用戶首次登入後更改密碼。`);

            hideAddUserForm();
        }

        function editUser(userId) {
            const users = {
                admin: {
                    name: '系統管理員',
                    email: '<EMAIL>',
                    role: 'admin',
                    department: 'IT部門',
                    status: 'active'
                },
                service: {
                    name: '客服主管',
                    email: '<EMAIL>',
                    role: 'service',
                    department: '客服部門',
                    status: 'active'
                },
                tech: {
                    name: '維修技師',
                    email: '<EMAIL>',
                    role: 'tech',
                    department: '維修部門',
                    status: 'active'
                }
            };

            const user = users[userId];
            if (user) {
                // 填充編輯表單
                document.getElementById('editUserId').value = userId;
                document.getElementById('editUserNameInput').value = user.name;
                document.getElementById('editUserEmailInput').value = user.email;
                document.getElementById('editUserRoleInput').value = user.role;
                document.getElementById('editUserDepartmentInput').value = user.department;
                document.getElementById('editUserStatusInput').value = user.status;

                // 顯示編輯表單
                document.getElementById('editUserForm').classList.remove('hidden');
            }
        }

        function hideEditUserForm() {
            document.getElementById('editUserForm').classList.add('hidden');
        }

        function submitEditUserForm(event) {
            event.preventDefault();

            const userId = document.getElementById('editUserId').value;
            const formData = {
                name: document.getElementById('editUserNameInput').value,
                email: document.getElementById('editUserEmailInput').value,
                role: document.getElementById('editUserRoleInput').value,
                department: document.getElementById('editUserDepartmentInput').value,
                status: document.getElementById('editUserStatusInput').value
            };

            const roleNames = {
                admin: '系統管理員',
                service: '客服主管',
                tech: '維修技師',
                viewer: '查詢用戶'
            };

            alert(`✅ 用戶資料更新成功！\n\n用戶ID：${userId}\n用戶名稱：${formData.name}\n電子郵箱：${formData.email}\n角色：${roleNames[formData.role]}\n部門：${formData.department}\n狀態：${formData.status === 'active' ? '啟用' : '停用'}`);

            hideEditUserForm();
        }

        function resetPassword(userId) {
            const users = {
                admin: '系統管理員',
                service: '客服主管',
                tech: '維修技師'
            };

            const userName = users[userId];
            if (userName) {
                const confirmed = confirm(`🔑 重置密碼確認\n\n確定要重置 "${userName}" 的密碼嗎？\n\n新密碼將會發送到用戶的註冊郵箱。`);

                if (confirmed) {
                    const newPassword = 'Temp' + Math.random().toString(36).substr(2, 6);
                    alert(`✅ 密碼重置成功！\n\n用戶：${userName}\n新密碼：${newPassword}\n\n新密碼已發送到用戶郵箱，請提醒用戶登入後立即更改密碼。`);
                }
            }
        }

        // 角色權限管理功能
        function showAddRoleForm() {
            document.getElementById('addRoleForm').classList.remove('hidden');
        }

        function hideAddRoleForm() {
            document.getElementById('addRoleForm').classList.add('hidden');
            document.getElementById('roleForm').reset();
            // 清除所有權限選擇
            const checkboxes = document.querySelectorAll('#addRoleForm input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        }

        function submitRoleForm(event) {
            event.preventDefault();

            const formData = {
                name: document.getElementById('roleNameInput').value,
                description: document.getElementById('roleDescriptionInput').value,
                color: document.getElementById('roleColorInput').value,
                permissions: []
            };

            // 收集權限設定
            const permissionIds = ['repair', 'customer', 'product', 'parts', 'reports', 'system'];
            permissionIds.forEach(id => {
                if (document.getElementById(`perm_${id}`).checked) {
                    formData.permissions.push(id);
                }
            });

            // 驗證必填欄位
            if (!formData.name || !formData.description) {
                alert('請填寫角色名稱和描述！');
                return;
            }

            if (formData.permissions.length === 0) {
                alert('請至少選擇一個權限！');
                return;
            }

            const permissionNames = {
                repair: '維修記錄管理',
                customer: '客戶管理',
                product: '產品管理',
                parts: '零件管理',
                reports: '統計報表',
                system: '系統設定'
            };

            const permissionList = formData.permissions.map(p => permissionNames[p]).join('、');

            alert(`✅ 角色新增成功！\n\n角色名稱：${formData.name}\n角色描述：${formData.description}\n標籤顏色：${formData.color}\n權限範圍：${permissionList}\n\n新角色已添加到系統中，可以開始分配給用戶使用。`);

            hideAddRoleForm();
        }

        function editRole(roleId) {
            const roles = {
                admin: {
                    name: '系統管理員',
                    description: '擁有系統所有權限',
                    color: 'red',
                    permissions: ['repair', 'customer', 'product', 'parts', 'reports', 'system']
                },
                service: {
                    name: '客服主管',
                    description: '客戶服務和維修管理',
                    color: 'orange',
                    permissions: ['repair', 'customer', 'product', 'parts', 'reports']
                },
                tech: {
                    name: '維修技師',
                    description: '維修記錄和零件管理',
                    color: 'blue',
                    permissions: ['repair', 'parts']
                },
                viewer: {
                    name: '查詢用戶',
                    description: '僅查看權限',
                    color: 'green',
                    permissions: ['repair', 'customer', 'product', 'parts', 'reports']
                }
            };

            const role = roles[roleId];
            if (role) {
                // 填充編輯表單
                document.getElementById('editRoleId').value = roleId;
                document.getElementById('editRoleNameInput').value = role.name;
                document.getElementById('editRoleDescriptionInput').value = role.description;
                document.getElementById('editRoleColorInput').value = role.color;

                // 設定權限選擇
                const permissionIds = ['repair', 'customer', 'product', 'parts', 'reports', 'system'];
                permissionIds.forEach(id => {
                    const checkbox = document.getElementById(`edit_perm_${id}`);
                    checkbox.checked = role.permissions.includes(id);
                });

                // 顯示編輯表單
                document.getElementById('editRoleForm').classList.remove('hidden');
            }
        }

        function hideEditRoleForm() {
            document.getElementById('editRoleForm').classList.add('hidden');
        }

        function submitEditRoleForm(event) {
            event.preventDefault();

            const roleId = document.getElementById('editRoleId').value;
            const formData = {
                name: document.getElementById('editRoleNameInput').value,
                description: document.getElementById('editRoleDescriptionInput').value,
                color: document.getElementById('editRoleColorInput').value,
                permissions: []
            };

            // 收集權限設定
            const permissionIds = ['repair', 'customer', 'product', 'parts', 'reports', 'system'];
            permissionIds.forEach(id => {
                if (document.getElementById(`edit_perm_${id}`).checked) {
                    formData.permissions.push(id);
                }
            });

            const permissionNames = {
                repair: '維修記錄管理',
                customer: '客戶管理',
                product: '產品管理',
                parts: '零件管理',
                reports: '統計報表',
                system: '系統設定'
            };

            const permissionList = formData.permissions.map(p => permissionNames[p]).join('、');

            alert(`✅ 角色更新成功！\n\n角色ID：${roleId}\n角色名稱：${formData.name}\n角色描述：${formData.description}\n標籤顏色：${formData.color}\n權限範圍：${permissionList}`);

            hideEditRoleForm();
        }

        function viewRolePermissions(roleId) {
            const roles = {
                admin: {
                    name: '系統管理員',
                    permissions: {
                        '維修記錄管理': '完整權限 - 新增、編輯、刪除、查看',
                        '客戶管理': '完整權限 - 新增、編輯、刪除、查看',
                        '產品管理': '完整權限 - 新增、編輯、刪除、查看',
                        '零件管理': '完整權限 - 新增、編輯、刪除、查看',
                        '統計報表': '完整權限 - 查看所有報表、匯出數據',
                        '系統設定': '完整權限 - 系統配置、用戶管理、角色管理'
                    }
                },
                service: {
                    name: '客服主管',
                    permissions: {
                        '維修記錄管理': '完整權限 - 新增、編輯、刪除、查看',
                        '客戶管理': '完整權限 - 新增、編輯、刪除、查看',
                        '產品管理': '編輯權限 - 編輯、查看（無刪除權限）',
                        '零件管理': '編輯權限 - 編輯、查看（無刪除權限）',
                        '統計報表': '完整權限 - 查看所有報表、匯出數據',
                        '系統設定': '無權限'
                    }
                },
                tech: {
                    name: '維修技師',
                    permissions: {
                        '維修記錄管理': '編輯權限 - 新增、編輯、查看',
                        '客戶管理': '查看權限 - 僅查看客戶資訊',
                        '產品管理': '查看權限 - 僅查看產品資訊',
                        '零件管理': '完整權限 - 新增、編輯、刪除、查看',
                        '統計報表': '查看權限 - 查看基本報表',
                        '系統設定': '無權限'
                    }
                },
                viewer: {
                    name: '查詢用戶',
                    permissions: {
                        '維修記錄管理': '查看權限 - 僅查看維修記錄',
                        '客戶管理': '查看權限 - 僅查看客戶資訊',
                        '產品管理': '查看權限 - 僅查看產品資訊',
                        '零件管理': '查看權限 - 僅查看零件資訊',
                        '統計報表': '查看權限 - 查看基本報表',
                        '系統設定': '無權限'
                    }
                }
            };

            const role = roles[roleId];
            if (role) {
                let content = `<h4>🛡️ ${role.name} 權限詳情</h4><div style="margin-top: 16px;">`;

                Object.entries(role.permissions).forEach(([module, permission]) => {
                    const level = permission.includes('完整權限') ? 'green' :
                                 permission.includes('編輯權限') ? 'blue' :
                                 permission.includes('查看權限') ? 'orange' : 'red';

                    content += `
                        <div style="margin-bottom: 12px; padding: 12px; border: 1px solid #f0f0f0; border-radius: 6px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                <strong>${module}</strong>
                                <span class="tag tag-${level}">${permission.split(' - ')[0]}</span>
                            </div>
                            <div style="font-size: 14px; color: #666;">${permission.split(' - ')[1] || ''}</div>
                        </div>
                    `;
                });

                content += '</div>';

                document.getElementById('rolePermissionsContent').innerHTML = content;
                document.getElementById('rolePermissionsForm').classList.remove('hidden');
            }
        }

        function hideRolePermissionsForm() {
            document.getElementById('rolePermissionsForm').classList.add('hidden');
        }

        // 顯示查看維修記錄模態框
        function showViewRepairModal(record) {
            document.getElementById('viewRepairId').textContent = record.id;
            document.getElementById('viewRepairDate').textContent = record.date;
            document.getElementById('viewCustomerName').textContent = record.customer;
            document.getElementById('viewCustomerPhone').textContent = record.phone;
            document.getElementById('viewProductSerial').textContent = record.serialNumber;
            document.getElementById('viewProductName').textContent = record.product;
            document.getElementById('viewProductModel').textContent = record.model;
            document.getElementById('viewIssueDescription').textContent = record.issue;

            // 顯示維保狀態
            const serviceStatus = serviceStatusConfig[record.serviceStatus];
            document.getElementById('viewServiceStatus').innerHTML = `<span class="tag ${serviceStatus.class}">${serviceStatus.text}</span>`;

            // 顯示維修記錄
            const repairRecord = repairRecordConfig[record.repairRecord];
            document.getElementById('viewRepairRecord').innerHTML = `<span class="tag ${repairRecord.class}">${repairRecord.text}</span>`;

            // 顯示測試結果
            const testResult = testResultConfig[record.testResult];
            document.getElementById('viewTestResult').innerHTML = `<span class="tag ${testResult.class}">${testResult.text}</span>`;

            document.getElementById('viewTechnician').textContent = record.technician;

            document.getElementById('viewRepairModal').classList.remove('hidden');
        }

        // 隱藏查看維修記錄模態框
        function hideViewRepairModal() {
            document.getElementById('viewRepairModal').classList.add('hidden');
        }

        // 從查看模式切換到編輯模式
        function editFromView() {
            const repairId = document.getElementById('viewRepairId').textContent;
            hideViewRepairModal();
            const record = repairRecords.find(r => r.id === repairId);
            if (record) {
                showEditRepairModal(record);
            }
        }

        // 顯示編輯維修記錄模態框
        function showEditRepairModal(record) {
            document.getElementById('editRepairId').value = record.id;
            document.getElementById('editRepairDate').value = record.date;
            document.getElementById('editCustomerName').value = record.customer;
            document.getElementById('editCustomerPhone').value = record.phone;
            document.getElementById('editProductSerial').value = record.serialNumber;
            document.getElementById('editProductName').value = record.product;
            document.getElementById('editProductModel').value = record.model;
            document.getElementById('editIssueDescription').value = record.issue;
            document.getElementById('editServiceStatus').value = record.serviceStatus;
            document.getElementById('editRepairRecord').value = record.repairRecord;
            document.getElementById('editTestResult').value = record.testResult;
            document.getElementById('editTechnician').value = record.technician;

            // 更新維修記錄選項
            updateEditRepairRecordOptions();

            document.getElementById('editRepairModal').classList.remove('hidden');
        }

        // 隱藏編輯維修記錄模態框
        function hideEditRepairModal() {
            document.getElementById('editRepairModal').classList.add('hidden');
            document.getElementById('editRepairForm').reset();
        }

        // 更新編輯表單的維修記錄選項
        function updateEditRepairRecordOptions() {
            const serviceStatus = document.getElementById('editServiceStatus').value;
            const repairRecordSelect = document.getElementById('editRepairRecord');
            const currentValue = repairRecordSelect.value;

            // 清空現有選項
            repairRecordSelect.innerHTML = '';

            if (!serviceStatus) {
                repairRecordSelect.innerHTML = '<option value="">請先選擇維保狀態</option>';
                return;
            }

            // 添加預設選項
            repairRecordSelect.innerHTML = '<option value="">請選擇維修記錄</option>';

            // 根據維保狀態添加對應的維修記錄選項
            repairRecordSelect.innerHTML += '<option value="adjustment">調整</option>';
            repairRecordSelect.innerHTML += '<option value="motor_replacement">換馬達</option>';

            // 恢復原來的值
            if (currentValue) {
                repairRecordSelect.value = currentValue;
            }
        }

        // 提交編輯維修記錄表單
        function submitEditRepairForm(event) {
            event.preventDefault();

            const repairId = document.getElementById('editRepairId').value;
            const formData = {
                id: repairId,
                date: document.getElementById('editRepairDate').value,
                customer: document.getElementById('editCustomerName').value,
                phone: document.getElementById('editCustomerPhone').value,
                serialNumber: document.getElementById('editProductSerial').value.toUpperCase(),
                product: document.getElementById('editProductName').value,
                model: document.getElementById('editProductModel').value || '未指定',
                issue: document.getElementById('editIssueDescription').value,
                serviceStatus: document.getElementById('editServiceStatus').value,
                repairRecord: document.getElementById('editRepairRecord').value,
                testResult: document.getElementById('editTestResult').value,
                technician: document.getElementById('editTechnician').value || '未指派'
            };

            // 驗證必填欄位
            if (!formData.customer || !formData.phone || !formData.serialNumber || !formData.product || !formData.issue || !formData.serviceStatus || !formData.repairRecord) {
                alert('請填寫所有必填欄位！');
                return;
            }

            // 驗證產品序號格式
            const serialRegex = /^SN\d{9}$/;
            if (!serialRegex.test(formData.serialNumber)) {
                alert('產品序號格式不正確！\n正確格式：SN + 9位數字 (例如：SN240120001)');
                return;
            }

            // 驗證電話格式
            const phoneRegex = /^09\d{8}$|^\d{2,4}-\d{6,8}$/;
            if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
                alert('請輸入正確的電話號碼格式！');
                return;
            }

            // 檢查產品序號是否重複（排除自己）
            const existingSerial = repairRecords.find(record =>
                record.serialNumber === formData.serialNumber && record.id !== repairId
            );
            if (existingSerial) {
                const confirmed = confirm(`產品序號 ${formData.serialNumber} 已存在於維修記錄 ${existingSerial.id} 中。\n\n這可能是回修案件，是否繼續儲存變更？`);
                if (!confirmed) {
                    return;
                }
            }

            // 更新記錄
            const recordIndex = repairRecords.findIndex(r => r.id === repairId);
            if (recordIndex !== -1) {
                repairRecords[recordIndex] = formData;
                filteredRecords = [...repairRecords];

                // 重新渲染表格
                renderRepairTable();
                updateFilterStats(repairRecords.length, repairRecords.length);

                // 隱藏表單
                hideEditRepairModal();

                // 顯示成功訊息
                alert(`維修記錄 ${formData.id} 更新成功！\n客戶：${formData.customer}\n產品序號：${formData.serialNumber}\n產品：${formData.product}`);
            }
        }

        // 二級下拉菜單功能
        function updateRepairRecordOptions() {
            const serviceStatus = document.getElementById('serviceStatus').value;
            const repairRecordSelect = document.getElementById('repairRecord');

            // 清空現有選項
            repairRecordSelect.innerHTML = '';

            if (!serviceStatus) {
                repairRecordSelect.innerHTML = '<option value="">請先選擇維保狀態</option>';
                return;
            }

            // 添加預設選項
            repairRecordSelect.innerHTML = '<option value="">請選擇維修記錄</option>';

            // 根據維保狀態添加對應的維修記錄選項
            if (serviceStatus === 'complaint') {
                // 客訴相關的維修記錄
                repairRecordSelect.innerHTML += '<option value="adjustment">調整</option>';
                repairRecordSelect.innerHTML += '<option value="motor_replacement">換馬達</option>';
            } else if (serviceStatus === 'maintenance') {
                // 維保相關的維修記錄
                repairRecordSelect.innerHTML += '<option value="adjustment">調整</option>';
                repairRecordSelect.innerHTML += '<option value="motor_replacement">換馬達</option>';
            }
        }

        // 儀表板功能
        function refreshDashboard() {
            // 模擬重新整理
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '重新整理中...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                alert('儀表板數據已更新！');
            }, 1500);
        }

        // 記憶上次輸入的資料
        let lastInputData = {
            customer: '',
            product: '',
            model: ''
        };

        // 數據存儲配置
        const storageConfig = {
            mode: localStorage.getItem('storageMode') || 'localStorage', // 'localStorage' | 'sharepoint'
            sharepoint: {
                siteUrl: localStorage.getItem('sharepointSiteUrl') || '',
                listName: localStorage.getItem('sharepointListName') || '維修記錄',
                isEnabled: false,
                credentials: {}
            }
        };

        // 統一數據存儲管理類
        class DataStorageManager {
            constructor() {
                this.localStorage = new LocalStorageHandler();
                this.sharepoint = new SharePointHandler(storageConfig.sharepoint);
                this.currentMode = storageConfig.mode;
            }

            // 保存數據
            async save(dataType, data) {
                try {
                    if (this.currentMode === 'localStorage') {
                        return await this.localStorage.save(dataType, data);
                    } else if (this.currentMode === 'sharepoint' && storageConfig.sharepoint.isEnabled) {
                        return await this.sharepoint.save(dataType, data);
                    } else {
                        // 如果 SharePoint 不可用，回退到 LocalStorage
                        console.warn('SharePoint 不可用，回退到 LocalStorage');
                        return await this.localStorage.save(dataType, data);
                    }
                } catch (error) {
                    console.error('數據保存失敗:', error);
                    // 錯誤時回退到 LocalStorage
                    return await this.localStorage.save(dataType, data);
                }
            }

            // 載入數據
            async load(dataType) {
                try {
                    if (this.currentMode === 'localStorage') {
                        return await this.localStorage.load(dataType);
                    } else if (this.currentMode === 'sharepoint' && storageConfig.sharepoint.isEnabled) {
                        return await this.sharepoint.load(dataType);
                    } else {
                        return await this.localStorage.load(dataType);
                    }
                } catch (error) {
                    console.error('數據載入失敗:', error);
                    return await this.localStorage.load(dataType);
                }
            }

            // 切換存儲模式
            async switchMode(newMode) {
                if (newMode === this.currentMode) return true;

                try {
                    if (newMode === 'sharepoint') {
                        const isConnected = await this.sharepoint.testConnection();
                        if (!isConnected) {
                            throw new Error('SharePoint 連線失敗');
                        }
                        storageConfig.sharepoint.isEnabled = true;
                    }

                    this.currentMode = newMode;
                    storageConfig.mode = newMode;
                    localStorage.setItem('storageMode', newMode);

                    return true;
                } catch (error) {
                    console.error('切換存儲模式失敗:', error);
                    return false;
                }
            }

            // 數據遷移
            async migrateData(fromMode, toMode) {
                try {
                    const sourceHandler = fromMode === 'localStorage' ? this.localStorage : this.sharepoint;
                    const targetHandler = toMode === 'localStorage' ? this.localStorage : this.sharepoint;

                    // 載入所有數據類型
                    const dataTypes = ['repairRecords', 'customers', 'products', 'parts'];
                    const migrationResults = {};

                    for (const dataType of dataTypes) {
                        const data = await sourceHandler.load(dataType);
                        if (data && data.length > 0) {
                            await targetHandler.save(dataType, data);
                            migrationResults[dataType] = data.length;
                        }
                    }

                    return migrationResults;
                } catch (error) {
                    console.error('數據遷移失敗:', error);
                    throw error;
                }
            }
        }

        // LocalStorage 處理類
        class LocalStorageHandler {
            async save(dataType, data) {
                try {
                    const dataWithTimestamp = {
                        data: data,
                        lastUpdated: new Date().toISOString(),
                        version: '1.0'
                    };
                    localStorage.setItem(`maintenance_${dataType}`, JSON.stringify(dataWithTimestamp));
                    return true;
                } catch (error) {
                    console.error('LocalStorage 保存失敗:', error);
                    throw error;
                }
            }

            async load(dataType) {
                try {
                    const stored = localStorage.getItem(`maintenance_${dataType}`);
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        return parsed.data || [];
                    }
                    return [];
                } catch (error) {
                    console.error('LocalStorage 載入失敗:', error);
                    return [];
                }
            }

            async clear(dataType) {
                localStorage.removeItem(`maintenance_${dataType}`);
            }

            async getAllKeys() {
                const keys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('maintenance_')) {
                        keys.push(key);
                    }
                }
                return keys;
            }
        }

        // SharePoint 處理類
        class SharePointHandler {
            constructor(config) {
                this.siteUrl = config.siteUrl;
                this.listName = config.listName;
                this.isEnabled = config.isEnabled;
                this.credentials = config.credentials;
            }

            // 測試 SharePoint 連線
            async testConnection() {
                if (!this.siteUrl) {
                    console.warn('SharePoint 網站 URL 未設定');
                    return false;
                }

                try {
                    const response = await fetch(`${this.siteUrl}/_api/web`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json;odata=verbose',
                            'Content-Type': 'application/json;odata=verbose'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) {
                        console.log('SharePoint 連線成功');
                        return true;
                    } else {
                        console.warn('SharePoint 連線失敗:', response.status, response.statusText);
                        return false;
                    }
                } catch (error) {
                    console.error('SharePoint 連線測試失敗:', error);
                    return false;
                }
            }

            // 獲取 SharePoint 清單項目
            async load(dataType) {
                if (!this.isEnabled) {
                    throw new Error('SharePoint 未啟用');
                }

                try {
                    const listNames = this.getListName(dataType);
                    let response;

                    // 先嘗試中文清單名稱，如果失敗則嘗試英文名稱
                    try {
                        response = await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listNames.chinese}')/items`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json;odata=verbose',
                                'Content-Type': 'application/json;odata=verbose'
                            }
                        });

                        if (!response.ok) {
                            throw new Error('Chinese list name failed');
                        }
                    } catch (error) {
                        console.log(`嘗試英文清單名稱: ${listNames.english}`);
                        response = await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listNames.english}')/items`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json;odata=verbose',
                            'Content-Type': 'application/json;odata=verbose'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        return this.transformFromSharePoint(dataType, data.d.results);
                    } else {
                        throw new Error(`SharePoint 載入失敗: ${response.status}`);
                    }
                } catch (error) {
                    console.error('SharePoint 載入失敗:', error);
                    throw error;
                }
            }

            // 保存到 SharePoint
            async save(dataType, data) {
                if (!this.isEnabled) {
                    throw new Error('SharePoint 未啟用');
                }

                try {
                    const listNames = this.getListName(dataType);
                    const results = [];

                    // 確定要使用的清單名稱
                    let actualListName = listNames.chinese;
                    try {
                        // 測試中文清單名稱是否存在
                        await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listNames.chinese}')`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json;odata=verbose'
                            }
                        });
                    } catch (error) {
                        console.log(`使用英文清單名稱: ${listNames.english}`);
                        actualListName = listNames.english;
                    }

                    for (const item of data) {
                        const transformedItem = this.transformToSharePoint(dataType, item);
                        const result = await this.createOrUpdateItem(actualListName, transformedItem);
                        results.push(result);
                    }

                    return results;
                } catch (error) {
                    console.error('SharePoint 保存失敗:', error);
                    throw error;
                }
            }

            // 創建或更新 SharePoint 項目
            async createOrUpdateItem(listName, item) {
                try {
                    // 首先嘗試查找現有項目
                    const existingItem = await this.findExistingItem(listName, item);

                    if (existingItem) {
                        return await this.updateItem(listName, existingItem.Id, item);
                    } else {
                        return await this.createItem(listName, item);
                    }
                } catch (error) {
                    console.error('SharePoint 項目操作失敗:', error);
                    throw error;
                }
            }

            // 創建新項目
            async createItem(listName, item) {
                const response = await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listName}')/items`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json;odata=verbose',
                        'Content-Type': 'application/json;odata=verbose',
                        'X-RequestDigest': await this.getRequestDigest()
                    },
                    credentials: 'include',
                    body: JSON.stringify(item)
                });

                if (response.ok) {
                    return await response.json();
                } else {
                    throw new Error(`創建項目失敗: ${response.status}`);
                }
            }

            // 更新現有項目
            async updateItem(listName, itemId, item) {
                const response = await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json;odata=verbose',
                        'Content-Type': 'application/json;odata=verbose',
                        'X-RequestDigest': await this.getRequestDigest(),
                        'X-HTTP-Method': 'MERGE',
                        'IF-MATCH': '*'
                    },
                    credentials: 'include',
                    body: JSON.stringify(item)
                });

                return response.ok;
            }

            // 查找現有項目
            async findExistingItem(listName, item) {
                try {
                    let filter = '';
                    if (item.RepairId) {
                        filter = `RepairId eq '${item.RepairId}'`;
                    } else if (item.CustomerId) {
                        filter = `CustomerId eq '${item.CustomerId}'`;
                    } else if (item.ProductId) {
                        filter = `ProductId eq '${item.ProductId}'`;
                    } else if (item.PartId) {
                        filter = `PartId eq '${item.PartId}'`;
                    }

                    if (!filter) return null;

                    const response = await fetch(`${this.siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$filter=${filter}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json;odata=verbose',
                            'Content-Type': 'application/json;odata=verbose'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        return data.d.results.length > 0 ? data.d.results[0] : null;
                    }
                    return null;
                } catch (error) {
                    console.error('查找現有項目失敗:', error);
                    return null;
                }
            }

            // 獲取請求摘要
            async getRequestDigest() {
                try {
                    const response = await fetch(`${this.siteUrl}/_api/contextinfo`, {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json;odata=verbose',
                            'Content-Type': 'application/json;odata=verbose'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        return data.d.GetContextWebInformation.FormDigestValue;
                    } else {
                        throw new Error('獲取請求摘要失敗');
                    }
                } catch (error) {
                    console.error('獲取請求摘要失敗:', error);
                    throw error;
                }
            }

            // 獲取對應的 SharePoint 清單名稱 (支援中英文)
            getListName(dataType) {
                const listMapping = {
                    'repairRecords': '維修記錄',
                    'customers': '客戶資料',
                    'products': '產品資料',
                    'parts': '零件資料'
                };

                // 英文清單名稱映射 (避免編碼問題)
                const englishListMapping = {
                    'repairRecords': 'RepairRecords',
                    'customers': 'CustomerData',
                    'products': 'ProductData',
                    'parts': 'PartsData'
                };

                // 先嘗試中文名稱，如果不存在則使用英文名稱
                const chineseName = listMapping[dataType];
                const englishName = englishListMapping[dataType];

                return {
                    chinese: chineseName || dataType,
                    english: englishName || dataType
                };
            }

            // 轉換數據格式到 SharePoint
            transformToSharePoint(dataType, item) {
                switch (dataType) {
                    case 'repairRecords':
                        return {
                            Title: item.id,
                            RepairId: item.id,
                            CustomerName: item.customer,
                            Phone: item.phone || '',
                            SerialNumber: item.serialNumber,
                            ProductName: item.product,
                            ProductModel: item.model || '',
                            Issue: item.issue,
                            ServiceStatus: item.serviceStatus,
                            RepairRecord: item.repairRecord,
                            TestResult: item.testResult,
                            Technician: item.technician,
                            RepairDate: item.date ? new Date(item.date).toISOString() : new Date().toISOString()
                        };
                    case 'customers':
                        return {
                            Title: item.name,
                            CustomerId: item.id,
                            CustomerName: item.name,
                            Phone: item.phone || '',
                            Email: item.email || '',
                            Address: item.address || ''
                        };
                    case 'products':
                        return {
                            Title: item.name,
                            ProductId: item.id,
                            ProductName: item.name,
                            Brand: item.brand || '',
                            Model: item.model || '',
                            Category: item.category || ''
                        };
                    case 'parts':
                        return {
                            Title: item.name,
                            PartId: item.id,
                            PartName: item.name,
                            Category: item.category,
                            Model: item.model || '',
                            Supplier: item.supplier || '',
                            Stock: item.stock,
                            Price: item.price || 0,
                            MinStock: item.minStock || 0,
                            Location: item.location || '',
                            Notes: item.notes || ''
                        };
                    default:
                        return item;
                }
            }

            // 從 SharePoint 轉換數據格式
            transformFromSharePoint(dataType, items) {
                return items.map(item => {
                    switch (dataType) {
                        case 'repairRecords':
                            return {
                                id: item.RepairId,
                                date: item.RepairDate ? new Date(item.RepairDate).toLocaleDateString() : '',
                                customer: item.CustomerName,
                                phone: item.Phone || '',
                                serialNumber: item.SerialNumber,
                                product: item.ProductName,
                                model: item.ProductModel || '',
                                issue: item.Issue,
                                serviceStatus: item.ServiceStatus,
                                repairRecord: item.RepairRecord,
                                testResult: item.TestResult,
                                technician: item.Technician
                            };
                        case 'customers':
                            return {
                                id: item.CustomerId,
                                name: item.CustomerName,
                                phone: item.Phone || '',
                                email: item.Email || '',
                                address: item.Address || ''
                            };
                        case 'products':
                            return {
                                id: item.ProductId,
                                name: item.ProductName,
                                brand: item.Brand || '',
                                model: item.Model || '',
                                category: item.Category || ''
                            };
                        case 'parts':
                            return {
                                id: item.PartId,
                                name: item.PartName,
                                category: item.Category,
                                model: item.Model || '',
                                supplier: item.Supplier || '',
                                stock: item.Stock,
                                price: item.Price || 0,
                                minStock: item.MinStock || 0,
                                location: item.Location || '',
                                notes: item.Notes || ''
                            };
                        default:
                            return item;
                    }
                });
            }
        }

        // 初始化數據存儲管理器
        const dataStorage = new DataStorageManager();

        // 維修記錄數據
        const repairRecords = [
            {
                id: 'R2024-001',
                date: '01/15',
                customer: '張先生',
                phone: '0912-345-678',
                serialNumber: 'SN240115001',
                product: 'iPhone 14',
                model: 'Apple A2882',
                issue: '螢幕破裂，觸控異常',
                serviceStatus: 'complaint',
                repairRecord: 'adjustment',
                testResult: 'testing',
                technician: '維保員A',
                repairTechnician: '維保員A'
            },
            {
                id: 'R2024-002',
                date: '01/16',
                customer: '李小姐',
                phone: '0923-456-789',
                serialNumber: 'SN240116002',
                product: 'MacBook Pro',
                model: 'Apple MBP-M2',
                issue: '電池續航力下降',
                serviceStatus: 'maintenance',
                repairRecord: 'adjustment',
                testResult: 'pending',
                technician: '維保員B',
                repairTechnician: '維保員B'
            },
            {
                id: 'R2024-003',
                date: '01/14',
                customer: '王先生',
                phone: '0934-567-890',
                serialNumber: 'SN240114003',
                product: 'iPad Air',
                model: 'Apple IPAD-AIR-5',
                issue: '無法開機，疑似主機板問題',
                serviceStatus: 'complaint',
                repairRecord: 'motor_replacement',
                testResult: 'pass',
                technician: '維保員A',
                repairTechnician: '維保員C'
            },
            {
                id: 'R2024-004',
                date: '01/17',
                customer: '陳小姐',
                phone: '0945-678-901',
                serialNumber: 'SN240117004',
                product: 'Samsung Galaxy S23',
                model: 'Samsung SM-S911',
                issue: '相機無法對焦',
                serviceStatus: 'maintenance',
                repairRecord: 'adjustment',
                testResult: 'pending',
                technician: '未指派',
                repairTechnician: '未指派'
            },
            {
                id: 'R2024-005',
                date: '01/18',
                customer: '林先生',
                phone: '0956-789-012',
                serialNumber: 'SN240118005',
                product: 'ASUS ROG Phone',
                model: 'ASUS AI2201',
                issue: '充電孔接觸不良',
                serviceStatus: 'complaint',
                repairRecord: 'motor_replacement',
                testResult: 'fail',
                technician: '維保員C',
                repairTechnician: '維保員D'
            },
            {
                id: 'R2024-006',
                date: '01/19',
                customer: '黃小姐',
                phone: '0967-890-123',
                serialNumber: 'SN240119006',
                product: 'iPhone 13 Pro',
                model: 'Apple A2483',
                issue: 'Face ID無法使用',
                serviceStatus: 'maintenance',
                repairRecord: 'adjustment',
                testResult: 'testing',
                technician: '維保員B',
                repairTechnician: '維保員A'
            },
            {
                id: 'R2024-007',
                date: '01/20',
                customer: '劉先生',
                phone: '0978-901-234',
                serialNumber: 'SN240120007',
                product: 'MacBook Air',
                model: 'Apple MBA-M2',
                issue: '鍵盤按鍵失靈',
                serviceStatus: 'maintenance',
                repairRecord: 'adjustment',
                testResult: 'pass',
                technician: '維保員A',
                repairTechnician: '維保員B'
            }
        ];

        let filteredRecords = [...repairRecords];

        // 維保狀態和維修記錄的顯示配置
        const serviceStatusConfig = {
            complaint: { text: '客訴', class: 'tag-red' },
            maintenance: { text: '維保', class: 'tag-blue' }
        };

        const repairRecordConfig = {
            adjustment: { text: '調整', class: 'tag-orange' },
            motor_replacement: { text: '換馬達', class: 'tag-green' }
        };

        const testResultConfig = {
            pass: { text: 'Pass', class: 'tag-green' },
            fail: { text: 'Fail', class: 'tag-red' },
            pending: { text: '待測試', class: 'tag-orange' },
            testing: { text: '測試中', class: 'tag-blue' }
        };

        // 渲染維修記錄表格
        function renderRepairTable(records = filteredRecords) {
            const tbody = document.getElementById('repairTableBody');
            if (!tbody) return;

            try {
                tbody.innerHTML = records.map(record => {
                    // 確保所有必要的配置都存在
                    const serviceStatus = serviceStatusConfig[record.serviceStatus] || { text: record.serviceStatus || '未知', class: 'tag-gray' };
                    const repairRecord = repairRecordConfig[record.repairRecord] || { text: record.repairRecord || '未知', class: 'tag-gray' };
                    const testResult = testResultConfig[record.testResult] || { text: record.testResult || '未知', class: 'tag-gray' };

                    return `
                        <tr>
                            <td><strong>${record.id}</strong><br><small>${record.date}</small></td>
                            <td>${record.customer}<br><small>${record.phone}</small></td>
                            <td><strong>${record.serialNumber || '未設定'}</strong><br><small style="color: #666;">序號</small></td>
                            <td>${record.product}<br><small>${record.model}</small></td>
                            <td>${record.issue}</td>
                            <td><span class="tag ${serviceStatus.class}">${serviceStatus.text}</span></td>
                            <td><span class="tag ${repairRecord.class}">${repairRecord.text}</span></td>
                            <td><span class="tag ${testResult.class}">${testResult.text}</span></td>
                            <td>${record.technician}</td>
                            <td>
                                <button class="btn" style="margin-right: 8px;" onclick="viewRepair('${record.id}')">查看</button>
                                <button class="btn" onclick="editRepair('${record.id}')">編輯</button>
                            </td>
                        </tr>
                    `;
                }).join('');
            } catch (error) {
                console.error('渲染維修記錄表格時發生錯誤:', error);
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center; color: red;">載入維修記錄時發生錯誤，請重新整理頁面</td></tr>';
            }
        }

        // 智能搜索功能
        function searchRepairs() {
            const searchTerm = document.getElementById('repairSearchInput').value.toLowerCase().trim();

            if (searchTerm === '') {
                filteredRecords = [...repairRecords];
            } else {
                filteredRecords = repairRecords.filter(record => {
                    return record.id.toLowerCase().includes(searchTerm) ||
                           record.customer.toLowerCase().includes(searchTerm) ||
                           record.serialNumber.toLowerCase().includes(searchTerm) ||
                           record.product.toLowerCase().includes(searchTerm) ||
                           record.model.toLowerCase().includes(searchTerm) ||
                           record.issue.toLowerCase().includes(searchTerm) ||
                           record.technician.toLowerCase().includes(searchTerm) ||
                           record.phone.includes(searchTerm);
                });
            }

            // 應用其他篩選條件
            applyFilters();
        }

        // 篩選功能
        function filterRepairs() {
            applyFilters();
        }

        // 應用篩選條件
        function applyFilters() {
            const testResultFilter = document.getElementById('testResultFilter').value;
            const serviceStatusFilter = document.getElementById('serviceStatusFilter').value;

            let filtered = [...filteredRecords];

            if (testResultFilter) {
                filtered = filtered.filter(record => record.testResult === testResultFilter);
            }

            if (serviceStatusFilter) {
                filtered = filtered.filter(record => record.serviceStatus === serviceStatusFilter);
            }

            renderRepairTable(filtered);

            // 顯示篩選結果統計
            updateFilterStats(filtered.length, repairRecords.length);
        }

        // 清除篩選
        function clearFilters() {
            document.getElementById('repairSearchInput').value = '';
            document.getElementById('testResultFilter').value = '';
            document.getElementById('serviceStatusFilter').value = '';

            filteredRecords = [...repairRecords];
            renderRepairTable();
            updateFilterStats(repairRecords.length, repairRecords.length);
        }

        // 更新篩選統計
        function updateFilterStats(filtered, total) {
            const statsElement = document.getElementById('filterStats');
            if (statsElement) {
                if (filtered === total) {
                    statsElement.innerHTML = `顯示全部 ${total} 筆維修記錄`;
                } else {
                    statsElement.innerHTML = `顯示 ${filtered} / ${total} 筆維修記錄 <span style="color: #1890ff;">(已篩選)</span>`;
                }
            }
        }

        // 查看維修記錄
        function viewRepair(repairId) {
            const record = repairRecords.find(r => r.id === repairId);
            if (record) {
                showViewRepairModal(record);
            }
        }

        // 編輯維修記錄
        function editRepair(repairId) {
            const record = repairRecords.find(r => r.id === repairId);
            if (record) {
                showEditRepairModal(record);
            }
        }

        // 顯示新增維修記錄表單
        function showAddRepairForm() {
            document.getElementById('addRepairForm').classList.remove('hidden');

            // 自動填入上次輸入的資料
            if (lastInputData.customer) {
                document.getElementById('customerName').value = lastInputData.customer;
            }
            if (lastInputData.product) {
                document.getElementById('productName').value = lastInputData.product;
            }
            if (lastInputData.model) {
                document.getElementById('productModel').value = lastInputData.model;
            }

            // 設定預設的預計完成日期（7天後）
            const expectedDate = new Date();
            expectedDate.setDate(expectedDate.getDate() + 7);
            document.getElementById('expectedDate').value = expectedDate.toISOString().split('T')[0];
        }

        // 隱藏新增維修記錄表單
        function hideAddRepairForm() {
            document.getElementById('addRepairForm').classList.add('hidden');
            document.getElementById('repairForm').reset();

            // 重新填入要保留的欄位
            if (lastInputData.customer) {
                document.getElementById('customerName').value = lastInputData.customer;
            }
            if (lastInputData.product) {
                document.getElementById('productName').value = lastInputData.product;
            }
            if (lastInputData.model) {
                document.getElementById('productModel').value = lastInputData.model;
            }
        }

        // 清除記憶的輸入資料
        function clearLastInputData() {
            lastInputData = {
                customer: '',
                product: '',
                model: ''
            };

            // 清除當前表單中的這些欄位
            document.getElementById('customerName').value = '';
            document.getElementById('productName').value = '';
            document.getElementById('productModel').value = '';

            alert('已清除記憶的客戶姓名、產品名稱和產品型號！');
        }

        // 生成新的維修編號
        function generateRepairId() {
            const year = new Date().getFullYear();
            const existingIds = repairRecords.map(r => r.id);
            let maxNumber = 0;

            existingIds.forEach(id => {
                const match = id.match(/R(\d{4})-(\d{3})/);
                if (match && match[1] === year.toString()) {
                    maxNumber = Math.max(maxNumber, parseInt(match[2]));
                }
            });

            const newNumber = (maxNumber + 1).toString().padStart(3, '0');
            return `R${year}-${newNumber}`;
        }

        // 提交新增維修記錄表單
        function submitRepairForm(event) {
            event.preventDefault();

            // 獲取表單數據
            const formData = {
                id: generateRepairId(),
                date: new Date().toLocaleDateString('zh-TW', { month: '2-digit', day: '2-digit' }),
                customer: document.getElementById('customerName').value,
                phone: document.getElementById('customerPhone').value,
                serialNumber: document.getElementById('productSerial').value.toUpperCase(),
                product: document.getElementById('productName').value,
                model: document.getElementById('productModel').value || '未指定',
                issue: document.getElementById('issueDescription').value,
                serviceStatus: document.getElementById('serviceStatus').value,
                repairRecord: document.getElementById('repairRecord').value,
                testResult: document.getElementById('testResult').value,
                technician: document.getElementById('technician').value || '未指派',
                repairTechnician: '未指派' // 新增記錄時維修員預設為未指派
            };

            // 驗證必填欄位
            if (!formData.customer || !formData.serialNumber || !formData.product || !formData.issue || !formData.serviceStatus || !formData.repairRecord) {
                alert('請填寫所有必填欄位！');
                return;
            }

            // 驗證產品序號格式
            const serialRegex = /^SN\d{9}$/;
            if (!serialRegex.test(formData.serialNumber)) {
                alert('產品序號格式不正確！\n正確格式：SN + 9位數字 (例如：SN240120001)');
                return;
            }

            // 檢查產品序號是否重複
            const existingSerial = repairRecords.find(record => record.serialNumber === formData.serialNumber);
            if (existingSerial) {
                const confirmed = confirm(`產品序號 ${formData.serialNumber} 已存在於維修記錄 ${existingSerial.id} 中。\n\n這可能是回修案件，是否繼續建立新的維修記錄？`);
                if (!confirmed) {
                    return;
                }
            }

            // 驗證電話格式
            const phoneRegex = /^09\d{8}$|^\d{2,4}-\d{6,8}$/;
            if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
                alert('請輸入正確的電話號碼格式！');
                return;
            }

            // 添加到記錄陣列
            repairRecords.unshift(formData); // 新記錄加到最前面
            filteredRecords = [...repairRecords];

            // 保存到數據存儲
            saveRepairRecordsToStorage();

            // 重新渲染表格
            renderRepairTable();
            updateFilterStats(repairRecords.length, repairRecords.length);

            // 保存這次輸入的資料供下次使用
            lastInputData = {
                customer: formData.customer,
                product: formData.product,
                model: formData.model
            };

            // 隱藏表單
            hideAddRepairForm();

            // 顯示成功訊息
            alert(`維修記錄 ${formData.id} 建立成功！\n客戶：${formData.customer}\n產品序號：${formData.serialNumber}\n產品：${formData.product}`);
        }

        // 數據存儲相關函數
        async function saveRepairRecordsToStorage() {
            try {
                await dataStorage.save('repairRecords', repairRecords);
                console.log('維修記錄已保存到存儲');
            } catch (error) {
                console.error('保存維修記錄失敗:', error);
                showNotification('數據保存失敗，請檢查存儲設定', 'error');
            }
        }

        async function loadRepairRecordsFromStorage() {
            try {
                console.log('開始載入維修記錄...');

                if (!dataStorage) {
                    console.warn('數據存儲管理器未初始化，使用預設數據');
                    return;
                }

                const loadedRecords = await dataStorage.load('repairRecords');
                console.log('載入的維修記錄:', loadedRecords);

                if (loadedRecords && loadedRecords.length > 0) {
                    repairRecords.splice(0, repairRecords.length, ...loadedRecords);
                    filteredRecords = [...repairRecords];

                    // 確保渲染函數存在
                    if (typeof renderRepairTable === 'function') {
                        renderRepairTable();
                    }
                    if (typeof updateFilterStats === 'function') {
                        updateFilterStats(repairRecords.length, repairRecords.length);
                    }

                    console.log(`已載入 ${loadedRecords.length} 筆維修記錄`);
                } else {
                    console.log('沒有找到已保存的維修記錄，使用預設數據');
                }
            } catch (error) {
                console.error('載入維修記錄失敗:', error);
                if (typeof showNotification === 'function') {
                    showNotification('維修記錄載入失敗，使用預設數據', 'warning');
                }
            }
        }

        async function saveCustomersToStorage() {
            try {
                await dataStorage.save('customers', customerRecords);
                console.log('客戶資料已保存到存儲');
            } catch (error) {
                console.error('保存客戶資料失敗:', error);
            }
        }

        async function loadCustomersFromStorage() {
            try {
                const loadedCustomers = await dataStorage.load('customers');
                if (loadedCustomers && loadedCustomers.length > 0) {
                    customerRecords.splice(0, customerRecords.length, ...loadedCustomers);
                    console.log(`已載入 ${loadedCustomers.length} 筆客戶資料`);
                }
            } catch (error) {
                console.error('載入客戶資料失敗:', error);
            }
        }

        async function savePartsToStorage() {
            try {
                await dataStorage.save('parts', partRecords);
                console.log('零件資料已保存到存儲');
            } catch (error) {
                console.error('保存零件資料失敗:', error);
            }
        }

        async function loadPartsFromStorage() {
            try {
                console.log('開始載入零件資料...');

                if (!dataStorage) {
                    console.warn('數據存儲管理器未初始化，使用預設數據');
                    return;
                }

                const loadedParts = await dataStorage.load('parts');
                console.log('載入的零件資料:', loadedParts);

                if (loadedParts && loadedParts.length > 0) {
                    partRecords.splice(0, partRecords.length, ...loadedParts);
                    filteredParts = [...partRecords];

                    // 確保渲染函數存在
                    if (typeof renderPartTable === 'function') {
                        renderPartTable();
                    }
                    if (typeof updatePartFilterStats === 'function') {
                        updatePartFilterStats(partRecords.length, partRecords.length);
                    }

                    console.log(`已載入 ${loadedParts.length} 筆零件資料`);
                } else {
                    console.log('沒有找到已保存的零件資料，使用預設數據');
                }
            } catch (error) {
                console.error('載入零件資料失敗:', error);
                if (typeof showNotification === 'function') {
                    showNotification('零件資料載入失敗，使用預設數據', 'warning');
                }
            }
        }

        async function saveProductsToStorage() {
            try {
                await dataStorage.save('products', productRecords);
                console.log('產品資料已保存到存儲');
            } catch (error) {
                console.error('保存產品資料失敗:', error);
            }
        }

        async function loadProductsFromStorage() {
            try {
                const loadedProducts = await dataStorage.load('products');
                if (loadedProducts && loadedProducts.length > 0) {
                    productRecords.splice(0, productRecords.length, ...loadedProducts);
                    console.log(`已載入 ${loadedProducts.length} 筆產品資料`);
                }
            } catch (error) {
                console.error('載入產品資料失敗:', error);
            }
        }

        // 通知函數
        function showNotification(message, type = 'info') {
            // 創建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            // 根據類型設定顏色
            switch (type) {
                case 'success':
                    notification.style.backgroundColor = '#52c41a';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#f5222d';
                    break;
                case 'warning':
                    notification.style.backgroundColor = '#fa8c16';
                    break;
                default:
                    notification.style.backgroundColor = '#1890ff';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 3秒後自動移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 點擊模態框背景關閉表單
        document.addEventListener('click', function(event) {
            const addRepairModal = document.getElementById('addRepairForm');
            const viewRepairModal = document.getElementById('viewRepairModal');
            const editRepairModal = document.getElementById('editRepairModal');
            const addPartModal = document.getElementById('addPartForm');
            const editPartModal = document.getElementById('editPartForm');

            if (event.target === addRepairModal) {
                hideAddRepairForm();
            } else if (event.target === viewRepairModal) {
                hideViewRepairModal();
            } else if (event.target === editRepairModal) {
                hideEditRepairModal();
            } else if (event.target === addPartModal) {
                hideAddPartForm();
            } else if (event.target === editPartModal) {
                hideEditPartForm();
            }
        });

        // 客戶管理數據
        const customerRecords = [
            {
                id: 'C001',
                name: '張先生',
                phone: '0912-345-678',
                email: '<EMAIL>',
                address: '台北市信義區信義路100號',
                status: 'active',
                registerDate: '2023-01-15',
                notes: 'VIP客戶，優先處理'
            },
            {
                id: 'C002',
                name: '李小姐',
                phone: '0923-456-789',
                email: '<EMAIL>',
                address: '新北市板橋區中山路200號',
                status: 'vip',
                registerDate: '2023-02-20',
                notes: '長期合作客戶'
            },
            {
                id: 'C003',
                name: '王先生',
                phone: '0934-567-890',
                email: '<EMAIL>',
                address: '桃園市中壢區中正路300號',
                status: 'active',
                registerDate: '2023-03-10',
                notes: ''
            },
            {
                id: 'C004',
                name: '陳小姐',
                phone: '0945-678-901',
                email: '<EMAIL>',
                address: '台中市西屯區台灣大道400號',
                status: 'inactive',
                registerDate: '2022-12-05',
                notes: '暫停服務'
            },
            {
                id: 'C005',
                name: '林先生',
                phone: '0956-789-012',
                email: '<EMAIL>',
                address: '高雄市前金區中正四路500號',
                status: 'active',
                registerDate: '2023-04-18',
                notes: '企業客戶'
            }
        ];

        let filteredCustomers = [...customerRecords];

        // 客戶狀態配置
        const customerStatusConfig = {
            active: { text: '活躍', class: 'tag-green' },
            inactive: { text: '非活躍', class: 'tag-orange' },
            vip: { text: 'VIP客戶', class: 'tag-blue' }
        };

        // 渲染客戶表格
        function renderCustomerTable(customers = filteredCustomers) {
            const tbody = document.getElementById('customerTableBody');
            if (!tbody) return;

            tbody.innerHTML = customers.map(customer => `
                <tr>
                    <td><strong>${customer.id}</strong></td>
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td>${customer.email || '未提供'}</td>
                    <td>${customer.address}</td>
                    <td><span class="tag ${customerStatusConfig[customer.status].class}">${customerStatusConfig[customer.status].text}</span></td>
                    <td>${customer.registerDate}</td>
                    <td>
                        <button class="btn" style="margin-right: 8px;" onclick="viewCustomer('${customer.id}')">查看</button>
                        <button class="btn" onclick="editCustomer('${customer.id}')">編輯</button>
                    </td>
                </tr>
            `).join('');
        }

        // 搜尋客戶
        function searchCustomers() {
            const searchTerm = document.getElementById('customerSearchInput').value.toLowerCase().trim();

            if (searchTerm === '') {
                filteredCustomers = [...customerRecords];
            } else {
                filteredCustomers = customerRecords.filter(customer => {
                    return customer.name.toLowerCase().includes(searchTerm) ||
                           customer.phone.includes(searchTerm) ||
                           customer.email.toLowerCase().includes(searchTerm) ||
                           customer.address.toLowerCase().includes(searchTerm) ||
                           customer.id.toLowerCase().includes(searchTerm);
                });
            }

            applyCustomerFilters();
        }

        // 篩選客戶
        function filterCustomers() {
            applyCustomerFilters();
        }

        // 應用客戶篩選條件
        function applyCustomerFilters() {
            const statusFilter = document.getElementById('customerStatusFilter').value;

            let filtered = [...filteredCustomers];

            if (statusFilter) {
                filtered = filtered.filter(customer => customer.status === statusFilter);
            }

            renderCustomerTable(filtered);
            updateCustomerFilterStats(filtered.length, customerRecords.length);
        }

        // 清除客戶篩選
        function clearCustomerFilters() {
            document.getElementById('customerSearchInput').value = '';
            document.getElementById('customerStatusFilter').value = '';

            filteredCustomers = [...customerRecords];
            renderCustomerTable();
            updateCustomerFilterStats(customerRecords.length, customerRecords.length);
        }

        // 更新客戶篩選統計
        function updateCustomerFilterStats(filtered, total) {
            const statsElement = document.getElementById('customerFilterStats');
            if (statsElement) {
                if (filtered === total) {
                    statsElement.innerHTML = `顯示全部 ${total} 位客戶`;
                } else {
                    statsElement.innerHTML = `顯示 ${filtered} / ${total} 位客戶 <span style="color: #1890ff;">(已篩選)</span>`;
                }
            }
        }

        // 顯示新增客戶表單
        function showAddCustomerForm() {
            document.getElementById('addCustomerForm').classList.remove('hidden');
        }

        // 隱藏新增客戶表單
        function hideAddCustomerForm() {
            document.getElementById('addCustomerForm').classList.add('hidden');
            document.getElementById('customerForm').reset();
        }

        // 生成新的客戶編號
        function generateCustomerId() {
            const existingIds = customerRecords.map(c => c.id);
            let maxNumber = 0;

            existingIds.forEach(id => {
                const match = id.match(/C(\d+)/);
                if (match) {
                    maxNumber = Math.max(maxNumber, parseInt(match[1]));
                }
            });

            const newNumber = (maxNumber + 1).toString().padStart(3, '0');
            return `C${newNumber}`;
        }

        // 提交新增客戶表單
        function submitCustomerForm(event) {
            event.preventDefault();

            const formData = {
                id: generateCustomerId(),
                name: document.getElementById('customerNameInput').value,
                phone: document.getElementById('customerPhoneInput').value,
                email: document.getElementById('customerEmailInput').value,
                address: document.getElementById('customerAddressInput').value,
                status: document.getElementById('customerStatusInput').value,
                registerDate: new Date().toISOString().split('T')[0],
                notes: document.getElementById('customerNotesInput').value
            };

            // 驗證必填欄位
            if (!formData.name || !formData.phone) {
                alert('請填寫客戶姓名和聯絡電話！');
                return;
            }

            // 驗證電話格式
            const phoneRegex = /^09\d{8}$|^\d{2,4}-\d{6,8}$/;
            if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
                alert('請輸入正確的電話號碼格式！');
                return;
            }

            // 驗證電子郵件格式
            if (formData.email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(formData.email)) {
                    alert('請輸入正確的電子郵件格式！');
                    return;
                }
            }

            // 添加到記錄陣列
            customerRecords.unshift(formData);
            filteredCustomers = [...customerRecords];

            // 重新渲染表格
            renderCustomerTable();
            updateCustomerFilterStats(customerRecords.length, customerRecords.length);

            // 隱藏表單
            hideAddCustomerForm();

            // 顯示成功訊息
            alert(`客戶 ${formData.id} 建立成功！\n姓名：${formData.name}\n電話：${formData.phone}`);
        }

        // 查看客戶
        function viewCustomer(customerId) {
            const customer = customerRecords.find(c => c.id === customerId);
            if (customer) {
                alert(`客戶資訊：${customer.id}\n姓名：${customer.name}\n電話：${customer.phone}\n郵件：${customer.email || '未提供'}\n地址：${customer.address}\n狀態：${customerStatusConfig[customer.status].text}`);
            }
        }

        // 編輯客戶
        function editCustomer(customerId) {
            const customer = customerRecords.find(c => c.id === customerId);
            if (customer) {
                alert(`編輯客戶：${customer.id}\n(這裡會開啟編輯表單)`);
            }
        }

        // ESC鍵關閉表單
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const repairModal = document.getElementById('addRepairForm');
                const viewRepairModal = document.getElementById('viewRepairModal');
                const editRepairModal = document.getElementById('editRepairModal');
                const customerModal = document.getElementById('addCustomerForm');
                const partModal = document.getElementById('addPartForm');
                const editPartModal = document.getElementById('editPartForm');
                const importPartModal = document.getElementById('importPartForm');
                const productModal = document.getElementById('addProductForm');
                const addUserModal = document.getElementById('addUserForm');
                const editUserModal = document.getElementById('editUserForm');
                const addRoleModal = document.getElementById('addRoleForm');
                const editRoleModal = document.getElementById('editRoleForm');
                const rolePermissionsModal = document.getElementById('rolePermissionsForm');
                const viewTemplatesModal = document.getElementById('viewTemplatesModal');

                if (!repairModal.classList.contains('hidden')) {
                    hideAddRepairForm();
                } else if (!viewRepairModal.classList.contains('hidden')) {
                    hideViewRepairModal();
                } else if (!editRepairModal.classList.contains('hidden')) {
                    hideEditRepairModal();
                } else if (!customerModal.classList.contains('hidden')) {
                    hideAddCustomerForm();
                } else if (!partModal.classList.contains('hidden')) {
                    hideAddPartForm();
                } else if (!editPartModal.classList.contains('hidden')) {
                    hideEditPartForm();
                } else if (!importPartModal.classList.contains('hidden')) {
                    hideImportPartForm();
                } else if (!productModal.classList.contains('hidden')) {
                    hideAddProductForm();
                } else if (!addUserModal.classList.contains('hidden')) {
                    hideAddUserForm();
                } else if (!editUserModal.classList.contains('hidden')) {
                    hideEditUserForm();
                } else if (!addRoleModal.classList.contains('hidden')) {
                    hideAddRoleForm();
                } else if (!editRoleModal.classList.contains('hidden')) {
                    hideEditRoleForm();
                } else if (!rolePermissionsModal.classList.contains('hidden')) {
                    hideRolePermissionsForm();
                } else if (!viewTemplatesModal.classList.contains('hidden')) {
                    hideViewTemplatesModal();
                }
            }
        });

        // 零件資料表 - 根據零件編號查詢零件名稱
        const partDatabase = {
            'P001': 'iPhone 14 螢幕總成',
            'P002': 'MacBook Pro 電池',
            'P003': 'iPad Air 主機板',
            'P004': 'Samsung S23 相機模組',
            'P005': 'iPhone 13 喇叭',
            'P006': 'MacBook Air 鍵盤',
            'P007': 'iPad Pro 觸控筆',
            'P008': 'iPhone 12 電池',
            'P009': 'Samsung S22 螢幕',
            'P010': 'MacBook Pro 風扇',
            'P011': 'iPhone 15 充電孔',
            'P012': 'iPad Mini 電池',
            'P013': 'Samsung Note 觸控筆',
            'P014': 'MacBook Air 電池',
            'P015': 'iPhone 14 Pro 相機',
            'P016': 'iPad Air 充電孔',
            'P017': 'Samsung S23 電池',
            'P018': 'MacBook Pro 螢幕',
            'P019': 'iPhone 13 Pro 主機板',
            'P020': 'iPad Pro 喇叭'
        };

        // 根據零件編號查詢零件名稱
        function lookupPartName() {
            const partId = document.getElementById('partIdInput').value.trim().toUpperCase();
            const partNameField = document.getElementById('partNameInput');

            if (partId && partDatabase[partId]) {
                partNameField.value = partDatabase[partId];
                partNameField.style.background = '#e8f5e8'; // 淺綠色背景表示找到
            } else if (partId) {
                partNameField.value = '未找到對應零件名稱';
                partNameField.style.background = '#ffe8e8'; // 淺紅色背景表示未找到
            } else {
                partNameField.value = '';
                partNameField.style.background = '#f5f5f5'; // 預設背景
            }
        }

        // 零件管理數據
        const partRecords = [
            {
                id: 'P001',
                name: 'iPhone 14 螢幕總成',
                category: 'screen',
                model: 'A2882',
                supplier: '富士康',
                stock: 25,
                price: 3500,
                minStock: 5,
                location: 'A1-01',
                notes: '原廠品質'
            },
            {
                id: 'P002',
                name: 'MacBook Pro 電池',
                category: 'battery',
                model: 'A2527',
                supplier: '德賽電池',
                stock: 2,
                price: 4200,
                minStock: 3,
                location: 'B2-05',
                notes: '庫存不足'
            },
            {
                id: 'P003',
                name: 'iPad Air 主機板',
                category: 'motherboard',
                model: 'A2588',
                supplier: '台積電',
                stock: 0,
                price: 8500,
                minStock: 2,
                location: 'C1-03',
                notes: '缺貨中'
            },
            {
                id: 'P004',
                name: 'Samsung S23 相機模組',
                category: 'camera',
                model: 'SM-S911',
                supplier: '三星',
                stock: 15,
                price: 2800,
                minStock: 5,
                location: 'A2-08',
                notes: ''
            },
            {
                id: 'P005',
                name: 'iPhone 13 喇叭',
                category: 'speaker',
                model: 'A2482',
                supplier: '瑞聲科技',
                stock: 30,
                price: 450,
                minStock: 10,
                location: 'D1-12',
                notes: '充足庫存'
            }
        ];

        let filteredParts = [...partRecords];

        // 零件分類配置
        const partCategoryConfig = {
            screen: { text: '螢幕', class: 'tag-blue' },
            battery: { text: '電池', class: 'tag-green' },
            motherboard: { text: '主機板', class: 'tag-red' },
            camera: { text: '相機', class: 'tag-orange' },
            speaker: { text: '喇叭', class: 'tag-blue' },
            other: { text: '其他', class: 'tag-gray' }
        };

        // 獲取庫存狀態
        function getStockStatus(part) {
            if (part.stock === 0) {
                return { text: '缺貨', class: 'tag-red' };
            } else if (part.stock <= part.minStock) {
                return { text: '庫存不足', class: 'tag-orange' };
            } else {
                return { text: '有庫存', class: 'tag-green' };
            }
        }

        // 渲染零件表格
        function renderPartTable(parts = filteredParts) {
            const tbody = document.getElementById('partTableBody');
            if (!tbody) return;

            tbody.innerHTML = parts.map(part => {
                const stockStatus = getStockStatus(part);
                return `
                    <tr>
                        <td><strong>${part.id}</strong></td>
                        <td>${part.name}</td>
                        <td><span class="tag ${partCategoryConfig[part.category].class}">${partCategoryConfig[part.category].text}</span></td>
                        <td>${part.model || '未指定'}</td>
                        <td>${part.supplier || '未指定'}</td>
                        <td>${part.stock}</td>
                        <td>NT$ ${part.price ? part.price.toLocaleString() : '未定價'}</td>
                        <td><span class="tag ${stockStatus.class}">${stockStatus.text}</span></td>
                        <td>
                            <button class="btn" style="margin-right: 8px;" onclick="viewPart('${part.id}')">查看</button>
                            <button class="btn" onclick="editPart('${part.id}')">編輯</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 搜尋零件
        function searchParts() {
            const searchTerm = document.getElementById('partSearchInput').value.toLowerCase().trim();

            if (searchTerm === '') {
                filteredParts = [...partRecords];
            } else {
                filteredParts = partRecords.filter(part => {
                    return part.name.toLowerCase().includes(searchTerm) ||
                           part.model.toLowerCase().includes(searchTerm) ||
                           part.supplier.toLowerCase().includes(searchTerm) ||
                           part.id.toLowerCase().includes(searchTerm) ||
                           part.location.toLowerCase().includes(searchTerm);
                });
            }

            applyPartFilters();
        }

        // 篩選零件
        function filterParts() {
            applyPartFilters();
        }

        // 應用零件篩選條件
        function applyPartFilters() {
            const categoryFilter = document.getElementById('partCategoryFilter').value;
            const stockFilter = document.getElementById('partStockFilter').value;

            let filtered = [...filteredParts];

            if (categoryFilter) {
                filtered = filtered.filter(part => part.category === categoryFilter);
            }

            if (stockFilter) {
                filtered = filtered.filter(part => {
                    const stockStatus = getStockStatus(part);
                    switch (stockFilter) {
                        case 'in_stock':
                            return part.stock > part.minStock;
                        case 'low_stock':
                            return part.stock > 0 && part.stock <= part.minStock;
                        case 'out_of_stock':
                            return part.stock === 0;
                        default:
                            return true;
                    }
                });
            }

            renderPartTable(filtered);
            updatePartFilterStats(filtered.length, partRecords.length);
        }

        // 清除零件篩選
        function clearPartFilters() {
            document.getElementById('partSearchInput').value = '';
            document.getElementById('partCategoryFilter').value = '';
            document.getElementById('partStockFilter').value = '';

            filteredParts = [...partRecords];
            renderPartTable();
            updatePartFilterStats(partRecords.length, partRecords.length);
        }

        // 更新零件篩選統計
        function updatePartFilterStats(filtered, total) {
            const statsElement = document.getElementById('partFilterStats');
            if (statsElement) {
                if (filtered === total) {
                    statsElement.innerHTML = `顯示全部 ${total} 個零件`;
                } else {
                    statsElement.innerHTML = `顯示 ${filtered} / ${total} 個零件 <span style="color: #1890ff;">(已篩選)</span>`;
                }
            }
        }

        // 顯示新增零件表單
        function showAddPartForm() {
            document.getElementById('addPartForm').classList.remove('hidden');
        }

        // 隱藏新增零件表單
        function hideAddPartForm() {
            document.getElementById('addPartForm').classList.add('hidden');
            document.getElementById('partForm').reset();
        }

        // 生成新的零件編號
        function generatePartId() {
            const existingIds = partRecords.map(p => p.id);
            let maxNumber = 0;

            existingIds.forEach(id => {
                const match = id.match(/P(\d+)/);
                if (match) {
                    maxNumber = Math.max(maxNumber, parseInt(match[1]));
                }
            });

            const newNumber = (maxNumber + 1).toString().padStart(3, '0');
            return `P${newNumber}`;
        }

        // 提交新增零件表單
        function submitPartForm(event) {
            event.preventDefault();

            const partId = document.getElementById('partIdInput').value.trim().toUpperCase();
            const partName = document.getElementById('partNameInput').value;

            const formData = {
                id: partId,
                name: partName,
                category: document.getElementById('partCategoryInput').value,
                model: document.getElementById('partModelInput').value,
                supplier: document.getElementById('partSupplierInput').value,
                stock: parseInt(document.getElementById('partStockInput').value),
                price: parseFloat(document.getElementById('partPriceInput').value) || 0,
                minStock: parseInt(document.getElementById('partMinStockInput').value) || 0,
                location: document.getElementById('partLocationInput').value,
                notes: document.getElementById('partNotesInput').value
            };

            // 驗證必填欄位
            if (!partId || !formData.category || isNaN(formData.stock)) {
                alert('請填寫零件編號、分類和庫存數量！');
                return;
            }

            // 檢查零件編號是否重複
            const existingPart = partRecords.find(part => part.id === partId);
            if (existingPart) {
                alert(`零件編號 ${partId} 已存在！\n現有零件：${existingPart.name}`);
                return;
            }

            // 檢查零件名稱是否從資料表查詢到
            if (partName === '未找到對應零件名稱' || !partName) {
                const confirmed = confirm(`零件編號 ${partId} 在資料表中未找到對應名稱。\n\n是否要手動輸入零件名稱？`);
                if (confirmed) {
                    const manualName = prompt('請輸入零件名稱：', '');
                    if (!manualName || manualName.trim() === '') {
                        alert('零件名稱不能為空！');
                        return;
                    }
                    formData.name = manualName.trim();
                } else {
                    return;
                }
            }

            // 添加到記錄陣列
            partRecords.unshift(formData);
            filteredParts = [...partRecords];

            // 重新渲染表格
            renderPartTable();
            updatePartFilterStats(partRecords.length, partRecords.length);

            // 隱藏表單
            hideAddPartForm();

            // 顯示成功訊息
            alert(`零件 ${formData.id} 建立成功！\n名稱：${formData.name}\n庫存：${formData.stock}`);
        }

        // 查看零件
        function viewPart(partId) {
            const part = partRecords.find(p => p.id === partId);
            if (part) {
                const stockStatus = getStockStatus(part);
                alert(`零件資訊：${part.id}\n名稱：${part.name}\n分類：${partCategoryConfig[part.category].text}\n庫存：${part.stock}\n狀態：${stockStatus.text}\n位置：${part.location}`);
            }
        }

        // 編輯零件
        function editPart(partId) {
            const part = partRecords.find(p => p.id === partId);
            if (part) {
                showEditPartModal(part);
            }
        }

        // 顯示編輯零件模態框
        function showEditPartModal(part) {
            document.getElementById('editPartId').value = part.id;
            document.getElementById('editPartName').value = part.name;
            document.getElementById('editPartCategory').value = part.category;
            document.getElementById('editPartModel').value = part.model || '';
            document.getElementById('editPartSupplier').value = part.supplier || '';
            document.getElementById('editPartStock').value = part.stock;
            document.getElementById('editPartPrice').value = part.price || '';
            document.getElementById('editPartMinStock').value = part.minStock || '';
            document.getElementById('editPartLocation').value = part.location || '';
            document.getElementById('editPartNotes').value = part.notes || '';

            document.getElementById('editPartForm').classList.remove('hidden');
        }

        // 隱藏編輯零件模態框
        function hideEditPartForm() {
            document.getElementById('editPartForm').classList.add('hidden');
            document.getElementById('editPartFormElement').reset();
        }

        // 提交編輯零件表單
        function submitEditPartForm(event) {
            event.preventDefault();

            const partId = document.getElementById('editPartId').value;
            const formData = {
                id: partId,
                name: document.getElementById('editPartName').value,
                category: document.getElementById('editPartCategory').value,
                model: document.getElementById('editPartModel').value || '',
                supplier: document.getElementById('editPartSupplier').value || '',
                stock: parseInt(document.getElementById('editPartStock').value),
                price: parseFloat(document.getElementById('editPartPrice').value) || 0,
                minStock: parseInt(document.getElementById('editPartMinStock').value) || 0,
                location: document.getElementById('editPartLocation').value || '',
                notes: document.getElementById('editPartNotes').value || ''
            };

            // 驗證必填欄位
            if (!formData.name || !formData.category || formData.stock < 0) {
                alert('請填寫所有必填欄位！');
                return;
            }

            // 更新記錄
            const partIndex = partRecords.findIndex(p => p.id === partId);
            if (partIndex !== -1) {
                partRecords[partIndex] = formData;
                filteredParts = [...partRecords];

                // 重新渲染表格
                renderPartTable();
                updatePartFilterStats(partRecords.length, partRecords.length);

                // 隱藏表單
                hideEditPartForm();

                // 顯示成功訊息
                alert(`零件 ${formData.id} 更新成功！\n名稱：${formData.name}\n庫存：${formData.stock}`);
            }
        }

        // 顯示導入零件資料表單
        function showImportPartForm() {
            document.getElementById('importPartForm').classList.remove('hidden');
        }

        // 隱藏導入零件資料表單
        function hideImportPartForm() {
            document.getElementById('importPartForm').classList.add('hidden');
            document.getElementById('importFileInput').value = '';
            document.getElementById('replaceExistingData').checked = false;
        }

        // 下載範本檔案
        function downloadTemplate() {
            const templateData = [
                {
                    '零件編號': 'P001',
                    '零件名稱': 'iPhone 14 螢幕總成',
                    '零件分類': 'screen',
                    '型號': 'A2882-SCREEN',
                    '供應商': '供應商A',
                    '庫存數量': 10,
                    '單價': 2500,
                    '最低庫存警告': 5,
                    '存放位置': 'A區-01',
                    '備註': '原廠零件'
                },
                {
                    '零件編號': 'P002',
                    '零件名稱': 'MacBook Pro 電池',
                    '零件分類': 'battery',
                    '型號': 'MBP-BATTERY-M2',
                    '供應商': '供應商B',
                    '庫存數量': 8,
                    '單價': 3200,
                    '最低庫存警告': 3,
                    '存放位置': 'B區-02',
                    '備註': '高容量電池'
                }
            ];

            // 轉換為CSV格式
            const headers = Object.keys(templateData[0]);
            const csvContent = [
                headers.join(','),
                ...templateData.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\\n');

            // 創建下載連結
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', '零件資料導入範本.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('範本檔案已下載！\\n請參考範本格式準備您的零件資料。');
        }

        // 導入零件資料
        function importPartData() {
            const fileInput = document.getElementById('importFileInput');
            const replaceExisting = document.getElementById('replaceExistingData').checked;

            if (!fileInput.files.length) {
                alert('請選擇要導入的檔案！');
                return;
            }

            const file = fileInput.files[0];
            const fileName = file.name.toLowerCase();

            if (fileName.endsWith('.csv')) {
                importCSVData(file, replaceExisting);
            } else if (fileName.endsWith('.json')) {
                importJSONData(file, replaceExisting);
            } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                alert('Excel檔案導入功能需要額外的程式庫支援。\\n請使用CSV格式或下載範本檔案。');
            } else {
                alert('不支援的檔案格式！\\n請使用CSV、JSON或Excel檔案。');
            }
        }

        // 導入CSV資料
        function importCSVData(file, replaceExisting) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\\n').filter(line => line.trim());

                    if (lines.length < 2) {
                        alert('檔案內容不足！請確保檔案包含標題行和至少一行資料。');
                        return;
                    }

                    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
                    const requiredHeaders = ['零件編號', '零件名稱', '零件分類', '庫存數量'];

                    // 檢查必要欄位
                    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
                    if (missingHeaders.length > 0) {
                        alert(`缺少必要欄位：${missingHeaders.join(', ')}\\n\\n請確保檔案包含所有必要欄位。`);
                        return;
                    }

                    const importedParts = [];
                    const errors = [];

                    for (let i = 1; i < lines.length; i++) {
                        const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());

                        if (values.length !== headers.length) {
                            errors.push(`第${i + 1}行：欄位數量不匹配`);
                            continue;
                        }

                        const partData = {};
                        headers.forEach((header, index) => {
                            partData[header] = values[index];
                        });

                        // 轉換為系統格式
                        const systemPart = {
                            id: partData['零件編號'],
                            name: partData['零件名稱'],
                            category: partData['零件分類'],
                            model: partData['型號'] || '',
                            supplier: partData['供應商'] || '',
                            stock: parseInt(partData['庫存數量']) || 0,
                            price: parseFloat(partData['單價']) || 0,
                            minStock: parseInt(partData['最低庫存警告']) || 0,
                            location: partData['存放位置'] || '',
                            notes: partData['備註'] || ''
                        };

                        // 驗證必要欄位
                        if (!systemPart.id || !systemPart.name || !systemPart.category) {
                            errors.push(`第${i + 1}行：零件編號、名稱或分類為空`);
                            continue;
                        }

                        importedParts.push(systemPart);
                    }

                    if (errors.length > 0) {
                        alert(`發現 ${errors.length} 個錯誤：\\n${errors.slice(0, 5).join('\\n')}${errors.length > 5 ? '\\n...' : ''}`);
                        return;
                    }

                    processImportedParts(importedParts, replaceExisting);

                } catch (error) {
                    alert(`檔案解析失敗：${error.message}\\n\\n請檢查檔案格式是否正確。`);
                }
            };
            reader.readAsText(file, 'UTF-8');
        }

        // 導入JSON資料
        function importJSONData(file, replaceExisting) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const jsonData = JSON.parse(e.target.result);

                    if (!Array.isArray(jsonData)) {
                        alert('JSON檔案格式錯誤！請確保檔案包含零件資料陣列。');
                        return;
                    }

                    const importedParts = jsonData.map(item => ({
                        id: item.id || item['零件編號'],
                        name: item.name || item['零件名稱'],
                        category: item.category || item['零件分類'],
                        model: item.model || item['型號'] || '',
                        supplier: item.supplier || item['供應商'] || '',
                        stock: parseInt(item.stock || item['庫存數量']) || 0,
                        price: parseFloat(item.price || item['單價']) || 0,
                        minStock: parseInt(item.minStock || item['最低庫存警告']) || 0,
                        location: item.location || item['存放位置'] || '',
                        notes: item.notes || item['備註'] || ''
                    }));

                    processImportedParts(importedParts, replaceExisting);

                } catch (error) {
                    alert(`JSON檔案解析失敗：${error.message}\\n\\n請檢查檔案格式是否正確。`);
                }
            };
            reader.readAsText(file, 'UTF-8');
        }

        // 處理導入的零件資料
        function processImportedParts(importedParts, replaceExisting) {
            let addedCount = 0;
            let updatedCount = 0;
            let skippedCount = 0;
            const errors = [];

            importedParts.forEach((part, index) => {
                // 檢查是否已存在
                const existingIndex = partRecords.findIndex(p => p.id === part.id);

                if (existingIndex !== -1) {
                    if (replaceExisting) {
                        partRecords[existingIndex] = part;
                        updatedCount++;
                    } else {
                        skippedCount++;
                    }
                } else {
                    partRecords.push(part);
                    addedCount++;
                }
            });

            // 更新顯示
            filteredParts = [...partRecords];
            renderPartTable();
            updatePartFilterStats(partRecords.length, partRecords.length);

            // 隱藏表單
            hideImportPartForm();

            // 顯示結果
            const resultMessage = [
                `導入完成！`,
                `新增：${addedCount} 筆`,
                `更新：${updatedCount} 筆`,
                `跳過：${skippedCount} 筆`,
                `總計：${importedParts.length} 筆資料`
            ].join('\\n');

            alert(resultMessage);
        }

        // 匯出零件資料
        function exportPartData() {
            if (partRecords.length === 0) {
                alert('沒有資料可以匯出！');
                return;
            }

            // 轉換為匯出格式
            const exportData = partRecords.map(part => ({
                '零件編號': part.id,
                '零件名稱': part.name,
                '零件分類': part.category,
                '型號': part.model,
                '供應商': part.supplier,
                '庫存數量': part.stock,
                '單價': part.price,
                '最低庫存警告': part.minStock,
                '存放位置': part.location,
                '備註': part.notes
            }));

            // 轉換為CSV格式
            const headers = Object.keys(exportData[0]);
            const csvContent = [
                headers.join(','),
                ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\\n');

            // 創建下載連結
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);

            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-');
            link.setAttribute('download', `零件資料_${timestamp}.csv`);

            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert(`已匯出 ${partRecords.length} 筆零件資料！`);
        }

        // 產品管理數據
        const productRecords = [
            {
                id: 'PR001',
                name: 'iPhone 14',
                brand: 'Apple',
                model: 'A2882',
                category: 'smartphone',
                warranty: 12,
                price: 28900,
                status: 'active',
                releaseDate: '2022-09-16',
                description: '6.1吋Super Retina XDR顯示器，A15仿生晶片',
                notes: '熱銷產品'
            },
            {
                id: 'PR002',
                name: 'MacBook Pro',
                brand: 'Apple',
                model: 'MBP-M2-13',
                category: 'laptop',
                warranty: 12,
                price: 42900,
                status: 'active',
                releaseDate: '2022-06-24',
                description: '13吋MacBook Pro配備M2晶片',
                notes: '專業用戶首選'
            },
            {
                id: 'PR003',
                name: 'iPad Air',
                brand: 'Apple',
                model: 'A2588',
                category: 'tablet',
                warranty: 12,
                price: 18900,
                status: 'discontinued',
                releaseDate: '2022-03-18',
                description: '10.9吋Liquid Retina顯示器，M1晶片',
                notes: '已停產'
            },
            {
                id: 'PR004',
                name: 'Galaxy S23',
                brand: 'Samsung',
                model: 'SM-S911',
                category: 'smartphone',
                warranty: 24,
                price: 26900,
                status: 'active',
                releaseDate: '2023-02-17',
                description: '6.1吋Dynamic AMOLED 2X顯示器',
                notes: '旗艦機種'
            },
            {
                id: 'PR005',
                name: 'ROG Phone 7',
                brand: 'ASUS',
                model: 'AI2205',
                category: 'smartphone',
                warranty: 18,
                price: 32900,
                status: 'coming_soon',
                releaseDate: '2023-04-13',
                description: '電競手機，Snapdragon 8 Gen 2',
                notes: '即將上市'
            }
        ];

        let filteredProducts = [...productRecords];

        // 產品分類配置
        const productCategoryConfig = {
            smartphone: { text: '智慧型手機', class: 'tag-blue' },
            laptop: { text: '筆記型電腦', class: 'tag-green' },
            tablet: { text: '平板電腦', class: 'tag-orange' },
            desktop: { text: '桌上型電腦', class: 'tag-red' },
            accessory: { text: '配件', class: 'tag-gray' },
            other: { text: '其他', class: 'tag-gray' }
        };

        // 產品狀態配置
        const productStatusConfig = {
            active: { text: '上架中', class: 'tag-green' },
            discontinued: { text: '停產', class: 'tag-red' },
            coming_soon: { text: '即將推出', class: 'tag-blue' }
        };

        // 渲染產品表格
        function renderProductTable(products = filteredProducts) {
            const tbody = document.getElementById('productTableBody');
            if (!tbody) return;

            tbody.innerHTML = products.map(product => `
                <tr>
                    <td><strong>${product.id}</strong></td>
                    <td>${product.name}</td>
                    <td>${product.brand}</td>
                    <td>${product.model || '未指定'}</td>
                    <td><span class="tag ${productCategoryConfig[product.category].class}">${productCategoryConfig[product.category].text}</span></td>
                    <td>${product.warranty ? product.warranty + '個月' : '未設定'}</td>
                    <td>NT$ ${product.price ? product.price.toLocaleString() : '未定價'}</td>
                    <td><span class="tag ${productStatusConfig[product.status].class}">${productStatusConfig[product.status].text}</span></td>
                    <td>
                        <button class="btn" style="margin-right: 8px;" onclick="viewProduct('${product.id}')">查看</button>
                        <button class="btn" onclick="editProduct('${product.id}')">編輯</button>
                    </td>
                </tr>
            `).join('');
        }

        // 搜尋產品
        function searchProducts() {
            const searchTerm = document.getElementById('productSearchInput').value.toLowerCase().trim();

            if (searchTerm === '') {
                filteredProducts = [...productRecords];
            } else {
                filteredProducts = productRecords.filter(product => {
                    return product.name.toLowerCase().includes(searchTerm) ||
                           product.brand.toLowerCase().includes(searchTerm) ||
                           product.model.toLowerCase().includes(searchTerm) ||
                           product.id.toLowerCase().includes(searchTerm) ||
                           product.description.toLowerCase().includes(searchTerm);
                });
            }

            applyProductFilters();
        }

        // 篩選產品
        function filterProducts() {
            applyProductFilters();
        }

        // 應用產品篩選條件
        function applyProductFilters() {
            const brandFilter = document.getElementById('productBrandFilter').value;
            const categoryFilter = document.getElementById('productCategoryFilter').value;

            let filtered = [...filteredProducts];

            if (brandFilter) {
                filtered = filtered.filter(product => product.brand === brandFilter);
            }

            if (categoryFilter) {
                filtered = filtered.filter(product => product.category === categoryFilter);
            }

            renderProductTable(filtered);
            updateProductFilterStats(filtered.length, productRecords.length);
        }

        // 清除產品篩選
        function clearProductFilters() {
            document.getElementById('productSearchInput').value = '';
            document.getElementById('productBrandFilter').value = '';
            document.getElementById('productCategoryFilter').value = '';

            filteredProducts = [...productRecords];
            renderProductTable();
            updateProductFilterStats(productRecords.length, productRecords.length);
        }

        // 更新產品篩選統計
        function updateProductFilterStats(filtered, total) {
            const statsElement = document.getElementById('productFilterStats');
            if (statsElement) {
                if (filtered === total) {
                    statsElement.innerHTML = `顯示全部 ${total} 個產品`;
                } else {
                    statsElement.innerHTML = `顯示 ${filtered} / ${total} 個產品 <span style="color: #1890ff;">(已篩選)</span>`;
                }
            }
        }

        // 顯示新增產品表單
        function showAddProductForm() {
            document.getElementById('addProductForm').classList.remove('hidden');
        }

        // 隱藏新增產品表單
        function hideAddProductForm() {
            document.getElementById('addProductForm').classList.add('hidden');
            document.getElementById('productForm').reset();
        }

        // 生成新的產品編號
        function generateProductId() {
            const existingIds = productRecords.map(p => p.id);
            let maxNumber = 0;

            existingIds.forEach(id => {
                const match = id.match(/PR(\d+)/);
                if (match) {
                    maxNumber = Math.max(maxNumber, parseInt(match[1]));
                }
            });

            const newNumber = (maxNumber + 1).toString().padStart(3, '0');
            return `PR${newNumber}`;
        }

        // 提交新增產品表單
        function submitProductForm(event) {
            event.preventDefault();

            const formData = {
                id: generateProductId(),
                name: document.getElementById('productNameInput').value,
                brand: document.getElementById('productBrandInput').value,
                model: document.getElementById('productModelInput').value,
                category: document.getElementById('productCategoryInput').value,
                warranty: parseInt(document.getElementById('productWarrantyInput').value) || 0,
                price: parseFloat(document.getElementById('productPriceInput').value) || 0,
                status: document.getElementById('productStatusInput').value,
                releaseDate: document.getElementById('productReleaseDateInput').value,
                description: document.getElementById('productDescriptionInput').value,
                notes: document.getElementById('productNotesInput').value
            };

            // 驗證必填欄位
            if (!formData.name || !formData.brand || !formData.category) {
                alert('請填寫產品名稱、品牌和分類！');
                return;
            }

            // 添加到記錄陣列
            productRecords.unshift(formData);
            filteredProducts = [...productRecords];

            // 重新渲染表格
            renderProductTable();
            updateProductFilterStats(productRecords.length, productRecords.length);

            // 隱藏表單
            hideAddProductForm();

            // 顯示成功訊息
            alert(`產品 ${formData.id} 建立成功！\n名稱：${formData.name}\n品牌：${formData.brand}`);
        }

        // 查看產品
        function viewProduct(productId) {
            const product = productRecords.find(p => p.id === productId);
            if (product) {
                alert(`產品資訊：${product.id}\n名稱：${product.name}\n品牌：${product.brand}\n型號：${product.model}\n分類：${productCategoryConfig[product.category].text}\n狀態：${productStatusConfig[product.status].text}\n價格：NT$ ${product.price.toLocaleString()}`);
            }
        }

        // 編輯產品
        function editProduct(productId) {
            const product = productRecords.find(p => p.id === productId);
            if (product) {
                alert(`編輯產品：${product.id}\n(這裡會開啟編輯表單)`);
            }
        }

        // 統計卡片點擊效果
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有表格
            console.log('初始化維修記錄表格，記錄數量:', repairRecords.length);
            console.log('維修記錄數據:', repairRecords);
            renderRepairTable();
            updateFilterStats(repairRecords.length, repairRecords.length);

            renderCustomerTable();
            updateCustomerFilterStats(customerRecords.length, customerRecords.length);

            renderPartTable();
            updatePartFilterStats(partRecords.length, partRecords.length);

            renderProductTable();
            updateProductFilterStats(productRecords.length, productRecords.length);

            // 為統計卡片添加懸停效果
            const statCards = document.querySelectorAll('.stat-card[onclick]');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                });
            });
        });

        // 維修統計相關函數
        function updateStatistics() {
            const timeRange = document.getElementById('statisticsTimeRange').value;
            const year = document.getElementById('statisticsYear').value;
            const month = document.getElementById('statisticsMonth').value;

            // 根據選擇的時間範圍更新統計數據
            updateStatisticsData(timeRange, year, month);
            updateStatisticsCharts(timeRange, year, month);
            updateEfficiencyTable(timeRange, year, month);
        }

        // 更新統計數據
        function updateStatisticsData(timeRange, year, month) {
            // 模擬根據時間範圍計算統計數據
            const mockData = generateMockStatistics(timeRange, year, month);

            document.getElementById('totalRepairsCount').textContent = mockData.totalRepairs;
            document.getElementById('avgRepairsPerDay').textContent = mockData.avgPerDay;
            document.getElementById('completionRate').textContent = mockData.completionRate + '%';
            document.getElementById('avgRepairTime').textContent = mockData.avgTime + '天';
        }

        // 生成模擬統計數據
        function generateMockStatistics(timeRange, year, month) {
            const baseData = {
                year: { totalRepairs: 1247, avgPerDay: 4.2, completionRate: 92, avgTime: 2.3 },
                month: { totalRepairs: 165, avgPerDay: 5.3, completionRate: 95, avgTime: 1.8 },
                week: { totalRepairs: 42, avgPerDay: 6.0, completionRate: 97, avgTime: 1.5 },
                day: { totalRepairs: 8, avgPerDay: 8.0, completionRate: 100, avgTime: 1.2 }
            };

            return baseData[timeRange] || baseData.year;
        }

        // 更新統計圖表
        function updateStatisticsCharts(timeRange, year, month) {
            const chartContainer = document.getElementById('repairTrendChart');

            // 根據時間範圍生成不同的圖表數據
            const chartData = generateChartData(timeRange, year, month);

            // 重新渲染圖表
            renderTrendChart(chartContainer, chartData);
        }

        // 生成圖表數據
        function generateChartData(timeRange, year, month) {
            const mockData = {
                year: [
                    { label: '1月', value: 98 },
                    { label: '2月', value: 115 },
                    { label: '3月', value: 132 },
                    { label: '4月', value: 148 },
                    { label: '5月', value: 165 },
                    { label: '6月', value: 152 }
                ],
                month: [
                    { label: '第1週', value: 38 },
                    { label: '第2週', value: 42 },
                    { label: '第3週', value: 45 },
                    { label: '第4週', value: 40 }
                ],
                week: [
                    { label: '週一', value: 8 },
                    { label: '週二', value: 6 },
                    { label: '週三', value: 7 },
                    { label: '週四', value: 5 },
                    { label: '週五', value: 9 },
                    { label: '週六', value: 4 },
                    { label: '週日', value: 3 }
                ],
                day: [
                    { label: '09:00', value: 2 },
                    { label: '11:00', value: 3 },
                    { label: '14:00', value: 2 },
                    { label: '16:00', value: 1 }
                ]
            };

            return mockData[timeRange] || mockData.year;
        }

        // 渲染趨勢圖表
        function renderTrendChart(container, data) {
            const maxValue = Math.max(...data.map(d => d.value));

            container.innerHTML = data.map(item => {
                const height = (item.value / maxValue) * 200;
                const color = item.value > maxValue * 0.8 ? '#52c41a' : '#1890ff';

                return `
                    <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                        <div style="background: ${color}; width: 100%; height: ${height}px; border-radius: 4px 4px 0 0;"></div>
                        <div style="margin-top: 8px; font-size: 12px;">${item.label}</div>
                        <div style="font-size: 10px; color: #666;">${item.value}</div>
                    </div>
                `;
            }).join('');
        }

        // 更新效率表格
        function updateEfficiencyTable(timeRange, year, month) {
            const tableBody = document.getElementById('efficiencyTableBody');

            // 根據時間範圍生成不同的表格數據
            const tableData = generateEfficiencyData(timeRange, year, month);

            tableBody.innerHTML = tableData.map(row => `
                <tr>
                    <td><span class="tag ${row.tagClass}">${row.period}</span></td>
                    <td>${row.count}筆</td>
                    <td>${row.avgTime}天</td>
                    <td><span class="tag tag-green">${row.completion}%</span></td>
                    <td><span class="tag tag-green">${row.satisfaction}</span></td>
                    <td><span style="color: ${row.trendColor};">${row.trend}</span></td>
                </tr>
            `).join('');
        }

        // 生成效率數據
        function generateEfficiencyData(timeRange, year, month) {
            const mockData = {
                year: [
                    { period: '2024年', tagClass: 'tag-red', count: 1247, avgTime: 2.3, completion: 89, satisfaction: 4.3, trend: '↗ +12%', trendColor: '#52c41a' },
                    { period: '2023年', tagClass: 'tag-blue', count: 1089, avgTime: 2.8, completion: 85, satisfaction: 4.1, trend: '↗ +8%', trendColor: '#52c41a' },
                    { period: '2022年', tagClass: 'tag-orange', count: 967, avgTime: 3.2, completion: 82, satisfaction: 3.9, trend: '↗ +5%', trendColor: '#52c41a' }
                ],
                month: [
                    { period: '本月', tagClass: 'tag-red', count: 165, avgTime: 2.1, completion: 92, satisfaction: 4.5, trend: '↗ +5%', trendColor: '#52c41a' },
                    { period: '上月', tagClass: 'tag-blue', count: 152, avgTime: 2.3, completion: 89, satisfaction: 4.3, trend: '↗ +3%', trendColor: '#52c41a' },
                    { period: '前月', tagClass: 'tag-orange', count: 148, avgTime: 2.4, completion: 87, satisfaction: 4.2, trend: '↗ +2%', trendColor: '#52c41a' }
                ],
                week: [
                    { period: '本週', tagClass: 'tag-red', count: 42, avgTime: 1.8, completion: 95, satisfaction: 4.7, trend: '↗ +8%', trendColor: '#52c41a' },
                    { period: '上週', tagClass: 'tag-blue', count: 38, avgTime: 2.0, completion: 92, satisfaction: 4.5, trend: '↗ +5%', trendColor: '#52c41a' },
                    { period: '前週', tagClass: 'tag-orange', count: 35, avgTime: 2.2, completion: 89, satisfaction: 4.3, trend: '↗ +3%', trendColor: '#52c41a' }
                ]
            };

            return mockData[timeRange] || mockData.year;
        }

        // 初始化統計頁面
        function initializeStatistics() {
            // 設定預設值
            document.getElementById('statisticsTimeRange').value = 'year';
            document.getElementById('statisticsYear').value = '2024';
            document.getElementById('statisticsMonth').value = new Date().getMonth() + 1;

            // 初始化月份選擇器顯示/隱藏
            toggleMonthSelector();

            // 載入初始統計數據
            updateStatistics();
        }

        // 切換月份選擇器顯示
        function toggleMonthSelector() {
            const timeRange = document.getElementById('statisticsTimeRange').value;
            const monthSelector = document.getElementById('monthSelector');

            if (timeRange === 'month' || timeRange === 'week' || timeRange === 'day') {
                monthSelector.style.display = 'flex';
            } else {
                monthSelector.style.display = 'none';
            }
        }

        // 監聽時間範圍變化
        document.addEventListener('DOMContentLoaded', function() {
            const timeRangeSelect = document.getElementById('statisticsTimeRange');
            if (timeRangeSelect) {
                timeRangeSelect.addEventListener('change', toggleMonthSelector);
            }
        });

        // 存儲模式管理函數
        function onStorageModeChange() {
            const selectedMode = document.getElementById('storageMode').value;
            const sharepointConfig = document.getElementById('sharepointConfig');

            if (selectedMode === 'sharepoint') {
                sharepointConfig.style.display = 'block';
                loadSharePointConfig();
            } else {
                sharepointConfig.style.display = 'none';
            }

            updateStorageStatus();
        }

        // 載入 SharePoint 配置
        function loadSharePointConfig() {
            const siteUrl = localStorage.getItem('sharepointSiteUrl') || '';
            const listName = localStorage.getItem('sharepointListName') || '維修記錄';

            document.getElementById('sharepointSiteUrl').value = siteUrl;
            document.getElementById('sharepointListName').value = listName;
        }

        // 保存 SharePoint 配置
        function saveSharePointConfig() {
            const siteUrl = document.getElementById('sharepointSiteUrl').value;
            const listName = document.getElementById('sharepointListName').value;

            localStorage.setItem('sharepointSiteUrl', siteUrl);
            localStorage.setItem('sharepointListName', listName);

            // 更新存儲配置
            storageConfig.sharepoint.siteUrl = siteUrl;
            storageConfig.sharepoint.listName = listName;

            dataStorage.sharepoint.siteUrl = siteUrl;
            dataStorage.sharepoint.listName = listName;
        }

        // 測試 SharePoint 連線
        async function testSharePointConnection() {
            const siteUrl = document.getElementById('sharepointSiteUrl').value;
            const statusElement = document.getElementById('connectionStatus');

            if (!siteUrl) {
                alert('請先輸入 SharePoint 網站 URL');
                return;
            }

            // 保存配置
            saveSharePointConfig();

            // 顯示測試中狀態
            statusElement.innerHTML = '<span class="tag tag-blue">測試中...</span>';

            try {
                const isConnected = await dataStorage.sharepoint.testConnection();

                if (isConnected) {
                    statusElement.innerHTML = '<span class="tag tag-green">連線成功</span>';
                    showNotification('SharePoint 連線成功！', 'success');

                    // 啟用 SharePoint 模式選項
                    const storageSelect = document.getElementById('storageMode');
                    const sharepointOption = storageSelect.querySelector('option[value="sharepoint"]');
                    sharepointOption.disabled = false;
                    sharepointOption.textContent = 'SharePoint 整合 (已連線)';
                } else {
                    statusElement.innerHTML = '<span class="tag tag-red">連線失敗</span>';
                    showNotification('SharePoint 連線失敗，請檢查網站 URL 和權限設定', 'error');
                }
            } catch (error) {
                statusElement.innerHTML = '<span class="tag tag-red">連線錯誤</span>';
                showNotification(`連線錯誤: ${error.message}`, 'error');
            }
        }

        // 建立 SharePoint 清單結構
        async function createSharePointLists() {
            const siteUrl = document.getElementById('sharepointSiteUrl').value;

            if (!siteUrl) {
                alert('請先輸入 SharePoint 網站 URL');
                return;
            }

            // 顯示建立選項對話框
            const createOption = await showCreateListsDialog();
            if (!createOption) return;

            if (createOption === 'auto') {
                await createListsAutomatically(siteUrl);
            } else if (createOption === 'manual') {
                showManualInstructions();
            } else if (createOption === 'script') {
                showPowerShellScript(siteUrl);
            }
        }

        // 顯示建立清單選項對話框
        async function showCreateListsDialog() {
            return new Promise((resolve) => {
                const dialog = document.createElement('div');
                dialog.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.5); z-index: 10000;
                    display: flex; align-items: center; justify-content: center;
                `;

                dialog.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 12px; max-width: 600px; width: 90%;">
                        <h3 style="margin: 0 0 20px 0; color: #1890ff;">🛠️ SharePoint 清單建立選項</h3>

                        <div style="margin-bottom: 20px;">
                            <p style="margin: 0 0 15px 0; color: #666;">請選擇建立 SharePoint 清單的方式：</p>
                        </div>

                        <div style="display: grid; gap: 15px;">
                            <button onclick="selectOption('auto')" style="
                                padding: 15px; border: 2px solid #52c41a; border-radius: 8px;
                                background: #f6ffed; color: #52c41a; cursor: pointer; text-align: left;
                                transition: all 0.3s;
                            " onmouseover="this.style.background='#52c41a'; this.style.color='white';"
                               onmouseout="this.style.background='#f6ffed'; this.style.color='#52c41a';">
                                <div style="font-weight: bold; margin-bottom: 5px;">🚀 自動建立 (推薦)</div>
                                <div style="font-size: 12px; opacity: 0.8;">使用 REST API 自動建立所有必要清單和欄位</div>
                            </button>

                            <button onclick="selectOption('script')" style="
                                padding: 15px; border: 2px solid #1890ff; border-radius: 8px;
                                background: #f0f9ff; color: #1890ff; cursor: pointer; text-align: left;
                                transition: all 0.3s;
                            " onmouseover="this.style.background='#1890ff'; this.style.color='white';"
                               onmouseout="this.style.background='#f0f9ff'; this.style.color='#1890ff';">
                                <div style="font-weight: bold; margin-bottom: 5px;">⚡ PowerShell 腳本</div>
                                <div style="font-size: 12px; opacity: 0.8;">生成 PowerShell 腳本，在本地執行建立清單</div>
                            </button>

                            <button onclick="selectOption('manual')" style="
                                padding: 15px; border: 2px solid #fa8c16; border-radius: 8px;
                                background: #fff7e6; color: #fa8c16; cursor: pointer; text-align: left;
                                transition: all 0.3s;
                            " onmouseover="this.style.background='#fa8c16'; this.style.color='white';"
                               onmouseout="this.style.background='#fff7e6'; this.style.color='#fa8c16';">
                                <div style="font-weight: bold; margin-bottom: 5px;">📋 手動建立</div>
                                <div style="font-size: 12px; opacity: 0.8;">顯示詳細說明，手動在 SharePoint 中建立清單</div>
                            </button>
                        </div>

                        <div style="margin-top: 20px; text-align: right;">
                            <button onclick="selectOption(null)" style="
                                padding: 8px 16px; border: 1px solid #d9d9d9; border-radius: 4px;
                                background: white; color: #666; cursor: pointer; margin-right: 10px;
                            ">取消</button>
                        </div>
                    </div>
                `;

                document.body.appendChild(dialog);

                window.selectOption = (option) => {
                    document.body.removeChild(dialog);
                    delete window.selectOption;
                    resolve(option);
                };
            });
        }

        // 自動建立清單
        async function createListsAutomatically(siteUrl) {
            try {
                showNotification('正在自動建立 SharePoint 清單...', 'info');

                // 定義清單結構
                const listsToCreate = [
                    {
                        name: '維修記錄',
                        description: 'IACT MIO維保管理系統 - 維修記錄清單',
                        fields: [
                            { name: 'RepairId', type: 'Text', displayName: '維修編號', required: true },
                            { name: 'CustomerName', type: 'Text', displayName: '客戶姓名', required: true },
                            { name: 'Phone', type: 'Text', displayName: '聯絡電話' },
                            { name: 'SerialNumber', type: 'Text', displayName: '產品序號' },
                            { name: 'ProductName', type: 'Text', displayName: '產品名稱', required: true },
                            { name: 'ProductModel', type: 'Text', displayName: '產品型號' },
                            { name: 'Issue', type: 'Note', displayName: '問題描述' },
                            { name: 'ServiceStatus', type: 'Choice', displayName: '維保狀態', choices: ['客訴', '維保'] },
                            { name: 'RepairRecord', type: 'Choice', displayName: '維修記錄', choices: ['調整', '換馬達', '清潔', '校正', '更換零件', '軟體更新'] },
                            { name: 'TestResult', type: 'Choice', displayName: '測試結果', choices: ['正常', '異常', '待測試'] },
                            { name: 'Technician', type: 'Text', displayName: '維保員' },
                            { name: 'RepairDate', type: 'DateTime', displayName: '維修日期' },
                            { name: 'CompletionDate', type: 'DateTime', displayName: '完成日期' },
                            { name: 'Notes', type: 'Note', displayName: '備註' }
                        ]
                    },
                    {
                        name: '客戶資料',
                        description: 'IACT MIO維保管理系統 - 客戶資料清單',
                        fields: [
                            { name: 'CustomerId', type: 'Text', displayName: '客戶編號', required: true },
                            { name: 'CustomerName', type: 'Text', displayName: '客戶姓名', required: true },
                            { name: 'Phone', type: 'Text', displayName: '聯絡電話' },
                            { name: 'Email', type: 'Text', displayName: '電子郵箱' },
                            { name: 'Address', type: 'Note', displayName: '地址' },
                            { name: 'Company', type: 'Text', displayName: '公司名稱' },
                            { name: 'Status', type: 'Choice', displayName: '客戶狀態', choices: ['活躍', '非活躍', 'VIP', '黑名單'] },
                            { name: 'RegisterDate', type: 'DateTime', displayName: '註冊日期' },
                            { name: 'Notes', type: 'Note', displayName: '備註' }
                        ]
                    },
                    {
                        name: '零件資料',
                        description: 'IACT MIO維保管理系統 - 零件資料清單',
                        fields: [
                            { name: 'PartId', type: 'Text', displayName: '零件編號', required: true },
                            { name: 'PartName', type: 'Text', displayName: '零件名稱', required: true },
                            { name: 'Category', type: 'Choice', displayName: '零件分類', choices: ['電子零件', '機械零件', '消耗品', '工具', '配件'] },
                            { name: 'Model', type: 'Text', displayName: '型號' },
                            { name: 'Supplier', type: 'Text', displayName: '供應商' },
                            { name: 'Stock', type: 'Number', displayName: '庫存數量' },
                            { name: 'Price', type: 'Currency', displayName: '單價' },
                            { name: 'MinStock', type: 'Number', displayName: '最低庫存警告' },
                            { name: 'Location', type: 'Text', displayName: '存放位置' },
                            { name: 'Specifications', type: 'Note', displayName: '規格說明' },
                            { name: 'Notes', type: 'Note', displayName: '備註' }
                        ]
                    },
                    {
                        name: '產品資料',
                        description: 'IACT MIO維保管理系統 - 產品資料清單',
                        fields: [
                            { name: 'ProductId', type: 'Text', displayName: '產品編號', required: true },
                            { name: 'ProductName', type: 'Text', displayName: '產品名稱', required: true },
                            { name: 'Brand', type: 'Text', displayName: '品牌' },
                            { name: 'Model', type: 'Text', displayName: '型號' },
                            { name: 'Category', type: 'Choice', displayName: '產品分類', choices: ['手機', '平板', '筆電', '桌機', '印表機', '其他'] },
                            { name: 'Description', type: 'Note', displayName: '產品描述' },
                            { name: 'WarrantyPeriod', type: 'Text', displayName: '保固期間' },
                            { name: 'Notes', type: 'Note', displayName: '備註' }
                        ]
                    }
                ];

                let successCount = 0;
                let errorCount = 0;
                const results = [];

                for (const listConfig of listsToCreate) {
                    try {
                        const result = await createSharePointList(siteUrl, listConfig);
                        if (result.success) {
                            successCount++;
                            results.push(`✅ ${listConfig.name}: 建立成功`);
                        } else {
                            errorCount++;
                            results.push(`❌ ${listConfig.name}: ${result.error}`);
                        }
                    } catch (error) {
                        errorCount++;
                        results.push(`❌ ${listConfig.name}: ${error.message}`);
                    }
                }

                // 顯示結果
                const resultMessage = `
SharePoint 清單建立完成！

成功: ${successCount} 個清單
失敗: ${errorCount} 個清單

詳細結果:
${results.join('\n')}

${successCount > 0 ? '\n✅ 可以開始使用 SharePoint 整合功能了！' : '\n⚠️ 請檢查權限設定或使用其他建立方式。'}
                `;

                alert(resultMessage);

                if (successCount > 0) {
                    showNotification(`成功建立 ${successCount} 個 SharePoint 清單`, 'success');
                } else {
                    showNotification('清單建立失敗，請檢查權限或使用其他方式', 'warning');
                }

            } catch (error) {
                console.error('自動建立清單失敗:', error);

                // 顯示詳細的錯誤分析和解決方案
                showAutoCreateErrorDialog(error, siteUrl);
            }
        }

        // 顯示自動建立錯誤對話框
        function showAutoCreateErrorDialog(error, siteUrl) {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;

            // 分析錯誤類型
            let errorType = 'unknown';
            let errorTitle = '❌ 自動建立失敗';
            let errorDescription = '';
            let solutions = [];

            if (error.message.includes('Failed to fetch') || error.message.includes('CORS')) {
                errorType = 'cors';
                errorTitle = '🚫 CORS 政策限制';
                errorDescription = 'SharePoint 不允許從瀏覽器直接建立清單，這是正常的安全限制。';
                solutions = [
                    '使用 PowerShell 腳本 (推薦)',
                    '手動建立清單',
                    '聯絡 IT 管理員設定 CORS 政策'
                ];
            } else if (error.message.includes('Access denied') || error.message.includes('401') || error.message.includes('403')) {
                errorType = 'permission';
                errorTitle = '🔒 權限不足';
                errorDescription = '您沒有足夠的權限在 SharePoint 中建立清單。';
                solutions = [
                    '聯絡 SharePoint 管理員申請權限',
                    '使用具有管理員權限的帳號',
                    '使用 PowerShell 腳本 (需要管理員執行)',
                    '請管理員手動建立清單'
                ];
            } else if (error.message.includes('404') || error.message.includes('not found')) {
                errorType = 'notfound';
                errorTitle = '🔍 網站不存在';
                errorDescription = 'SharePoint 網站 URL 可能不正確或網站不存在。';
                solutions = [
                    '檢查 SharePoint 網站 URL 是否正確',
                    '確認網站存在且可以存取',
                    '檢查網路連線',
                    '聯絡 SharePoint 管理員確認網站狀態'
                ];
            } else {
                errorDescription = `發生未預期的錯誤：${error.message}`;
                solutions = [
                    '使用 PowerShell 腳本作為替代方案',
                    '手動建立清單',
                    '檢查網路連線',
                    '聯絡技術支援'
                ];
            }

            dialog.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 12px; max-width: 700px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <h3 style="margin: 0 0 10px 0; color: #f5222d; font-size: 24px;">${errorTitle}</h3>
                        <p style="margin: 0; color: #666; font-size: 16px;">${errorDescription}</p>
                    </div>

                    <div style="background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #cf1322;">🔍 錯誤詳情</h4>
                        <code style="background: #f5f5f5; padding: 8px; border-radius: 4px; display: block; font-size: 12px; color: #d4380d;">
                            ${error.message}
                        </code>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="margin: 0 0 15px 0; color: #1890ff;">💡 建議解決方案</h4>
                        <div style="display: grid; gap: 10px;">
                            ${solutions.map((solution, index) => `
                                <div style="display: flex; align-items: center; padding: 10px; background: #f0f9ff; border-radius: 6px; border-left: 3px solid #1890ff;">
                                    <span style="color: #1890ff; font-weight: bold; margin-right: 10px;">${index + 1}.</span>
                                    <span style="color: #333;">${solution}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #52c41a;">✅ 推薦做法</h4>
                        <p style="margin: 0; color: #389e0d;">
                            ${errorType === 'cors' ?
                                '由於瀏覽器安全限制，建議使用 <strong>PowerShell 腳本</strong> 來建立清單。這是最可靠的方法。' :
                                errorType === 'permission' ?
                                '請聯絡您的 SharePoint 管理員申請適當權限，或使用 <strong>PowerShell 腳本</strong>。' :
                                '建議使用 <strong>PowerShell 腳本</strong> 或 <strong>手動建立</strong> 作為替代方案。'
                            }
                        </p>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="usePowerShellScript()" style="
                            padding: 12px 24px; border: none; border-radius: 6px;
                            background: #52c41a; color: white; cursor: pointer; font-weight: bold;
                            font-size: 14px; min-width: 140px;
                        ">⚡ PowerShell 腳本</button>

                        <button onclick="useManualCreate()" style="
                            padding: 12px 24px; border: 1px solid #1890ff; border-radius: 6px;
                            background: white; color: #1890ff; cursor: pointer; font-weight: bold;
                            font-size: 14px; min-width: 140px;
                        ">📋 手動建立</button>

                        <button onclick="closeErrorDialog()" style="
                            padding: 12px 24px; border: 1px solid #d9d9d9; border-radius: 6px;
                            background: white; color: #666; cursor: pointer;
                            font-size: 14px; min-width: 100px;
                        ">關閉</button>
                    </div>

                    <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #f0f0f0; text-align: center;">
                        <p style="margin: 0; color: #999; font-size: 12px;">
                            💡 提示：PowerShell 腳本是最可靠的建立方式，不受瀏覽器安全限制影響
                        </p>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            window.usePowerShellScript = () => {
                document.body.removeChild(dialog);
                showPowerShellScript(siteUrl);
                cleanupErrorDialog();
            };

            window.useManualCreate = () => {
                document.body.removeChild(dialog);
                showManualInstructions();
                cleanupErrorDialog();
            };

            window.closeErrorDialog = () => {
                document.body.removeChild(dialog);
                cleanupErrorDialog();
            };

            function cleanupErrorDialog() {
                delete window.usePowerShellScript;
                delete window.useManualCreate;
                delete window.closeErrorDialog;
            }
        }

        // 實際建立 SharePoint 清單
        async function createSharePointList(siteUrl, listConfig) {
            try {
                // 首先建立清單
                const createListResponse = await fetch(`${siteUrl}/_api/web/lists`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json;odata=verbose',
                        'Content-Type': 'application/json;odata=verbose',
                        'X-RequestDigest': await getRequestDigest(siteUrl)
                    },
                    body: JSON.stringify({
                        '__metadata': { 'type': 'SP.List' },
                        'AllowContentTypes': true,
                        'BaseTemplate': 100, // Generic List
                        'ContentTypesEnabled': false,
                        'Description': listConfig.description,
                        'Title': listConfig.name
                    })
                });

                if (!createListResponse.ok) {
                    const errorText = await createListResponse.text();
                    if (errorText.includes('already exists')) {
                        return { success: false, error: '清單已存在' };
                    }
                    throw new Error(`建立清單失敗: ${createListResponse.status} ${errorText}`);
                }

                // 等待一下讓清單完全建立
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 建立欄位
                let fieldSuccessCount = 0;
                for (const field of listConfig.fields) {
                    try {
                        await createSharePointField(siteUrl, listConfig.name, field);
                        fieldSuccessCount++;
                    } catch (fieldError) {
                        console.warn(`建立欄位 ${field.name} 失敗:`, fieldError);
                    }
                }

                return {
                    success: true,
                    fieldsCreated: fieldSuccessCount,
                    totalFields: listConfig.fields.length
                };

            } catch (error) {
                console.error('建立清單失敗:', error);
                return { success: false, error: error.message };
            }
        }

        // 建立 SharePoint 欄位
        async function createSharePointField(siteUrl, listName, fieldConfig) {
            let fieldXml = '';

            switch (fieldConfig.type) {
                case 'Text':
                    fieldXml = `<Field Type='Text' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' ${fieldConfig.required ? "Required='TRUE'" : ''} />`;
                    break;
                case 'Note':
                    fieldXml = `<Field Type='Note' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' />`;
                    break;
                case 'Number':
                    fieldXml = `<Field Type='Number' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' />`;
                    break;
                case 'Currency':
                    fieldXml = `<Field Type='Currency' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' />`;
                    break;
                case 'DateTime':
                    fieldXml = `<Field Type='DateTime' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' Format='DateOnly' />`;
                    break;
                case 'Choice':
                    const choices = fieldConfig.choices.map(choice => `<CHOICE>${choice}</CHOICE>`).join('');
                    fieldXml = `<Field Type='Choice' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}'><CHOICES>${choices}</CHOICES></Field>`;
                    break;
                default:
                    fieldXml = `<Field Type='Text' DisplayName='${fieldConfig.displayName}' Name='${fieldConfig.name}' />`;
            }

            const response = await fetch(`${siteUrl}/_api/web/lists/getbytitle('${listName}')/fields/createfieldasxml`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json;odata=verbose',
                    'Content-Type': 'application/json;odata=verbose',
                    'X-RequestDigest': await getRequestDigest(siteUrl)
                },
                body: JSON.stringify({
                    'parameters': {
                        '__metadata': { 'type': 'SP.XmlSchemaFieldCreationInformation' },
                        'SchemaXml': fieldXml
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`建立欄位失敗: ${response.status} ${errorText}`);
            }

            return true;
        }

        // 取得 SharePoint Request Digest
        async function getRequestDigest(siteUrl) {
            try {
                const response = await fetch(`${siteUrl}/_api/contextinfo`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json;odata=verbose',
                        'Content-Type': 'application/json;odata=verbose'
                    }
                });

                if (!response.ok) {
                    throw new Error('無法取得 Request Digest');
                }

                const data = await response.json();
                return data.d.GetContextWebInformation.FormDigestValue;
            } catch (error) {
                console.error('取得 Request Digest 失敗:', error);
                throw error;
            }
        }

        // 顯示手動建立說明
        function showManualInstructions() {
            const instructions = `
📋 SharePoint 清單手動建立指南

請在您的 SharePoint 網站中按照以下步驟建立清單：

🔧 1. 維修記錄清單
   清單名稱：維修記錄
   欄位：
   • RepairId (文字) - 維修編號 *必填
   • CustomerName (文字) - 客戶姓名 *必填
   • Phone (文字) - 聯絡電話
   • SerialNumber (文字) - 產品序號
   • ProductName (文字) - 產品名稱 *必填
   • ProductModel (文字) - 產品型號
   • Issue (多行文字) - 問題描述
   • ServiceStatus (選擇) - 維保狀態 [客訴, 維保]
   • RepairRecord (選擇) - 維修記錄 [調整, 換馬達, 清潔, 校正, 更換零件, 軟體更新]
   • TestResult (選擇) - 測試結果 [正常, 異常, 待測試]
   • Technician (文字) - 維保員
   • RepairDate (日期時間) - 維修日期
   • CompletionDate (日期時間) - 完成日期
   • Notes (多行文字) - 備註

👥 2. 客戶資料清單
   清單名稱：客戶資料
   欄位：
   • CustomerId (文字) - 客戶編號 *必填
   • CustomerName (文字) - 客戶姓名 *必填
   • Phone (文字) - 聯絡電話
   • Email (文字) - 電子郵箱
   • Address (多行文字) - 地址
   • Company (文字) - 公司名稱
   • Status (選擇) - 客戶狀態 [活躍, 非活躍, VIP, 黑名單]
   • RegisterDate (日期時間) - 註冊日期
   • Notes (多行文字) - 備註

🔩 3. 零件資料清單
   清單名稱：零件資料
   欄位：
   • PartId (文字) - 零件編號 *必填
   • PartName (文字) - 零件名稱 *必填
   • Category (選擇) - 零件分類 [電子零件, 機械零件, 消耗品, 工具, 配件]
   • Model (文字) - 型號
   • Supplier (文字) - 供應商
   • Stock (數字) - 庫存數量
   • Price (貨幣) - 單價
   • MinStock (數字) - 最低庫存警告
   • Location (文字) - 存放位置
   • Specifications (多行文字) - 規格說明
   • Notes (多行文字) - 備註

📱 4. 產品資料清單
   清單名稱：產品資料
   欄位：
   • ProductId (文字) - 產品編號 *必填
   • ProductName (文字) - 產品名稱 *必填
   • Brand (文字) - 品牌
   • Model (文字) - 型號
   • Category (選擇) - 產品分類 [手機, 平板, 筆電, 桌機, 印表機, 其他]
   • Description (多行文字) - 產品描述
   • WarrantyPeriod (文字) - 保固期間
   • Notes (多行文字) - 備註

📝 建立步驟：
1. 進入您的 SharePoint 網站
2. 點擊 "新增" → "清單"
3. 選擇 "空白清單"
4. 輸入清單名稱
5. 點擊 "建立"
6. 新增上述欄位

完成後請回到系統測試連線！
            `;

            // 建立一個可複製的對話框
            showInstructionsDialog(instructions);
        }

        // 顯示說明對話框
        function showInstructionsDialog(content) {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;

            dialog.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 12px; max-width: 800px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #1890ff;">📋 SharePoint 清單建立指南</h3>
                        <button onclick="closeDialog()" style="
                            background: none; border: none; font-size: 24px; cursor: pointer;
                            color: #999; padding: 0; width: 30px; height: 30px;
                        ">×</button>
                    </div>

                    <div style="background: #f6f6f6; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <pre style="white-space: pre-wrap; font-family: 'Microsoft JhengHei', Arial, sans-serif; font-size: 14px; line-height: 1.6; margin: 0;">${content}</pre>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="copyInstructions()" style="
                            padding: 8px 16px; border: 1px solid #1890ff; border-radius: 4px;
                            background: white; color: #1890ff; cursor: pointer;
                        ">📋 複製說明</button>
                        <button onclick="closeDialog()" style="
                            padding: 8px 16px; border: 1px solid #d9d9d9; border-radius: 4px;
                            background: white; color: #666; cursor: pointer;
                        ">關閉</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            window.closeDialog = () => {
                document.body.removeChild(dialog);
                delete window.closeDialog;
                delete window.copyInstructions;
            };

            window.copyInstructions = () => {
                navigator.clipboard.writeText(content).then(() => {
                    showNotification('說明已複製到剪貼簿', 'success');
                }).catch(() => {
                    showNotification('複製失敗，請手動複製', 'warning');
                });
            };
        }

        // 顯示 PowerShell 腳本輔助界面
        function showPowerShellScript(siteUrl) {
            // 顯示簡化的輔助界面而不是複雜的腳本
            showPowerShellAssistant(siteUrl);
        }

        // PowerShell 輔助界面
        function showPowerShellAssistant(siteUrl) {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;

            dialog.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 12px; max-width: 800px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <h3 style="margin: 0 0 10px 0; color: #1890ff; font-size: 24px;">⚡ PowerShell 輔助工具</h3>
                        <p style="margin: 0; color: #666; font-size: 16px;">我們將協助您一步步完成 SharePoint 清單建立</p>
                    </div>

                    <div id="assistantSteps" style="margin-bottom: 25px;">
                        <!-- 步驟內容會動態載入 -->
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                        <button id="prevBtn" onclick="previousStep()" style="
                            padding: 12px 24px; border: 1px solid #d9d9d9; border-radius: 6px;
                            background: white; color: #666; cursor: pointer; font-size: 14px;
                            display: none;
                        ">⬅️ 上一步</button>

                        <button id="nextBtn" onclick="nextStep()" style="
                            padding: 12px 24px; border: none; border-radius: 6px;
                            background: #1890ff; color: white; cursor: pointer; font-weight: bold;
                            font-size: 14px; min-width: 120px;
                        ">開始 ➡️</button>

                        <button onclick="closePowerShellAssistant()" style="
                            padding: 12px 24px; border: 1px solid #d9d9d9; border-radius: 6px;
                            background: white; color: #666; cursor: pointer; font-size: 14px;
                        ">關閉</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // 初始化輔助工具
            initializePowerShellAssistant(siteUrl);

            window.closePowerShellAssistant = () => {
                document.body.removeChild(dialog);
                cleanupPowerShellAssistant();
            };
        }

        // 初始化 PowerShell 輔助工具
        function initializePowerShellAssistant(siteUrl) {
            window.currentStep = 0;
            window.siteUrl = siteUrl;
            window.steps = [
                {
                    title: "📋 準備工作",
                    content: `
                        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #1890ff;">
                            <h4 style="margin: 0 0 15px 0; color: #1890ff;">開始之前，請確認以下事項：</h4>
                            <div style="display: grid; gap: 10px;">
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" id="check1" style="margin-right: 10px; transform: scale(1.2);">
                                    <span>我有 SharePoint 網站的管理員權限或參與者權限</span>
                                </label>
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" id="check2" style="margin-right: 10px; transform: scale(1.2);">
                                    <span>我可以存取 SharePoint 網站：<br><code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">${siteUrl}</code></span>
                                </label>
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" id="check3" style="margin-right: 10px; transform: scale(1.2);">
                                    <span>我的電腦可以正常連接網路</span>
                                </label>
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" id="check4" style="margin-right: 10px; transform: scale(1.2);">
                                    <span>我願意按照指導完成 PowerShell 操作</span>
                                </label>
                            </div>
                        </div>
                    `
                },
                {
                    title: "🔧 開啟 PowerShell",
                    content: `
                        <div style="background: #fff7e6; padding: 20px; border-radius: 8px; border-left: 4px solid #fa8c16;">
                            <h4 style="margin: 0 0 15px 0; color: #fa8c16;">請按照以下步驟開啟 PowerShell：</h4>
                            <div style="display: grid; gap: 15px;">
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #ffd591;">
                                    <span style="background: #fa8c16; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">1</span>
                                    <span>按 <kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px; border: 1px solid #ddd;">Windows 鍵 + R</kbd></span>
                                </div>
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #ffd591;">
                                    <span style="background: #fa8c16; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">2</span>
                                    <span>輸入 <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">powershell</code> 然後按 Enter</span>
                                </div>
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #ffd591;">
                                    <span style="background: #fa8c16; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">3</span>
                                    <span>如果出現藍色視窗，表示 PowerShell 已開啟成功</span>
                                </div>
                            </div>
                            <div style="margin-top: 15px; padding: 10px; background: #f0f9ff; border-radius: 6px;">
                                <strong>💡 提示：</strong> 如果需要管理員權限，請右鍵點擊 PowerShell 選擇「以系統管理員身分執行」
                            </div>
                        </div>
                    `
                },
                {
                    title: "⚙️ 設定執行原則",
                    content: `
                        <div style="background: #f6ffed; padding: 20px; border-radius: 8px; border-left: 4px solid #52c41a;">
                            <h4 style="margin: 0 0 15px 0; color: #52c41a;">在 PowerShell 中執行以下命令：</h4>
                            <div style="background: #1e1e1e; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                                <code style="color: #d4d4d4; font-family: 'Consolas', monospace; font-size: 14px;">
                                    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
                                </code>
                                <button onclick="copyToClipboard('Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser')" style="
                                    float: right; background: #52c41a; color: white; border: none; padding: 4px 8px;
                                    border-radius: 3px; cursor: pointer; font-size: 12px;
                                ">📋 複製</button>
                            </div>
                            <div style="background: #fff2f0; padding: 10px; border-radius: 6px; border: 1px solid #ffccc7;">
                                <strong>⚠️ 注意：</strong> 如果系統詢問是否要變更執行原則，請輸入 <code>Y</code> 然後按 Enter
                            </div>
                        </div>
                    `
                },
                {
                    title: "📥 下載並執行腳本",
                    content: `
                        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #1890ff;">
                            <h4 style="margin: 0 0 15px 0; color: #1890ff;">現在下載並執行建立腳本：</h4>

                            <div style="margin-bottom: 20px;">
                                <button onclick="downloadSimpleScript()" style="
                                    padding: 15px 30px; background: #52c41a; color: white; border: none;
                                    border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold;
                                    width: 100%; margin-bottom: 15px;
                                ">💾 下載簡化腳本</button>
                            </div>

                            <div style="background: #fff7e6; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                                <h5 style="margin: 0 0 10px 0; color: #fa8c16;">下載後請執行以下步驟：</h5>
                                <ol style="margin: 0; padding-left: 20px; color: #666;">
                                    <li>在 PowerShell 中輸入：<code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">cd $env:USERPROFILE\\Downloads</code></li>
                                    <li>然後輸入：<code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">Unblock-File -Path "SharePointHelper.ps1"</code></li>
                                    <li>最後輸入：<code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">.\\SharePointHelper.ps1</code></li>
                                </ol>
                            </div>

                            <div style="background: #f6ffed; padding: 10px; border-radius: 6px; border: 1px solid #b7eb8f;">
                                <strong>✅ 成功標誌：</strong> 腳本會自動安裝必要模組並開啟瀏覽器進行 SharePoint 登入
                            </div>
                        </div>
                    `
                },
                {
                    title: "🎉 完成建立",
                    content: `
                        <div style="background: #f6ffed; padding: 20px; border-radius: 8px; border-left: 4px solid #52c41a;">
                            <h4 style="margin: 0 0 15px 0; color: #52c41a;">腳本執行完成後：</h4>
                            <div style="display: grid; gap: 10px;">
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #b7eb8f;">
                                    <span style="background: #52c41a; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">✓</span>
                                    <span>檢查 SharePoint 網站是否已建立所需清單</span>
                                </div>
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #b7eb8f;">
                                    <span style="background: #52c41a; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">✓</span>
                                    <span>回到維保管理系統測試 SharePoint 連線</span>
                                </div>
                                <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; border: 1px solid #b7eb8f;">
                                    <span style="background: #52c41a; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">✓</span>
                                    <span>開始使用企業級維保管理功能</span>
                                </div>
                            </div>

                            <div style="margin-top: 20px; text-align: center;">
                                <button onclick="testSharePointConnection()" style="
                                    padding: 12px 24px; background: #1890ff; color: white; border: none;
                                    border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold;
                                ">🔗 立即測試連線</button>
                            </div>
                        </div>
                    `
                }
            ];

            showCurrentStep();
        }

        // 顯示當前步驟
        function showCurrentStep() {
            const step = window.steps[window.currentStep];
            const stepsContainer = document.getElementById('assistantSteps');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            stepsContainer.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: #1890ff;">${step.title}</h4>
                        <span style="background: #f0f0f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #666;">
                            ${window.currentStep + 1} / ${window.steps.length}
                        </span>
                    </div>
                    ${step.content}
                </div>
            `;

            // 更新按鈕狀態
            prevBtn.style.display = window.currentStep > 0 ? 'inline-block' : 'none';

            if (window.currentStep === window.steps.length - 1) {
                nextBtn.textContent = '完成 ✅';
                nextBtn.onclick = () => {
                    showNotification('PowerShell 輔助完成！請測試 SharePoint 連線', 'success');
                    closePowerShellAssistant();
                };
            } else {
                nextBtn.textContent = '下一步 ➡️';
                nextBtn.onclick = nextStep;
            }
        }

        // 下一步
        function nextStep() {
            if (window.currentStep === 0) {
                // 檢查第一步的核取方塊
                const checks = ['check1', 'check2', 'check3', 'check4'];
                const allChecked = checks.every(id => document.getElementById(id)?.checked);

                if (!allChecked) {
                    alert('請確認所有準備事項都已完成！');
                    return;
                }
            }

            if (window.currentStep < window.steps.length - 1) {
                window.currentStep++;
                showCurrentStep();
            }
        }

        // 上一步
        function previousStep() {
            if (window.currentStep > 0) {
                window.currentStep--;
                showCurrentStep();
            }
        }

        // 複製到剪貼簿
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('已複製到剪貼簿', 'success');
            }).catch(() => {
                showNotification('複製失敗，請手動複製', 'warning');
            });
        }

        // 下載簡化腳本
        function downloadSimpleScript() {
            const script = generateSimpleScript(window.siteUrl);
            const blob = new Blob([script], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'SharePointHelper.ps1';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showNotification('簡化腳本已下載', 'success');
        }

        // 生成簡化腳本 (修復編碼問題)
        function generateSimpleScript(siteUrl) {
            return `# SharePoint List Creation Helper Script
# IACT MIO Maintenance Management System - Simplified Version

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  IACT MIO Maintenance Management System" -ForegroundColor Cyan
Write-Host "  SharePoint List Creation Helper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$SiteUrl = "${siteUrl}"
Write-Host "Target SharePoint Site: $SiteUrl" -ForegroundColor Green
Write-Host ""

# Check and install PnP PowerShell module
Write-Host "Checking PnP PowerShell module..." -ForegroundColor Yellow
if (!(Get-Module -ListAvailable -Name PnP.PowerShell)) {
    Write-Host "Installing PnP PowerShell module, please wait..." -ForegroundColor Yellow
    try {
        Install-Module -Name PnP.PowerShell -Force -AllowClobber -Scope CurrentUser
        Write-Host "PnP PowerShell module installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "Module installation failed: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "PnP PowerShell module is already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "Connecting to SharePoint..." -ForegroundColor Yellow
Write-Host "Browser will open for authentication" -ForegroundColor Cyan

try {
    Connect-PnPOnline -Url $SiteUrl -Interactive
    Write-Host "SharePoint connection successful!" -ForegroundColor Green
} catch {
    Write-Host "SharePoint connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "   1. Check if the site URL is correct" -ForegroundColor White
    Write-Host "   2. Confirm you have site access permissions" -ForegroundColor White
    Write-Host "   3. Check network connection" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting SharePoint list creation..." -ForegroundColor Yellow

# Define lists to create with English names to avoid encoding issues
$listsToCreate = @(
    @{Name="RepairRecords"; DisplayName="Repair Records"; Description="Repair records management list"},
    @{Name="CustomerData"; DisplayName="Customer Data"; Description="Customer data management list"},
    @{Name="PartsData"; DisplayName="Parts Data"; Description="Parts data management list"},
    @{Name="ProductData"; DisplayName="Product Data"; Description="Product data management list"}
)

$successCount = 0
$skipCount = 0

foreach ($listInfo in $listsToCreate) {
    Write-Host "Creating list: $($listInfo.DisplayName)..." -ForegroundColor Cyan
    try {
        $existingList = Get-PnPList -Identity $listInfo.Name -ErrorAction SilentlyContinue
        if ($existingList) {
            Write-Host "   List already exists, skipping creation" -ForegroundColor Yellow
            $skipCount++
        } else {
            $newList = New-PnPList -Title $listInfo.Name -Template GenericList -Description $listInfo.Description
            Write-Host "   List created successfully" -ForegroundColor Green
            $successCount++

            # Add basic fields for each list
            if ($listInfo.Name -eq "RepairRecords") {
                Add-PnPField -List $newList -DisplayName "Repair ID" -InternalName "RepairId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Customer Name" -InternalName "CustomerName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Phone" -InternalName "Phone" -Type Text
                Add-PnPField -List $newList -DisplayName "Serial Number" -InternalName "SerialNumber" -Type Text
                Add-PnPField -List $newList -DisplayName "Product Name" -InternalName "ProductName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Product Model" -InternalName "ProductModel" -Type Text
                Add-PnPField -List $newList -DisplayName "Issue" -InternalName "Issue" -Type Note
                Add-PnPField -List $newList -DisplayName "Service Status" -InternalName "ServiceStatus" -Type Choice -Choices @("Complaint", "Maintenance")
                Add-PnPField -List $newList -DisplayName "Repair Record" -InternalName "RepairRecord" -Type Choice -Choices @("Adjustment", "Motor Replacement", "Cleaning", "Calibration", "Parts Replacement", "Software Update")
                Add-PnPField -List $newList -DisplayName "Test Result" -InternalName "TestResult" -Type Choice -Choices @("Normal", "Abnormal", "Pending Test")
                Add-PnPField -List $newList -DisplayName "Technician" -InternalName "Technician" -Type Text
                Add-PnPField -List $newList -DisplayName "Repair Date" -InternalName "RepairDate" -Type DateTime
                Write-Host "   Added fields to Repair Records list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "CustomerData") {
                Add-PnPField -List $newList -DisplayName "Customer ID" -InternalName "CustomerId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Customer Name" -InternalName "CustomerName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Phone" -InternalName "Phone" -Type Text
                Add-PnPField -List $newList -DisplayName "Email" -InternalName "Email" -Type Text
                Add-PnPField -List $newList -DisplayName "Address" -InternalName "Address" -Type Note
                Add-PnPField -List $newList -DisplayName "Company" -InternalName "Company" -Type Text
                Add-PnPField -List $newList -DisplayName "Status" -InternalName "Status" -Type Choice -Choices @("Active", "Inactive", "VIP", "Blacklist")
                Write-Host "   Added fields to Customer Data list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "PartsData") {
                Add-PnPField -List $newList -DisplayName "Part ID" -InternalName "PartId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Part Name" -InternalName "PartName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Category" -InternalName "Category" -Type Choice -Choices @("Electronic Parts", "Mechanical Parts", "Consumables", "Tools", "Accessories")
                Add-PnPField -List $newList -DisplayName "Model" -InternalName "Model" -Type Text
                Add-PnPField -List $newList -DisplayName "Supplier" -InternalName "Supplier" -Type Text
                Add-PnPField -List $newList -DisplayName "Stock" -InternalName "Stock" -Type Number
                Add-PnPField -List $newList -DisplayName "Price" -InternalName "Price" -Type Currency
                Add-PnPField -List $newList -DisplayName "Min Stock" -InternalName "MinStock" -Type Number
                Add-PnPField -List $newList -DisplayName "Location" -InternalName "Location" -Type Text
                Write-Host "   Added fields to Parts Data list" -ForegroundColor Green
            }
            elseif ($listInfo.Name -eq "ProductData") {
                Add-PnPField -List $newList -DisplayName "Product ID" -InternalName "ProductId" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Product Name" -InternalName "ProductName" -Type Text -Required
                Add-PnPField -List $newList -DisplayName "Brand" -InternalName "Brand" -Type Text
                Add-PnPField -List $newList -DisplayName "Model" -InternalName "Model" -Type Text
                Add-PnPField -List $newList -DisplayName "Category" -InternalName "Category" -Type Choice -Choices @("Mobile Phone", "Tablet", "Laptop", "Desktop", "Printer", "Other")
                Add-PnPField -List $newList -DisplayName "Description" -InternalName "Description" -Type Note
                Add-PnPField -List $newList -DisplayName "Warranty Period" -InternalName "WarrantyPeriod" -Type Text
                Write-Host "   Added fields to Product Data list" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "   List creation failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Creation Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Successfully created: $successCount lists" -ForegroundColor Green
Write-Host "Already existed: $skipCount lists" -ForegroundColor Yellow
Write-Host ""

if ($successCount -gt 0 -or $skipCount -gt 0) {
    Write-Host "SharePoint lists are ready!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "   1. Return to the maintenance management system" -ForegroundColor White
    Write-Host "   2. Go to System Settings -> SharePoint Integration" -ForegroundColor White
    Write-Host "   3. Click 'Test Connection' button" -ForegroundColor White
    Write-Host "   4. Start using the system after successful connection" -ForegroundColor White
} else {
    Write-Host "No lists were successfully created" -ForegroundColor Red
    Write-Host "   Please check permissions or contact SharePoint administrator" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "IMPORTANT: The lists are created with English names to avoid encoding issues." -ForegroundColor Cyan
Write-Host "The system will automatically map to the correct display names." -ForegroundColor Cyan
Write-Host ""

Disconnect-PnPOnline
Read-Host "Press Enter to exit"`;
        }

        // 測試 SharePoint 連線
        function testSharePointConnection() {
            closePowerShellAssistant();
            // 觸發連線測試
            testSharePointConnection();
        }

        // 清理輔助工具
        function cleanupPowerShellAssistant() {
            delete window.currentStep;
            delete window.siteUrl;
            delete window.steps;
            delete window.closePowerShellAssistant;
            delete window.nextStep;
            delete window.previousStep;
            delete window.copyToClipboard;
            delete window.downloadSimpleScript;
            delete window.testSharePointConnection;
        }

        // 原始的 PowerShell 腳本顯示功能 (備用)
        function showPowerShellScriptOriginal(siteUrl) {
            const script = `# SharePoint 清單建立腳本
# IACT MIO維保管理系統 - 自動建立所需清單

# 檢查 PnP PowerShell 模組
if (!(Get-Module -ListAvailable -Name PnP.PowerShell)) {
    Write-Host "正在安裝 PnP PowerShell 模組..." -ForegroundColor Yellow
    Install-Module -Name PnP.PowerShell -Force -AllowClobber
}

# 連線到 SharePoint
$SiteUrl = "${siteUrl}"
Write-Host "正在連線到 SharePoint: $SiteUrl" -ForegroundColor Green

try {
    Connect-PnPOnline -Url $SiteUrl -Interactive
    Write-Host "✅ SharePoint 連線成功" -ForegroundColor Green
} catch {
    Write-Host "❌ SharePoint 連線失敗: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 建立維修記錄清單
Write-Host "正在建立維修記錄清單..." -ForegroundColor Cyan
try {
    $repairList = New-PnPList -Title "維修記錄" -Template GenericList -EnableVersioning

    Add-PnPField -List $repairList -DisplayName "維修編號" -InternalName "RepairId" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "客戶姓名" -InternalName "CustomerName" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "聯絡電話" -InternalName "Phone" -Type Text
    Add-PnPField -List $repairList -DisplayName "產品序號" -InternalName "SerialNumber" -Type Text
    Add-PnPField -List $repairList -DisplayName "產品名稱" -InternalName "ProductName" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "產品型號" -InternalName "ProductModel" -Type Text
    Add-PnPField -List $repairList -DisplayName "問題描述" -InternalName "Issue" -Type Note

    $serviceStatusChoices = @("客訴", "維保")
    Add-PnPField -List $repairList -DisplayName "維保狀態" -InternalName "ServiceStatus" -Type Choice -Choices $serviceStatusChoices

    $repairRecordChoices = @("調整", "換馬達", "清潔", "校正", "更換零件", "軟體更新")
    Add-PnPField -List $repairList -DisplayName "維修記錄" -InternalName "RepairRecord" -Type Choice -Choices $repairRecordChoices

    $testResultChoices = @("正常", "異常", "待測試")
    Add-PnPField -List $repairList -DisplayName "測試結果" -InternalName "TestResult" -Type Choice -Choices $testResultChoices

    Add-PnPField -List $repairList -DisplayName "維保員" -InternalName "Technician" -Type Text
    Add-PnPField -List $repairList -DisplayName "維修日期" -InternalName "RepairDate" -Type DateTime
    Add-PnPField -List $repairList -DisplayName "完成日期" -InternalName "CompletionDate" -Type DateTime
    Add-PnPField -List $repairList -DisplayName "備註" -InternalName "Notes" -Type Note

    Write-Host "✅ 維修記錄清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 維修記錄清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立客戶資料清單
Write-Host "正在建立客戶資料清單..." -ForegroundColor Cyan
try {
    $customerList = New-PnPList -Title "客戶資料" -Template GenericList -EnableVersioning

    Add-PnPField -List $customerList -DisplayName "客戶編號" -InternalName "CustomerId" -Type Text -Required
    Add-PnPField -List $customerList -DisplayName "客戶姓名" -InternalName "CustomerName" -Type Text -Required
    Add-PnPField -List $customerList -DisplayName "聯絡電話" -InternalName "Phone" -Type Text
    Add-PnPField -List $customerList -DisplayName "電子郵箱" -InternalName "Email" -Type Text
    Add-PnPField -List $customerList -DisplayName "地址" -InternalName "Address" -Type Note
    Add-PnPField -List $customerList -DisplayName "公司名稱" -InternalName "Company" -Type Text

    $customerStatusChoices = @("活躍", "非活躍", "VIP", "黑名單")
    Add-PnPField -List $customerList -DisplayName "客戶狀態" -InternalName "Status" -Type Choice -Choices $customerStatusChoices

    Add-PnPField -List $customerList -DisplayName "註冊日期" -InternalName "RegisterDate" -Type DateTime
    Add-PnPField -List $customerList -DisplayName "備註" -InternalName "Notes" -Type Note

    Write-Host "✅ 客戶資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 客戶資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立零件資料清單
Write-Host "正在建立零件資料清單..." -ForegroundColor Cyan
try {
    $partsList = New-PnPList -Title "零件資料" -Template GenericList -EnableVersioning

    Add-PnPField -List $partsList -DisplayName "零件編號" -InternalName "PartId" -Type Text -Required
    Add-PnPField -List $partsList -DisplayName "零件名稱" -InternalName "PartName" -Type Text -Required

    $categoryChoices = @("電子零件", "機械零件", "消耗品", "工具", "配件")
    Add-PnPField -List $partsList -DisplayName "零件分類" -InternalName "Category" -Type Choice -Choices $categoryChoices

    Add-PnPField -List $partsList -DisplayName "型號" -InternalName "Model" -Type Text
    Add-PnPField -List $partsList -DisplayName "供應商" -InternalName "Supplier" -Type Text
    Add-PnPField -List $partsList -DisplayName "庫存數量" -InternalName "Stock" -Type Number
    Add-PnPField -List $partsList -DisplayName "單價" -InternalName "Price" -Type Currency
    Add-PnPField -List $partsList -DisplayName "最低庫存警告" -InternalName "MinStock" -Type Number
    Add-PnPField -List $partsList -DisplayName "存放位置" -InternalName "Location" -Type Text
    Add-PnPField -List $partsList -DisplayName "規格說明" -InternalName "Specifications" -Type Note
    Add-PnPField -List $partsList -DisplayName "備註" -InternalName "Notes" -Type Note

    Write-Host "✅ 零件資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 零件資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立產品資料清單
Write-Host "正在建立產品資料清單..." -ForegroundColor Cyan
try {
    $productList = New-PnPList -Title "產品資料" -Template GenericList -EnableVersioning

    Add-PnPField -List $productList -DisplayName "產品編號" -InternalName "ProductId" -Type Text -Required
    Add-PnPField -List $productList -DisplayName "產品名稱" -InternalName "ProductName" -Type Text -Required
    Add-PnPField -List $productList -DisplayName "品牌" -InternalName "Brand" -Type Text
    Add-PnPField -List $productList -DisplayName "型號" -InternalName "Model" -Type Text

    $productCategoryChoices = @("手機", "平板", "筆電", "桌機", "印表機", "其他")
    Add-PnPField -List $productList -DisplayName "產品分類" -InternalName "Category" -Type Choice -Choices $productCategoryChoices

    Add-PnPField -List $productList -DisplayName "產品描述" -InternalName "Description" -Type Note
    Add-PnPField -List $productList -DisplayName "保固期間" -InternalName "WarrantyPeriod" -Type Text
    Add-PnPField -List $productList -DisplayName "備註" -InternalName "Notes" -Type Note

    Write-Host "✅ 產品資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 產品資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 SharePoint 清單建立完成！" -ForegroundColor Green
Write-Host "請回到維保管理系統測試連線。" -ForegroundColor Yellow

Disconnect-PnPOnline`;

            // 建立腳本下載對話框
            showScriptDialog(script, siteUrl);
        }

        // 顯示腳本對話框
        function showScriptDialog(script, siteUrl) {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;

            dialog.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 12px; max-width: 900px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #1890ff;">⚡ PowerShell 腳本生成</h3>
                        <button onclick="closeScriptDialog()" style="
                            background: none; border: none; font-size: 24px; cursor: pointer;
                            color: #999; padding: 0; width: 30px; height: 30px;
                        ">×</button>
                    </div>

                    <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1890ff;">
                        <h4 style="margin: 0 0 10px 0; color: #1890ff;">📋 使用說明</h4>
                        <ol style="margin: 0; padding-left: 20px; color: #666; line-height: 1.6;">
                            <li><strong>下載腳本</strong>：點擊下方 "💾 下載腳本" 按鈕</li>
                            <li><strong>開啟 PowerShell</strong>：以管理員身分執行 PowerShell</li>
                            <li><strong>設定執行原則</strong>：執行 <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">Set-ExecutionPolicy RemoteSigned</code></li>
                            <li><strong>執行腳本</strong>：導航到腳本位置並執行 <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">.\\CreateSharePointLists.ps1</code></li>
                            <li><strong>登入驗證</strong>：按照提示完成 SharePoint 登入</li>
                            <li><strong>等待完成</strong>：腳本會自動建立所有清單和欄位</li>
                        </ol>
                    </div>

                    <div style="background: #fff7e6; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #fa8c16;">
                        <h4 style="margin: 0 0 10px 0; color: #fa8c16;">⚠️ 重要提醒</h4>
                        <ul style="margin: 0; padding-left: 20px; color: #666; line-height: 1.6;">
                            <li>需要 <strong>SharePoint 管理員權限</strong> 或 <strong>網站擁有者權限</strong></li>
                            <li>首次執行會自動安裝 PnP PowerShell 模組</li>
                            <li>確保網路連線正常且可以存取 SharePoint</li>
                            <li>如果清單已存在，腳本會跳過該清單</li>
                        </ul>
                    </div>

                    <div style="background: #1e1e1e; padding: 20px; border-radius: 8px; margin-bottom: 20px; max-height: 400px; overflow-y: auto;">
                        <pre style="color: #d4d4d4; font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; line-height: 1.4; margin: 0; white-space: pre-wrap;">${script}</pre>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="downloadScript()" style="
                            padding: 10px 20px; border: none; border-radius: 4px;
                            background: #52c41a; color: white; cursor: pointer; font-weight: bold;
                        ">💾 下載腳本</button>
                        <button onclick="copyScript()" style="
                            padding: 10px 20px; border: 1px solid #1890ff; border-radius: 4px;
                            background: white; color: #1890ff; cursor: pointer;
                        ">📋 複製腳本</button>
                        <button onclick="closeScriptDialog()" style="
                            padding: 10px 20px; border: 1px solid #d9d9d9; border-radius: 4px;
                            background: white; color: #666; cursor: pointer;
                        ">關閉</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            window.closeScriptDialog = () => {
                document.body.removeChild(dialog);
                delete window.closeScriptDialog;
                delete window.copyScript;
                delete window.downloadScript;
            };

            window.copyScript = () => {
                navigator.clipboard.writeText(script).then(() => {
                    showNotification('PowerShell 腳本已複製到剪貼簿', 'success');
                }).catch(() => {
                    showNotification('複製失敗，請手動複製', 'warning');
                });
            };

            window.downloadScript = () => {
                const blob = new Blob([script], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'CreateSharePointLists.ps1';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showNotification('PowerShell 腳本已下載', 'success');
            };
        }

        // 遷移數據到 SharePoint
        async function migrateToSharePoint() {
            try {
                const confirmed = confirm('確定要將所有數據遷移到 SharePoint 嗎？\n\n這個操作會將本地數據上傳到 SharePoint，請確保已建立相應的清單結構。');
                if (!confirmed) return;

                showNotification('開始數據遷移...', 'info');

                // 切換到 SharePoint 模式
                const success = await dataStorage.switchMode('sharepoint');
                if (!success) {
                    throw new Error('無法切換到 SharePoint 模式');
                }

                // 執行數據遷移
                const results = await dataStorage.migrateData('localStorage', 'sharepoint');

                // 更新 UI
                document.getElementById('storageMode').value = 'sharepoint';
                storageConfig.mode = 'sharepoint';

                const resultMessage = `數據遷移完成！\n\n維修記錄: ${results.repairRecords || 0} 筆\n客戶資料: ${results.customers || 0} 筆\n零件資料: ${results.parts || 0} 筆\n產品資料: ${results.products || 0} 筆`;

                alert(resultMessage);
                showNotification('數據遷移成功！', 'success');
                updateStorageStatus();

            } catch (error) {
                showNotification(`數據遷移失敗: ${error.message}`, 'error');
                console.error('數據遷移失敗:', error);
            }
        }

        // 報表選項卡切換函數
        function showReportTab(tabName) {
            // 隱藏所有選項卡內容
            const tabContents = ['overviewTab', 'repairsTab', 'customersTab', 'revenueTab', 'statisticsTab'];
            tabContents.forEach(tabId => {
                const tab = document.getElementById(tabId);
                if (tab) {
                    tab.classList.add('hidden');
                }
            });

            // 移除所有選項卡的active類
            const tabItems = document.querySelectorAll('[data-tab]');
            tabItems.forEach(item => {
                if (item.closest('.page-content[id="reportsPage"]')) {
                    item.classList.remove('active');
                }
            });

            // 顯示選中的選項卡內容
            const targetTab = document.getElementById(tabName + 'Tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
            }

            // 添加active類到選中的選項卡
            const activeTabItem = document.querySelector(`[data-tab="${tabName}"]`);
            if (activeTabItem && activeTabItem.closest('.page-content[id="reportsPage"]')) {
                activeTabItem.classList.add('active');
            }

            // 如果是維修統計選項卡，初始化統計數據
            if (tabName === 'statistics') {
                setTimeout(() => {
                    initializeStatistics();
                }, 100);
            }
        }

        // 系統診斷功能
        function runSystemDiagnostics() {
            console.log('=== 系統診斷開始 ===');

            const diagnostics = {
                browser: navigator.userAgent,
                localStorage: typeof(Storage) !== "undefined",
                screenResolution: screen.width + 'x' + screen.height,
                dataStorage: typeof dataStorage !== 'undefined',
                repairRecords: typeof repairRecords !== 'undefined' ? repairRecords.length : 'undefined',
                partRecords: typeof partRecords !== 'undefined' ? partRecords.length : 'undefined',
                functions: {
                    renderRepairTable: typeof renderRepairTable === 'function',
                    updateFilterStats: typeof updateFilterStats === 'function',
                    showNotification: typeof showNotification === 'function',
                    loadRepairRecordsFromStorage: typeof loadRepairRecordsFromStorage === 'function'
                }
            };

            console.log('診斷結果:', diagnostics);

            // 顯示診斷結果
            const results = [
                '=== 系統診斷結果 ===',
                `瀏覽器: ${diagnostics.browser.split(' ')[0]}`,
                `LocalStorage 支援: ${diagnostics.localStorage ? '✅' : '❌'}`,
                `螢幕解析度: ${diagnostics.screenResolution}`,
                `數據存儲管理器: ${diagnostics.dataStorage ? '✅' : '❌'}`,
                `維修記錄數量: ${diagnostics.repairRecords}`,
                `零件記錄數量: ${diagnostics.partRecords}`,
                '',
                '=== 關鍵函數檢查 ===',
                `renderRepairTable: ${diagnostics.functions.renderRepairTable ? '✅' : '❌'}`,
                `updateFilterStats: ${diagnostics.functions.updateFilterStats ? '✅' : '❌'}`,
                `showNotification: ${diagnostics.functions.showNotification ? '✅' : '❌'}`,
                `loadRepairRecordsFromStorage: ${diagnostics.functions.loadRepairRecordsFromStorage ? '✅' : '❌'}`
            ].join('\\n');

            alert(results);

            console.log('=== 系統診斷結束 ===');
            return diagnostics;
        }

        // 在控制台提供診斷功能
        window.runSystemDiagnostics = runSystemDiagnostics;

        // 初始化系統
        async function initializeSystem() {
            try {
                console.log('開始初始化系統...');

                // 初始化存儲模式設定
                const savedMode = localStorage.getItem('storageMode') || 'localStorage';
                const storageModeSelect = document.getElementById('storageMode');
                if (storageModeSelect) {
                    storageModeSelect.value = savedMode;
                    if (typeof onStorageModeChange === 'function') {
                        onStorageModeChange();
                    }
                }

                // 載入數據
                if (typeof loadRepairRecordsFromStorage === 'function') {
                    await loadRepairRecordsFromStorage();
                }
                if (typeof loadCustomersFromStorage === 'function') {
                    await loadCustomersFromStorage();
                }
                if (typeof loadPartsFromStorage === 'function') {
                    await loadPartsFromStorage();
                }
                if (typeof loadProductsFromStorage === 'function') {
                    await loadProductsFromStorage();
                }

                // 更新存儲狀態
                if (typeof updateStorageStatus === 'function') {
                    updateStorageStatus();
                }

                console.log('系統初始化完成');

                // 延遲顯示歡迎訊息
                setTimeout(() => {
                    alert('🎉 IACT MIO維保管理系統 已載入完成！\n\n✅ 主要功能：\n• 維修記錄管理\n• 客戶資料管理\n• 零件庫存管理\n• 統計報表分析\n• 數據持久化保存\n\n🔑 請使用測試帳號登入：\n• 管理員：admin / admin123\n• 客服：service / service123\n• 技師：tech / tech123\n• 查詢：viewer / viewer123');
                }, 1500);

            } catch (error) {
                console.error('系統初始化失敗:', error);

                // 顯示錯誤但不阻止系統運行
                setTimeout(() => {
                    alert('⚠️ 系統初始化遇到問題，但系統仍可正常使用\n\n• 數據將使用預設值\n• 所有功能正常可用\n• 建議重新整理頁面\n\n請使用測試帳號登入：admin / admin123');
                }, 1000);
            }
        }

        // 頁面載入完成後初始化
        window.addEventListener('load', function() {
            console.log('頁面載入完成，開始初始化...');

            // 延遲初始化，確保所有函數都已定義
            setTimeout(() => {
                initializeSystem();
            }, 500);
        });

        // DOM 內容載入完成後也嘗試初始化（備用）
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 載入完成');

            // 如果頁面已經載入完成，立即初始化
            if (document.readyState === 'complete') {
                setTimeout(() => {
                    initializeSystem();
                }, 100);
            }
        });
    </script>
</body>
</html>
