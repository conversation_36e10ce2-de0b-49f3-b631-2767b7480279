import { api, ApiResponse } from './api';

// 認證相關類型定義
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: UserInfo;
}

export interface UserInfo {
  id: number;
  email: string;
  name: string;
  role: 'ADMIN' | 'CUSTOMER_SERVICE' | 'TECHNICIAN' | 'VIEWER';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  role?: 'CUSTOMER_SERVICE' | 'TECHNICIAN' | 'VIEWER';
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileRequest {
  name: string;
  email: string;
}

// 模擬用戶數據
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    password: 'admin123',
    name: '系統管理員',
    role: 'ADMIN' as const,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    email: '<EMAIL>',
    password: 'service123',
    name: '客服人員',
    role: 'CUSTOMER_SERVICE' as const,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 3,
    email: '<EMAIL>',
    password: 'tech123',
    name: '維修技師',
    role: 'TECHNICIAN' as const,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 4,
    email: '<EMAIL>',
    password: 'viewer123',
    name: '查詢用戶',
    role: 'VIEWER' as const,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

// 模擬API延遲
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 認證服務
export const authService = {
  // 用戶登入
  login: async (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    try {
      const response = await api.post<LoginResponse>('/auth/login', credentials);

      if (response.success && response.data) {
        // 保存 token 和用戶資訊到本地存儲
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userInfo', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error: any) {
      // 如果後端未啟動，使用模擬數據
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        console.log('使用模擬登入數據');
        await mockDelay();

        const user = mockUsers.find(u =>
          u.email === credentials.email && u.password === credentials.password
        );

        if (user) {
          const mockToken = 'mock-jwt-token-' + Date.now();
          const userInfo = {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          };

          // 保存到本地存儲
          localStorage.setItem('authToken', mockToken);
          localStorage.setItem('userInfo', JSON.stringify(userInfo));

          return {
            success: true,
            data: {
              token: mockToken,
              user: userInfo,
            },
          };
        } else {
          return {
            success: false,
            error: '帳號或密碼錯誤',
          };
        }
      }

      throw error;
    }
  },

  // 用戶登出
  logout: async (): Promise<ApiResponse<void>> => {
    try {
      const response = await api.post<void>('/auth/logout');
      return response;
    } catch (error: any) {
      // 如果後端未啟動，直接返回成功
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        console.log('使用模擬登出');
        await mockDelay(200);
        return { success: true, data: undefined };
      }
      throw error;
    } finally {
      // 無論 API 調用是否成功，都清除本地存儲
      localStorage.removeItem('authToken');
      localStorage.removeItem('userInfo');
    }
  },

  // 用戶註冊
  register: async (userData: RegisterRequest): Promise<ApiResponse<UserInfo>> => {
    return await api.post<UserInfo>('/auth/register', userData);
  },

  // 獲取當前用戶資訊
  getCurrentUser: async (): Promise<ApiResponse<UserInfo>> => {
    return await api.get<UserInfo>('/auth/profile');
  },

  // 更新用戶資料
  updateProfile: async (userData: UpdateProfileRequest): Promise<ApiResponse<UserInfo>> => {
    const response = await api.put<UserInfo>('/auth/profile', userData);
    
    if (response.success && response.data) {
      // 更新本地存儲的用戶資訊
      localStorage.setItem('userInfo', JSON.stringify(response.data));
    }
    
    return response;
  },

  // 修改密碼
  changePassword: async (passwordData: ChangePasswordRequest): Promise<ApiResponse<void>> => {
    return await api.put<void>('/auth/change-password', passwordData);
  },

  // 檢查 token 是否有效
  validateToken: async (): Promise<boolean> => {
    try {
      const response = await api.get<UserInfo>('/auth/profile');
      return response.success;
    } catch {
      return false;
    }
  },

  // 從本地存儲獲取用戶資訊
  getStoredUserInfo: (): UserInfo | null => {
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
  },

  // 從本地存儲獲取 token
  getStoredToken: (): string | null => {
    return localStorage.getItem('authToken');
  },

  // 檢查用戶是否已登入
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('authToken');
    const userInfo = localStorage.getItem('userInfo');
    return !!(token && userInfo);
  },

  // 檢查用戶權限
  hasPermission: (requiredRole: UserInfo['role']): boolean => {
    const userInfo = authService.getStoredUserInfo();
    if (!userInfo) return false;

    const roleHierarchy = {
      'VIEWER': 1,
      'TECHNICIAN': 2,
      'CUSTOMER_SERVICE': 3,
      'ADMIN': 4,
    };

    const userLevel = roleHierarchy[userInfo.role];
    const requiredLevel = roleHierarchy[requiredRole];

    return userLevel >= requiredLevel;
  },
};

export default authService;
