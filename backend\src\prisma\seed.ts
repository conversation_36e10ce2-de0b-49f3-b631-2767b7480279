import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 開始資料庫種子資料初始化...');

  // 清理現有資料 (開發環境)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 清理現有資料...');
    await prisma.sharePointSyncLog.deleteMany();
    await prisma.sharePointFile.deleteMany();
    await prisma.repairProgress.deleteMany();
    await prisma.repairPart.deleteMany();
    await prisma.repairStatusHistory.deleteMany();
    await prisma.repairRecord.deleteMany();
    await prisma.part.deleteMany();
    await prisma.product.deleteMany();
    await prisma.productCategory.deleteMany();
    await prisma.customer.deleteMany();
    await prisma.systemSetting.deleteMany();
    await prisma.user.deleteMany();
  }

  // 1. 建立系統設定
  console.log('⚙️ 建立系統設定...');
  const systemSettings = await prisma.systemSetting.createMany({
    data: [
      {
        settingKey: 'company_name',
        settingValue: '維修服務中心',
        description: '公司名稱',
      },
      {
        settingKey: 'default_warranty_months',
        settingValue: '12',
        description: '預設保固月數',
      },
      {
        settingKey: 'auto_notification',
        settingValue: 'true',
        description: '自動通知功能',
      },
      {
        settingKey: 'backup_retention_days',
        settingValue: '30',
        description: '備份保留天數',
      },
      {
        settingKey: 'max_file_size_mb',
        settingValue: '10',
        description: '最大檔案大小(MB)',
      },
    ],
  });

  // 2. 建立用戶
  console.log('👥 建立用戶...');
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const admin = await prisma.user.create({
    data: {
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      fullName: '系統管理員',
      role: 'ADMIN',
    },
  });

  const technician1 = await prisma.user.create({
    data: {
      username: 'tech001',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('tech123', 12),
      fullName: '張技師',
      role: 'TECHNICIAN',
    },
  });

  const technician2 = await prisma.user.create({
    data: {
      username: 'tech002',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('tech123', 12),
      fullName: '李技師',
      role: 'TECHNICIAN',
    },
  });

  const customerService = await prisma.user.create({
    data: {
      username: 'cs001',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('cs123', 12),
      fullName: '王客服',
      role: 'CUSTOMER_SERVICE',
    },
  });

  // 3. 建立產品類別
  console.log('📦 建立產品類別...');
  const categories = await Promise.all([
    prisma.productCategory.create({
      data: {
        name: '手機',
        description: '智慧型手機及相關配件',
      },
    }),
    prisma.productCategory.create({
      data: {
        name: '筆記型電腦',
        description: '筆記型電腦及周邊設備',
      },
    }),
    prisma.productCategory.create({
      data: {
        name: '平板電腦',
        description: '平板電腦及配件',
      },
    }),
    prisma.productCategory.create({
      data: {
        name: '家電',
        description: '家用電器設備',
      },
    }),
  ]);

  // 4. 建立產品
  console.log('🔧 建立產品...');
  const products = await Promise.all([
    // 手機產品
    prisma.product.create({
      data: {
        categoryId: categories[0].id,
        model: 'iPhone 14 Pro',
        brand: 'Apple',
        description: 'Apple iPhone 14 Pro 128GB',
        warrantyMonths: 12,
      },
    }),
    prisma.product.create({
      data: {
        categoryId: categories[0].id,
        model: 'Galaxy S23',
        brand: 'Samsung',
        description: 'Samsung Galaxy S23 256GB',
        warrantyMonths: 24,
      },
    }),
    // 筆電產品
    prisma.product.create({
      data: {
        categoryId: categories[1].id,
        model: 'MacBook Pro 14',
        brand: 'Apple',
        description: 'MacBook Pro 14吋 M2 晶片',
        warrantyMonths: 12,
      },
    }),
    prisma.product.create({
      data: {
        categoryId: categories[1].id,
        model: 'ThinkPad X1',
        brand: 'Lenovo',
        description: 'ThinkPad X1 Carbon Gen 10',
        warrantyMonths: 36,
      },
    }),
    // 平板產品
    prisma.product.create({
      data: {
        categoryId: categories[2].id,
        model: 'iPad Pro 12.9',
        brand: 'Apple',
        description: 'iPad Pro 12.9吋 第6代',
        warrantyMonths: 12,
      },
    }),
  ]);

  // 5. 建立客戶
  console.log('👤 建立客戶...');
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: '陳小明',
        phone: '0912-345-678',
        email: '<EMAIL>',
        address: '台北市信義區信義路五段7號',
        company: 'ABC科技公司',
      },
    }),
    prisma.customer.create({
      data: {
        name: '林小華',
        phone: '0923-456-789',
        email: '<EMAIL>',
        address: '新北市板橋區中山路一段161號',
        company: 'XYZ貿易有限公司',
      },
    }),
    prisma.customer.create({
      data: {
        name: '王大同',
        phone: '0934-567-890',
        email: '<EMAIL>',
        address: '台中市西屯區台灣大道三段99號',
      },
    }),
    prisma.customer.create({
      data: {
        name: '張美麗',
        phone: '0945-678-901',
        email: '<EMAIL>',
        address: '高雄市前鎮區中山二路5號',
        company: '美麗設計工作室',
      },
    }),
  ]);

  // 6. 建立零件
  console.log('🔩 建立零件...');
  const parts = await Promise.all([
    prisma.part.create({
      data: {
        partNumber: 'SCR-001',
        name: 'iPhone 14 Pro 螢幕總成',
        description: '原廠螢幕總成，包含觸控面板',
        unitPrice: 8500.00,
        stockQuantity: 10,
        minimumStock: 2,
        supplier: '蘋果原廠',
      },
    }),
    prisma.part.create({
      data: {
        partNumber: 'BAT-002',
        name: 'iPhone 14 Pro 電池',
        description: '原廠鋰電池',
        unitPrice: 2500.00,
        stockQuantity: 15,
        minimumStock: 3,
        supplier: '蘋果原廠',
      },
    }),
    prisma.part.create({
      data: {
        partNumber: 'KB-003',
        name: 'MacBook Pro 鍵盤',
        description: '繁體中文鍵盤',
        unitPrice: 4500.00,
        stockQuantity: 5,
        minimumStock: 1,
        supplier: '蘋果原廠',
      },
    }),
    prisma.part.create({
      data: {
        partNumber: 'CHG-004',
        name: 'USB-C 充電器',
        description: '67W USB-C 電源轉接器',
        unitPrice: 1800.00,
        stockQuantity: 20,
        minimumStock: 5,
        supplier: '副廠',
      },
    }),
  ]);

  // 7. 建立維修記錄
  console.log('🔧 建立維修記錄...');
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const repairRecords = await Promise.all([
    prisma.repairRecord.create({
      data: {
        repairNumber: `R${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}001`,
        customerId: customers[0].id,
        productId: products[0].id,
        productSerial: 'A1234567890',
        purchaseDate: new Date('2023-10-15'),
        warrantyStatus: 'IN_WARRANTY',
        faultDescription: '螢幕破裂，觸控功能異常',
        receivedDate: today,
        estimatedCompletionDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000), // 3天後
        priority: 'HIGH',
        status: 'INSPECTING',
        assignedTechnicianId: technician1.id,
        createdBy: customerService.id,
        notes: '客戶反映摔落後螢幕出現裂痕',
      },
    }),
    prisma.repairRecord.create({
      data: {
        repairNumber: `R${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}002`,
        customerId: customers[1].id,
        productId: products[2].id,
        productSerial: 'B9876543210',
        purchaseDate: new Date('2023-08-20'),
        warrantyStatus: 'IN_WARRANTY',
        faultDescription: '鍵盤按鍵失靈，部分按鍵無反應',
        receivedDate: yesterday,
        estimatedCompletionDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000), // 5天後
        priority: 'MEDIUM',
        status: 'REPAIRING',
        assignedTechnicianId: technician2.id,
        createdBy: customerService.id,
        repairCost: 4500.00,
        partsCost: 4500.00,
        totalCost: 9000.00,
      },
    }),
    prisma.repairRecord.create({
      data: {
        repairNumber: `R${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate() - 1).padStart(2, '0')}003`,
        customerId: customers[2].id,
        productId: products[1].id,
        productSerial: 'C1122334455',
        purchaseDate: new Date('2022-12-10'),
        warrantyStatus: 'OUT_OF_WARRANTY',
        faultDescription: '電池續航力不足，充電異常',
        receivedDate: lastWeek,
        estimatedCompletionDate: new Date(yesterday.getTime() + 2 * 24 * 60 * 60 * 1000),
        actualCompletionDate: yesterday,
        priority: 'LOW',
        status: 'COMPLETED',
        assignedTechnicianId: technician1.id,
        createdBy: admin.id,
        repairCost: 1500.00,
        partsCost: 2500.00,
        totalCost: 4000.00,
      },
    }),
  ]);

  // 8. 建立維修進度記錄
  console.log('📝 建立維修進度記錄...');
  await Promise.all([
    prisma.repairProgress.create({
      data: {
        repairRecordId: repairRecords[0].id,
        progressDate: today,
        description: '已完成初步檢測，確認螢幕總成需要更換',
        technicianId: technician1.id,
        hoursSpent: 0.5,
      },
    }),
    prisma.repairProgress.create({
      data: {
        repairRecordId: repairRecords[1].id,
        progressDate: yesterday,
        description: '已拆解鍵盤，確認需要更換鍵盤總成',
        technicianId: technician2.id,
        hoursSpent: 1.0,
      },
    }),
    prisma.repairProgress.create({
      data: {
        repairRecordId: repairRecords[1].id,
        progressDate: today,
        description: '正在安裝新鍵盤，預計明天完成',
        technicianId: technician2.id,
        hoursSpent: 2.0,
      },
    }),
    prisma.repairProgress.create({
      data: {
        repairRecordId: repairRecords[2].id,
        progressDate: lastWeek,
        description: '檢測電池狀況，確認需要更換電池',
        technicianId: technician1.id,
        hoursSpent: 0.5,
      },
    }),
    prisma.repairProgress.create({
      data: {
        repairRecordId: repairRecords[2].id,
        progressDate: new Date(lastWeek.getTime() + 24 * 60 * 60 * 1000),
        description: '已更換電池，進行功能測試',
        technicianId: technician1.id,
        hoursSpent: 1.5,
      },
    }),
  ]);

  // 9. 建立使用零件記錄
  console.log('🔩 建立使用零件記錄...');
  await Promise.all([
    prisma.repairPart.create({
      data: {
        repairRecordId: repairRecords[1].id,
        partId: parts[2].id, // MacBook Pro 鍵盤
        quantity: 1,
        unitPrice: 4500.00,
        totalPrice: 4500.00,
        notes: '更換故障鍵盤',
      },
    }),
    prisma.repairPart.create({
      data: {
        repairRecordId: repairRecords[2].id,
        partId: parts[1].id, // iPhone 電池 (假設通用)
        quantity: 1,
        unitPrice: 2500.00,
        totalPrice: 2500.00,
        notes: '更換老化電池',
      },
    }),
  ]);

  // 10. 建立狀態變更歷史
  console.log('📊 建立狀態變更歷史...');
  await Promise.all([
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[0].id,
        oldStatus: 'PENDING_INSPECTION',
        newStatus: 'INSPECTING',
        changedBy: technician1.id,
        changeReason: '開始檢測作業',
      },
    }),
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[1].id,
        oldStatus: 'PENDING_INSPECTION',
        newStatus: 'INSPECTING',
        changedBy: technician2.id,
        changeReason: '開始檢測作業',
      },
    }),
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[1].id,
        oldStatus: 'INSPECTING',
        newStatus: 'REPAIRING',
        changedBy: technician2.id,
        changeReason: '檢測完成，開始維修',
      },
    }),
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[2].id,
        oldStatus: 'PENDING_INSPECTION',
        newStatus: 'INSPECTING',
        changedBy: technician1.id,
        changeReason: '開始檢測作業',
      },
    }),
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[2].id,
        oldStatus: 'INSPECTING',
        newStatus: 'REPAIRING',
        changedBy: technician1.id,
        changeReason: '檢測完成，開始維修',
      },
    }),
    prisma.repairStatusHistory.create({
      data: {
        repairRecordId: repairRecords[2].id,
        oldStatus: 'REPAIRING',
        newStatus: 'COMPLETED',
        changedBy: technician1.id,
        changeReason: '維修完成，功能測試通過',
      },
    }),
  ]);

  console.log('✅ 資料庫種子資料初始化完成！');
  console.log(`📊 建立統計:`);
  console.log(`   - 用戶: 4 個`);
  console.log(`   - 產品類別: 4 個`);
  console.log(`   - 產品: 5 個`);
  console.log(`   - 客戶: 4 個`);
  console.log(`   - 零件: 4 個`);
  console.log(`   - 維修記錄: 3 個`);
  console.log(`   - 維修進度: 5 個`);
  console.log(`   - 使用零件: 2 個`);
  console.log(`   - 狀態歷史: 6 個`);
  console.log(`   - 系統設定: 5 個`);
}

main()
  .catch((e) => {
    console.error('❌ 種子資料初始化失敗:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
