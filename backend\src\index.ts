import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { initializeDatabase, getDatabaseHealth } from './config/database';
import { validateJWTConfig } from './utils/jwt';
import { validateBcryptConfig } from './utils/password';

// 導入路由
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import customerRoutes from './routes/customerRoutes';
import productRoutes from './routes/productRoutes';
import productCategoryRoutes from './routes/productCategoryRoutes';
import partRoutes from './routes/partRoutes';
import repairRecordRoutes from './routes/repairRecordRoutes';

// 載入環境變數
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// 基礎中間件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
}));

app.use(compression());
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));

// 速率限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 分鐘
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 限制每個 IP 100 個請求
  message: {
    error: '請求過於頻繁，請稍後再試',
  },
});

app.use('/api/', limiter);

// 解析請求體
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康檢查端點
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await getDatabaseHealth();
    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      database: dbHealth,
    });
  } catch (error) {
    res.status(503).json({
      status: 'Service Unavailable',
      timestamp: new Date().toISOString(),
      error: 'Database health check failed',
    });
  }
});

// API 路由
app.get('/api/v1', (req, res) => {
  res.json({
    message: '客退維修品記錄管理系統 API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      customers: '/api/v1/customers',
      products: '/api/v1/products',
      productCategories: '/api/v1/product-categories',
      parts: '/api/v1/parts',
      repairRecords: '/api/v1/repair-records',
      health: '/health',
      docs: '/api-docs',
    },
  });
});

// 註冊路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/users', userRoutes);
app.use('/api/v1/customers', customerRoutes);
app.use('/api/v1/products', productRoutes);
app.use('/api/v1/product-categories', productCategoryRoutes);
app.use('/api/v1/parts', partRoutes);
app.use('/api/v1/repair-records', repairRecordRoutes);

// 錯誤處理中間件
app.use(notFoundHandler);
app.use(errorHandler);

// 啟動服務器
const startServer = async () => {
  try {
    // 驗證配置
    logger.info('🔧 驗證系統配置...');

    if (!validateJWTConfig()) {
      logger.warn('⚠️ JWT 配置存在問題，請檢查環境變數');
    }

    if (!validateBcryptConfig()) {
      logger.warn('⚠️ Bcrypt 配置存在問題，請檢查環境變數');
    }

    // 初始化資料庫
    await initializeDatabase();

    const server = app.listen(PORT, () => {
      logger.info(`🚀 服務器啟動成功`);
      logger.info(`📍 端口: ${PORT}`);
      logger.info(`🌍 環境: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 健康檢查: http://localhost:${PORT}/health`);
      logger.info(`📚 API 端點: http://localhost:${PORT}/api/v1`);
    });

    return server;
  } catch (error) {
    logger.error('❌ 服務器啟動失敗:', error);
    process.exit(1);
  }
};

// 啟動應用
startServer().then((server) => {

  // 優雅關閉
  process.on('SIGTERM', () => {
    logger.info('收到 SIGTERM 信號，正在關閉服務器...');
    server.close(() => {
      logger.info('服務器已關閉');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    logger.info('收到 SIGINT 信號，正在關閉服務器...');
    server.close(() => {
      logger.info('服務器已關閉');
      process.exit(0);
    });
  });
}).catch((error) => {
  logger.error('❌ 應用啟動失敗:', error);
  process.exit(1);
});

export default app;
