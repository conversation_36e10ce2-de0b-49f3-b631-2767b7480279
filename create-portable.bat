@echo off
echo ========================================
echo   IACT MIO維保管理系統 便攜版打包
echo ========================================
echo.

echo 正在建立便攜版部署包...
echo.

REM 取得當前日期
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "PACKAGE_NAME=IACT_MIO維保管理系統_便攜版_v1.0_%YYYY%%MM%%DD%"

echo [1/7] 建立部署資料夾...
mkdir "%PACKAGE_NAME%" 2>nul
echo     ✓ 資料夾建立成功: %PACKAGE_NAME%

echo [2/7] 複製主程式檔案...
copy "complete-system-gui.html" "%PACKAGE_NAME%\" >nul
if exist "%PACKAGE_NAME%\complete-system-gui.html" (
    echo     ✓ complete-system-gui.html 複製成功
) else (
    echo     ✗ complete-system-gui.html 複製失敗
    pause
    exit /b 1
)

echo [3/7] 建立 Windows 啟動腳本...
(
echo @echo off
echo echo 啟動 IACT MIO維保管理系統...
echo start "" "complete-system-gui.html"
) > "%PACKAGE_NAME%\啟動系統.bat"
echo     ✓ 啟動系統.bat 建立成功

echo [4/7] 建立無視窗啟動腳本...
(
echo Set objShell = CreateObject("WScript.Shell"^)
echo objShell.Run "complete-system-gui.html", 0, False
) > "%PACKAGE_NAME%\無視窗啟動.vbs"
echo     ✓ 無視窗啟動.vbs 建立成功

echo [5/7] 建立 macOS/Linux 啟動腳本...
(
echo #!/bin/bash
echo echo "啟動 IACT MIO維保管理系統..."
echo if [[ "$OSTYPE" == "darwin"* ]]; then
echo     open complete-system-gui.html
echo elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
echo     xdg-open complete-system-gui.html
echo else
echo     echo "請手動開啟 complete-system-gui.html"
echo fi
) > "%PACKAGE_NAME%\start.sh"
echo     ✓ start.sh 建立成功

echo [6/7] 建立使用說明...
(
echo # IACT MIO維保管理系統 便攜版
echo.
echo ## 🚀 快速開始
echo.
echo ### Windows 用戶
echo 1. 雙擊 `啟動系統.bat` 啟動系統
echo 2. 或雙擊 `無視窗啟動.vbs` ^(無視窗啟動^)
echo 3. 系統會在預設瀏覽器中開啟
echo.
echo ### macOS/Linux 用戶
echo 1. 雙擊 `complete-system-gui.html` 檔案
echo 2. 或在終端執行 `bash start.sh`
echo 3. 系統會在預設瀏覽器中開啟
echo.
echo ## 🔑 登入資訊
echo.
echo **管理員帳號：**
echo - 用戶名：admin
echo - 密碼：admin123
echo.
echo **客服帳號：**
echo - 用戶名：service  
echo - 密碼：service123
echo.
echo ## 💾 數據存儲
echo.
echo - 數據自動保存在瀏覽器的 LocalStorage 中
echo - 重啟瀏覽器後數據會自動載入
echo - 建議定期使用系統內建的匯出功能備份數據
echo.
echo ## ⚙️ 系統需求
echo.
echo - **瀏覽器**：Chrome 80+, Firefox 75+, Edge 80+, Safari 13+
echo - **作業系統**：Windows 7+, macOS 10.12+, Linux
echo - **記憶體**：512MB 可用記憶體
echo - **硬碟**：^<1MB 程式檔案
echo.
echo ## 🔧 主要功能
echo.
echo - ✅ 維修記錄管理 ^(新增/編輯/查看/刪除^)
echo - ✅ 客戶資料管理 ^(客戶資訊維護^)
echo - ✅ 零件庫存管理 ^(庫存追蹤/導入導出^)
echo - ✅ 統計報表分析 ^(維修統計/趨勢分析^)
echo - ✅ 數據匯出/匯入 ^(備份恢復^)
echo - ✅ SharePoint 整合準備 ^(企業級擴展^)
echo.
echo ## 📱 行動裝置支援
echo.
echo - 支援手機和平板電腦瀏覽器存取
echo - 響應式設計，自動適應螢幕大小
echo - 觸控操作優化
echo.
echo ## 🔒 數據安全
echo.
echo - 數據存儲在本地瀏覽器中，不會上傳到網路
echo - 清除瀏覽器數據會導致資料遺失
echo - 建議定期備份重要數據
echo - 支援數據匯出為 JSON 格式
echo.
echo ## 📞 技術支援
echo.
echo **常見問題：**
echo 1. 系統無法開啟 → 檢查瀏覽器版本
echo 2. 數據消失 → 檢查是否清除了瀏覽器數據
echo 3. 功能異常 → 重新整理頁面或重啟瀏覽器
echo.
echo **系統檢查：**
echo - 在瀏覽器按 F12 開啟開發者工具
echo - 查看 Console 標籤是否有錯誤訊息
echo.
echo ---
echo.
echo **版本資訊：**
echo - 版本：v1.0 便攜版
echo - 建立日期：%YYYY%-%MM%-%DD%
echo - 系統名稱：IACT MIO維保管理系統
echo - 部署方式：便攜式網頁應用
) > "%PACKAGE_NAME%\使用說明.md"
echo     ✓ 使用說明.md 建立成功

echo [7/7] 建立版本資訊...
(
echo {
echo   "name": "IACT MIO維保管理系統",
echo   "version": "1.0.0-portable",
echo   "description": "專業的維修記錄管理系統 - 便攜版",
echo   "buildDate": "%YYYY%-%MM%-%DD%",
echo   "deploymentType": "portable",
echo   "features": [
echo     "維修記錄管理",
echo     "客戶資料管理",
echo     "零件庫存管理", 
echo     "統計報表分析",
echo     "數據匯出匯入",
echo     "SharePoint整合準備",
echo     "LocalStorage數據持久化"
echo   ],
echo   "requirements": {
echo     "browser": "Chrome 80+, Firefox 75+, Edge 80+, Safari 13+",
echo     "os": "Windows 7+, macOS 10.12+, Linux",
echo     "memory": "512MB",
echo     "storage": "^<1MB"
echo   },
echo   "files": [
echo     "complete-system-gui.html",
echo     "啟動系統.bat",
echo     "無視窗啟動.vbs", 
echo     "start.sh",
echo     "使用說明.md",
echo     "version.json"
echo   ]
echo }
) > "%PACKAGE_NAME%\version.json"
echo     ✓ version.json 建立成功

echo.
echo ========================================
echo   便攜版部署包建立完成！
echo ========================================
echo.
echo 📦 部署包位置：%PACKAGE_NAME%
echo.
echo 📁 包含檔案：
dir "%PACKAGE_NAME%" /b
echo.
echo 🚀 使用方式：
echo   1. 將整個資料夾複製到目標電腦
echo   2. Windows: 雙擊 啟動系統.bat
echo   3. macOS/Linux: 雙擊 complete-system-gui.html
echo.
echo 💡 便攜版特色：
echo   - ✅ 零安裝：無需安裝任何軟體
echo   - ✅ 跨平台：支援所有主流作業系統
echo   - ✅ 便攜性：可放在 USB 隨身碟中使用
echo   - ✅ 數據安全：數據存儲在本地瀏覽器
echo   - ✅ 離線使用：無需網路連線
echo.
echo 按任意鍵開啟部署包資料夾...
pause >nul
explorer "%PACKAGE_NAME%"
