import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Alert } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const LoginForm: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: LoginFormData) => {
    setLoading(true);
    setError(null);

    console.log('� React 登入嘗試:', values.email);

    // 模擬登入模式
    if (values.email === '<EMAIL>' && values.password === 'admin123') {
      console.log('🎯 使用模擬登入模式');

      // 模擬用戶資料
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        username: 'admin',
        fullName: '系統管理員',
        role: 'ADMIN' as const,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 模擬 token
      const mockToken = 'mock-jwt-token-' + Date.now();

      // 儲存到 localStorage
      localStorage.setItem('authToken', mockToken);
      localStorage.setItem('userInfo', JSON.stringify(mockUser));

      // 延遲一下模擬真實登入
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1000);
      return;
    } else {
      setError('帳號或密碼錯誤');
      setLoading(false);
      return;
    }
      await dispatch(loginAsync(loginData));
      console.log('✅ React 登入成功');
    } catch (error) {
      console.error('❌ React 登入失敗:', error);
      // 錯誤已經在 slice 中處理
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <LoginOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            系統登入
          </Title>
          <Text type="secondary">
            客退維修品記錄管理系統
          </Text>
        </div>

        {error && (
          <Alert
            message="登入失敗"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => dispatch(clearError())}
            style={{ marginBottom: '24px' }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          autoComplete="off"
        >
          <Form.Item
            name="email"
            label="電子郵件"
            rules={[
              { required: true, message: '請輸入電子郵件' },
              { type: 'email', message: '請輸入有效的電子郵件格式' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="請輸入電子郵件"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密碼"
            rules={[
              { required: true, message: '請輸入密碼' },
              { min: 6, message: '密碼至少需要6個字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="請輸入密碼"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              {isLoading ? '登入中...' : '登入'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>或</Divider>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            還沒有帳號？{' '}
            <Link to="/register" style={{ color: '#1890ff' }}>
              立即註冊
            </Link>
          </Text>
        </div>

        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <Link to="/forgot-password" style={{ color: '#1890ff' }}>
            忘記密碼？
          </Link>
        </div>
      </Card>
    </div>
  );
};

export default LoginForm;
