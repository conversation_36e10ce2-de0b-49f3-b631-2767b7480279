import React, { useEffect } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, Alert, Divider } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { loginAsync, clearError } from '../../store/slices/authSlice';
import { LoginRequest } from '../../services/authService';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const LoginForm: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth);

  // 獲取重定向路徑
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  useEffect(() => {
    // 如果已經登入，重定向到目標頁面
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  useEffect(() => {
    // 清除錯誤信息
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleSubmit = async (values: LoginFormData) => {
    console.log('🔐 React 登入嘗試:', values.email);
    console.log('📡 API 基礎 URL:', (import.meta as any).env?.VITE_API_BASE_URL || 'http://localhost:5000');

    const loginData: LoginRequest = {
      email: values.email,
      password: values.password,
    };

    try {
      await dispatch(loginAsync(loginData));
      console.log('✅ React 登入成功');
    } catch (error) {
      console.error('❌ React 登入失敗:', error);
      // 錯誤已經在 slice 中處理
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <LoginOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            系統登入
          </Title>
          <Text type="secondary">
            客退維修品記錄管理系統
          </Text>
        </div>

        {error && (
          <Alert
            message="登入失敗"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => dispatch(clearError())}
            style={{ marginBottom: '24px' }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          autoComplete="off"
        >
          <Form.Item
            name="email"
            label="電子郵件"
            rules={[
              { required: true, message: '請輸入電子郵件' },
              { type: 'email', message: '請輸入有效的電子郵件格式' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="請輸入電子郵件"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密碼"
            rules={[
              { required: true, message: '請輸入密碼' },
              { min: 6, message: '密碼至少需要6個字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="請輸入密碼"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              {isLoading ? '登入中...' : '登入'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>或</Divider>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            還沒有帳號？{' '}
            <Link to="/register" style={{ color: '#1890ff' }}>
              立即註冊
            </Link>
          </Text>
        </div>

        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <Link to="/forgot-password" style={{ color: '#1890ff' }}>
            忘記密碼？
          </Link>
        </div>
      </Card>
    </div>
  );
};

export default LoginForm;
