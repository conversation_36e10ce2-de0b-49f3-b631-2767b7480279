@echo off
echo 正在啟動後端開發伺服器...
echo.

REM 檢查 Node.js 是否安裝
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤: 未找到 Node.js，請先安裝 Node.js
    pause
    exit /b 1
)

REM 檢查 npm 是否安裝
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤: 未找到 npm，請檢查 Node.js 安裝
    pause
    exit /b 1
)

REM 檢查 node_modules 是否存在
if not exist "node_modules" (
    echo 正在安裝依賴套件...
    npm install
    if %errorlevel% neq 0 (
        echo 錯誤: 依賴套件安裝失敗
        pause
        exit /b 1
    )
)

REM 檢查 .env 文件是否存在
if not exist ".env" (
    echo 正在創建環境配置文件...
    copy .env.example .env >nul 2>&1
    if not exist ".env" (
        echo 警告: 未找到 .env 文件，請手動創建
    )
)

echo 正在啟動後端伺服器...
echo 請稍候，API伺服器將在 http://localhost:5000 啟動
echo.
echo 按 Ctrl+C 可停止伺服器
echo.

npm run dev

pause
