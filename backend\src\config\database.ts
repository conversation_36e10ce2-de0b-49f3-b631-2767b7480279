import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

// Prisma 客戶端配置
const prismaConfig = {
  log: [
    {
      emit: 'event' as const,
      level: 'query' as const,
    },
    {
      emit: 'event' as const,
      level: 'error' as const,
    },
    {
      emit: 'event' as const,
      level: 'info' as const,
    },
    {
      emit: 'event' as const,
      level: 'warn' as const,
    },
  ],
  errorFormat: 'pretty' as const,
};

// 建立 Prisma 客戶端實例
export const prisma = new PrismaClient(prismaConfig);

// 設定日誌事件監聽
prisma.$on('query', (e) => {
  if (process.env.NODE_ENV === 'development') {
    logger.debug('Query:', {
      query: e.query,
      params: e.params,
      duration: `${e.duration}ms`,
    });
  }
});

prisma.$on('error', (e) => {
  logger.error('Prisma Error:', e);
});

prisma.$on('info', (e) => {
  logger.info('Prisma Info:', e);
});

prisma.$on('warn', (e) => {
  logger.warn('Prisma Warning:', e);
});

// 資料庫連接測試
export const testDatabaseConnection = async (): Promise<boolean> => {
  try {
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ 資料庫連接成功');
    return true;
  } catch (error) {
    logger.error('❌ 資料庫連接失敗:', error);
    return false;
  }
};

// 資料庫健康檢查
export const getDatabaseHealth = async () => {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;

    // 獲取資料庫統計資訊
    const [userCount, customerCount, repairRecordCount] = await Promise.all([
      prisma.user.count(),
      prisma.customer.count(),
      prisma.repairRecord.count(),
    ]);

    return {
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      statistics: {
        users: userCount,
        customers: customerCount,
        repairRecords: repairRecordCount,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error('資料庫健康檢查失敗:', error);
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
};

// 優雅關閉資料庫連接
export const closeDatabaseConnection = async () => {
  try {
    await prisma.$disconnect();
    logger.info('✅ 資料庫連接已關閉');
  } catch (error) {
    logger.error('❌ 關閉資料庫連接時發生錯誤:', error);
  }
};

// 資料庫遷移檢查
export const checkMigrationStatus = async () => {
  try {
    // 檢查是否有待執行的遷移
    const result = await prisma.$queryRaw`
      SELECT COUNT(*) as pending_migrations 
      FROM _prisma_migrations 
      WHERE finished_at IS NULL
    ` as any[];

    const pendingMigrations = Number(result[0]?.pending_migrations || 0);
    
    return {
      hasPendingMigrations: pendingMigrations > 0,
      pendingCount: pendingMigrations,
    };
  } catch (error) {
    logger.warn('無法檢查遷移狀態:', error);
    return {
      hasPendingMigrations: false,
      pendingCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// 資料庫初始化
export const initializeDatabase = async () => {
  try {
    logger.info('🔄 初始化資料庫連接...');
    
    // 測試連接
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      throw new Error('無法連接到資料庫');
    }

    // 檢查遷移狀態
    const migrationStatus = await checkMigrationStatus();
    if (migrationStatus.hasPendingMigrations) {
      logger.warn(`⚠️ 發現 ${migrationStatus.pendingCount} 個待執行的遷移`);
    }

    // 獲取健康狀態
    const health = await getDatabaseHealth();
    logger.info('📊 資料庫統計:', health.statistics);

    logger.info('✅ 資料庫初始化完成');
    return true;
  } catch (error) {
    logger.error('❌ 資料庫初始化失敗:', error);
    throw error;
  }
};

// 處理程序退出時關閉連接
process.on('beforeExit', async () => {
  await closeDatabaseConnection();
});

process.on('SIGINT', async () => {
  await closeDatabaseConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeDatabaseConnection();
  process.exit(0);
});

export default prisma;
