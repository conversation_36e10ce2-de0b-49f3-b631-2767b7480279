import { Router } from 'express';
import RepairRecordController from '../controllers/repairRecordController';
import { authenticate, authorize, Role } from '../middleware/auth';
import {
  createRepairRecordValidation,
  updateRepairRecordValidation,
  repairRecordListValidation,
  repairRecordIdValidation,
  repairRecordSearchValidation,
  repairNumberParamValidation,
  repairRecordDetailValidation,
  updateRepairStatusValidation,
  assignTechnicianValidation,
  usePartValidation,
  batchUsePartsValidation,
  repairRecordListWithRangeValidation,
} from '../validators/repairRecordValidators';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateRepairRecordRequest:
 *       type: object
 *       required:
 *         - customerId
 *         - productId
 *         - issueDescription
 *       properties:
 *         customerId:
 *           type: string
 *           description: 客戶ID
 *         productId:
 *           type: string
 *           description: 產品ID
 *         serialNumber:
 *           type: string
 *           maxLength: 100
 *           description: 序號
 *         issueDescription:
 *           type: string
 *           minLength: 10
 *           maxLength: 2000
 *           description: 問題描述
 *         symptoms:
 *           type: array
 *           items:
 *             type: string
 *             maxLength: 200
 *           description: 症狀列表
 *         priority:
 *           type: string
 *           enum: [LOW, NORMAL, HIGH, URGENT, CRITICAL]
 *           default: NORMAL
 *           description: 優先級
 *         assignedTechnicianId:
 *           type: string
 *           description: 指派技師ID
 *         estimatedCost:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 預估費用
 *         estimatedCompletionDate:
 *           type: string
 *           format: date-time
 *           description: 預計完成日期
 *         warrantyStatus:
 *           type: string
 *           enum: [IN_WARRANTY, OUT_OF_WARRANTY, EXTENDED_WARRANTY, UNKNOWN]
 *           default: UNKNOWN
 *           description: 保固狀態
 *         warrantyExpiryDate:
 *           type: string
 *           format: date-time
 *           description: 保固到期日期
 *         receivedDate:
 *           type: string
 *           format: date-time
 *           description: 接收日期
 *         notes:
 *           type: string
 *           maxLength: 2000
 *           description: 備註
 *         internalNotes:
 *           type: string
 *           maxLength: 2000
 *           description: 內部備註
 *     
 *     UpdateRepairRecordRequest:
 *       type: object
 *       properties:
 *         customerId:
 *           type: string
 *           description: 客戶ID
 *         productId:
 *           type: string
 *           description: 產品ID
 *         serialNumber:
 *           type: string
 *           maxLength: 100
 *           description: 序號
 *         issueDescription:
 *           type: string
 *           minLength: 10
 *           maxLength: 2000
 *           description: 問題描述
 *         symptoms:
 *           type: array
 *           items:
 *             type: string
 *             maxLength: 200
 *           description: 症狀列表
 *         priority:
 *           type: string
 *           enum: [LOW, NORMAL, HIGH, URGENT, CRITICAL]
 *           description: 優先級
 *         status:
 *           type: string
 *           enum: [RECEIVED, DIAGNOSED, WAITING_PARTS, IN_PROGRESS, TESTING, COMPLETED, QUALITY_CHECK, READY_PICKUP, DELIVERED, CANCELLED, ON_HOLD]
 *           description: 維修狀態
 *         assignedTechnicianId:
 *           type: string
 *           description: 指派技師ID
 *         estimatedCost:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 預估費用
 *         actualCost:
 *           type: number
 *           minimum: 0
 *           maximum: 999999999
 *           description: 實際費用
 *         estimatedCompletionDate:
 *           type: string
 *           format: date-time
 *           description: 預計完成日期
 *         actualCompletionDate:
 *           type: string
 *           format: date-time
 *           description: 實際完成日期
 *         warrantyStatus:
 *           type: string
 *           enum: [IN_WARRANTY, OUT_OF_WARRANTY, EXTENDED_WARRANTY, UNKNOWN]
 *           description: 保固狀態
 *         warrantyExpiryDate:
 *           type: string
 *           format: date-time
 *           description: 保固到期日期
 *         startedDate:
 *           type: string
 *           format: date-time
 *           description: 開始日期
 *         completedDate:
 *           type: string
 *           format: date-time
 *           description: 完成日期
 *         deliveredDate:
 *           type: string
 *           format: date-time
 *           description: 交付日期
 *         notes:
 *           type: string
 *           maxLength: 2000
 *           description: 備註
 *         internalNotes:
 *           type: string
 *           maxLength: 2000
 *           description: 內部備註
 *     
 *     UpdateRepairStatusRequest:
 *       type: object
 *       required:
 *         - status
 *       properties:
 *         status:
 *           type: string
 *           enum: [RECEIVED, DIAGNOSED, WAITING_PARTS, IN_PROGRESS, TESTING, COMPLETED, QUALITY_CHECK, READY_PICKUP, DELIVERED, CANCELLED, ON_HOLD]
 *           description: 新的維修狀態
 *         reason:
 *           type: string
 *           minLength: 1
 *           maxLength: 500
 *           description: 狀態變更原因
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: 備註
 *         estimatedCompletionDate:
 *           type: string
 *           format: date-time
 *           description: 預計完成日期
 *         actualCompletionDate:
 *           type: string
 *           format: date-time
 *           description: 實際完成日期
 *     
 *     UsePartRequest:
 *       type: object
 *       required:
 *         - partId
 *         - quantity
 *       properties:
 *         partId:
 *           type: string
 *           description: 零件ID
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: 使用數量
 *         notes:
 *           type: string
 *           maxLength: 500
 *           description: 備註
 *         isWarrantyPart:
 *           type: boolean
 *           default: false
 *           description: 是否為保固零件
 *     
 *     BatchUsePartsRequest:
 *       type: object
 *       required:
 *         - parts
 *       properties:
 *         parts:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/UsePartRequest'
 *           minItems: 1
 *           maxItems: 50
 *           description: 零件使用列表
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: 批量操作備註
 */

// === 維修記錄相關路由 ===

/**
 * @swagger
 * /api/v1/repair-records:
 *   get:
 *     summary: 獲取維修記錄列表
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每頁數量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         description: 客戶ID篩選
 *       - in: query
 *         name: productId
 *         schema:
 *           type: string
 *         description: 產品ID篩選
 *       - in: query
 *         name: assignedTechnicianId
 *         schema:
 *           type: string
 *         description: 指派技師ID篩選
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [RECEIVED, DIAGNOSED, WAITING_PARTS, IN_PROGRESS, TESTING, COMPLETED, QUALITY_CHECK, READY_PICKUP, DELIVERED, CANCELLED, ON_HOLD]
 *         description: 狀態篩選
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, NORMAL, HIGH, URGENT, CRITICAL]
 *         description: 優先級篩選
 *       - in: query
 *         name: warrantyStatus
 *         schema:
 *           type: string
 *           enum: [IN_WARRANTY, OUT_OF_WARRANTY, EXTENDED_WARRANTY, UNKNOWN]
 *         description: 保固狀態篩選
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 開始日期
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 結束日期
 *       - in: query
 *         name: costMin
 *         schema:
 *           type: number
 *         description: 最低費用
 *       - in: query
 *         name: costMax
 *         schema:
 *           type: number
 *         description: 最高費用
 *       - in: query
 *         name: overdue
 *         schema:
 *           type: boolean
 *         description: 逾期篩選
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [repairNumber, receivedDate, priority, status, estimatedCompletionDate, actualCost, createdAt, updatedAt]
 *           default: createdAt
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序順序
 *     responses:
 *       200:
 *         description: 獲取維修記錄列表成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), repairRecordListWithRangeValidation, RepairRecordController.getRepairRecordList);

/**
 * @swagger
 * /api/v1/repair-records:
 *   post:
 *     summary: 創建維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRepairRecordRequest'
 *     responses:
 *       201:
 *         description: 維修記錄創建成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.post('/', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), createRepairRecordValidation, RepairRecordController.createRepairRecord);

/**
 * @swagger
 * /api/v1/repair-records/search:
 *   get:
 *     summary: 搜尋維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜尋關鍵字
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: 結果數量限制
 *     responses:
 *       200:
 *         description: 搜尋成功
 *       400:
 *         description: 搜尋參數錯誤
 *       401:
 *         description: 未認證
 */
router.get('/search', authenticate, repairRecordSearchValidation, RepairRecordController.searchRepairRecords);

/**
 * @swagger
 * /api/v1/repair-records/statistics:
 *   get:
 *     summary: 獲取維修記錄統計
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取統計成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/statistics', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), RepairRecordController.getRepairRecordStatistics);

/**
 * @swagger
 * /api/v1/repair-records/my-assigned:
 *   get:
 *     summary: 獲取我指派的維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 狀態篩選
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *         description: 優先級篩選
 *     responses:
 *       200:
 *         description: 獲取我指派的維修記錄成功
 *       401:
 *         description: 未認證
 */
router.get('/my-assigned', authenticate, RepairRecordController.getMyAssignedRepairRecords);

/**
 * @swagger
 * /api/v1/repair-records/my-created:
 *   get:
 *     summary: 獲取我創建的維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *     responses:
 *       200:
 *         description: 獲取我創建的維修記錄成功
 *       401:
 *         description: 未認證
 */
router.get('/my-created', authenticate, RepairRecordController.getMyCreatedRepairRecords);

/**
 * @swagger
 * /api/v1/repair-records/overdue:
 *   get:
 *     summary: 獲取逾期維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *       - in: query
 *         name: assignedTechnicianId
 *         schema:
 *           type: string
 *         description: 指派技師ID篩選
 *     responses:
 *       200:
 *         description: 獲取逾期維修記錄成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 */
router.get('/overdue', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), RepairRecordController.getOverdueRepairRecords);

/**
 * @swagger
 * /api/v1/repair-records/repair-number/{repairNumber}:
 *   get:
 *     summary: 根據維修單號查找維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: repairNumber
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修單號
 *     responses:
 *       200:
 *         description: 查找成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 維修記錄不存在
 */
router.get('/repair-number/:repairNumber', authenticate, repairNumberParamValidation, RepairRecordController.findRepairRecordByRepairNumber);

/**
 * @swagger
 * /api/v1/repair-records/{id}:
 *   get:
 *     summary: 根據ID獲取維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含詳細資訊
 *     responses:
 *       200:
 *         description: 獲取維修記錄成功
 *       401:
 *         description: 未認證
 *       404:
 *         description: 維修記錄不存在
 */
router.get('/:id', authenticate, repairRecordDetailValidation, RepairRecordController.getRepairRecordById);

/**
 * @swagger
 * /api/v1/repair-records/{id}:
 *   put:
 *     summary: 更新維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRepairRecordRequest'
 *     responses:
 *       200:
 *         description: 維修記錄更新成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄不存在
 */
router.put('/:id', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), updateRepairRecordValidation, RepairRecordController.updateRepairRecord);

/**
 * @swagger
 * /api/v1/repair-records/{id}:
 *   delete:
 *     summary: 刪除維修記錄
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *       - in: query
 *         name: hardDelete
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否永久刪除
 *     responses:
 *       200:
 *         description: 維修記錄刪除成功
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄不存在
 */
router.delete('/:id', authenticate, authorize(Role.ADMIN), repairRecordIdValidation, RepairRecordController.deleteRepairRecord);

/**
 * @swagger
 * /api/v1/repair-records/{id}/status:
 *   put:
 *     summary: 更新維修狀態
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRepairStatusRequest'
 *     responses:
 *       200:
 *         description: 維修狀態更新成功
 *       400:
 *         description: 請求參數錯誤或狀態轉換無效
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄不存在
 */
router.put('/:id/status', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), updateRepairStatusValidation, RepairRecordController.updateRepairStatus);

/**
 * @swagger
 * /api/v1/repair-records/{id}/assign-technician:
 *   post:
 *     summary: 指派技師
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - technicianId
 *             properties:
 *               technicianId:
 *                 type: string
 *                 description: 技師ID
 *     responses:
 *       200:
 *         description: 技師指派成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄不存在
 */
router.post('/:id/assign-technician', authenticate, authorize(Role.CUSTOMER_SERVICE, Role.ADMIN), assignTechnicianValidation, RepairRecordController.assignTechnician);

/**
 * @swagger
 * /api/v1/repair-records/{id}/use-part:
 *   post:
 *     summary: 使用零件
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UsePartRequest'
 *     responses:
 *       200:
 *         description: 零件使用成功
 *       400:
 *         description: 請求參數錯誤或庫存不足
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄或零件不存在
 */
router.post('/:id/use-part', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), usePartValidation, RepairRecordController.usePart);

/**
 * @swagger
 * /api/v1/repair-records/{id}/batch-use-parts:
 *   post:
 *     summary: 批量使用零件
 *     tags: [Repair Records]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 維修記錄ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchUsePartsRequest'
 *     responses:
 *       200:
 *         description: 批量零件使用成功
 *       400:
 *         description: 請求參數錯誤或庫存不足
 *       401:
 *         description: 未認證
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 維修記錄或零件不存在
 */
router.post('/:id/batch-use-parts', authenticate, authorize(Role.TECHNICIAN, Role.CUSTOMER_SERVICE, Role.ADMIN), batchUsePartsValidation, RepairRecordController.batchUseParts);

export default router;
