import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Table,
  Tag,
  Space,
  Statistic,
  Progress,
  Select,
  Button
} from 'antd';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart,
  Area,
  AreaChart
} from 'recharts';
import { 
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ToolOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined
} from '@ant-design/icons';
import { StatisticsQueryParams } from '../../services/statisticsService';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Option } = Select;

interface RepairAnalyticsProps {
  filters: StatisticsQueryParams;
}

// 模擬數據
const mockRepairTrends = [
  { date: '2024-01-01', newRepairs: 12, completedRepairs: 8, pendingRepairs: 15, avgRepairTime: 3.2 },
  { date: '2024-01-02', newRepairs: 15, completedRepairs: 12, pendingRepairs: 18, avgRepairTime: 3.1 },
  { date: '2024-01-03', newRepairs: 8, completedRepairs: 14, pendingRepairs: 12, avgRepairTime: 2.9 },
  { date: '2024-01-04', newRepairs: 18, completedRepairs: 10, pendingRepairs: 20, avgRepairTime: 3.4 },
  { date: '2024-01-05', newRepairs: 22, completedRepairs: 16, pendingRepairs: 26, avgRepairTime: 3.0 },
  { date: '2024-01-06', newRepairs: 14, completedRepairs: 20, pendingRepairs: 20, avgRepairTime: 2.8 },
  { date: '2024-01-07', newRepairs: 16, completedRepairs: 18, pendingRepairs: 18, avgRepairTime: 3.1 },
];

const mockStatusDistribution = [
  { status: 'COMPLETED', statusName: '已完成', count: 756, percentage: 60.6, color: '#52c41a' },
  { status: 'REPAIRING', statusName: '維修中', count: 189, percentage: 15.1, color: '#1890ff' },
  { status: 'PENDING_INSPECTION', statusName: '待檢測', count: 145, percentage: 11.6, color: '#faad14' },
  { status: 'PENDING_PARTS', statusName: '待零件', count: 98, percentage: 7.9, color: '#f5222d' },
  { status: 'DELIVERED', statusName: '已交付', count: 60, percentage: 4.8, color: '#52c41a' },
];

const mockPriorityAnalysis = [
  { priority: 'URGENT', priorityName: '緊急', count: 45, avgTime: 1.2, completionRate: 95 },
  { priority: 'HIGH', priorityName: '高', count: 123, avgTime: 2.1, completionRate: 92 },
  { priority: 'MEDIUM', priorityName: '中', count: 567, avgTime: 3.2, completionRate: 89 },
  { priority: 'LOW', priorityName: '低', count: 513, avgTime: 4.5, completionRate: 87 },
];

const mockCommonIssues = [
  { issue: '螢幕破裂', count: 234, percentage: 18.7, avgCost: 2500, avgTime: 2.1 },
  { issue: '電池問題', count: 189, percentage: 15.1, avgCost: 1800, avgTime: 1.8 },
  { issue: '充電異常', count: 156, percentage: 12.5, avgCost: 1200, avgTime: 1.5 },
  { issue: '系統故障', count: 134, percentage: 10.7, avgCost: 800, avgTime: 3.2 },
  { issue: '按鍵失靈', count: 98, percentage: 7.8, avgCost: 600, avgTime: 1.2 },
  { issue: '相機故障', count: 87, percentage: 7.0, avgCost: 1500, avgTime: 2.5 },
  { issue: '聲音異常', count: 76, percentage: 6.1, avgCost: 900, avgTime: 1.8 },
  { issue: '其他', count: 274, percentage: 22.1, avgCost: 1100, avgTime: 2.8 },
];

const RepairAnalytics: React.FC<RepairAnalyticsProps> = ({ filters }) => {
  const [loading, setLoading] = useState(false);
  const [viewType, setViewType] = useState<'trend' | 'status' | 'priority' | 'issues'>('trend');

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 這裡會調用相關的統計服務
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Failed to fetch repair analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const issueColumns: ColumnsType<any> = [
    {
      title: '問題類型',
      dataIndex: 'issue',
      key: 'issue',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: '發生次數',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count,
      render: (count) => <Text>{count.toLocaleString()}</Text>,
    },
    {
      title: '佔比',
      dataIndex: 'percentage',
      key: 'percentage',
      sorter: (a, b) => a.percentage - b.percentage,
      render: (percentage) => (
        <div>
          <Text>{percentage}%</Text>
          <Progress 
            percent={percentage} 
            size="small" 
            showInfo={false}
            style={{ marginTop: 4 }}
          />
        </div>
      ),
    },
    {
      title: '平均費用',
      dataIndex: 'avgCost',
      key: 'avgCost',
      sorter: (a, b) => a.avgCost - b.avgCost,
      render: (cost) => <Text>${cost.toLocaleString()}</Text>,
    },
    {
      title: '平均時間',
      dataIndex: 'avgTime',
      key: 'avgTime',
      sorter: (a, b) => a.avgTime - b.avgTime,
      render: (time) => <Text>{time}天</Text>,
    },
  ];

  const priorityColumns: ColumnsType<any> = [
    {
      title: '優先級',
      dataIndex: 'priorityName',
      key: 'priorityName',
      render: (text, record) => {
        const colors = {
          'URGENT': 'red',
          'HIGH': 'orange', 
          'MEDIUM': 'blue',
          'LOW': 'green'
        };
        return <Tag color={colors[record.priority as keyof typeof colors]}>{text}</Tag>;
      },
    },
    {
      title: '數量',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count,
    },
    {
      title: '平均處理時間',
      dataIndex: 'avgTime',
      key: 'avgTime',
      sorter: (a, b) => a.avgTime - b.avgTime,
      render: (time) => <Text>{time}天</Text>,
    },
    {
      title: '完成率',
      dataIndex: 'completionRate',
      key: 'completionRate',
      sorter: (a, b) => a.completionRate - b.completionRate,
      render: (rate) => (
        <div>
          <Text>{rate}%</Text>
          <Progress 
            percent={rate} 
            size="small" 
            showInfo={false}
            style={{ marginTop: 4 }}
          />
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* 控制面板 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Text strong>分析視圖：</Text>
              <Select value={viewType} onChange={setViewType} style={{ width: 150 }}>
                <Option value="trend">趨勢分析</Option>
                <Option value="status">狀態分析</Option>
                <Option value="priority">優先級分析</Option>
                <Option value="issues">問題分析</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={fetchData} loading={loading}>
                重新整理
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 關鍵指標 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="本期新增"
              value={156}
              prefix={<TrendingUpOutlined />}
              suffix={<Tag color="green">+12%</Tag>}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="本期完成"
              value={142}
              prefix={<CheckCircleOutlined />}
              suffix={<Tag color="green">+8%</Tag>}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均處理時間"
              value={3.1}
              precision={1}
              suffix="天"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="逾期維修"
              value={8}
              prefix={<ExclamationCircleOutlined />}
              suffix={<Tag color="red">-2</Tag>}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要圖表區域 */}
      {viewType === 'trend' && (
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card title="維修趨勢分析" loading={loading}>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={mockRepairTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(value) => value.slice(5)} />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip 
                    labelFormatter={(value) => `日期: ${value}`}
                    formatter={(value, name) => [
                      value,
                      name === 'newRepairs' ? '新增維修' :
                      name === 'completedRepairs' ? '完成維修' :
                      name === 'pendingRepairs' ? '待處理' : '平均時間(天)'
                    ]}
                  />
                  <Legend />
                  <Bar yAxisId="left" dataKey="newRepairs" fill="#1890ff" name="新增維修" />
                  <Bar yAxisId="left" dataKey="completedRepairs" fill="#52c41a" name="完成維修" />
                  <Line yAxisId="right" type="monotone" dataKey="avgRepairTime" stroke="#fa8c16" strokeWidth={3} name="平均時間" />
                </ComposedChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      )}

      {viewType === 'status' && (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="狀態分布圓餅圖" loading={loading}>
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={mockStatusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ statusName, percentage }) => `${statusName} ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {mockStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="狀態統計柱狀圖" loading={loading}>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={mockStatusDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="statusName" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#1890ff">
                    {mockStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      )}

      {viewType === 'priority' && (
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card title="優先級分析" loading={loading}>
              <Table
                columns={priorityColumns}
                dataSource={mockPriorityAnalysis}
                rowKey="priority"
                pagination={false}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      )}

      {viewType === 'issues' && (
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card title="常見問題分析" loading={loading}>
              <Table
                columns={issueColumns}
                dataSource={mockCommonIssues}
                rowKey="issue"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
                }}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default RepairAnalytics;
