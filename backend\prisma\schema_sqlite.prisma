// SQLite 相容的 Prisma schema
// 簡化版本，適用於開發環境

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用戶模型
model User {
  id           Int      @id @default(autoincrement())
  username     String   @unique
  email        String   @unique
  passwordHash String   @map("password_hash")
  fullName     String   @map("full_name")
  role         String   // ADMIN, TECHNICIAN, CUSTOMER_SERVICE, VIEWER
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 關聯
  assignedRepairs RepairRecord[] @relation("AssignedTechnician")
  createdRepairs  RepairRecord[] @relation("CreatedBy")

  @@map("users")
}

// 客戶模型
model Customer {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  address   String?
  company   String?
  notes     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 關聯
  repairRecords RepairRecord[]

  @@map("customers")
}

// 產品類別模型
model ProductCategory {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 關聯
  products Product[]

  @@map("product_categories")
}

// 產品模型
model Product {
  id          Int      @id @default(autoincrement())
  categoryId  Int?     @map("category_id")
  model       String
  brand       String?
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 關聯
  category      ProductCategory? @relation(fields: [categoryId], references: [id])
  repairRecords RepairRecord[]

  @@map("products")
}

// 維修記錄模型
model RepairRecord {
  id                      Int       @id @default(autoincrement())
  repairNumber            String    @unique @map("repair_number")
  customerId              Int       @map("customer_id")
  productId               Int       @map("product_id")
  productSerial           String?   @map("product_serial")
  purchaseDate            DateTime? @map("purchase_date")
  warrantyStatus          String    @default("IN_WARRANTY") @map("warranty_status") // IN_WARRANTY, OUT_OF_WARRANTY, EXTENDED
  faultDescription        String    @map("fault_description")
  receivedDate            DateTime  @map("received_date")
  estimatedCompletionDate DateTime? @map("estimated_completion_date")
  actualCompletionDate    DateTime? @map("actual_completion_date")
  status                  String    @default("PENDING_INSPECTION") // PENDING_INSPECTION, INSPECTING, REPAIRING, COMPLETED, etc.
  priority                String    @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  assignedTechnicianId    Int?      @map("assigned_technician_id")
  repairCost              Float     @default(0.00) @map("repair_cost")
  partsCost               Float     @default(0.00) @map("parts_cost")
  totalCost               Float     @default(0.00) @map("total_cost")
  notes                   String?
  createdBy               Int       @map("created_by")
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")

  // 關聯
  customer           Customer @relation(fields: [customerId], references: [id])
  product            Product  @relation(fields: [productId], references: [id])
  assignedTechnician User?    @relation("AssignedTechnician", fields: [assignedTechnicianId], references: [id])
  creator            User     @relation("CreatedBy", fields: [createdBy], references: [id])

  @@map("repair_records")
}

// 零件模型
model Part {
  id           Int      @id @default(autoincrement())
  partNumber   String   @unique @map("part_number")
  name         String
  description  String?
  unitPrice    Float    @default(0.00) @map("unit_price")
  currentStock Int      @default(0) @map("current_stock")
  minimumStock Int      @default(0) @map("minimum_stock")
  supplier     String?
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("parts")
}

// 系統設定模型
model SystemSetting {
  id           Int      @id @default(autoincrement())
  settingKey   String   @unique @map("setting_key")
  settingValue String?  @map("setting_value")
  description  String?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}
