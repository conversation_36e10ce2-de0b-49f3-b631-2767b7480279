@echo off
chcp 65001 >nul
echo ========================================
echo   IACT MIO維保管理系統 伺服器版
echo ========================================
echo.

REM 檢查 Node.js 是否已安裝
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 錯誤：未找到 Node.js
    echo.
    echo 請先安裝 Node.js：
    echo 1. 前往 https://nodejs.org/
    echo 2. 下載並安裝 LTS 版本
    echo 3. 重新執行此腳本
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 版本：
node --version
echo.

REM 檢查是否已安裝依賴
if not exist "node_modules" (
    echo 📦 首次執行，正在安裝依賴套件...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依賴安裝失敗
        pause
        exit /b 1
    )
    echo.
    echo ✅ 依賴安裝完成
    echo.
)

REM 檢查主程式檔案
if not exist "complete-system-gui.html" (
    echo ❌ 錯誤：找不到主程式檔案 complete-system-gui.html
    echo.
    echo 請確保以下檔案存在：
    echo - complete-system-gui.html
    echo - server.js
    echo - package.json
    echo.
    pause
    exit /b 1
)

REM 建立數據目錄
if not exist "data" (
    mkdir data
    echo ✅ 建立數據目錄：data/
)

echo 🚀 啟動伺服器...
echo.
echo 💡 提示：
echo   - 伺服器啟動後，請在瀏覽器中開啟顯示的網址
echo   - 按 Ctrl+C 可停止伺服器
echo   - 數據會自動保存到 data/ 目錄
echo.
echo ========================================
echo.

REM 啟動伺服器
npm start
