import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { UserRole } from '@prisma/client';
import { UserService } from '../services/userService';
import { UserRepository } from '../repositories/userRepository';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserQueryParams,
  BatchUserOperation 
} from '../types/user';

// 初始化服務
const userRepository = new UserRepository(prisma);
const userService = new UserService(userRepository);

export class UserController {
  // 獲取當前用戶資訊（已在 authController 中實作）
  static async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const user = await userService.getUserById(req.user.userId);
      if (!user) {
        throw createError('用戶不存在', 404);
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取用戶列表
  static async getUserList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: UserQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        role: req.query.role as UserRole,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await userService.getUserList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取用戶
  static async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const user = await userService.getUserById(id);
      if (!user) {
        throw createError('用戶不存在', 404);
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建用戶
  static async createUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const userData: CreateUserRequest = req.body;
      const createdBy = req.user?.userId;

      const newUser = await userService.createUser(userData, createdBy);

      res.status(201).json({
        success: true,
        message: '用戶創建成功',
        data: { user: newUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新用戶
  static async updateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const userData: UpdateUserRequest = req.body;
      const updatedBy = req.user?.userId;

      const updatedUser = await userService.updateUser(id, userData, updatedBy);

      res.json({
        success: true,
        message: '用戶更新成功',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除用戶
  static async deleteUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId;

      await userService.deleteUser(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '用戶已永久刪除' : '用戶已停用',
      });
    } catch (error) {
      next(error);
    }
  }

  // 搜尋用戶
  static async searchUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw createError('搜尋關鍵字不能為空', 400);
      }

      const results = await userService.searchUsers(query, parseInt(limit as string));

      res.json({
        success: true,
        data: { 
          query,
          results,
          count: results.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取用戶統計
  static async getUserStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await userService.getUserStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量操作用戶
  static async batchOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const operation: BatchUserOperation = req.body;
      const operatedBy = req.user?.userId;

      const result = await userService.batchOperation(operation, operatedBy);

      res.json({
        success: true,
        message: '批量操作完成',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 激活用戶
  static async activateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const activatedBy = req.user?.userId;

      const user = await userService.activateUser(id, activatedBy);

      res.json({
        success: true,
        message: '用戶已激活',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 停用用戶
  static async deactivateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const deactivatedBy = req.user?.userId;

      const user = await userService.deactivateUser(id, deactivatedBy);

      res.json({
        success: true,
        message: '用戶已停用',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更改用戶角色
  static async changeUserRole(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const { role } = req.body;
      const changedBy = req.user?.userId;

      const user = await userService.changeUserRole(id, role, changedBy);

      res.json({
        success: true,
        message: '用戶角色已更新',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新用戶資料（用戶自己）
  static async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const userId = req.user.userId;
      const { fullName, email } = req.body;

      // 用戶只能更新自己的基本資訊
      const userData: UpdateUserRequest = {
        fullName,
        email,
      };

      const updatedUser = await userService.updateUser(userId, userData, userId);

      res.json({
        success: true,
        message: '個人資料更新成功',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取用戶角色列表
  static async getUserRoles(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const roles = Object.values(UserRole).map(role => ({
        value: role,
        label: this.getRoleLabel(role),
        description: this.getRoleDescription(role),
      }));

      res.json({
        success: true,
        data: { roles },
      });
    } catch (error) {
      next(error);
    }
  }

  // 檢查用戶名是否可用
  static async checkUsernameAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { username } = req.params;
      const { excludeId } = req.query;

      if (!username) {
        throw createError('用戶名不能為空', 400);
      }

      const user = await userService.getUserByUsername(username);
      const isAvailable = !user || (excludeId && user.id === excludeId);

      res.json({
        success: true,
        data: { 
          username,
          available: isAvailable,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 輔助方法：獲取角色標籤
  private static getRoleLabel(role: UserRole): string {
    const labels = {
      [UserRole.ADMIN]: '管理員',
      [UserRole.TECHNICIAN]: '技師',
      [UserRole.CUSTOMER_SERVICE]: '客服',
      [UserRole.VIEWER]: '查看者',
    };
    return labels[role];
  }

  // 輔助方法：獲取角色描述
  private static getRoleDescription(role: UserRole): string {
    const descriptions = {
      [UserRole.ADMIN]: '系統管理員，擁有所有權限',
      [UserRole.TECHNICIAN]: '維修技師，負責維修作業和進度更新',
      [UserRole.CUSTOMER_SERVICE]: '客服人員，負責客戶服務和維修記錄管理',
      [UserRole.VIEWER]: '查看者，只能查看相關資訊',
    };
    return descriptions[role];
  }
}

export default UserController;
