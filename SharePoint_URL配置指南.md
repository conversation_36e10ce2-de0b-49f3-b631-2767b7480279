# SharePoint URL 配置指南

## 🌐 **URL 格式說明**

### SharePoint Online (Office 365)

#### 標準格式
```
https://[租戶名稱].sharepoint.com/sites/[網站名稱]
```

#### 實際範例
```
✅ 正確格式：
https://contoso.sharepoint.com/sites/maintenance
https://abc123.sharepoint.com/sites/repair-management
https://yourcompany.sharepoint.com/sites/service-desk

❌ 錯誤格式：
https://contoso.sharepoint.com/sites/maintenance/Lists/Tasks
https://contoso.sharepoint.com/sites/maintenance/
https://contoso.sharepoint.com/maintenance
```

### SharePoint On-Premises

#### 標準格式
```
https://[伺服器名稱]/sites/[網站名稱]
http://[伺服器名稱]:[端口]/sites/[網站名稱]
```

#### 實際範例
```
✅ 正確格式：
https://sharepoint.company.com/sites/maintenance
http://sp-server:8080/sites/repair
https://intranet.local/sites/service

❌ 錯誤格式：
https://sharepoint.company.com/maintenance
http://sp-server/sites/maintenance/default.aspx
```

## 🔍 **如何找到您的 SharePoint URL**

### 方法一：從瀏覽器地址欄

#### 步驟
1. **開啟您的 SharePoint 網站**
2. **查看瀏覽器地址欄**
3. **複製 URL 到網站根目錄**

#### 範例
```
瀏覽器顯示：
https://contoso.sharepoint.com/sites/maintenance/SitePages/Home.aspx

正確的網站 URL：
https://contoso.sharepoint.com/sites/maintenance
```

### 方法二：從 SharePoint 網站資訊

#### 步驟
1. **進入 SharePoint 網站**
2. **點擊右上角設定齒輪 ⚙️**
3. **選擇 "網站資訊"**
4. **複製 "網站 URL"**

### 方法三：從 Office 365 管理中心

#### 步驟
1. **登入 Office 365 管理中心**
2. **進入 "SharePoint" 管理中心**
3. **查看 "作用中網站"**
4. **複製網站 URL**

## 🏢 **不同組織的 URL 範例**

### 小型企業
```
租戶名稱通常是公司名稱：
https://abccompany.sharepoint.com/sites/maintenance
https://xyzltd.sharepoint.com/sites/repair-service
```

### 中型企業
```
可能有多個網站：
https://contoso.sharepoint.com/sites/it-maintenance
https://contoso.sharepoint.com/sites/facility-management
https://contoso.sharepoint.com/sites/equipment-repair
```

### 大型企業
```
可能有部門分隔：
https://contoso.sharepoint.com/sites/it-dept-maintenance
https://contoso.sharepoint.com/sites/manufacturing-repair
https://contoso.sharepoint.com/sites/customer-service-desk
```

### 教育機構
```
通常包含 .edu：
https://university.sharepoint.com/sites/facilities-maintenance
https://school-district.sharepoint.com/sites/equipment-repair
```

## 🔧 **URL 驗證方法**

### 瀏覽器測試
```
在瀏覽器中開啟您的 URL：
https://your-sharepoint-url

應該看到 SharePoint 網站首頁
```

### REST API 測試
```
在瀏覽器中開啟：
https://your-sharepoint-url/_api/web

應該看到 XML 格式的網站資訊
```

### 系統內建測試
```
1. 在維保管理系統中
2. 進入系統設定
3. 輸入 SharePoint URL
4. 點擊 "🔗 測試連線"
```

## ⚠️ **常見 URL 錯誤**

### 錯誤 1：包含頁面路徑
```
❌ 錯誤：
https://contoso.sharepoint.com/sites/maintenance/SitePages/Home.aspx

✅ 正確：
https://contoso.sharepoint.com/sites/maintenance
```

### 錯誤 2：缺少 /sites/
```
❌ 錯誤：
https://contoso.sharepoint.com/maintenance

✅ 正確：
https://contoso.sharepoint.com/sites/maintenance
```

### 錯誤 3：多餘的斜線
```
❌ 錯誤：
https://contoso.sharepoint.com/sites/maintenance/

✅ 正確：
https://contoso.sharepoint.com/sites/maintenance
```

### 錯誤 4：協定錯誤
```
❌ 錯誤：
http://contoso.sharepoint.com/sites/maintenance

✅ 正確：
https://contoso.sharepoint.com/sites/maintenance
```

## 🔐 **安全性考量**

### HTTPS 要求
```
✅ 安全：https://your-sharepoint-url
❌ 不安全：http://your-sharepoint-url
```

### 內部網路
```
企業內部可能使用：
https://sharepoint.company.local/sites/maintenance
http://sp-server:8080/sites/maintenance
```

### 外部存取
```
通常需要 VPN 或特殊設定：
https://sharepoint.company.com/sites/maintenance
```

## 📋 **URL 配置檢查清單**

### 配置前檢查
- [ ] 確認 SharePoint 網站存在
- [ ] 確認您有存取權限
- [ ] 確認網站 URL 格式正確
- [ ] 確認網路連線正常

### 配置步驟
- [ ] 複製正確的網站 URL
- [ ] 在系統中輸入 URL
- [ ] 執行連線測試
- [ ] 確認測試成功

### 配置後驗證
- [ ] 測試數據讀取
- [ ] 測試數據寫入
- [ ] 檢查清單存取
- [ ] 驗證權限正常

## 🛠️ **故障排除**

### 連線失敗
```
可能原因：
1. URL 格式錯誤
2. 網站不存在
3. 權限不足
4. 網路問題

解決方案：
1. 檢查 URL 格式
2. 確認網站存在
3. 檢查權限設定
4. 測試網路連線
```

### 權限錯誤
```
錯誤訊息：Access Denied

解決方案：
1. 確認使用者權限
2. 聯絡 SharePoint 管理員
3. 檢查網站權限設定
```

### 網站不存在
```
錯誤訊息：Site not found

解決方案：
1. 確認 URL 拼寫正確
2. 確認網站已建立
3. 檢查網站是否被刪除
```

## 📞 **取得協助**

### 內部資源
- **IT 部門**：確認 SharePoint 環境
- **SharePoint 管理員**：提供正確 URL
- **網路管理員**：解決連線問題

### 外部資源
- **Microsoft 文檔**：SharePoint URL 格式
- **Office 365 支援**：技術支援
- **社群論壇**：經驗分享

## 🎯 **快速配置範本**

### 範本 1：Office 365
```
URL 格式：https://[公司名稱].sharepoint.com/sites/[網站名稱]
範例：https://contoso.sharepoint.com/sites/maintenance
清單名稱：維修記錄, 客戶資料, 零件資料, 產品資料
```

### 範本 2：企業內部
```
URL 格式：https://[伺服器名稱]/sites/[網站名稱]
範例：https://sharepoint.company.com/sites/maintenance
清單名稱：維修記錄, 客戶資料, 零件資料, 產品資料
```

### 範本 3：部門網站
```
URL 格式：https://[租戶].sharepoint.com/sites/[部門]-[功能]
範例：https://contoso.sharepoint.com/sites/it-maintenance
清單名稱：維修記錄, 客戶資料, 零件資料, 產品資料
```

---

**💡 提示：如果不確定 URL 格式，請聯絡您的 IT 部門或 SharePoint 管理員獲取正確的網站 URL。**
