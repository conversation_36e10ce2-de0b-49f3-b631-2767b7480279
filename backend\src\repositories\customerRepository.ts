import { PrismaClient, Customer } from '@prisma/client';
import { 
  CustomerInfo, 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerQueryParams,
  CustomerListResponse,
  CustomerStatistics,
  CustomerSearchResult,
  CustomerDetailInfo,
  BatchCustomerOperation,
  BatchOperationResult,
  DEFAULT_CUSTOMER_PAGINATION
} from '../types/customer';
import { logger } from '../utils/logger';

export class CustomerRepository {
  private prisma: PrismaClient;

  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  // 根據ID查找客戶
  async findById(id: string): Promise<CustomerInfo | null> {
    try {
      const customer = await this.prisma.customer.findUnique({
        where: { id: BigInt(id) },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          address: true,
          companyName: true,
          contactPerson: true,
          taxId: true,
          notes: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!customer) return null;

      return {
        ...customer,
        id: customer.id.toString(),
      };
    } catch (error) {
      logger.error('查找客戶失敗:', error);
      throw new Error('查找客戶失敗');
    }
  }

  // 根據電子郵件查找客戶
  async findByEmail(email: string): Promise<Customer | null> {
    try {
      return await this.prisma.customer.findUnique({
        where: { email },
      });
    } catch (error) {
      logger.error('根據電子郵件查找客戶失敗:', error);
      throw new Error('查找客戶失敗');
    }
  }

  // 根據電話查找客戶
  async findByPhone(phone: string): Promise<Customer | null> {
    try {
      return await this.prisma.customer.findFirst({
        where: { phone },
      });
    } catch (error) {
      logger.error('根據電話查找客戶失敗:', error);
      throw new Error('查找客戶失敗');
    }
  }

  // 檢查客戶是否存在
  async checkExistence(email?: string, phone?: string, excludeId?: string): Promise<{
    emailExists: boolean;
    phoneExists: boolean;
  }> {
    try {
      const conditions: any[] = [];
      
      if (email) {
        conditions.push({ email });
      }
      
      if (phone) {
        conditions.push({ phone });
      }

      if (conditions.length === 0) {
        return { emailExists: false, phoneExists: false };
      }

      const whereCondition = excludeId 
        ? { 
            OR: conditions,
            NOT: { id: BigInt(excludeId) }
          }
        : { OR: conditions };

      const existingCustomers = await this.prisma.customer.findMany({
        where: whereCondition,
        select: { email: true, phone: true },
      });

      return {
        emailExists: email ? existingCustomers.some(c => c.email === email) : false,
        phoneExists: phone ? existingCustomers.some(c => c.phone === phone) : false,
      };
    } catch (error) {
      logger.error('檢查客戶存在性失敗:', error);
      throw new Error('檢查客戶存在性失敗');
    }
  }

  // 創建客戶
  async create(customerData: CreateCustomerRequest): Promise<CustomerInfo> {
    try {
      const customer = await this.prisma.customer.create({
        data: {
          name: customerData.name,
          email: customerData.email,
          phone: customerData.phone,
          address: customerData.address,
          companyName: customerData.companyName,
          contactPerson: customerData.contactPerson,
          taxId: customerData.taxId,
          notes: customerData.notes,
          isActive: customerData.isActive ?? true,
        },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          address: true,
          companyName: true,
          contactPerson: true,
          taxId: true,
          notes: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('客戶創建成功:', { customerId: customer.id, name: customer.name });

      return {
        ...customer,
        id: customer.id.toString(),
      };
    } catch (error) {
      logger.error('創建客戶失敗:', error);
      throw new Error('創建客戶失敗');
    }
  }

  // 更新客戶
  async update(id: string, customerData: UpdateCustomerRequest): Promise<CustomerInfo> {
    try {
      const customer = await this.prisma.customer.update({
        where: { id: BigInt(id) },
        data: customerData,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          address: true,
          companyName: true,
          contactPerson: true,
          taxId: true,
          notes: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('客戶更新成功:', { customerId: customer.id, name: customer.name });

      return {
        ...customer,
        id: customer.id.toString(),
      };
    } catch (error) {
      logger.error('更新客戶失敗:', error);
      throw new Error('更新客戶失敗');
    }
  }

  // 刪除客戶（軟刪除）
  async softDelete(id: string): Promise<void> {
    try {
      await this.prisma.customer.update({
        where: { id: BigInt(id) },
        data: { isActive: false },
      });

      logger.info('客戶軟刪除成功:', { customerId: id });
    } catch (error) {
      logger.error('軟刪除客戶失敗:', error);
      throw new Error('刪除客戶失敗');
    }
  }

  // 硬刪除客戶
  async hardDelete(id: string): Promise<void> {
    try {
      await this.prisma.customer.delete({
        where: { id: BigInt(id) },
      });

      logger.info('客戶硬刪除成功:', { customerId: id });
    } catch (error) {
      logger.error('硬刪除客戶失敗:', error);
      throw new Error('刪除客戶失敗');
    }
  }

  // 獲取客戶列表
  async findMany(params: CustomerQueryParams): Promise<CustomerListResponse> {
    try {
      const {
        page = DEFAULT_CUSTOMER_PAGINATION.page,
        limit = DEFAULT_CUSTOMER_PAGINATION.limit,
        search,
        isActive,
        hasEmail,
        hasPhone,
        hasCompany,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      // 限制每頁數量
      const actualLimit = Math.min(limit, DEFAULT_CUSTOMER_PAGINATION.maxLimit);
      const skip = (page - 1) * actualLimit;

      // 構建查詢條件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          { companyName: { contains: search, mode: 'insensitive' } },
          { contactPerson: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      if (hasEmail) {
        where.email = { not: null };
      }

      if (hasPhone) {
        where.phone = { not: null };
      }

      if (hasCompany) {
        where.companyName = { not: null };
      }

      // 執行查詢
      const [customers, total] = await Promise.all([
        this.prisma.customer.findMany({
          where,
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            companyName: true,
            contactPerson: true,
            taxId: true,
            notes: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: actualLimit,
        }),
        this.prisma.customer.count({ where }),
      ]);

      const totalPages = Math.ceil(total / actualLimit);

      return {
        customers: customers.map(customer => ({
          ...customer,
          id: customer.id.toString(),
        })),
        pagination: {
          page,
          limit: actualLimit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        filters: {
          search,
          isActive,
          hasEmail,
          hasPhone,
          hasCompany,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      logger.error('獲取客戶列表失敗:', error);
      throw new Error('獲取客戶列表失敗');
    }
  }

  // 搜尋客戶
  async search(query: string, limit: number = 10): Promise<CustomerSearchResult[]> {
    try {
      const customers = await this.prisma.customer.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query, mode: 'insensitive' } },
            { companyName: { contains: query, mode: 'insensitive' } },
            { contactPerson: { contains: query, mode: 'insensitive' } },
          ],
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          companyName: true,
          contactPerson: true,
          isActive: true,
          _count: {
            select: {
              repairRecords: true,
            },
          },
        },
        take: limit,
      });

      return customers.map(customer => ({
        id: customer.id.toString(),
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        companyName: customer.companyName,
        contactPerson: customer.contactPerson,
        isActive: customer.isActive,
        relevanceScore: this.calculateRelevanceScore(customer, query),
        repairCount: customer._count.repairRecords,
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logger.error('搜尋客戶失敗:', error);
      throw new Error('搜尋客戶失敗');
    }
  }

  // 獲取客戶詳細資訊
  async findByIdWithDetails(id: string): Promise<CustomerDetailInfo | null> {
    try {
      const customer = await this.prisma.customer.findUnique({
        where: { id: BigInt(id) },
        include: {
          repairRecords: {
            select: {
              id: true,
              repairNumber: true,
              status: true,
              createdAt: true,
              completedAt: true,
              product: {
                select: {
                  model: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          _count: {
            select: {
              repairRecords: true,
            },
          },
        },
      });

      if (!customer) return null;

      // 計算統計資訊
      const completedRepairs = customer.repairRecords.filter(r => r.status === 'COMPLETED');
      const pendingRepairs = customer.repairRecords.filter(r => r.status !== 'COMPLETED');
      
      const averageRepairTime = completedRepairs.length > 0
        ? completedRepairs.reduce((sum, repair) => {
            if (repair.completedAt) {
              return sum + (repair.completedAt.getTime() - repair.createdAt.getTime());
            }
            return sum;
          }, 0) / completedRepairs.length / (1000 * 60 * 60 * 24) // 轉換為天數
        : 0;

      return {
        id: customer.id.toString(),
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        companyName: customer.companyName,
        contactPerson: customer.contactPerson,
        taxId: customer.taxId,
        notes: customer.notes,
        isActive: customer.isActive,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
        repairRecords: customer.repairRecords.map(record => ({
          id: record.id.toString(),
          repairNumber: record.repairNumber,
          productModel: record.product?.model || 'Unknown',
          status: record.status,
          createdAt: record.createdAt,
          completedAt: record.completedAt,
        })),
        statistics: {
          totalRepairs: customer._count.repairRecords,
          completedRepairs: completedRepairs.length,
          pendingRepairs: pendingRepairs.length,
          totalAmount: 0, // TODO: 計算總金額
          averageRepairTime: Math.round(averageRepairTime),
        },
      };
    } catch (error) {
      logger.error('獲取客戶詳細資訊失敗:', error);
      throw new Error('獲取客戶詳細資訊失敗');
    }
  }

  // 獲取客戶統計
  async getStatistics(): Promise<CustomerStatistics> {
    try {
      const [total, active, withEmail, withPhone, withCompany, recentCounts, topCustomers] = await Promise.all([
        this.prisma.customer.count(),
        this.prisma.customer.count({ where: { isActive: true } }),
        this.prisma.customer.count({ where: { email: { not: null } } }),
        this.prisma.customer.count({ where: { phone: { not: null } } }),
        this.prisma.customer.count({ where: { companyName: { not: null } } }),
        this.getRecentCustomerCounts(),
        this.getTopCustomers(),
      ]);

      const withoutContact = await this.prisma.customer.count({
        where: {
          AND: [
            { email: null },
            { phone: null },
          ],
        },
      });

      return {
        total,
        active,
        inactive: total - active,
        withEmail,
        withPhone,
        withCompany,
        withoutContact,
        recentCustomers: recentCounts,
        topCustomers,
      };
    } catch (error) {
      logger.error('獲取客戶統計失敗:', error);
      throw new Error('獲取客戶統計失敗');
    }
  }

  // 批量操作
  async batchOperation(operation: BatchCustomerOperation): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
    };

    try {
      for (const customerId of operation.customerIds) {
        try {
          switch (operation.operation) {
            case 'activate':
              await this.update(customerId, { isActive: true });
              break;
            case 'deactivate':
              await this.update(customerId, { isActive: false });
              break;
            case 'delete':
              await this.softDelete(customerId);
              break;
            case 'export':
              // TODO: 實作匯出功能
              break;
          }
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            customerId,
            error: error instanceof Error ? error.message : '未知錯誤',
          });
        }
      }

      logger.info('批量操作完成:', { 
        operation: operation.operation, 
        success: result.success, 
        failed: result.failed 
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw new Error('批量操作失敗');
    }
  }

  // 計算相關性分數
  private calculateRelevanceScore(customer: any, query: string): number {
    const lowerQuery = query.toLowerCase();
    let score = 0;

    // 精確匹配得分更高
    if (customer.name.toLowerCase() === lowerQuery) score += 100;
    else if (customer.name.toLowerCase().includes(lowerQuery)) score += 50;

    if (customer.email?.toLowerCase() === lowerQuery) score += 100;
    else if (customer.email?.toLowerCase().includes(lowerQuery)) score += 50;

    if (customer.phone?.includes(query)) score += 75;

    if (customer.companyName?.toLowerCase() === lowerQuery) score += 100;
    else if (customer.companyName?.toLowerCase().includes(lowerQuery)) score += 50;

    if (customer.contactPerson?.toLowerCase().includes(lowerQuery)) score += 30;

    // 開頭匹配得分較高
    if (customer.name.toLowerCase().startsWith(lowerQuery)) score += 25;
    if (customer.companyName?.toLowerCase().startsWith(lowerQuery)) score += 25;

    return score;
  }

  // 獲取最近客戶統計
  private async getRecentCustomerCounts() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [todayCount, weekCount, monthCount] = await Promise.all([
      this.prisma.customer.count({ where: { createdAt: { gte: today } } }),
      this.prisma.customer.count({ where: { createdAt: { gte: thisWeek } } }),
      this.prisma.customer.count({ where: { createdAt: { gte: thisMonth } } }),
    ]);

    return {
      today: todayCount,
      thisWeek: weekCount,
      thisMonth: monthCount,
    };
  }

  // 獲取頂級客戶
  private async getTopCustomers() {
    const topCustomers = await this.prisma.customer.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            repairRecords: true,
          },
        },
      },
      orderBy: {
        repairRecords: {
          _count: 'desc',
        },
      },
      take: 10,
    });

    return topCustomers.map(customer => ({
      id: customer.id.toString(),
      name: customer.name,
      repairCount: customer._count.repairRecords,
    }));
  }
}
