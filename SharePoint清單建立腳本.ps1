# SharePoint 清單建立腳本
# IACT MIO維保管理系統 - SharePoint 整合

param(
    [Parameter(Mandatory=$true)]
    [string]$SiteUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$Username,
    
    [Parameter(Mandatory=$false)]
    [string]$Password
)

# 檢查 PnP PowerShell 模組
if (!(Get-Module -ListAvailable -Name PnP.PowerShell)) {
    Write-Host "正在安裝 PnP PowerShell 模組..." -ForegroundColor Yellow
    Install-Module -Name PnP.PowerShell -Force -AllowClobber
}

# 連線到 SharePoint
Write-Host "正在連線到 SharePoint: $SiteUrl" -ForegroundColor Green
try {
    if ($Username -and $Password) {
        $SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
        $Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)
        Connect-PnPOnline -Url $SiteUrl -Credentials $Credential
    } else {
        Connect-PnPOnline -Url $SiteUrl -Interactive
    }
    Write-Host "✅ SharePoint 連線成功" -ForegroundColor Green
} catch {
    Write-Host "❌ SharePoint 連線失敗: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 建立維修記錄清單
Write-Host "正在建立維修記錄清單..." -ForegroundColor Cyan
try {
    $repairList = New-PnPList -Title "維修記錄" -Template GenericList -EnableVersioning
    
    # 新增欄位
    Add-PnPField -List $repairList -DisplayName "維修編號" -InternalName "RepairId" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "客戶姓名" -InternalName "CustomerName" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "聯絡電話" -InternalName "Phone" -Type Text
    Add-PnPField -List $repairList -DisplayName "產品序號" -InternalName "SerialNumber" -Type Text
    Add-PnPField -List $repairList -DisplayName "產品名稱" -InternalName "ProductName" -Type Text -Required
    Add-PnPField -List $repairList -DisplayName "產品型號" -InternalName "ProductModel" -Type Text
    Add-PnPField -List $repairList -DisplayName "問題描述" -InternalName "Issue" -Type Note
    
    # 維保狀態選擇欄位
    $serviceStatusChoices = @("客訴", "維保")
    Add-PnPField -List $repairList -DisplayName "維保狀態" -InternalName "ServiceStatus" -Type Choice -Choices $serviceStatusChoices
    
    # 維修記錄選擇欄位
    $repairRecordChoices = @("調整", "換馬達", "清潔", "校正", "更換零件", "軟體更新")
    Add-PnPField -List $repairList -DisplayName "維修記錄" -InternalName "RepairRecord" -Type Choice -Choices $repairRecordChoices
    
    # 測試結果選擇欄位
    $testResultChoices = @("正常", "異常", "待測試")
    Add-PnPField -List $repairList -DisplayName "測試結果" -InternalName "TestResult" -Type Choice -Choices $testResultChoices
    
    Add-PnPField -List $repairList -DisplayName "維保員" -InternalName "Technician" -Type Text
    Add-PnPField -List $repairList -DisplayName "維修日期" -InternalName "RepairDate" -Type DateTime
    Add-PnPField -List $repairList -DisplayName "完成日期" -InternalName "CompletionDate" -Type DateTime
    Add-PnPField -List $repairList -DisplayName "備註" -InternalName "Notes" -Type Note
    
    Write-Host "✅ 維修記錄清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 維修記錄清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立客戶資料清單
Write-Host "正在建立客戶資料清單..." -ForegroundColor Cyan
try {
    $customerList = New-PnPList -Title "客戶資料" -Template GenericList -EnableVersioning
    
    # 新增欄位
    Add-PnPField -List $customerList -DisplayName "客戶編號" -InternalName "CustomerId" -Type Text -Required
    Add-PnPField -List $customerList -DisplayName "客戶姓名" -InternalName "CustomerName" -Type Text -Required
    Add-PnPField -List $customerList -DisplayName "聯絡電話" -InternalName "Phone" -Type Text
    Add-PnPField -List $customerList -DisplayName "電子郵箱" -InternalName "Email" -Type Text
    Add-PnPField -List $customerList -DisplayName "地址" -InternalName "Address" -Type Note
    Add-PnPField -List $customerList -DisplayName "公司名稱" -InternalName "Company" -Type Text
    
    # 客戶狀態選擇欄位
    $customerStatusChoices = @("活躍", "非活躍", "VIP", "黑名單")
    Add-PnPField -List $customerList -DisplayName "客戶狀態" -InternalName "Status" -Type Choice -Choices $customerStatusChoices
    
    Add-PnPField -List $customerList -DisplayName "註冊日期" -InternalName "RegisterDate" -Type DateTime
    Add-PnPField -List $customerList -DisplayName "備註" -InternalName "Notes" -Type Note
    
    Write-Host "✅ 客戶資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 客戶資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立零件資料清單
Write-Host "正在建立零件資料清單..." -ForegroundColor Cyan
try {
    $partsList = New-PnPList -Title "零件資料" -Template GenericList -EnableVersioning
    
    # 新增欄位
    Add-PnPField -List $partsList -DisplayName "零件編號" -InternalName "PartId" -Type Text -Required
    Add-PnPField -List $partsList -DisplayName "零件名稱" -InternalName "PartName" -Type Text -Required
    
    # 零件分類選擇欄位
    $categoryChoices = @("電子零件", "機械零件", "消耗品", "工具", "配件")
    Add-PnPField -List $partsList -DisplayName "零件分類" -InternalName "Category" -Type Choice -Choices $categoryChoices
    
    Add-PnPField -List $partsList -DisplayName "型號" -InternalName "Model" -Type Text
    Add-PnPField -List $partsList -DisplayName "供應商" -InternalName "Supplier" -Type Text
    Add-PnPField -List $partsList -DisplayName "庫存數量" -InternalName "Stock" -Type Number
    Add-PnPField -List $partsList -DisplayName "單價" -InternalName "Price" -Type Currency
    Add-PnPField -List $partsList -DisplayName "最低庫存警告" -InternalName "MinStock" -Type Number
    Add-PnPField -List $partsList -DisplayName "存放位置" -InternalName "Location" -Type Text
    Add-PnPField -List $partsList -DisplayName "規格說明" -InternalName "Specifications" -Type Note
    Add-PnPField -List $partsList -DisplayName "備註" -InternalName "Notes" -Type Note
    
    Write-Host "✅ 零件資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 零件資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立產品資料清單
Write-Host "正在建立產品資料清單..." -ForegroundColor Cyan
try {
    $productList = New-PnPList -Title "產品資料" -Template GenericList -EnableVersioning
    
    # 新增欄位
    Add-PnPField -List $productList -DisplayName "產品編號" -InternalName "ProductId" -Type Text -Required
    Add-PnPField -List $productList -DisplayName "產品名稱" -InternalName "ProductName" -Type Text -Required
    Add-PnPField -List $productList -DisplayName "品牌" -InternalName "Brand" -Type Text
    Add-PnPField -List $productList -DisplayName "型號" -InternalName "Model" -Type Text
    
    # 產品分類選擇欄位
    $productCategoryChoices = @("手機", "平板", "筆電", "桌機", "印表機", "其他")
    Add-PnPField -List $productList -DisplayName "產品分類" -InternalName "Category" -Type Choice -Choices $productCategoryChoices
    
    Add-PnPField -List $productList -DisplayName "產品描述" -InternalName "Description" -Type Note
    Add-PnPField -List $productList -DisplayName "保固期間" -InternalName "WarrantyPeriod" -Type Text
    Add-PnPField -List $productList -DisplayName "備註" -InternalName "Notes" -Type Note
    
    Write-Host "✅ 產品資料清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 產品資料清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 建立系統設定清單
Write-Host "正在建立系統設定清單..." -ForegroundColor Cyan
try {
    $configList = New-PnPList -Title "系統設定" -Template GenericList
    
    # 新增欄位
    Add-PnPField -List $configList -DisplayName "設定鍵" -InternalName "ConfigKey" -Type Text -Required
    Add-PnPField -List $configList -DisplayName "設定值" -InternalName "ConfigValue" -Type Note
    Add-PnPField -List $configList -DisplayName "描述" -InternalName "Description" -Type Note
    Add-PnPField -List $configList -DisplayName "最後更新" -InternalName "LastUpdated" -Type DateTime
    
    # 新增預設設定項目
    $defaultConfigs = @(
        @{Title="系統名稱"; ConfigKey="SystemName"; ConfigValue="IACT MIO維保管理系統"; Description="系統顯示名稱"},
        @{Title="版本號"; ConfigKey="Version"; ConfigValue="v1.0.0"; Description="系統版本號"},
        @{Title="公司名稱"; ConfigKey="CompanyName"; ConfigValue="IACT"; Description="公司名稱"},
        @{Title="聯絡電話"; ConfigKey="ContactPhone"; ConfigValue=""; Description="公司聯絡電話"},
        @{Title="聯絡郵箱"; ConfigKey="ContactEmail"; ConfigValue=""; Description="公司聯絡郵箱"}
    )
    
    foreach ($config in $defaultConfigs) {
        Add-PnPListItem -List $configList -Values $config
    }
    
    Write-Host "✅ 系統設定清單建立成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 系統設定清單建立失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 設定清單權限
Write-Host "正在設定清單權限..." -ForegroundColor Cyan
try {
    # 這裡可以根據需要設定特定的權限
    # 例如：Set-PnPListPermission -Identity "維修記錄" -User "<EMAIL>" -AddRole "Contribute"
    Write-Host "✅ 權限設定完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 權限設定跳過 (需要管理員權限)" -ForegroundColor Yellow
}

# 建立範例數據 (可選)
Write-Host "是否要建立範例數據？ (Y/N)" -ForegroundColor Yellow
$createSampleData = Read-Host

if ($createSampleData -eq "Y" -or $createSampleData -eq "y") {
    Write-Host "正在建立範例數據..." -ForegroundColor Cyan
    
    try {
        # 範例客戶數據
        $sampleCustomers = @(
            @{Title="張三"; CustomerId="C001"; CustomerName="張三"; Phone="0912345678"; Email="<EMAIL>"; Status="活躍"},
            @{Title="李四"; CustomerId="C002"; CustomerName="李四"; Phone="0923456789"; Email="<EMAIL>"; Status="活躍"},
            @{Title="王五"; CustomerId="C003"; CustomerName="王五"; Phone="0934567890"; Email="<EMAIL>"; Status="VIP"}
        )
        
        foreach ($customer in $sampleCustomers) {
            Add-PnPListItem -List "客戶資料" -Values $customer
        }
        
        # 範例零件數據
        $sampleParts = @(
            @{Title="螢幕總成"; PartId="P001"; PartName="螢幕總成"; Category="電子零件"; Stock=10; Price=1500; MinStock=5},
            @{Title="電池"; PartId="P002"; PartName="電池"; Category="電子零件"; Stock=20; Price=800; MinStock=10},
            @{Title="充電線"; PartId="P003"; PartName="充電線"; Category="配件"; Stock=50; Price=200; MinStock=20}
        )
        
        foreach ($part in $sampleParts) {
            Add-PnPListItem -List "零件資料" -Values $part
        }
        
        # 範例產品數據
        $sampleProducts = @(
            @{Title="iPhone 13"; ProductId="PR001"; ProductName="iPhone 13"; Brand="Apple"; Model="A2482"; Category="手機"},
            @{Title="Samsung Galaxy S21"; ProductId="PR002"; ProductName="Samsung Galaxy S21"; Brand="Samsung"; Model="SM-G991B"; Category="手機"},
            @{Title="iPad Air"; ProductId="PR003"; ProductName="iPad Air"; Brand="Apple"; Model="A2316"; Category="平板"}
        )
        
        foreach ($product in $sampleProducts) {
            Add-PnPListItem -List "產品資料" -Values $product
        }
        
        Write-Host "✅ 範例數據建立成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 範例數據建立失敗: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 完成
Write-Host ""
Write-Host "🎉 SharePoint 清單建立完成！" -ForegroundColor Green
Write-Host ""
Write-Host "已建立的清單：" -ForegroundColor Cyan
Write-Host "• 維修記錄" -ForegroundColor White
Write-Host "• 客戶資料" -ForegroundColor White
Write-Host "• 零件資料" -ForegroundColor White
Write-Host "• 產品資料" -ForegroundColor White
Write-Host "• 系統設定" -ForegroundColor White
Write-Host ""
Write-Host "下一步：" -ForegroundColor Yellow
Write-Host "1. 在維保管理系統中設定 SharePoint URL" -ForegroundColor White
Write-Host "2. 測試連線" -ForegroundColor White
Write-Host "3. 開始使用 SharePoint 整合功能" -ForegroundColor White
Write-Host ""
Write-Host "SharePoint 網站 URL: $SiteUrl" -ForegroundColor Green

# 中斷連線
Disconnect-PnPOnline
