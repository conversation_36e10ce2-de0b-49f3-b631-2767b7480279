import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { CustomerService } from '../services/customerService';
import { CustomerRepository } from '../repositories/customerRepository';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerQueryParams,
  BatchCustomerOperation 
} from '../types/customer';

// 初始化服務
const customerRepository = new CustomerRepository(prisma);
const customerService = new CustomerService(customerRepository);

export class CustomerController {
  // 獲取客戶列表
  static async getCustomerList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('查詢參數驗證失敗', 400);
      }

      const queryParams: CustomerQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        search: req.query.search as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        hasEmail: req.query.hasEmail ? req.query.hasEmail === 'true' : undefined,
        hasPhone: req.query.hasPhone ? req.query.hasPhone === 'true' : undefined,
        hasCompany: req.query.hasCompany ? req.query.hasCompany === 'true' : undefined,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      };

      const result = await customerService.getCustomerList(queryParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據ID獲取客戶
  static async getCustomerById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeDetails = false } = req.query;

      let customer;
      if (includeDetails === 'true') {
        customer = await customerService.getCustomerDetailById(id);
      } else {
        customer = await customerService.getCustomerById(id);
      }

      if (!customer) {
        throw createError('客戶不存在', 404);
      }

      res.json({
        success: true,
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 創建客戶
  static async createCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const customerData: CreateCustomerRequest = req.body;
      const createdBy = req.user?.userId;

      const newCustomer = await customerService.createCustomer(customerData, createdBy);

      res.status(201).json({
        success: true,
        message: '客戶創建成功',
        data: { customer: newCustomer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新客戶
  static async updateCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const { id } = req.params;
      const customerData: UpdateCustomerRequest = req.body;
      const updatedBy = req.user?.userId;

      const updatedCustomer = await customerService.updateCustomer(id, customerData, updatedBy);

      res.json({
        success: true,
        message: '客戶更新成功',
        data: { customer: updatedCustomer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 刪除客戶
  static async deleteCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { hardDelete = false } = req.query;
      const deletedBy = req.user?.userId;

      await customerService.deleteCustomer(id, deletedBy, hardDelete === 'true');

      res.json({
        success: true,
        message: hardDelete ? '客戶已永久刪除' : '客戶已停用',
      });
    } catch (error) {
      next(error);
    }
  }

  // 搜尋客戶
  static async searchCustomers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw createError('搜尋關鍵字不能為空', 400);
      }

      const results = await customerService.searchCustomers(query, parseInt(limit as string));

      res.json({
        success: true,
        data: { 
          query,
          results,
          count: results.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取客戶統計
  static async getCustomerStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await customerService.getCustomerStatistics();

      res.json({
        success: true,
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量操作客戶
  static async batchOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createError('輸入資料驗證失敗', 400);
      }

      const operation: BatchCustomerOperation = req.body;
      const operatedBy = req.user?.userId;

      const result = await customerService.batchOperation(operation, operatedBy);

      res.json({
        success: true,
        message: '批量操作完成',
        data: { result },
      });
    } catch (error) {
      next(error);
    }
  }

  // 激活客戶
  static async activateCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const activatedBy = req.user?.userId;

      const customer = await customerService.activateCustomer(id, activatedBy);

      res.json({
        success: true,
        message: '客戶已激活',
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 停用客戶
  static async deactivateCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const deactivatedBy = req.user?.userId;

      const customer = await customerService.deactivateCustomer(id, deactivatedBy);

      res.json({
        success: true,
        message: '客戶已停用',
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據電子郵件查找客戶
  static async findCustomerByEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.params;

      if (!email) {
        throw createError('電子郵件不能為空', 400);
      }

      const customer = await customerService.findCustomerByEmail(email);

      if (!customer) {
        throw createError('客戶不存在', 404);
      }

      res.json({
        success: true,
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 根據電話查找客戶
  static async findCustomerByPhone(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { phone } = req.params;

      if (!phone) {
        throw createError('電話號碼不能為空', 400);
      }

      const customer = await customerService.findCustomerByPhone(phone);

      if (!customer) {
        throw createError('客戶不存在', 404);
      }

      res.json({
        success: true,
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // 檢查電子郵件是否可用
  static async checkEmailAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.params;
      const { excludeId } = req.query;

      if (!email) {
        throw createError('電子郵件不能為空', 400);
      }

      const customer = await customerService.findCustomerByEmail(email);
      const isAvailable = !customer || (excludeId && customer.id === excludeId);

      res.json({
        success: true,
        data: { 
          email,
          available: isAvailable,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 檢查電話是否可用
  static async checkPhoneAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { phone } = req.params;
      const { excludeId } = req.query;

      if (!phone) {
        throw createError('電話號碼不能為空', 400);
      }

      const customer = await customerService.findCustomerByPhone(phone);
      const isAvailable = !customer || (excludeId && customer.id === excludeId);

      res.json({
        success: true,
        data: { 
          phone,
          available: isAvailable,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取客戶聯絡方式統計
  static async getContactStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await customerService.getCustomerStatistics();

      const contactStats = {
        total: statistics.total,
        withEmail: statistics.withEmail,
        withPhone: statistics.withPhone,
        withBoth: statistics.withEmail + statistics.withPhone - statistics.total + statistics.withoutContact,
        withoutContact: statistics.withoutContact,
        emailPercentage: statistics.total > 0 ? Math.round((statistics.withEmail / statistics.total) * 100) : 0,
        phonePercentage: statistics.total > 0 ? Math.round((statistics.withPhone / statistics.total) * 100) : 0,
      };

      res.json({
        success: true,
        data: { contactStats },
      });
    } catch (error) {
      next(error);
    }
  }

  // 獲取客戶活動統計
  static async getActivityStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await customerService.getCustomerStatistics();

      const activityStats = {
        total: statistics.total,
        active: statistics.active,
        inactive: statistics.inactive,
        recentCustomers: statistics.recentCustomers,
        topCustomers: statistics.topCustomers,
        activePercentage: statistics.total > 0 ? Math.round((statistics.active / statistics.total) * 100) : 0,
      };

      res.json({
        success: true,
        data: { activityStats },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default CustomerController;
