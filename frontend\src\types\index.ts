// 通用類型定義

// API 響應類型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 分頁響應類型
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 分頁查詢參數
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 排序參數
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜尋參數
export interface SearchParams {
  search?: string;
}

// 基礎查詢參數
export interface BaseQueryParams extends PaginationParams, SortParams, SearchParams {}

// 表單狀態
export interface FormState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

// 表格列定義
export interface TableColumn {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 統計數據類型
export interface StatisticData {
  title: string;
  value: number | string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  precision?: number;
  valueStyle?: React.CSSProperties;
}

// 圖表數據類型
export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

// 選項類型
export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

// 文件上傳類型
export interface FileUpload {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error' | 'removed';
  url?: string;
  response?: any;
  error?: any;
}

// 通知類型
export interface Notification {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  timestamp: string;
  read: boolean;
}

// 用戶角色類型
export type UserRole = 'ADMIN' | 'CUSTOMER_SERVICE' | 'TECHNICIAN' | 'VIEWER';

// 維修狀態類型
export type RepairStatus = 
  | 'PENDING_INSPECTION'    // 待檢測
  | 'INSPECTING'           // 檢測中
  | 'PENDING_REPAIR'       // 待維修
  | 'REPAIRING'           // 維修中
  | 'PENDING_PARTS'       // 待零件
  | 'COMPLETED'           // 完成
  | 'DELIVERED'           // 已交付
  | 'CANCELLED';          // 已取消

// 庫存操作類型
export type StockOperationType = 
  | 'IN'          // 入庫
  | 'OUT'         // 出庫
  | 'ADJUSTMENT'  // 調整
  | 'RESERVED'    // 預留
  | 'RELEASED';   // 釋放

// 日期範圍類型
export interface DateRange {
  start: string;
  end: string;
}

// 地址類型
export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

// 聯絡資訊類型
export interface ContactInfo {
  phone: string;
  email: string;
  address: Address;
}

// 錯誤類型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// 環境變數類型
export interface EnvironmentConfig {
  apiBaseUrl: string;
  appTitle: string;
  appVersion: string;
  enableSharePoint: boolean;
  enableAzureAD: boolean;
  enableDebug: boolean;
  sharePointConfig?: {
    clientId: string;
    tenantId: string;
    siteUrl: string;
  };
  azureConfig?: {
    clientId: string;
    tenantId: string;
    redirectUri: string;
  };
}

// 權限類型
export interface Permission {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
  granted: boolean;
}

// 審計日誌類型
export interface AuditLog {
  id: string;
  userId: number;
  userName: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

// 系統設定類型
export interface SystemSetting {
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  category: string;
  isPublic: boolean;
  updatedAt: string;
  updatedBy: string;
}

export default {};
