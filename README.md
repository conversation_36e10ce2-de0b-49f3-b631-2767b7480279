# IACT MIO維保管理系統

## 專案概述

這是一個完整的IACT MIO維保管理系統，用於追蹤和管理設備維護保養流程，提高維修效率和服務品質。

## 功能特色

### 🔧 維修記錄管理
- 完整的維修記錄生命週期管理
- 多狀態流程追蹤 (待檢測 → 檢測中 → 維修中 → 完成 → 已交付)
- 自動生成維修單號
- 維修進度記錄和歷史追蹤

### 👥 客戶管理
- 客戶資料維護和管理
- 客戶維修歷史查詢
- 聯絡資訊管理

### 📦 產品管理
- 產品型號和規格管理
- 保固期限追蹤
- 產品分類管理

### 📊 統計報表
- 維修數量統計
- 維修狀態分布分析
- 維修人員工作量統計
- 平均維修時間分析
- Excel/PDF報表匯出

### 🔐 權限管理
- 多角色權限控制 (管理員/維修人員/客服/查詢用戶)
- JWT身份驗證
- 操作日誌記錄

### 🔔 通知功能
- 維修狀態變更通知
- 逾期維修提醒
- 客戶取件通知

### 📁 SharePoint整合
- 維修記錄文件自動同步到SharePoint
- Office 365線上編輯和協作
- 文件版本控制和歷史追蹤
- 企業級文件管理和安全性
- Azure AD統一身份驗證

### ⚖️ 法律合規保證
- 完整的智慧財產權風險評估
- 開源軟體授權合規審查
- 專利侵權風險分析
- 數據保護法規合規 (GDPR、個資法)
- 商標與著作權檢查

## 技術架構

### 前端技術棧
- **React 18.x** - 現代化前端框架
- **TypeScript** - 型別安全
- **Ant Design** - 企業級UI組件庫
- **Redux Toolkit** - 狀態管理
- **React Router v6** - 路由管理
- **React Hook Form** - 表單處理
- **Recharts** - 圖表展示
- **Vite** - 快速構建工具

### 後端技術棧
- **Node.js + Express.js** - 高性能後端框架
- **TypeScript** - 型別安全
- **Prisma** - 現代化ORM
- **MySQL 8.0** - 關聯式資料庫
- **JWT** - 身份驗證
- **bcrypt** - 密碼加密
- **Winston** - 日誌管理
- **Jest** - 測試框架

### SharePoint整合
- **Microsoft Graph API** - SharePoint文件操作
- **Azure AD** - 企業身份驗證
- **Office 365** - 線上文件編輯
- **SharePoint Online** - 企業文件管理

### 開發工具
- **Docker** - 容器化部署
- **Nginx** - 反向代理
- **Git** - 版本控制
- **ESLint + Prettier** - 程式碼品質
- **Swagger** - API文檔

## 專案結構

```
維修記錄管理/
├── docs/                    # 專案文檔
│   ├── 業務需求規格書.md
│   ├── 資料庫設計.md
│   ├── 技術架構設計.md
│   ├── SharePoint整合設計.md
│   ├── 法律合規檢查報告.md
│   ├── 專利檢索報告.md
│   ├── 隱私政策.md
│   └── 合規檢查清單.md
├── frontend/                # 前端應用
│   ├── src/
│   │   ├── components/      # 可重用組件
│   │   ├── pages/          # 頁面組件
│   │   ├── services/       # API服務
│   │   ├── store/          # 狀態管理
│   │   ├── sharepoint/     # SharePoint整合組件
│   │   └── utils/          # 工具函數
│   ├── package.json
│   └── vite.config.ts
├── backend/                 # 後端應用
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── services/       # 業務邏輯
│   │   ├── repositories/   # 資料存取
│   │   ├── middleware/     # 中間件
│   │   ├── sharepoint/     # SharePoint整合服務
│   │   └── routes/         # 路由定義
│   ├── prisma/             # 資料庫模型
│   └── package.json
├── docker-compose.yml       # Docker配置
├── LICENSES.md             # 開源軟體授權聲明
└── README.md               # 專案說明
```

## 資料庫設計

### 主要資料表
- **users** - 用戶管理
- **customers** - 客戶資料
- **products** - 產品資訊
- **product_categories** - 產品分類
- **repair_records** - 維修記錄
- **repair_status_history** - 狀態變更歷史
- **repair_parts** - 維修使用零件
- **repair_progress** - 維修進度記錄
- **parts** - 零件庫存
- **sharepoint_files** - SharePoint文件記錄
- **sharepoint_sync_log** - SharePoint同步日誌
- **system_settings** - 系統設定

### 關鍵特性
- 完整的外鍵約束
- 自動時間戳記錄
- 索引優化查詢性能
- 觸發器自動處理業務邏輯

## API設計

### RESTful API端點
```
# 維修記錄
GET    /api/v1/repair-records          # 查詢維修記錄列表
POST   /api/v1/repair-records          # 新增維修記錄
GET    /api/v1/repair-records/:id      # 查詢單一維修記錄
PUT    /api/v1/repair-records/:id      # 更新維修記錄
DELETE /api/v1/repair-records/:id      # 刪除維修記錄

# 客戶管理
GET    /api/v1/customers               # 查詢客戶列表
POST   /api/v1/customers               # 新增客戶
GET    /api/v1/customers/:id           # 查詢單一客戶
PUT    /api/v1/customers/:id           # 更新客戶資料
DELETE /api/v1/customers/:id           # 刪除客戶

# 用戶認證
POST   /api/v1/auth/login              # 用戶登入
POST   /api/v1/auth/logout             # 用戶登出
GET    /api/v1/auth/profile            # 獲取用戶資料

# 報表統計
GET    /api/v1/reports/dashboard       # 儀表板統計
GET    /api/v1/reports/repair-stats    # 維修統計
GET    /api/v1/reports/export          # 匯出報表

# SharePoint整合
POST   /api/v1/sharepoint/upload       # 上傳文件到SharePoint
GET    /api/v1/sharepoint/files        # 獲取SharePoint文件列表
GET    /api/v1/sharepoint/download/:id # 下載SharePoint文件
PUT    /api/v1/sharepoint/update/:id   # 更新SharePoint文件
DELETE /api/v1/sharepoint/delete/:id   # 刪除SharePoint文件
POST   /api/v1/sharepoint/sync         # 同步維修記錄到SharePoint
```

## 開發計畫

### 第一階段：專案初始化 (1-2週)
- [x] 需求分析與規劃
- [x] 技術架構設計
- [x] 資料庫設計
- [ ] 專案環境建置
- [ ] 基礎框架搭建

### 第二階段：後端開發 (3-4週)
- [ ] 資料庫建立與初始化
- [ ] RESTful API開發
- [ ] 身份驗證與權限控制
- [ ] 業務邏輯實現
- [ ] API測試

### 第三階段：前端開發 (3-4週)
- [ ] UI組件開發
- [ ] 頁面功能實現
- [ ] 狀態管理整合
- [ ] API整合
- [ ] 響應式設計

### 第四階段：測試與優化 (2週)
- [ ] 單元測試
- [ ] 整合測試
- [ ] 性能優化
- [ ] 安全性測試
- [ ] 用戶體驗優化

### 第五階段：部署與上線 (1週)
- [ ] 生產環境配置
- [ ] 應用部署
- [ ] 監控設置
- [ ] 用戶培訓
- [ ] 正式上線

## 預期效益

### 業務效益
- 提高維修流程效率 30%
- 減少人工錯誤 50%
- 提升客戶滿意度
- 改善維修追蹤能力

### 技術效益
- 現代化技術架構
- 高可維護性和擴展性
- 良好的用戶體驗
- 完整的測試覆蓋

## 風險評估

### 技術風險
- **低風險**: 使用成熟穩定的技術棧
- **緩解措施**: 充分的技術調研和原型驗證

### 進度風險
- **中風險**: 功能複雜度可能影響開發進度
- **緩解措施**: 分階段開發，優先實現核心功能

### 用戶接受度風險
- **低風險**: 直觀的用戶界面設計
- **緩解措施**: 用戶參與設計評審，提供培訓

## 後續維護

### 功能擴展
- 行動端應用開發
- 更多報表類型
- 自動化工作流程
- 第三方系統整合

### 技術升級
- 定期安全更新
- 性能監控和優化
- 新技術評估和採用

## 聯絡資訊

如有任何問題或建議，請聯絡專案團隊。

---

**專案狀態**: 規劃階段完成 ✅  
**下一步**: 開始專案初始化和環境建置
