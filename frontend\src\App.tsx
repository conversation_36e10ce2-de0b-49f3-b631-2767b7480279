import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from 'antd';
import SimpleLoginForm from './components/auth/SimpleLoginForm';
import MainLayout from './components/layout/MainLayout';
import EnhancedDashboard from './components/dashboard/EnhancedDashboard';
import SimpleRepairManagement from './components/repairs/SimpleRepairManagement';
import CustomerManagement from './components/customers/CustomerManagement';
import ProductManagement from './components/products/ProductManagement';
import ReportsManagement from './components/reports/ReportsManagement';
import SystemSettings from './components/system/SystemSettings';

const App: React.FC = () => {
  // 檢查是否已登入
  const isAuthenticated = () => {
    const token = localStorage.getItem('authToken');
    const userInfo = localStorage.getItem('userInfo');
    return !!(token && userInfo);
  };

  return (
    <Router>
      <Routes>
        {/* 登入頁面 */}
        <Route path="/login" element={<SimpleLoginForm />} />

        {/* 主應用路由 */}
        <Route path="/" element={
          isAuthenticated() ? (
            <MainLayout>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<EnhancedDashboard />} />
                <Route path="/repairs" element={<SimpleRepairManagement />} />
                <Route path="/customers" element={<CustomerManagement />} />
                <Route path="/products" element={<ProductManagement />} />
                <Route path="/reports" element={<ReportsManagement />} />
                <Route path="/settings" element={<SystemSettings />} />
              </Routes>
            </MainLayout>
          ) : (
            <Navigate to="/login" replace />
          )
        } />
      </Routes>
    </Router>
  );
};

export default App;
