import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import SimpleLoginForm from './components/auth/SimpleLoginForm';
import MainLayout from './components/layout/MainLayout';
import EnhancedDashboard from './components/dashboard/EnhancedDashboard';
import SimpleRepairManagement from './components/repairs/SimpleRepairManagement';

const App: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 檢查登入狀態
    const token = localStorage.getItem('authToken');
    const userInfo = localStorage.getItem('userInfo');
    setIsAuthenticated(!!(token && userInfo));
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px'
      }}>
        載入中...
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* 登入頁面 */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <SimpleLoginForm />
          }
        />

        {/* 儀表板 */}
        <Route
          path="/dashboard"
          element={
            isAuthenticated ? (
              <MainLayout>
                <EnhancedDashboard />
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* 維修記錄 */}
        <Route
          path="/repairs"
          element={
            isAuthenticated ? (
              <MainLayout>
                <SimpleRepairManagement />
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* 其他頁面 */}
        <Route
          path="/customers"
          element={
            isAuthenticated ? (
              <MainLayout>
                <div style={{ padding: '50px', textAlign: 'center' }}>
                  <h2>客戶管理頁面</h2>
                  <p>功能開發中，敬請期待...</p>
                </div>
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        <Route
          path="/products"
          element={
            isAuthenticated ? (
              <MainLayout>
                <div style={{ padding: '50px', textAlign: 'center' }}>
                  <h2>產品管理頁面</h2>
                  <p>功能開發中，敬請期待...</p>
                </div>
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        <Route
          path="/reports"
          element={
            isAuthenticated ? (
              <MainLayout>
                <div style={{ padding: '50px', textAlign: 'center' }}>
                  <h2>統計報表頁面</h2>
                  <p>功能開發中，敬請期待...</p>
                </div>
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        <Route
          path="/settings"
          element={
            isAuthenticated ? (
              <MainLayout>
                <div style={{ padding: '50px', textAlign: 'center' }}>
                  <h2>系統設定頁面</h2>
                  <p>功能開發中，敬請期待...</p>
                </div>
              </MainLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* 根路徑重定向 */}
        <Route
          path="/"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
          }
        />

        {/* 404 頁面 */}
        <Route
          path="*"
          element={
            <div style={{ padding: '50px', textAlign: 'center' }}>
              <h2>頁面不存在</h2>
              <p>請檢查網址是否正確</p>
            </div>
          }
        />
      </Routes>
    </Router>
  );
};

export default App;
