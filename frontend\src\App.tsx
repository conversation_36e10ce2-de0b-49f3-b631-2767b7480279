import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { Layout, Typography } from 'antd';

import { store } from './store';
import AppRoutes from './routes';
import ErrorBoundary from './components/common/ErrorBoundary';

const { Content } = Layout;
const { Title } = Typography;

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <Router>
          <Layout style={{ minHeight: '100vh' }}>
            <Content style={{ padding: '50px' }}>
              <div style={{ textAlign: 'center', marginBottom: '50px' }}>
                <Title level={1}>客退維修品記錄管理系統</Title>
                <p>專業的維修記錄管理解決方案</p>
              </div>
              <AppRoutes />
            </Content>
          </Layout>
        </Router>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
