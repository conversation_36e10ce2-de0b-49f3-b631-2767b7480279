import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import SimpleLoginForm from './components/auth/SimpleLoginForm';
import SimpleDashboard from './components/dashboard/SimpleDashboard';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<SimpleLoginForm />} />
        <Route path="/login" element={<SimpleLoginForm />} />
        <Route path="/dashboard" element={<SimpleDashboard />} />
      </Routes>
    </Router>
  );
};

export default App;
