# 開發環境配置
VITE_API_BASE_URL=http://localhost:5000/api/v1
VITE_APP_TITLE=客退維修品記錄管理系統
VITE_APP_VERSION=1.0.0

# SharePoint 配置 (開發環境)
VITE_SHAREPOINT_CLIENT_ID=your-sharepoint-client-id
VITE_SHAREPOINT_TENANT_ID=your-tenant-id
VITE_SHAREPOINT_SITE_URL=https://your-tenant.sharepoint.com/sites/repair-management

# Azure AD 配置 (開發環境)
VITE_AZURE_CLIENT_ID=your-azure-client-id
VITE_AZURE_TENANT_ID=your-azure-tenant-id
VITE_AZURE_REDIRECT_URI=http://localhost:3000/auth/callback

# 功能開關
VITE_ENABLE_SHAREPOINT=false
VITE_ENABLE_AZURE_AD=false
VITE_ENABLE_DEBUG=true
