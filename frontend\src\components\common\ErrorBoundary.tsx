import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <Result
            status="500"
            title="系統發生錯誤"
            subTitle="抱歉，系統遇到了一個意外錯誤"
            extra={
              <div>
                <Button type="primary" onClick={this.handleReload}>
                  重新載入頁面
                </Button>
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <div style={{ marginTop: '20px', textAlign: 'left' }}>
                    <details>
                      <summary>錯誤詳情 (開發模式)</summary>
                      <pre style={{ 
                        background: '#f5f5f5', 
                        padding: '10px', 
                        borderRadius: '4px',
                        fontSize: '12px',
                        overflow: 'auto'
                      }}>
                        {this.state.error.stack}
                      </pre>
                    </details>
                  </div>
                )}
              </div>
            }
          />
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
