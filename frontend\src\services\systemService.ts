import { api, ApiResponse, PaginatedResponse } from './api';

// 用戶管理相關類型定義
export interface SystemUser {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: UserRole;
  department: string;
  position: string;
  phone?: string;
  avatar?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  permissions: Permission[];
}

export interface UserRole {
  id: number;
  name: string;
  displayName: string;
  description: string;
  level: number;
  permissions: Permission[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: number;
  name: string;
  displayName: string;
  description: string;
  module: string;
  action: string;
  resource: string;
  isActive: boolean;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  roleId: number;
  department: string;
  position: string;
  phone?: string;
}

export interface UpdateUserRequest {
  email?: string;
  firstName?: string;
  lastName?: string;
  roleId?: number;
  department?: string;
  position?: string;
  phone?: string;
  isActive?: boolean;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 系統配置相關類型
export interface SystemConfig {
  id: number;
  category: string;
  key: string;
  value: string;
  displayName: string;
  description: string;
  dataType: 'string' | 'number' | 'boolean' | 'json';
  isEditable: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SystemSettings {
  general: {
    systemName: string;
    systemLogo: string;
    timezone: string;
    language: string;
    dateFormat: string;
    currency: string;
  };
  notification: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    emailHost: string;
    emailPort: number;
    emailUsername: string;
    emailPassword: string;
  };
  security: {
    passwordMinLength: number;
    passwordRequireUppercase: boolean;
    passwordRequireLowercase: boolean;
    passwordRequireNumbers: boolean;
    passwordRequireSymbols: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  business: {
    defaultWarrantyPeriod: number;
    autoAssignTechnician: boolean;
    requireCustomerApproval: boolean;
    allowPartialPayment: boolean;
    taxRate: number;
    laborRate: number;
  };
}

export interface AuditLog {
  id: number;
  userId: number;
  user?: {
    id: number;
    fullName: string;
    email: string;
  };
  action: string;
  resource: string;
  resourceId?: number;
  details: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalRoles: number;
  totalPermissions: number;
  systemUptime: number;
  lastBackup: string;
  diskUsage: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  memoryUsage: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
}

export interface UserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  roleId?: number;
  department?: string;
  isActive?: boolean;
  sortBy?: 'username' | 'email' | 'fullName' | 'createdAt' | 'lastLoginAt';
  sortOrder?: 'asc' | 'desc';
}

export interface AuditLogQueryParams {
  page?: number;
  limit?: number;
  userId?: number;
  action?: string;
  resource?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 系統服務
export const systemService = {
  // 用戶管理
  getUsers: async (params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<SystemUser>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/system/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<SystemUser>>(url);
  },

  getUser: async (id: number): Promise<ApiResponse<SystemUser>> => {
    return await api.get<SystemUser>(`/system/users/${id}`);
  },

  createUser: async (userData: CreateUserRequest): Promise<ApiResponse<SystemUser>> => {
    return await api.post<SystemUser>('/system/users', userData);
  },

  updateUser: async (id: number, userData: UpdateUserRequest): Promise<ApiResponse<SystemUser>> => {
    return await api.put<SystemUser>(`/system/users/${id}`, userData);
  },

  deleteUser: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/system/users/${id}`);
  },

  changePassword: async (id: number, passwordData: ChangePasswordRequest): Promise<ApiResponse<void>> => {
    return await api.post<void>(`/system/users/${id}/change-password`, passwordData);
  },

  resetPassword: async (id: number): Promise<ApiResponse<{ temporaryPassword: string }>> => {
    return await api.post<{ temporaryPassword: string }>(`/system/users/${id}/reset-password`);
  },

  // 角色管理
  getRoles: async (): Promise<ApiResponse<UserRole[]>> => {
    return await api.get<UserRole[]>('/system/roles');
  },

  getRole: async (id: number): Promise<ApiResponse<UserRole>> => {
    return await api.get<UserRole>(`/system/roles/${id}`);
  },

  createRole: async (roleData: Omit<UserRole, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<UserRole>> => {
    return await api.post<UserRole>('/system/roles', roleData);
  },

  updateRole: async (id: number, roleData: Partial<UserRole>): Promise<ApiResponse<UserRole>> => {
    return await api.put<UserRole>(`/system/roles/${id}`, roleData);
  },

  deleteRole: async (id: number): Promise<ApiResponse<void>> => {
    return await api.delete<void>(`/system/roles/${id}`);
  },

  // 權限管理
  getPermissions: async (): Promise<ApiResponse<Permission[]>> => {
    return await api.get<Permission[]>('/system/permissions');
  },

  updateRolePermissions: async (roleId: number, permissionIds: number[]): Promise<ApiResponse<void>> => {
    return await api.post<void>(`/system/roles/${roleId}/permissions`, { permissionIds });
  },

  // 系統配置
  getSystemSettings: async (): Promise<ApiResponse<SystemSettings>> => {
    return await api.get<SystemSettings>('/system/settings');
  },

  updateSystemSettings: async (settings: Partial<SystemSettings>): Promise<ApiResponse<SystemSettings>> => {
    return await api.put<SystemSettings>('/system/settings', settings);
  },

  getSystemConfigs: async (category?: string): Promise<ApiResponse<SystemConfig[]>> => {
    const url = category ? `/system/configs?category=${category}` : '/system/configs';
    return await api.get<SystemConfig[]>(url);
  },

  updateSystemConfig: async (id: number, value: string): Promise<ApiResponse<SystemConfig>> => {
    return await api.put<SystemConfig>(`/system/configs/${id}`, { value });
  },

  // 系統統計
  getSystemStats: async (): Promise<ApiResponse<SystemStats>> => {
    return await api.get<SystemStats>('/system/stats');
  },

  // 審計日誌
  getAuditLogs: async (params?: AuditLogQueryParams): Promise<ApiResponse<PaginatedResponse<AuditLog>>> => {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const url = `/system/audit-logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<PaginatedResponse<AuditLog>>(url);
  },

  // 系統維護
  backupDatabase: async (): Promise<ApiResponse<{ backupFile: string }>> => {
    return await api.post<{ backupFile: string }>('/system/backup');
  },

  restoreDatabase: async (backupFile: string): Promise<ApiResponse<void>> => {
    return await api.post<void>('/system/restore', { backupFile });
  },

  clearCache: async (): Promise<ApiResponse<void>> => {
    return await api.post<void>('/system/clear-cache');
  },

  exportData: async (dataType: string, filters?: any): Promise<ApiResponse<{ downloadUrl: string }>> => {
    return await api.post<{ downloadUrl: string }>('/system/export', { dataType, filters });
  },

  importData: async (dataType: string, file: File): Promise<ApiResponse<{ imported: number; failed: number }>> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('dataType', dataType);
    
    return await api.post<{ imported: number; failed: number }>('/system/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export default systemService;
