import React from 'react';
import { Card, Typography } from 'antd';

const { Title } = Typography;

interface SharePointConfigProps {
  onConnectionChange: (status: 'connected' | 'disconnected' | 'testing') => void;
}

const SharePointConfig: React.FC<SharePointConfigProps> = ({ onConnectionChange }) => {
  return (
    <Card>
      <Title level={3}>SharePoint設定</Title>
      <p>SharePoint配置功能開發中...</p>
      <ul>
        <li>租戶ID設定</li>
        <li>應用程式ID配置</li>
        <li>站點URL設定</li>
        <li>文檔庫選擇</li>
        <li>同步設定</li>
      </ul>
    </Card>
  );
};

export default SharePointConfig;
