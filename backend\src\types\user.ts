import { UserRole } from '@prisma/client';

// 用戶基本資訊介面
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 創建用戶請求介面
export interface CreateUserRequest {
  username: string;
  email: string;
  password?: string;
  fullName: string;
  role: UserRole;
  isActive?: boolean;
}

// 更新用戶請求介面
export interface UpdateUserRequest {
  fullName?: string;
  email?: string;
  role?: UserRole;
  isActive?: boolean;
}

// 用戶查詢參數介面
export interface UserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  sortBy?: 'username' | 'email' | 'fullName' | 'role' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 用戶列表響應介面
export interface UserListResponse {
  users: UserInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    role?: UserRole;
    isActive?: boolean;
  };
  sorting: {
    sortBy: string;
    sortOrder: string;
  };
}

// 用戶統計介面
export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  byRole: Record<UserRole, number>;
  recentRegistrations: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}

// 用戶活動記錄介面
export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  description: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// 用戶資料更新歷史介面
export interface UserUpdateHistory {
  id: string;
  userId: string;
  field: string;
  oldValue: string;
  newValue: string;
  updatedBy: string;
  updatedAt: Date;
}

// 批量操作請求介面
export interface BatchUserOperation {
  userIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'updateRole';
  data?: {
    role?: UserRole;
    isActive?: boolean;
  };
}

// 批量操作結果介面
export interface BatchOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    userId: string;
    error: string;
  }>;
}

// 用戶驗證結果介面
export interface UserValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 用戶搜尋結果介面
export interface UserSearchResult {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: UserRole;
  isActive: boolean;
  relevanceScore: number;
}

// 用戶角色權限映射
export const USER_ROLE_PERMISSIONS = {
  [UserRole.VIEWER]: {
    canView: ['own_profile', 'repair_records', 'customers', 'products'],
    canEdit: ['own_profile'],
    canCreate: [],
    canDelete: [],
  },
  [UserRole.CUSTOMER_SERVICE]: {
    canView: ['own_profile', 'repair_records', 'customers', 'products', 'parts'],
    canEdit: ['own_profile', 'repair_records', 'customers'],
    canCreate: ['repair_records', 'customers'],
    canDelete: [],
  },
  [UserRole.TECHNICIAN]: {
    canView: ['own_profile', 'repair_records', 'customers', 'products', 'parts'],
    canEdit: ['own_profile', 'repair_records', 'repair_progress', 'parts'],
    canCreate: ['repair_records', 'repair_progress'],
    canDelete: ['repair_progress'],
  },
  [UserRole.ADMIN]: {
    canView: ['*'],
    canEdit: ['*'],
    canCreate: ['*'],
    canDelete: ['*'],
  },
} as const;

// 用戶狀態枚舉
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
}

// 用戶操作類型枚舉
export enum UserActionType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PROFILE_UPDATE = 'PROFILE_UPDATE',
  ROLE_CHANGE = 'ROLE_CHANGE',
  STATUS_CHANGE = 'STATUS_CHANGE',
  PASSWORD_RESET = 'PASSWORD_RESET',
}

// 用戶排序選項
export const USER_SORT_OPTIONS = [
  { value: 'username', label: '用戶名' },
  { value: 'email', label: '電子郵件' },
  { value: 'fullName', label: '姓名' },
  { value: 'role', label: '角色' },
  { value: 'createdAt', label: '創建時間' },
  { value: 'updatedAt', label: '更新時間' },
] as const;

// 用戶篩選選項
export const USER_FILTER_OPTIONS = {
  roles: [
    { value: UserRole.ADMIN, label: '管理員' },
    { value: UserRole.TECHNICIAN, label: '技師' },
    { value: UserRole.CUSTOMER_SERVICE, label: '客服' },
    { value: UserRole.VIEWER, label: '查看者' },
  ],
  status: [
    { value: true, label: '活躍' },
    { value: false, label: '停用' },
  ],
} as const;

// 用戶角色描述
export const USER_ROLE_DESCRIPTIONS = {
  [UserRole.ADMIN]: '系統管理員，擁有所有權限',
  [UserRole.TECHNICIAN]: '維修技師，負責維修作業和進度更新',
  [UserRole.CUSTOMER_SERVICE]: '客服人員，負責客戶服務和維修記錄管理',
  [UserRole.VIEWER]: '查看者，只能查看相關資訊',
} as const;

// 預設分頁設定
export const DEFAULT_USER_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const;

// 用戶搜尋配置
export const USER_SEARCH_CONFIG = {
  minSearchLength: 2,
  maxSearchLength: 100,
  searchFields: ['username', 'email', 'fullName'],
  fuzzySearchThreshold: 0.6,
} as const;
