# CORS 問題解決指南

## 🚫 **什麼是 CORS 錯誤？**

### 錯誤現象
當您嘗試使用 "🚀 自動建立" 功能時，看到以下錯誤：
```
❌ 維修記錄: Failed to fetch
❌ 客戶資料: Failed to fetch
❌ 零件資料: Failed to fetch
❌ 產品資料: Failed to fetch
```

### 錯誤原因
**CORS (Cross-Origin Resource Sharing)** 是瀏覽器的安全機制，防止網頁從不同來源存取資源。SharePoint 預設不允許從本地 HTML 檔案直接建立清單，這是正常的安全限制。

## ✅ **解決方案**

### 方案一：使用 PowerShell 腳本 (推薦)

#### 為什麼推薦？
- ✅ **不受 CORS 限制**：PowerShell 直接與 SharePoint API 通訊
- ✅ **功能完整**：可以建立所有清單和欄位
- ✅ **自動化程度高**：一鍵執行，自動處理所有步驟
- ✅ **錯誤處理完善**：詳細的錯誤報告和處理

#### 使用步驟
1. **在系統中生成腳本**：
   - 點擊 "📋 建立清單結構"
   - 選擇 "⚡ PowerShell 腳本"
   - 點擊 "💾 下載腳本"

2. **準備 PowerShell 環境**：
   ```powershell
   # 以管理員身分執行 PowerShell
   # 設定執行原則
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **執行腳本**：
   ```powershell
   # 導航到腳本位置
   cd "C:\Downloads"
   
   # 執行腳本
   .\CreateSharePointLists.ps1
   ```

4. **按照提示登入**：
   - 腳本會開啟瀏覽器進行 SharePoint 登入
   - 完成登入後腳本會自動建立清單

### 方案二：手動建立清單

#### 適用情況
- 無法執行 PowerShell 腳本
- 需要自訂清單結構
- 學習 SharePoint 清單建立

#### 使用步驟
1. **在系統中查看指導**：
   - 點擊 "📋 建立清單結構"
   - 選擇 "📋 手動建立"
   - 複製詳細的建立指導

2. **在 SharePoint 中建立**：
   - 進入您的 SharePoint 網站
   - 按照指導逐步建立清單和欄位

### 方案三：請 IT 管理員協助

#### 適用情況
- 沒有足夠的 SharePoint 權限
- 公司有嚴格的 IT 政策
- 需要在多個網站建立相同結構

#### 協助內容
- 提供完整的清單結構文檔
- 提供 PowerShell 腳本
- 協助權限設定和配置

## 🔧 **PowerShell 腳本詳細使用指南**

### 系統需求
- **Windows PowerShell 5.1** 或 **PowerShell 7+**
- **網路連線**到 SharePoint
- **SharePoint 權限**：網站擁有者或管理員

### 詳細步驟

#### 步驟 1：下載腳本
```
1. 在維保管理系統中：
   系統設定 → SharePoint 整合 → 建立清單結構 → PowerShell 腳本
2. 點擊 "💾 下載腳本"
3. 儲存到容易找到的位置 (如：桌面)
```

#### 步驟 2：準備 PowerShell
```powershell
# 1. 以管理員身分執行 PowerShell
# 右鍵點擊 PowerShell → "以系統管理員身分執行"

# 2. 設定執行原則 (允許執行腳本)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 3. 確認設定
Get-ExecutionPolicy
# 應該顯示 "RemoteSigned"
```

#### 步驟 3：執行腳本
```powershell
# 1. 導航到腳本位置
cd "C:\Users\<USER>\Desktop"

# 2. 執行腳本
.\CreateSharePointLists.ps1

# 3. 按照提示操作
# - 腳本會自動安裝 PnP PowerShell 模組
# - 開啟瀏覽器進行 SharePoint 登入
# - 自動建立所有清單和欄位
```

#### 步驟 4：驗證結果
```
1. 腳本執行完成後，檢查 SharePoint 網站
2. 確認以下清單已建立：
   ✅ 維修記錄
   ✅ 客戶資料  
   ✅ 零件資料
   ✅ 產品資料
3. 回到維保管理系統測試連線
```

## 🚨 **常見問題和解決方案**

### 問題 1：PowerShell 執行原則錯誤
**錯誤訊息**：`無法載入檔案，因為這個系統上已停用指令碼執行`

**解決方案**：
```powershell
# 設定執行原則
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者臨時允許
PowerShell -ExecutionPolicy Bypass -File ".\CreateSharePointLists.ps1"
```

### 問題 2：PnP PowerShell 模組安裝失敗
**錯誤訊息**：`Install-Module 失敗`

**解決方案**：
```powershell
# 手動安裝模組
Install-Module -Name PnP.PowerShell -Force -AllowClobber -Scope CurrentUser

# 如果還是失敗，嘗試更新 PowerShellGet
Install-Module -Name PowerShellGet -Force -AllowClobber
```

### 問題 3：SharePoint 登入失敗
**錯誤訊息**：`Connect-PnPOnline 失敗`

**解決方案**：
1. **檢查 URL**：確認 SharePoint 網站 URL 正確
2. **檢查權限**：確認有網站擁有者或管理員權限
3. **檢查網路**：確認可以存取 SharePoint
4. **嘗試不同登入方式**：
   ```powershell
   # 使用互動式登入
   Connect-PnPOnline -Url "https://yoursite.sharepoint.com" -Interactive
   
   # 使用裝置代碼登入
   Connect-PnPOnline -Url "https://yoursite.sharepoint.com" -DeviceLogin
   ```

### 問題 4：清單已存在
**錯誤訊息**：`List already exists`

**解決方案**：
- 這是正常訊息，表示清單已經存在
- 腳本會跳過已存在的清單
- 檢查 SharePoint 確認清單是否正確建立

## 💡 **最佳實踐**

### 執行前準備
1. **確認權限**：確保有 SharePoint 網站的適當權限
2. **備份數據**：如果網站已有重要數據，先備份
3. **測試環境**：建議先在測試環境中執行
4. **網路連線**：確保網路連線穩定

### 執行後驗證
1. **檢查清單**：確認所有清單都已建立
2. **驗證欄位**：檢查欄位類型和設定是否正確
3. **測試權限**：確認可以新增、編輯項目
4. **系統測試**：在維保管理系統中測試連線

## 🎯 **為什麼會有 CORS 限制？**

### 安全考量
- **防止惡意攻擊**：阻止惡意網站存取其他網站的資源
- **保護用戶數據**：防止未授權的數據存取
- **企業安全**：保護企業內部系統

### SharePoint 的安全設計
- **預設限制**：SharePoint 預設不允許跨來源請求
- **API 保護**：保護 SharePoint REST API 不被濫用
- **權限控制**：確保只有授權的應用程式可以存取

### 為什麼 PowerShell 可以？
- **直接通訊**：PowerShell 直接與 SharePoint API 通訊，不經過瀏覽器
- **認證機制**：使用 OAuth 等安全認證機制
- **官方支援**：Microsoft 官方支援的存取方式

## 🎉 **總結**

### CORS 錯誤是正常的
- ✅ 這是瀏覽器的安全機制，不是系統問題
- ✅ SharePoint 的預設安全設定
- ✅ 可以透過其他方式解決

### 推薦解決順序
1. **🥇 PowerShell 腳本**：最可靠、最自動化
2. **🥈 手動建立**：完全控制、適用所有情況  
3. **🥉 IT 協助**：專業支援、企業環境

### 最終目標
無論使用哪種方式，最終目標都是在 SharePoint 中建立必要的清單結構，讓維保管理系統可以正常使用 SharePoint 整合功能。

**💡 記住：CORS 錯誤不是失敗，而是提醒我們使用更安全、更可靠的方式來建立 SharePoint 清單！**
