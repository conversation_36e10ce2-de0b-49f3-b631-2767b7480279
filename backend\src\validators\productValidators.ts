import { body, query, param, ValidationChain } from 'express-validator';

// 創建產品驗證
export const createProductValidation: ValidationChain[] = [
  body('name')
    .notEmpty()
    .withMessage('產品名稱不能為空')
    .isLength({ min: 2, max: 200 })
    .withMessage('產品名稱長度必須在2-200個字符之間')
    .trim(),

  body('model')
    .notEmpty()
    .withMessage('產品型號不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('產品型號長度必須在1-100個字符之間')
    .trim(),

  body('brand')
    .notEmpty()
    .withMessage('品牌不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('品牌長度必須在1-100個字符之間')
    .trim(),

  body('categoryId')
    .notEmpty()
    .withMessage('產品類別不能為空')
    .isNumeric()
    .withMessage('產品類別ID必須是數字'),

  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述長度不能超過1000個字符')
    .trim(),

  body('specifications')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('規格長度不能超過2000個字符')
    .trim(),

  body('warrantyPeriod')
    .optional()
    .isInt({ min: 0, max: 120 })
    .withMessage('保固期間必須在0-120個月之間')
    .toInt(),

  body('price')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('價格必須在0-999999999之間')
    .toFloat(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更新產品驗證
export const updateProductValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('產品ID不能為空')
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  body('name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('產品名稱長度必須在2-200個字符之間')
    .trim(),

  body('model')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('產品型號長度必須在1-100個字符之間')
    .trim(),

  body('brand')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('品牌長度必須在1-100個字符之間')
    .trim(),

  body('categoryId')
    .optional()
    .isNumeric()
    .withMessage('產品類別ID必須是數字'),

  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述長度不能超過1000個字符')
    .trim(),

  body('specifications')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('規格長度不能超過2000個字符')
    .trim(),

  body('warrantyPeriod')
    .optional()
    .isInt({ min: 0, max: 120 })
    .withMessage('保固期間必須在0-120個月之間')
    .toInt(),

  body('price')
    .optional()
    .isFloat({ min: 0, max: 999999999 })
    .withMessage('價格必須在0-999999999之間')
    .toFloat(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 產品列表查詢驗證
export const productListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('categoryId')
    .optional()
    .isNumeric()
    .withMessage('類別ID必須是數字'),

  query('brand')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('品牌長度必須在1-100個字符之間')
    .trim(),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('活躍狀態篩選必須是布林值')
    .toBoolean(),

  query('hasPrice')
    .optional()
    .isBoolean()
    .withMessage('價格篩選必須是布林值')
    .toBoolean(),

  query('priceMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低價格必須大於等於0')
    .toFloat(),

  query('priceMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高價格必須大於等於0')
    .toFloat(),

  query('warrantyMin')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最短保固期間必須大於等於0')
    .toInt(),

  query('warrantyMax')
    .optional()
    .isInt({ min: 0 })
    .withMessage('最長保固期間必須大於等於0')
    .toInt(),

  query('sortBy')
    .optional()
    .isIn(['name', 'model', 'brand', 'price', 'warrantyPeriod', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 產品ID參數驗證
export const productIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('產品ID不能為空')
    .isNumeric()
    .withMessage('產品ID必須是數字'),
];

// 產品搜尋驗證
export const productSearchValidation: ValidationChain[] = [
  query('q')
    .notEmpty()
    .withMessage('搜尋關鍵字不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('結果數量限制必須在1-50之間')
    .toInt(),
];

// 批量操作驗證
export const batchOperationValidation: ValidationChain[] = [
  body('productIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('產品ID列表必須是包含1-100個元素的陣列'),

  body('productIds.*')
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  body('operation')
    .notEmpty()
    .withMessage('操作類型不能為空')
    .isIn(['activate', 'deactivate', 'delete', 'updateCategory', 'updateBrand'])
    .withMessage('操作類型必須是activate、deactivate、delete、updateCategory或updateBrand'),

  body('data')
    .optional()
    .isObject()
    .withMessage('操作資料必須是物件'),

  body('data.isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),

  body('data.categoryId')
    .if(body('operation').equals('updateCategory'))
    .notEmpty()
    .withMessage('更新類別操作必須提供類別ID')
    .isNumeric()
    .withMessage('類別ID必須是數字'),

  body('data.brand')
    .if(body('operation').equals('updateBrand'))
    .notEmpty()
    .withMessage('更新品牌操作必須提供品牌名稱')
    .isLength({ min: 1, max: 100 })
    .withMessage('品牌長度必須在1-100個字符之間'),
];

// 產品型號參數驗證
export const modelParamValidation: ValidationChain[] = [
  param('model')
    .notEmpty()
    .withMessage('產品型號不能為空')
    .isLength({ min: 1, max: 100 })
    .withMessage('產品型號長度必須在1-100個字符之間')
    .trim(),

  query('excludeId')
    .optional()
    .isNumeric()
    .withMessage('排除ID必須是數字'),
];

// 產品詳細資訊查詢驗證
export const productDetailValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('產品ID不能為空')
    .isNumeric()
    .withMessage('產品ID必須是數字'),

  query('includeDetails')
    .optional()
    .isBoolean()
    .withMessage('includeDetails必須是布林值')
    .toBoolean(),
];

// === 產品類別驗證 ===

// 創建產品類別驗證
export const createCategoryValidation: ValidationChain[] = [
  body('name')
    .notEmpty()
    .withMessage('類別名稱不能為空')
    .isLength({ min: 2, max: 100 })
    .withMessage('類別名稱長度必須在2-100個字符之間')
    .trim(),

  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述長度不能超過500個字符')
    .trim(),

  body('parentId')
    .optional()
    .isNumeric()
    .withMessage('父類別ID必須是數字'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 更新產品類別驗證
export const updateCategoryValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('類別ID不能為空')
    .isNumeric()
    .withMessage('類別ID必須是數字'),

  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('類別名稱長度必須在2-100個字符之間')
    .trim(),

  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述長度不能超過500個字符')
    .trim(),

  body('parentId')
    .optional()
    .isNumeric()
    .withMessage('父類別ID必須是數字'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive必須是布林值'),
];

// 產品類別列表查詢驗證
export const categoryListValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('頁碼必須是大於0的整數')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每頁數量必須在1-100之間')
    .toInt(),

  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('搜尋關鍵字長度必須在2-100個字符之間')
    .trim(),

  query('parentId')
    .optional()
    .custom((value) => {
      if (value === '' || value === 'null') return true;
      return !isNaN(Number(value));
    })
    .withMessage('父類別ID必須是數字或空值'),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('活躍狀態篩選必須是布林值')
    .toBoolean(),

  query('includeChildren')
    .optional()
    .isBoolean()
    .withMessage('includeChildren必須是布林值')
    .toBoolean(),

  query('sortBy')
    .optional()
    .isIn(['name', 'createdAt', 'updatedAt'])
    .withMessage('排序欄位無效'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序順序必須是asc或desc'),
];

// 產品類別ID參數驗證
export const categoryIdValidation: ValidationChain[] = [
  param('id')
    .notEmpty()
    .withMessage('類別ID不能為空')
    .isNumeric()
    .withMessage('類別ID必須是數字'),
];

// 自定義驗證函數：檢查價格範圍
export const validatePriceRange = () => {
  return query().custom((value, { req }) => {
    const { priceMin, priceMax } = req.query;
    
    if (priceMin && priceMax && parseFloat(priceMin as string) > parseFloat(priceMax as string)) {
      throw new Error('最低價格不能大於最高價格');
    }
    
    return true;
  });
};

// 自定義驗證函數：檢查保固期間範圍
export const validateWarrantyRange = () => {
  return query().custom((value, { req }) => {
    const { warrantyMin, warrantyMax } = req.query;
    
    if (warrantyMin && warrantyMax && parseInt(warrantyMin as string) > parseInt(warrantyMax as string)) {
      throw new Error('最短保固期間不能大於最長保固期間');
    }
    
    return true;
  });
};

// 組合驗證：產品列表（包含範圍檢查）
export const productListWithRangeValidation = [
  ...productListValidation,
  validatePriceRange(),
  validateWarrantyRange(),
];

// 組合驗證：批量操作（包含權限檢查）
export const batchOperationWithPermissionValidation = [
  ...batchOperationValidation,
  // 可以在這裡添加額外的權限檢查
];
