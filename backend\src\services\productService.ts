import { ProductRepository } from '../repositories/productRepository';
import { ProductCategoryRepository } from '../repositories/productCategoryRepository';
import { logger } from '../utils/logger';
import { 
  ProductInfo, 
  ProductCategoryInfo,
  CreateProductRequest, 
  UpdateProductRequest, 
  CreateProductCategoryRequest,
  UpdateProductCategoryRequest,
  ProductQueryParams,
  ProductCategoryQueryParams,
  ProductListResponse,
  ProductCategoryListResponse,
  ProductStatistics,
  ProductCategoryStatistics,
  ProductSearchResult,
  ProductDetailInfo,
  BatchProductOperation,
  BatchOperationResult,
  ProductValidationResult,
  ProductCategoryTree,
  PRODUCT_VALIDATION_RULES,
  PRODUCT_CATEGORY_VALIDATION_RULES
} from '../types/product';

export class ProductService {
  private productRepository: ProductRepository;
  private categoryRepository: ProductCategoryRepository;

  constructor(productRepository: ProductRepository, categoryRepository: ProductCategoryRepository) {
    this.productRepository = productRepository;
    this.categoryRepository = categoryRepository;
  }

  // 根據ID獲取產品
  async getProductById(id: string): Promise<ProductInfo | null> {
    try {
      return await this.productRepository.findById(id);
    } catch (error) {
      logger.error('獲取產品失敗:', error);
      throw new Error('獲取產品失敗');
    }
  }

  // 根據ID獲取產品詳細資訊
  async getProductDetailById(id: string): Promise<ProductDetailInfo | null> {
    try {
      return await this.productRepository.findByIdWithDetails(id);
    } catch (error) {
      logger.error('獲取產品詳細資訊失敗:', error);
      throw new Error('獲取產品詳細資訊失敗');
    }
  }

  // 創建產品
  async createProduct(productData: CreateProductRequest, createdBy?: string): Promise<ProductInfo> {
    try {
      // 驗證產品資料
      const validation = await this.validateProductData(productData);
      if (!validation.isValid) {
        throw new Error(`產品資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查型號是否已存在
      const modelExists = await this.productRepository.checkExistence(productData.model);
      if (modelExists) {
        throw new Error('產品型號已存在');
      }

      // 檢查類別是否存在
      const category = await this.categoryRepository.findById(productData.categoryId);
      if (!category) {
        throw new Error('產品類別不存在');
      }

      if (!category.isActive) {
        throw new Error('產品類別已停用');
      }

      // 創建產品
      const newProduct = await this.productRepository.create(productData);

      logger.info('產品創建成功:', { 
        productId: newProduct.id, 
        name: newProduct.name,
        createdBy 
      });

      return newProduct;
    } catch (error) {
      logger.error('創建產品失敗:', error);
      throw error;
    }
  }

  // 更新產品
  async updateProduct(id: string, productData: UpdateProductRequest, updatedBy?: string): Promise<ProductInfo> {
    try {
      // 檢查產品是否存在
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error('產品不存在');
      }

      // 驗證更新資料
      const validation = await this.validateUpdateData(productData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查型號是否已存在（排除當前產品）
      if (productData.model && productData.model !== existingProduct.model) {
        const modelExists = await this.productRepository.checkExistence(productData.model, id);
        if (modelExists) {
          throw new Error('產品型號已存在');
        }
      }

      // 檢查類別是否存在
      if (productData.categoryId) {
        const category = await this.categoryRepository.findById(productData.categoryId);
        if (!category) {
          throw new Error('產品類別不存在');
        }
        if (!category.isActive) {
          throw new Error('產品類別已停用');
        }
      }

      // 更新產品
      const updatedProduct = await this.productRepository.update(id, productData);

      logger.info('產品更新成功:', { 
        productId: id, 
        name: updatedProduct.name,
        updatedBy,
        changes: Object.keys(productData)
      });

      return updatedProduct;
    } catch (error) {
      logger.error('更新產品失敗:', error);
      throw error;
    }
  }

  // 刪除產品
  async deleteProduct(id: string, deletedBy?: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查產品是否存在
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error('產品不存在');
      }

      // 檢查是否有關聯的維修記錄
      const productDetail = await this.productRepository.findByIdWithDetails(id);
      if (productDetail && productDetail.statistics.totalRepairs > 0 && hardDelete) {
        throw new Error('產品有關聯的維修記錄，無法永久刪除');
      }

      if (hardDelete) {
        await this.productRepository.hardDelete(id);
        logger.info('產品硬刪除成功:', { productId: id, deletedBy });
      } else {
        await this.productRepository.softDelete(id);
        logger.info('產品軟刪除成功:', { productId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除產品失敗:', error);
      throw error;
    }
  }

  // 獲取產品列表
  async getProductList(params: ProductQueryParams): Promise<ProductListResponse> {
    try {
      return await this.productRepository.findMany(params);
    } catch (error) {
      logger.error('獲取產品列表失敗:', error);
      throw new Error('獲取產品列表失敗');
    }
  }

  // 搜尋產品
  async searchProducts(query: string, limit: number = 10): Promise<ProductSearchResult[]> {
    try {
      if (query.length < 2) {
        throw new Error('搜尋關鍵字至少需要2個字符');
      }

      return await this.productRepository.search(query, limit);
    } catch (error) {
      logger.error('搜尋產品失敗:', error);
      throw error;
    }
  }

  // 獲取產品統計
  async getProductStatistics(): Promise<ProductStatistics> {
    try {
      return await this.productRepository.getStatistics();
    } catch (error) {
      logger.error('獲取產品統計失敗:', error);
      throw new Error('獲取產品統計失敗');
    }
  }

  // 批量操作產品
  async batchOperation(operation: BatchProductOperation, operatedBy?: string): Promise<BatchOperationResult> {
    try {
      // 驗證批量操作
      if (operation.productIds.length === 0) {
        throw new Error('未選擇任何產品');
      }

      if (operation.productIds.length > 100) {
        throw new Error('批量操作產品數量不能超過100個');
      }

      // 特殊驗證
      if (operation.operation === 'updateCategory' && !operation.data?.categoryId) {
        throw new Error('更新類別操作必須提供類別ID');
      }

      if (operation.operation === 'updateBrand' && !operation.data?.brand) {
        throw new Error('更新品牌操作必須提供品牌名稱');
      }

      const result = await this.productRepository.batchOperation(operation);

      logger.info('批量操作完成:', { 
        operation: operation.operation,
        productCount: operation.productIds.length,
        success: result.success,
        failed: result.failed,
        operatedBy
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw error;
    }
  }

  // 激活產品
  async activateProduct(id: string, activatedBy?: string): Promise<ProductInfo> {
    try {
      return await this.updateProduct(id, { isActive: true }, activatedBy);
    } catch (error) {
      logger.error('激活產品失敗:', error);
      throw error;
    }
  }

  // 停用產品
  async deactivateProduct(id: string, deactivatedBy?: string): Promise<ProductInfo> {
    try {
      return await this.updateProduct(id, { isActive: false }, deactivatedBy);
    } catch (error) {
      logger.error('停用產品失敗:', error);
      throw error;
    }
  }

  // 根據型號查找產品
  async findProductByModel(model: string): Promise<ProductInfo | null> {
    try {
      const product = await this.productRepository.findByModel(model);
      if (!product) return null;

      return await this.productRepository.findById(product.id.toString());
    } catch (error) {
      logger.error('根據型號查找產品失敗:', error);
      throw error;
    }
  }

  // 驗證產品資料
  private async validateProductData(productData: CreateProductRequest): Promise<ProductValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證產品名稱
    if (!productData.name || productData.name.trim().length < PRODUCT_VALIDATION_RULES.name.minLength) {
      errors.push(`產品名稱至少需要${PRODUCT_VALIDATION_RULES.name.minLength}個字符`);
    }

    if (productData.name && productData.name.length > PRODUCT_VALIDATION_RULES.name.maxLength) {
      errors.push(`產品名稱不能超過${PRODUCT_VALIDATION_RULES.name.maxLength}個字符`);
    }

    // 驗證型號
    if (!productData.model || productData.model.trim().length < PRODUCT_VALIDATION_RULES.model.minLength) {
      errors.push(`產品型號至少需要${PRODUCT_VALIDATION_RULES.model.minLength}個字符`);
    }

    if (productData.model && productData.model.length > PRODUCT_VALIDATION_RULES.model.maxLength) {
      errors.push(`產品型號不能超過${PRODUCT_VALIDATION_RULES.model.maxLength}個字符`);
    }

    // 驗證品牌
    if (!productData.brand || productData.brand.trim().length < PRODUCT_VALIDATION_RULES.brand.minLength) {
      errors.push(`品牌至少需要${PRODUCT_VALIDATION_RULES.brand.minLength}個字符`);
    }

    if (productData.brand && productData.brand.length > PRODUCT_VALIDATION_RULES.brand.maxLength) {
      errors.push(`品牌不能超過${PRODUCT_VALIDATION_RULES.brand.maxLength}個字符`);
    }

    // 驗證描述
    if (productData.description && productData.description.length > PRODUCT_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PRODUCT_VALIDATION_RULES.description.maxLength}個字符`);
    }

    // 驗證規格
    if (productData.specifications && productData.specifications.length > PRODUCT_VALIDATION_RULES.specifications.maxLength) {
      errors.push(`規格不能超過${PRODUCT_VALIDATION_RULES.specifications.maxLength}個字符`);
    }

    // 驗證保固期間
    if (productData.warrantyPeriod !== undefined) {
      if (productData.warrantyPeriod < PRODUCT_VALIDATION_RULES.warrantyPeriod.min) {
        errors.push(`保固期間不能小於${PRODUCT_VALIDATION_RULES.warrantyPeriod.min}個月`);
      }
      if (productData.warrantyPeriod > PRODUCT_VALIDATION_RULES.warrantyPeriod.max) {
        errors.push(`保固期間不能超過${PRODUCT_VALIDATION_RULES.warrantyPeriod.max}個月`);
      }
    }

    // 驗證價格
    if (productData.price !== undefined) {
      if (productData.price < PRODUCT_VALIDATION_RULES.price.min) {
        errors.push(`價格不能小於${PRODUCT_VALIDATION_RULES.price.min}`);
      }
      if (productData.price > PRODUCT_VALIDATION_RULES.price.max) {
        errors.push(`價格不能超過${PRODUCT_VALIDATION_RULES.price.max}`);
      }
    }

    // 警告：缺少價格
    if (productData.price === undefined) {
      warnings.push('建議設定產品價格');
    }

    // 警告：缺少保固期間
    if (productData.warrantyPeriod === undefined) {
      warnings.push('建議設定保固期間');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證更新資料
  private async validateUpdateData(productData: UpdateProductRequest): Promise<ProductValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證產品名稱
    if (productData.name !== undefined) {
      if (productData.name.trim().length < PRODUCT_VALIDATION_RULES.name.minLength) {
        errors.push(`產品名稱至少需要${PRODUCT_VALIDATION_RULES.name.minLength}個字符`);
      }
      if (productData.name.length > PRODUCT_VALIDATION_RULES.name.maxLength) {
        errors.push(`產品名稱不能超過${PRODUCT_VALIDATION_RULES.name.maxLength}個字符`);
      }
    }

    // 驗證型號
    if (productData.model !== undefined) {
      if (productData.model.trim().length < PRODUCT_VALIDATION_RULES.model.minLength) {
        errors.push(`產品型號至少需要${PRODUCT_VALIDATION_RULES.model.minLength}個字符`);
      }
      if (productData.model.length > PRODUCT_VALIDATION_RULES.model.maxLength) {
        errors.push(`產品型號不能超過${PRODUCT_VALIDATION_RULES.model.maxLength}個字符`);
      }
    }

    // 驗證品牌
    if (productData.brand !== undefined) {
      if (productData.brand.trim().length < PRODUCT_VALIDATION_RULES.brand.minLength) {
        errors.push(`品牌至少需要${PRODUCT_VALIDATION_RULES.brand.minLength}個字符`);
      }
      if (productData.brand.length > PRODUCT_VALIDATION_RULES.brand.maxLength) {
        errors.push(`品牌不能超過${PRODUCT_VALIDATION_RULES.brand.maxLength}個字符`);
      }
    }

    // 其他欄位驗證...
    if (productData.description !== undefined && productData.description && productData.description.length > PRODUCT_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PRODUCT_VALIDATION_RULES.description.maxLength}個字符`);
    }

    if (productData.specifications !== undefined && productData.specifications && productData.specifications.length > PRODUCT_VALIDATION_RULES.specifications.maxLength) {
      errors.push(`規格不能超過${PRODUCT_VALIDATION_RULES.specifications.maxLength}個字符`);
    }

    if (productData.warrantyPeriod !== undefined) {
      if (productData.warrantyPeriod < PRODUCT_VALIDATION_RULES.warrantyPeriod.min) {
        errors.push(`保固期間不能小於${PRODUCT_VALIDATION_RULES.warrantyPeriod.min}個月`);
      }
      if (productData.warrantyPeriod > PRODUCT_VALIDATION_RULES.warrantyPeriod.max) {
        errors.push(`保固期間不能超過${PRODUCT_VALIDATION_RULES.warrantyPeriod.max}個月`);
      }
    }

    if (productData.price !== undefined) {
      if (productData.price < PRODUCT_VALIDATION_RULES.price.min) {
        errors.push(`價格不能小於${PRODUCT_VALIDATION_RULES.price.min}`);
      }
      if (productData.price > PRODUCT_VALIDATION_RULES.price.max) {
        errors.push(`價格不能超過${PRODUCT_VALIDATION_RULES.price.max}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // === 產品類別相關方法 ===

  // 根據ID獲取產品類別
  async getCategoryById(id: string): Promise<ProductCategoryInfo | null> {
    try {
      return await this.categoryRepository.findById(id);
    } catch (error) {
      logger.error('獲取產品類別失敗:', error);
      throw new Error('獲取產品類別失敗');
    }
  }

  // 創建產品類別
  async createCategory(categoryData: CreateProductCategoryRequest, createdBy?: string): Promise<ProductCategoryInfo> {
    try {
      // 驗證類別資料
      const validation = await this.validateCategoryData(categoryData);
      if (!validation.isValid) {
        throw new Error(`產品類別資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查名稱是否已存在
      const nameExists = await this.categoryRepository.checkExistence(categoryData.name);
      if (nameExists) {
        throw new Error('產品類別名稱已存在');
      }

      // 檢查父類別是否存在
      if (categoryData.parentId) {
        const parentCategory = await this.categoryRepository.findById(categoryData.parentId);
        if (!parentCategory) {
          throw new Error('父類別不存在');
        }
        if (!parentCategory.isActive) {
          throw new Error('父類別已停用');
        }
      }

      // 創建類別
      const newCategory = await this.categoryRepository.create(categoryData);

      logger.info('產品類別創建成功:', {
        categoryId: newCategory.id,
        name: newCategory.name,
        createdBy
      });

      return newCategory;
    } catch (error) {
      logger.error('創建產品類別失敗:', error);
      throw error;
    }
  }

  // 更新產品類別
  async updateCategory(id: string, categoryData: UpdateProductCategoryRequest, updatedBy?: string): Promise<ProductCategoryInfo> {
    try {
      // 檢查類別是否存在
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        throw new Error('產品類別不存在');
      }

      // 驗證更新資料
      const validation = await this.validateCategoryUpdateData(categoryData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查名稱是否已存在（排除當前類別）
      if (categoryData.name && categoryData.name !== existingCategory.name) {
        const nameExists = await this.categoryRepository.checkExistence(categoryData.name, id);
        if (nameExists) {
          throw new Error('產品類別名稱已存在');
        }
      }

      // 檢查父類別是否存在
      if (categoryData.parentId) {
        const parentCategory = await this.categoryRepository.findById(categoryData.parentId);
        if (!parentCategory) {
          throw new Error('父類別不存在');
        }
        if (!parentCategory.isActive) {
          throw new Error('父類別已停用');
        }
        // 防止循環引用
        if (categoryData.parentId === id) {
          throw new Error('不能將自己設為父類別');
        }
      }

      // 更新類別
      const updatedCategory = await this.categoryRepository.update(id, categoryData);

      logger.info('產品類別更新成功:', {
        categoryId: id,
        name: updatedCategory.name,
        updatedBy,
        changes: Object.keys(categoryData)
      });

      return updatedCategory;
    } catch (error) {
      logger.error('更新產品類別失敗:', error);
      throw error;
    }
  }

  // 刪除產品類別
  async deleteCategory(id: string, deletedBy?: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查類別是否存在
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        throw new Error('產品類別不存在');
      }

      // 檢查是否有子類別
      if (existingCategory.children && existingCategory.children.length > 0) {
        throw new Error('類別有子類別，無法刪除');
      }

      // 檢查是否有關聯的產品
      const categoryProducts = await this.productRepository.findMany({ categoryId: id, limit: 1 });
      if (categoryProducts.products.length > 0 && hardDelete) {
        throw new Error('類別有關聯的產品，無法永久刪除');
      }

      if (hardDelete) {
        await this.categoryRepository.hardDelete(id);
        logger.info('產品類別硬刪除成功:', { categoryId: id, deletedBy });
      } else {
        await this.categoryRepository.softDelete(id);
        logger.info('產品類別軟刪除成功:', { categoryId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除產品類別失敗:', error);
      throw error;
    }
  }

  // 獲取產品類別列表
  async getCategoryList(params: ProductCategoryQueryParams): Promise<ProductCategoryListResponse> {
    try {
      return await this.categoryRepository.findMany(params);
    } catch (error) {
      logger.error('獲取產品類別列表失敗:', error);
      throw new Error('獲取產品類別列表失敗');
    }
  }

  // 獲取產品類別樹狀結構
  async getCategoryTree(): Promise<ProductCategoryTree[]> {
    try {
      return await this.categoryRepository.getCategoryTree();
    } catch (error) {
      logger.error('獲取產品類別樹狀結構失敗:', error);
      throw new Error('獲取產品類別樹狀結構失敗');
    }
  }

  // 獲取產品類別統計
  async getCategoryStatistics(): Promise<ProductCategoryStatistics> {
    try {
      return await this.categoryRepository.getStatistics();
    } catch (error) {
      logger.error('獲取產品類別統計失敗:', error);
      throw new Error('獲取產品類別統計失敗');
    }
  }

  // 激活產品類別
  async activateCategory(id: string, activatedBy?: string): Promise<ProductCategoryInfo> {
    try {
      return await this.updateCategory(id, { isActive: true }, activatedBy);
    } catch (error) {
      logger.error('激活產品類別失敗:', error);
      throw error;
    }
  }

  // 停用產品類別
  async deactivateCategory(id: string, deactivatedBy?: string): Promise<ProductCategoryInfo> {
    try {
      return await this.updateCategory(id, { isActive: false }, deactivatedBy);
    } catch (error) {
      logger.error('停用產品類別失敗:', error);
      throw error;
    }
  }

  // 驗證產品類別資料
  private async validateCategoryData(categoryData: CreateProductCategoryRequest): Promise<ProductValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證類別名稱
    if (!categoryData.name || categoryData.name.trim().length < PRODUCT_CATEGORY_VALIDATION_RULES.name.minLength) {
      errors.push(`類別名稱至少需要${PRODUCT_CATEGORY_VALIDATION_RULES.name.minLength}個字符`);
    }

    if (categoryData.name && categoryData.name.length > PRODUCT_CATEGORY_VALIDATION_RULES.name.maxLength) {
      errors.push(`類別名稱不能超過${PRODUCT_CATEGORY_VALIDATION_RULES.name.maxLength}個字符`);
    }

    // 驗證描述
    if (categoryData.description && categoryData.description.length > PRODUCT_CATEGORY_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PRODUCT_CATEGORY_VALIDATION_RULES.description.maxLength}個字符`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證產品類別更新資料
  private async validateCategoryUpdateData(categoryData: UpdateProductCategoryRequest): Promise<ProductValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證類別名稱
    if (categoryData.name !== undefined) {
      if (categoryData.name.trim().length < PRODUCT_CATEGORY_VALIDATION_RULES.name.minLength) {
        errors.push(`類別名稱至少需要${PRODUCT_CATEGORY_VALIDATION_RULES.name.minLength}個字符`);
      }
      if (categoryData.name.length > PRODUCT_CATEGORY_VALIDATION_RULES.name.maxLength) {
        errors.push(`類別名稱不能超過${PRODUCT_CATEGORY_VALIDATION_RULES.name.maxLength}個字符`);
      }
    }

    // 驗證描述
    if (categoryData.description !== undefined && categoryData.description && categoryData.description.length > PRODUCT_CATEGORY_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PRODUCT_CATEGORY_VALIDATION_RULES.description.maxLength}個字符`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
