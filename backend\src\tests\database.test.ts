import { PrismaClient } from '@prisma/client';
import { testDatabaseConnection, getDatabaseHealth } from '../config/database';
import { DatabaseHelpers } from '../utils/database-helpers';

describe('Database Tests', () => {
  let prisma: PrismaClient;
  let dbHelpers: DatabaseHelpers;

  beforeAll(async () => {
    prisma = new PrismaClient();
    dbHelpers = new DatabaseHelpers(prisma);
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('Database Connection', () => {
    test('should connect to database successfully', async () => {
      const isConnected = await testDatabaseConnection();
      expect(isConnected).toBe(true);
    });

    test('should get database health status', async () => {
      const health = await getDatabaseHealth();
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('responseTime');
      expect(health).toHaveProperty('statistics');
      expect(health).toHaveProperty('timestamp');
    });
  });

  describe('Database Helpers', () => {
    test('should generate unique repair number', async () => {
      const repairNumber1 = await dbHelpers.generateRepairNumber();
      const repairNumber2 = await dbHelpers.generateRepairNumber();
      
      expect(repairNumber1).toMatch(/^R\d{8}\d{3}$/);
      expect(repairNumber2).toMatch(/^R\d{8}\d{3}$/);
      expect(repairNumber1).not.toBe(repairNumber2);
    });

    test('should check if repair number exists', async () => {
      // 假設資料庫中沒有這個編號
      const nonExistentNumber = 'R20240101999';
      const exists = await dbHelpers.isRepairNumberExists(nonExistentNumber);
      expect(exists).toBe(false);
    });

    test('should get user statistics', async () => {
      const stats = await dbHelpers.getUserStatistics();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('active');
      expect(stats).toHaveProperty('inactive');
      expect(stats).toHaveProperty('byRole');
      expect(typeof stats.total).toBe('number');
    });

    test('should get repair statistics', async () => {
      const stats = await dbHelpers.getRepairStatistics();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('byStatus');
      expect(stats).toHaveProperty('byPriority');
      expect(typeof stats.total).toBe('number');
    });

    test('should get customer statistics', async () => {
      const stats = await dbHelpers.getCustomerStatistics();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('withEmail');
      expect(stats).toHaveProperty('withPhone');
      expect(stats).toHaveProperty('withCompany');
      expect(typeof stats.total).toBe('number');
    });

    test('should get product statistics', async () => {
      const stats = await dbHelpers.getProductStatistics();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('byCategory');
      expect(stats).toHaveProperty('byBrand');
      expect(typeof stats.total).toBe('number');
    });

    test('should get parts statistics', async () => {
      const stats = await dbHelpers.getPartsStatistics();
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('lowStock');
      expect(stats).toHaveProperty('outOfStock');
      expect(stats).toHaveProperty('totalQuantity');
      expect(typeof stats.total).toBe('number');
    });

    test('should get dashboard statistics', async () => {
      const stats = await dbHelpers.getDashboardStatistics();
      expect(stats).toHaveProperty('users');
      expect(stats).toHaveProperty('repairs');
      expect(stats).toHaveProperty('customers');
      expect(stats).toHaveProperty('products');
      expect(stats).toHaveProperty('parts');
      expect(stats).toHaveProperty('timestamp');
    });

    test('should validate data integrity', async () => {
      const validation = await dbHelpers.validateDataIntegrity();
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('issues');
      expect(validation).toHaveProperty('checkedAt');
      expect(typeof validation.isValid).toBe('boolean');
      expect(Array.isArray(validation.issues)).toBe(true);
    });
  });

  describe('Database Operations', () => {
    test('should perform basic CRUD operations on users', async () => {
      // Create
      const user = await prisma.user.create({
        data: {
          username: 'testuser',
          email: '<EMAIL>',
          passwordHash: 'hashedpassword',
          fullName: 'Test User',
          role: 'VIEWER',
        },
      });

      expect(user).toHaveProperty('id');
      expect(user.username).toBe('testuser');

      // Read
      const foundUser = await prisma.user.findUnique({
        where: { id: user.id },
      });

      expect(foundUser).not.toBeNull();
      expect(foundUser?.username).toBe('testuser');

      // Update
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { fullName: 'Updated Test User' },
      });

      expect(updatedUser.fullName).toBe('Updated Test User');

      // Delete
      await prisma.user.delete({
        where: { id: user.id },
      });

      const deletedUser = await prisma.user.findUnique({
        where: { id: user.id },
      });

      expect(deletedUser).toBeNull();
    });

    test('should handle foreign key relationships', async () => {
      // 建立測試資料
      const category = await prisma.productCategory.create({
        data: {
          name: 'Test Category',
          description: 'Test Description',
        },
      });

      const product = await prisma.product.create({
        data: {
          categoryId: category.id,
          model: 'Test Model',
          brand: 'Test Brand',
          description: 'Test Product',
        },
      });

      // 驗證關聯
      const productWithCategory = await prisma.product.findUnique({
        where: { id: product.id },
        include: { category: true },
      });

      expect(productWithCategory?.category?.name).toBe('Test Category');

      // 清理測試資料
      await prisma.product.delete({ where: { id: product.id } });
      await prisma.productCategory.delete({ where: { id: category.id } });
    });

    test('should handle transactions', async () => {
      const result = await prisma.$transaction(async (tx) => {
        const category = await tx.productCategory.create({
          data: {
            name: 'Transaction Test Category',
            description: 'Test',
          },
        });

        const product = await tx.product.create({
          data: {
            categoryId: category.id,
            model: 'Transaction Test Model',
            brand: 'Test',
          },
        });

        return { category, product };
      });

      expect(result.category).toHaveProperty('id');
      expect(result.product).toHaveProperty('id');
      expect(result.product.categoryId).toBe(result.category.id);

      // 清理
      await prisma.product.delete({ where: { id: result.product.id } });
      await prisma.productCategory.delete({ where: { id: result.category.id } });
    });
  });

  describe('Database Constraints', () => {
    test('should enforce unique constraints', async () => {
      const userData = {
        username: 'uniquetest',
        email: '<EMAIL>',
        passwordHash: 'hash',
        fullName: 'Unique Test',
        role: 'VIEWER' as const,
      };

      // 建立第一個用戶
      const user1 = await prisma.user.create({ data: userData });

      // 嘗試建立相同用戶名的用戶，應該失敗
      await expect(
        prisma.user.create({ data: userData })
      ).rejects.toThrow();

      // 清理
      await prisma.user.delete({ where: { id: user1.id } });
    });

    test('should enforce required fields', async () => {
      // 嘗試建立缺少必要欄位的記錄
      await expect(
        prisma.user.create({
          data: {
            username: 'incomplete',
            // 缺少 email, passwordHash, fullName, role
          } as any,
        })
      ).rejects.toThrow();
    });
  });
});
