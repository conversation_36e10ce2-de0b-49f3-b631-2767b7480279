import React from 'react';
import { Card, Typography, Button, Space, Row, Col } from 'antd';
import { UserOutlined, ToolOutlined, TeamOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const SimpleDashboard: React.FC = () => {
  const handleLogout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    window.location.href = '/login';
  };

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={1}>🎉 React 版本登入成功！</Title>
          <Text type="secondary">歡迎使用 IACT MIO維保管理系統</Text>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <ToolOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                <Title level={3}>維修記錄</Title>
                <Text>管理維修記錄和進度追蹤</Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <TeamOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
                <Title level={3}>客戶管理</Title>
                <Text>管理客戶資料和聯絡資訊</Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <UserOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
                <Title level={3}>系統管理</Title>
                <Text>系統設定和用戶管理</Text>
              </div>
            </Card>
          </Col>
        </Row>

        <Card style={{ marginTop: '24px', textAlign: 'center' }}>
          <Title level={2}>🚀 系統狀態</Title>
          <Space direction="vertical" size="large">
            <div>
              <Text strong>✅ 前端服務：正常運行</Text>
            </div>
            <div>
              <Text strong>⚠️ 後端服務：模擬模式</Text>
            </div>
            <div>
              <Text strong>💾 數據存儲：本地模式</Text>
            </div>
            <div style={{ marginTop: '24px' }}>
              <Space>
                <Button type="primary" size="large">
                  開始使用系統
                </Button>
                <Button size="large" onClick={handleLogout}>
                  登出
                </Button>
              </Space>
            </div>
          </Space>
        </Card>
      </div>
    </div>
  );
};

export default SimpleDashboard;
