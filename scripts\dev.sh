#!/bin/bash

# 客退維修品記錄管理系統 - 開發服務器啟動腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $port 已被占用 ($service)"
        return 1
    fi
    return 0
}

# 啟動前端開發服務器
start_frontend() {
    log_info "啟動前端開發服務器..."
    
    if ! check_port 3000 "前端"; then
        log_error "前端端口 3000 被占用，請先關閉占用該端口的程序"
        return 1
    fi
    
    cd frontend
    
    if [ ! -f ".env" ]; then
        log_warning "前端 .env 文件不存在，從 .env.example 複製..."
        cp .env.example .env
    fi
    
    if [ ! -d "node_modules" ]; then
        log_info "安裝前端依賴..."
        npm install
    fi
    
    log_success "前端開發服務器啟動中... (http://localhost:3000)"
    npm run dev &
    FRONTEND_PID=$!
    cd ..
}

# 啟動後端開發服務器
start_backend() {
    log_info "啟動後端開發服務器..."
    
    if ! check_port 5000 "後端"; then
        log_error "後端端口 5000 被占用，請先關閉占用該端口的程序"
        return 1
    fi
    
    cd backend
    
    if [ ! -f ".env" ]; then
        log_warning "後端 .env 文件不存在，從 .env.example 複製..."
        cp .env.example .env
    fi
    
    if [ ! -d "node_modules" ]; then
        log_info "安裝後端依賴..."
        npm install
    fi
    
    # 檢查 Prisma 客戶端
    if [ ! -d "node_modules/.prisma" ]; then
        log_info "生成 Prisma 客戶端..."
        npx prisma generate
    fi
    
    log_success "後端開發服務器啟動中... (http://localhost:5000)"
    npm run dev &
    BACKEND_PID=$!
    cd ..
}

# 檢查資料庫連接
check_database() {
    log_info "檢查資料庫連接..."
    
    cd backend
    if npx prisma db pull --force 2>/dev/null; then
        log_success "資料庫連接正常"
    else
        log_warning "資料庫連接失敗，請檢查 DATABASE_URL 配置"
        log_info "可以使用 'docker-compose up -d mysql' 啟動本地資料庫"
    fi
    cd ..
}

# 清理函數
cleanup() {
    log_info "正在關閉開發服務器..."
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    # 殺死所有相關進程
    pkill -f "vite" 2>/dev/null || true
    pkill -f "tsx watch" 2>/dev/null || true
    
    log_success "開發服務器已關閉"
    exit 0
}

# 設置信號處理
trap cleanup SIGINT SIGTERM

# 顯示幫助信息
show_help() {
    echo "客退維修品記錄管理系統 - 開發服務器啟動腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  -f, --frontend-only    僅啟動前端服務器"
    echo "  -b, --backend-only     僅啟動後端服務器"
    echo "  -h, --help            顯示此幫助信息"
    echo ""
    echo "默認行為: 同時啟動前端和後端服務器"
}

# 主要函數
main() {
    local frontend_only=false
    local backend_only=false
    
    # 解析命令行參數
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--frontend-only)
                frontend_only=true
                shift
                ;;
            -b|--backend-only)
                backend_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知選項: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "🚀 啟動客退維修品記錄管理系統開發環境"
    echo "========================================"
    
    # 檢查資料庫
    if [ "$frontend_only" = false ]; then
        check_database
    fi
    
    # 啟動服務
    if [ "$backend_only" = false ]; then
        start_frontend
    fi
    
    if [ "$frontend_only" = false ]; then
        start_backend
    fi
    
    echo ""
    log_success "🎉 開發環境啟動完成！"
    echo ""
    echo "📋 可用服務:"
    
    if [ "$backend_only" = false ]; then
        echo "   🌐 前端應用: http://localhost:3000"
    fi
    
    if [ "$frontend_only" = false ]; then
        echo "   🔧 後端API: http://localhost:5000"
        echo "   📚 API文檔: http://localhost:5000/api-docs"
        echo "   🏥 健康檢查: http://localhost:5000/health"
    fi
    
    echo ""
    echo "💡 提示:"
    echo "   - 按 Ctrl+C 停止所有服務"
    echo "   - 修改代碼會自動重新載入"
    echo "   - 查看日誌以獲取詳細信息"
    echo ""
    
    # 等待用戶中斷
    log_info "開發服務器運行中... 按 Ctrl+C 停止"
    wait
}

# 執行主函數
main "$@"
