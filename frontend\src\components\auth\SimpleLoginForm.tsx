import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Al<PERSON>, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const SimpleLoginForm: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: LoginFormData) => {
    setLoading(true);
    setError(null);
    
    console.log('🔐 React 登入嘗試:', values.email);

    // 模擬登入模式
    if (values.email === '<EMAIL>' && values.password === 'admin123') {
      console.log('🎯 使用模擬登入模式');
      
      // 模擬用戶資料
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        username: 'admin',
        fullName: '系統管理員',
        role: 'ADMIN' as const,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 模擬 token
      const mockToken = 'mock-jwt-token-' + Date.now();
      
      // 儲存到 localStorage
      localStorage.setItem('authToken', mockToken);
      localStorage.setItem('userInfo', JSON.stringify(mockUser));
      
      // 延遲一下模擬真實登入
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1000);
      return;
    } else {
      setError('帳號或密碼錯誤，請使用 <EMAIL> / admin123');
      setLoading(false);
      return;
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>
            系統登入
          </Title>
          <Text type="secondary">IACT MIO維保管理系統</Text>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="電子郵件"
            rules={[
              { required: true, message: '請輸入電子郵件' },
              { type: 'email', message: '請輸入有效的電子郵件格式' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="<EMAIL>"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密碼"
            rules={[
              { required: true, message: '請輸入密碼' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="admin123"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              icon={<LoginOutlined />}
              style={{ height: '48px', fontSize: '16px' }}
            >
              {loading ? '登入中...' : '登入'}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Space direction="vertical" size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              測試帳號：<EMAIL>
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              測試密碼：admin123
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SimpleLoginForm;
