import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Typography, 
  Modal, 
  message, 
  Popconfirm,
  Select,
  Row,
  Col,
  Tooltip,
  Badge
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ShoppingOutlined,
  BrandOutlined,
  TagOutlined,
  ReloadOutlined,
  ExportOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Product, ProductQueryParams, ProductCategory } from '../../services/productService';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 模擬數據
const mockProducts: Product[] = [
  {
    id: 1,
    name: 'iPhone 14',
    model: 'A2882',
    brand: 'Apple',
    categoryId: 1,
    category: { id: 1, name: '智慧型手機', description: '', isActive: true, createdAt: '', updatedAt: '' },
    description: 'Apple iPhone 14 智慧型手機',
    specifications: '6.1吋螢幕, A15晶片, 128GB儲存空間',
    warrantyPeriod: 12,
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    name: 'MacBook Pro',
    model: 'MBP-M2-13',
    brand: 'Apple',
    categoryId: 2,
    category: { id: 2, name: '筆記型電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
    description: 'MacBook Pro 13吋 M2晶片',
    specifications: '13.3吋螢幕, M2晶片, 256GB SSD',
    warrantyPeriod: 12,
    isActive: true,
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-14T14:20:00Z',
  },
  {
    id: 3,
    name: 'iPad Air',
    model: 'IPAD-AIR-5',
    brand: 'Apple',
    categoryId: 3,
    category: { id: 3, name: '平板電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
    description: 'iPad Air 第5代',
    specifications: '10.9吋螢幕, M1晶片, 64GB儲存空間',
    warrantyPeriod: 12,
    isActive: false,
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z',
  },
];

const mockCategories: ProductCategory[] = [
  { id: 1, name: '智慧型手機', description: '', isActive: true, createdAt: '', updatedAt: '' },
  { id: 2, name: '筆記型電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
  { id: 3, name: '平板電腦', description: '', isActive: true, createdAt: '', updatedAt: '' },
];

interface ProductListProps {
  onEdit?: (product: Product) => void;
  onAdd?: () => void;
  onManageCategories?: () => void;
}

const ProductList: React.FC<ProductListProps> = ({ onEdit, onAdd, onManageCategories }) => {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [categories, setCategories] = useState<ProductCategory[]>(mockCategories);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<number | undefined>(undefined);
  const [brandFilter, setBrandFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockProducts.length,
  });

  // 獲取品牌列表
  const brands = Array.from(new Set(mockProducts.map(p => p.brand)));

  const fetchProducts = async (params?: ProductQueryParams) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredData = [...mockProducts];
      
      if (params?.search) {
        filteredData = filteredData.filter(product =>
          product.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          product.model.toLowerCase().includes(params.search!.toLowerCase()) ||
          product.brand.toLowerCase().includes(params.search!.toLowerCase())
        );
      }
      
      if (params?.categoryId) {
        filteredData = filteredData.filter(product => product.categoryId === params.categoryId);
      }
      
      if (params?.brand) {
        filteredData = filteredData.filter(product => product.brand === params.brand);
      }
      
      if (params?.isActive !== undefined) {
        filteredData = filteredData.filter(product => product.isActive === params.isActive);
      }
      
      setProducts(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length,
      }));
    } catch (error) {
      message.error('載入產品資料失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts({
      search: searchText,
      categoryId: categoryFilter,
      brand: brandFilter,
      isActive: statusFilter,
      page: pagination.current,
      limit: pagination.pageSize,
    });
  }, [searchText, categoryFilter, brandFilter, statusFilter, pagination.current, pagination.pageSize]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('產品刪除成功');
      fetchProducts();
    } catch (error) {
      message.error('刪除產品失敗');
    }
  };

  const handleToggleStatus = async (product: Product) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`產品${product.isActive ? '停用' : '激活'}成功`);
      fetchProducts();
    } catch (error) {
      message.error(`${product.isActive ? '停用' : '激活'}產品失敗`);
    }
  };

  const columns: ColumnsType<Product> = [
    {
      title: '產品資訊',
      key: 'productInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            <ShoppingOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {record.name}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            型號：{record.model}
          </div>
        </div>
      ),
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      key: 'brand',
      width: 100,
      render: (brand) => (
        <Space>
          <BrandOutlined />
          <span>{brand}</span>
        </Space>
      ),
    },
    {
      title: '分類',
      key: 'category',
      width: 120,
      render: (_, record) => (
        <Tag color="blue" icon={<TagOutlined />}>
          {record.category?.name || '未分類'}
        </Tag>
      ),
    },
    {
      title: '規格',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (specifications) => (
        <Tooltip placement="topLeft" title={specifications}>
          <span style={{ fontSize: '12px' }}>{specifications || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '保固期',
      dataIndex: 'warrantyPeriod',
      key: 'warrantyPeriod',
      width: 80,
      render: (period) => `${period}個月`,
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '正常' : '停用'} 
        />
      ),
    },
    {
      title: '建立時間',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString('zh-TW'),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '停用' : '激活'}>
            <Button
              type="text"
              onClick={() => handleToggleStatus(record)}
              style={{ color: record.isActive ? '#faad14' : '#52c41a' }}
            >
              {record.isActive ? '停用' : '激活'}
            </Button>
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個產品嗎？"
            description="刪除後將無法恢復，請謹慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="確定"
            cancelText="取消"
          >
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              產品管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Search
                placeholder="搜尋產品名稱、型號或品牌"
                allowClear
                style={{ width: 250 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="分類篩選"
                allowClear
                style={{ width: 120 }}
                onChange={setCategoryFilter}
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="品牌篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setBrandFilter}
              >
                {brands.map(brand => (
                  <Option key={brand} value={brand}>
                    {brand}
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="狀態篩選"
                allowClear
                style={{ width: 100 }}
                onChange={setStatusFilter}
              >
                <Option value={true}>正常</Option>
                <Option value={false}>停用</Option>
              </Select>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchProducts()}
                loading={loading}
              >
                重新整理
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('匯出功能開發中')}
              >
                匯出
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={onManageCategories}
              >
                分類管理
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                新增產品
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Table
        columns={columns}
        dataSource={products}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        scroll={{ x: 1200 }}
        size="small"
      />
    </Card>
  );
};

export default ProductList;
