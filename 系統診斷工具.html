<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IACT MIO維保管理系統 - 診斷工具</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .diagnostic-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .diagnostic-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-ok {
            color: #52c41a;
            font-weight: bold;
        }
        
        .status-error {
            color: #f5222d;
            font-weight: bold;
        }
        
        .status-warning {
            color: #fa8c16;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);
        }
        
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 系統診斷工具</h1>
            <p>IACT MIO維保管理系統 - 問題診斷和修復</p>
        </div>
        
        <div class="content">
            <div class="diagnostic-section">
                <h3>🌐 瀏覽器相容性檢查</h3>
                <div class="status-item">
                    <span>瀏覽器版本</span>
                    <span id="browserInfo">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>LocalStorage 支援</span>
                    <span id="localStorageSupport">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>JavaScript 支援</span>
                    <span id="jsSupport">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>螢幕解析度</span>
                    <span id="screenResolution">檢查中...</span>
                </div>
            </div>
            
            <div class="diagnostic-section">
                <h3>📁 檔案完整性檢查</h3>
                <div class="status-item">
                    <span>主程式檔案</span>
                    <span id="mainFileStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>系統功能</span>
                    <span id="systemFunctions">檢查中...</span>
                </div>
            </div>
            
            <div class="diagnostic-section">
                <h3>💾 數據存儲檢查</h3>
                <div class="status-item">
                    <span>LocalStorage 可用空間</span>
                    <span id="storageSpace">檢查中...</span>
                </div>
                <div class="status-item">
                    <span>已保存的數據</span>
                    <span id="savedData">檢查中...</span>
                </div>
            </div>
            
            <div class="diagnostic-section">
                <h3>📊 診斷日誌</h3>
                <div class="log-area" id="diagnosticLog">
                    正在執行系統診斷...\n
                </div>
            </div>
            
            <div class="actions">
                <button class="btn" onclick="runFullDiagnostic()">🔍 執行完整診斷</button>
                <button class="btn btn-success" onclick="openMainSystem()">🚀 開啟主系統</button>
                <button class="btn btn-danger" onclick="clearAllData()">🗑️ 清除所有數據</button>
            </div>
        </div>
    </div>

    <script>
        let logArea;
        
        function log(message) {
            if (!logArea) {
                logArea = document.getElementById('diagnosticLog');
            }
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = status === 'ok' ? 'status-ok' : 
                               status === 'error' ? 'status-error' : 'status-warning';
        }
        
        function checkBrowserCompatibility() {
            log('檢查瀏覽器相容性...');
            
            // 瀏覽器資訊
            const userAgent = navigator.userAgent;
            let browserName = 'Unknown';
            if (userAgent.includes('Chrome')) browserName = 'Chrome';
            else if (userAgent.includes('Firefox')) browserName = 'Firefox';
            else if (userAgent.includes('Safari')) browserName = 'Safari';
            else if (userAgent.includes('Edge')) browserName = 'Edge';
            
            setStatus('browserInfo', 'ok', browserName);
            log(`瀏覽器: ${browserName}`);
            
            // LocalStorage 支援
            const hasLocalStorage = typeof(Storage) !== "undefined";
            setStatus('localStorageSupport', hasLocalStorage ? 'ok' : 'error', 
                     hasLocalStorage ? '✅ 支援' : '❌ 不支援');
            log(`LocalStorage 支援: ${hasLocalStorage ? '是' : '否'}`);
            
            // JavaScript 支援
            setStatus('jsSupport', 'ok', '✅ 支援');
            log('JavaScript 支援: 是');
            
            // 螢幕解析度
            const resolution = `${screen.width}x${screen.height}`;
            setStatus('screenResolution', 'ok', resolution);
            log(`螢幕解析度: ${resolution}`);
        }
        
        function checkFileIntegrity() {
            log('檢查檔案完整性...');
            
            // 檢查主程式檔案是否存在
            fetch('complete-system-gui.html')
                .then(response => {
                    if (response.ok) {
                        setStatus('mainFileStatus', 'ok', '✅ 檔案存在');
                        log('主程式檔案: 存在');
                        return response.text();
                    } else {
                        throw new Error('檔案不存在');
                    }
                })
                .then(content => {
                    // 檢查檔案內容
                    if (content.includes('IACT MIO維保管理系統')) {
                        setStatus('systemFunctions', 'ok', '✅ 功能完整');
                        log('系統功能: 完整');
                    } else {
                        setStatus('systemFunctions', 'error', '❌ 功能不完整');
                        log('系統功能: 不完整');
                    }
                })
                .catch(error => {
                    setStatus('mainFileStatus', 'error', '❌ 檔案遺失');
                    setStatus('systemFunctions', 'error', '❌ 無法檢查');
                    log(`檔案檢查失敗: ${error.message}`);
                });
        }
        
        function checkDataStorage() {
            log('檢查數據存儲...');
            
            try {
                // 檢查 LocalStorage 可用空間
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                
                const sizeKB = (totalSize / 1024).toFixed(2);
                setStatus('storageSpace', 'ok', `${sizeKB} KB 已使用`);
                log(`LocalStorage 使用空間: ${sizeKB} KB`);
                
                // 檢查已保存的數據
                let dataCount = 0;
                const maintenanceKeys = [];
                for (let key in localStorage) {
                    if (key.startsWith('maintenance_')) {
                        maintenanceKeys.push(key);
                        dataCount++;
                    }
                }
                
                if (dataCount > 0) {
                    setStatus('savedData', 'ok', `✅ ${dataCount} 個數據集`);
                    log(`已保存數據: ${dataCount} 個數據集`);
                    maintenanceKeys.forEach(key => {
                        log(`  - ${key}`);
                    });
                } else {
                    setStatus('savedData', 'warning', '⚠️ 無已保存數據');
                    log('已保存數據: 無');
                }
                
            } catch (error) {
                setStatus('storageSpace', 'error', '❌ 檢查失敗');
                setStatus('savedData', 'error', '❌ 檢查失敗');
                log(`數據存儲檢查失敗: ${error.message}`);
            }
        }
        
        function runFullDiagnostic() {
            log('=== 開始完整系統診斷 ===');
            
            // 清除之前的結果
            document.getElementById('diagnosticLog').textContent = '';
            
            checkBrowserCompatibility();
            checkFileIntegrity();
            checkDataStorage();
            
            log('=== 診斷完成 ===');
            log('');
            log('如果發現問題，請嘗試以下解決方案:');
            log('1. 重新整理頁面 (F5)');
            log('2. 清除瀏覽器快取');
            log('3. 使用不同的瀏覽器');
            log('4. 重新下載系統檔案');
        }
        
        function openMainSystem() {
            log('開啟主系統...');
            window.open('complete-system-gui.html', '_blank');
        }
        
        function clearAllData() {
            if (confirm('⚠️ 警告：這會清除所有已保存的數據！\n\n確定要繼續嗎？')) {
                try {
                    const keysToRemove = [];
                    for (let key in localStorage) {
                        if (key.startsWith('maintenance_') || key.startsWith('sharepoint') || key === 'storageMode') {
                            keysToRemove.push(key);
                        }
                    }
                    
                    keysToRemove.forEach(key => {
                        localStorage.removeItem(key);
                    });
                    
                    log(`已清除 ${keysToRemove.length} 個數據項目`);
                    alert('✅ 所有數據已清除！\n\n系統將重新使用預設數據。');
                    
                    // 重新檢查數據存儲
                    checkDataStorage();
                    
                } catch (error) {
                    log(`清除數據失敗: ${error.message}`);
                    alert('❌ 清除數據失敗！\n\n請手動清除瀏覽器數據。');
                }
            }
        }
        
        // 頁面載入時自動執行診斷
        window.addEventListener('load', function() {
            setTimeout(runFullDiagnostic, 500);
        });
    </script>
</body>
</html>
