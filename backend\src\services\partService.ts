import { PartRepository } from '../repositories/partRepository';
import { logger } from '../utils/logger';
import { 
  PartInfo, 
  CreatePartRequest, 
  UpdatePartRequest, 
  PartQueryParams,
  PartListResponse,
  PartStatistics,
  PartSearchResult,
  PartDetailInfo,
  StockOperationRequest,
  StockOperationResult,
  BatchStockOperationRequest,
  BatchStockOperationResult,
  PartValidationResult,
  StockAlert,
  PART_VALIDATION_RULES
} from '../types/part';

export class PartService {
  private partRepository: PartRepository;

  constructor(partRepository: PartRepository) {
    this.partRepository = partRepository;
  }

  // 根據ID獲取零件
  async getPartById(id: string): Promise<PartInfo | null> {
    try {
      return await this.partRepository.findById(id);
    } catch (error) {
      logger.error('獲取零件失敗:', error);
      throw new Error('獲取零件失敗');
    }
  }

  // 根據ID獲取零件詳細資訊
  async getPartDetailById(id: string): Promise<PartDetailInfo | null> {
    try {
      return await this.partRepository.findByIdWithDetails(id);
    } catch (error) {
      logger.error('獲取零件詳細資訊失敗:', error);
      throw new Error('獲取零件詳細資訊失敗');
    }
  }

  // 創建零件
  async createPart(partData: CreatePartRequest, createdBy?: string): Promise<PartInfo> {
    try {
      // 驗證零件資料
      const validation = await this.validatePartData(partData);
      if (!validation.isValid) {
        throw new Error(`零件資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查零件編號是否已存在
      const partNumberExists = await this.partRepository.checkPartNumberExists(partData.partNumber);
      if (partNumberExists) {
        throw new Error('零件編號已存在');
      }

      // 創建零件
      const newPart = await this.partRepository.create(partData);

      logger.info('零件創建成功:', { 
        partId: newPart.id, 
        name: newPart.name,
        partNumber: newPart.partNumber,
        createdBy 
      });

      return newPart;
    } catch (error) {
      logger.error('創建零件失敗:', error);
      throw error;
    }
  }

  // 更新零件
  async updatePart(id: string, partData: UpdatePartRequest, updatedBy?: string): Promise<PartInfo> {
    try {
      // 檢查零件是否存在
      const existingPart = await this.partRepository.findById(id);
      if (!existingPart) {
        throw new Error('零件不存在');
      }

      // 驗證更新資料
      const validation = await this.validateUpdateData(partData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查零件編號是否已存在（排除當前零件）
      if (partData.partNumber && partData.partNumber !== existingPart.partNumber) {
        const partNumberExists = await this.partRepository.checkPartNumberExists(partData.partNumber, id);
        if (partNumberExists) {
          throw new Error('零件編號已存在');
        }
      }

      // 更新零件
      const updatedPart = await this.partRepository.update(id, partData);

      logger.info('零件更新成功:', { 
        partId: id, 
        name: updatedPart.name,
        updatedBy,
        changes: Object.keys(partData)
      });

      return updatedPart;
    } catch (error) {
      logger.error('更新零件失敗:', error);
      throw error;
    }
  }

  // 刪除零件
  async deletePart(id: string, deletedBy?: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查零件是否存在
      const existingPart = await this.partRepository.findById(id);
      if (!existingPart) {
        throw new Error('零件不存在');
      }

      // 檢查是否有關聯的使用記錄
      const partDetail = await this.partRepository.findByIdWithDetails(id);
      if (partDetail && partDetail.statistics.totalUsage > 0 && hardDelete) {
        throw new Error('零件有使用記錄，無法永久刪除');
      }

      // 檢查是否有庫存
      if (existingPart.currentStock > 0 && hardDelete) {
        throw new Error('零件有庫存，無法永久刪除');
      }

      if (hardDelete) {
        await this.partRepository.hardDelete(id);
        logger.info('零件硬刪除成功:', { partId: id, deletedBy });
      } else {
        await this.partRepository.softDelete(id);
        logger.info('零件軟刪除成功:', { partId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除零件失敗:', error);
      throw error;
    }
  }

  // 獲取零件列表
  async getPartList(params: PartQueryParams): Promise<PartListResponse> {
    try {
      return await this.partRepository.findMany(params);
    } catch (error) {
      logger.error('獲取零件列表失敗:', error);
      throw new Error('獲取零件列表失敗');
    }
  }

  // 搜尋零件
  async searchParts(query: string, limit: number = 10): Promise<PartSearchResult[]> {
    try {
      if (query.length < 2) {
        throw new Error('搜尋關鍵字至少需要2個字符');
      }

      return await this.partRepository.search(query, limit);
    } catch (error) {
      logger.error('搜尋零件失敗:', error);
      throw error;
    }
  }

  // 獲取零件統計
  async getPartStatistics(): Promise<PartStatistics> {
    try {
      return await this.partRepository.getStatistics();
    } catch (error) {
      logger.error('獲取零件統計失敗:', error);
      throw new Error('獲取零件統計失敗');
    }
  }

  // 激活零件
  async activatePart(id: string, activatedBy?: string): Promise<PartInfo> {
    try {
      return await this.updatePart(id, { isActive: true }, activatedBy);
    } catch (error) {
      logger.error('激活零件失敗:', error);
      throw error;
    }
  }

  // 停用零件
  async deactivatePart(id: string, deactivatedBy?: string): Promise<PartInfo> {
    try {
      return await this.updatePart(id, { isActive: false }, deactivatedBy);
    } catch (error) {
      logger.error('停用零件失敗:', error);
      throw error;
    }
  }

  // 根據零件編號查找零件
  async findPartByPartNumber(partNumber: string): Promise<PartInfo | null> {
    try {
      const part = await this.partRepository.findByPartNumber(partNumber);
      if (!part) return null;

      return await this.partRepository.findById(part.id.toString());
    } catch (error) {
      logger.error('根據零件編號查找零件失敗:', error);
      throw error;
    }
  }

  // 檢查零件編號是否可用
  async checkPartNumberAvailability(partNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const exists = await this.partRepository.checkPartNumberExists(partNumber, excludeId);
      return !exists;
    } catch (error) {
      logger.error('檢查零件編號可用性失敗:', error);
      throw error;
    }
  }

  // === 庫存管理相關方法 ===

  // 執行庫存操作
  async performStockOperation(operation: StockOperationRequest, performedBy: string): Promise<StockOperationResult> {
    try {
      // 驗證庫存操作
      const validation = await this.validateStockOperation(operation);
      if (!validation.isValid) {
        throw new Error(`庫存操作驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查零件是否存在且活躍
      const part = await this.partRepository.findById(operation.partId);
      if (!part) {
        throw new Error('零件不存在');
      }

      if (!part.isActive) {
        throw new Error('零件已停用，無法進行庫存操作');
      }

      // 執行庫存操作
      const result = await this.partRepository.performStockOperation(operation, performedBy);

      logger.info('庫存操作成功:', { 
        partId: operation.partId,
        type: operation.type,
        quantity: operation.quantity,
        performedBy
      });

      return result;
    } catch (error) {
      logger.error('庫存操作失敗:', error);
      throw error;
    }
  }

  // 批量庫存操作
  async performBatchStockOperation(batchOperation: BatchStockOperationRequest, performedBy: string): Promise<BatchStockOperationResult> {
    try {
      // 驗證批量操作
      if (batchOperation.operations.length === 0) {
        throw new Error('未提供任何庫存操作');
      }

      if (batchOperation.operations.length > 100) {
        throw new Error('批量操作數量不能超過100個');
      }

      // 驗證每個操作
      for (const operation of batchOperation.operations) {
        const validation = await this.validateStockOperation(operation);
        if (!validation.isValid) {
          throw new Error(`庫存操作驗證失敗: ${validation.errors.join(', ')}`);
        }
      }

      const result = await this.partRepository.performBatchStockOperation(batchOperation, performedBy);

      logger.info('批量庫存操作完成:', { 
        total: batchOperation.operations.length,
        success: result.success,
        failed: result.failed,
        performedBy
      });

      return result;
    } catch (error) {
      logger.error('批量庫存操作失敗:', error);
      throw error;
    }
  }

  // 入庫操作
  async stockIn(partId: string, quantity: number, reason: string, performedBy: string, referenceId?: string): Promise<StockOperationResult> {
    return this.performStockOperation({
      partId,
      type: 'IN',
      quantity,
      reason,
      referenceId,
    }, performedBy);
  }

  // 出庫操作
  async stockOut(partId: string, quantity: number, reason: string, performedBy: string, referenceId?: string): Promise<StockOperationResult> {
    return this.performStockOperation({
      partId,
      type: 'OUT',
      quantity,
      reason,
      referenceId,
    }, performedBy);
  }

  // 庫存調整
  async adjustStock(partId: string, newQuantity: number, reason: string, performedBy: string): Promise<StockOperationResult> {
    return this.performStockOperation({
      partId,
      type: 'ADJUSTMENT',
      quantity: newQuantity,
      reason,
    }, performedBy);
  }

  // 預留庫存
  async reserveStock(partId: string, quantity: number, reason: string, performedBy: string, referenceId?: string): Promise<StockOperationResult> {
    return this.performStockOperation({
      partId,
      type: 'RESERVED',
      quantity,
      reason,
      referenceId,
    }, performedBy);
  }

  // 釋放預留庫存
  async releaseReservedStock(partId: string, quantity: number, reason: string, performedBy: string, referenceId?: string): Promise<StockOperationResult> {
    return this.performStockOperation({
      partId,
      type: 'RELEASED',
      quantity,
      reason,
      referenceId,
    }, performedBy);
  }

  // 獲取庫存警報
  async getStockAlerts(): Promise<StockAlert[]> {
    try {
      return await this.partRepository.getStockAlerts();
    } catch (error) {
      logger.error('獲取庫存警報失敗:', error);
      throw new Error('獲取庫存警報失敗');
    }
  }

  // 驗證零件資料
  private async validatePartData(partData: CreatePartRequest): Promise<PartValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證零件名稱
    if (!partData.name || partData.name.trim().length < PART_VALIDATION_RULES.name.minLength) {
      errors.push(`零件名稱至少需要${PART_VALIDATION_RULES.name.minLength}個字符`);
    }

    if (partData.name && partData.name.length > PART_VALIDATION_RULES.name.maxLength) {
      errors.push(`零件名稱不能超過${PART_VALIDATION_RULES.name.maxLength}個字符`);
    }

    // 驗證零件編號
    if (!partData.partNumber || partData.partNumber.trim().length < PART_VALIDATION_RULES.partNumber.minLength) {
      errors.push(`零件編號至少需要${PART_VALIDATION_RULES.partNumber.minLength}個字符`);
    }

    if (partData.partNumber && partData.partNumber.length > PART_VALIDATION_RULES.partNumber.maxLength) {
      errors.push(`零件編號不能超過${PART_VALIDATION_RULES.partNumber.maxLength}個字符`);
    }

    if (partData.partNumber && !PART_VALIDATION_RULES.partNumber.pattern.test(partData.partNumber)) {
      errors.push('零件編號只能包含字母、數字、連字符和底線');
    }

    // 驗證類別
    if (!partData.category || partData.category.trim().length < PART_VALIDATION_RULES.category.minLength) {
      errors.push(`類別至少需要${PART_VALIDATION_RULES.category.minLength}個字符`);
    }

    if (partData.category && partData.category.length > PART_VALIDATION_RULES.category.maxLength) {
      errors.push(`類別不能超過${PART_VALIDATION_RULES.category.maxLength}個字符`);
    }

    // 驗證描述
    if (partData.description && partData.description.length > PART_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PART_VALIDATION_RULES.description.maxLength}個字符`);
    }

    // 驗證品牌
    if (partData.brand && partData.brand.length > PART_VALIDATION_RULES.brand.maxLength) {
      errors.push(`品牌不能超過${PART_VALIDATION_RULES.brand.maxLength}個字符`);
    }

    // 驗證型號
    if (partData.model && partData.model.length > PART_VALIDATION_RULES.model.maxLength) {
      errors.push(`型號不能超過${PART_VALIDATION_RULES.model.maxLength}個字符`);
    }

    // 驗證規格
    if (partData.specifications && partData.specifications.length > PART_VALIDATION_RULES.specifications.maxLength) {
      errors.push(`規格不能超過${PART_VALIDATION_RULES.specifications.maxLength}個字符`);
    }

    // 驗證單價
    if (partData.unitPrice !== undefined) {
      if (partData.unitPrice < PART_VALIDATION_RULES.unitPrice.min) {
        errors.push(`單價不能小於${PART_VALIDATION_RULES.unitPrice.min}`);
      }
      if (partData.unitPrice > PART_VALIDATION_RULES.unitPrice.max) {
        errors.push(`單價不能超過${PART_VALIDATION_RULES.unitPrice.max}`);
      }
    }

    // 驗證幣別
    if (partData.currency && !PART_VALIDATION_RULES.currency.pattern.test(partData.currency)) {
      errors.push('幣別必須是3位大寫字母（如TWD、USD）');
    }

    // 驗證供應商
    if (partData.supplier && partData.supplier.length > PART_VALIDATION_RULES.supplier.maxLength) {
      errors.push(`供應商不能超過${PART_VALIDATION_RULES.supplier.maxLength}個字符`);
    }

    // 驗證供應商零件編號
    if (partData.supplierPartNumber && partData.supplierPartNumber.length > PART_VALIDATION_RULES.supplierPartNumber.maxLength) {
      errors.push(`供應商零件編號不能超過${PART_VALIDATION_RULES.supplierPartNumber.maxLength}個字符`);
    }

    // 驗證最小庫存
    if (partData.minimumStock !== undefined) {
      if (partData.minimumStock < PART_VALIDATION_RULES.minimumStock.min) {
        errors.push(`最小庫存不能小於${PART_VALIDATION_RULES.minimumStock.min}`);
      }
      if (partData.minimumStock > PART_VALIDATION_RULES.minimumStock.max) {
        errors.push(`最小庫存不能超過${PART_VALIDATION_RULES.minimumStock.max}`);
      }
    }

    // 驗證目前庫存
    if (partData.currentStock !== undefined) {
      if (partData.currentStock < PART_VALIDATION_RULES.currentStock.min) {
        errors.push(`目前庫存不能小於${PART_VALIDATION_RULES.currentStock.min}`);
      }
      if (partData.currentStock > PART_VALIDATION_RULES.currentStock.max) {
        errors.push(`目前庫存不能超過${PART_VALIDATION_RULES.currentStock.max}`);
      }
    }

    // 驗證位置
    if (partData.location && partData.location.length > PART_VALIDATION_RULES.location.maxLength) {
      errors.push(`位置不能超過${PART_VALIDATION_RULES.location.maxLength}個字符`);
    }

    // 警告：缺少單價
    if (partData.unitPrice === undefined) {
      warnings.push('建議設定零件單價');
    }

    // 警告：缺少最小庫存
    if (partData.minimumStock === undefined) {
      warnings.push('建議設定最小庫存量');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證更新資料
  private async validateUpdateData(partData: UpdatePartRequest): Promise<PartValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證零件名稱
    if (partData.name !== undefined) {
      if (partData.name.trim().length < PART_VALIDATION_RULES.name.minLength) {
        errors.push(`零件名稱至少需要${PART_VALIDATION_RULES.name.minLength}個字符`);
      }
      if (partData.name.length > PART_VALIDATION_RULES.name.maxLength) {
        errors.push(`零件名稱不能超過${PART_VALIDATION_RULES.name.maxLength}個字符`);
      }
    }

    // 驗證零件編號
    if (partData.partNumber !== undefined) {
      if (partData.partNumber.trim().length < PART_VALIDATION_RULES.partNumber.minLength) {
        errors.push(`零件編號至少需要${PART_VALIDATION_RULES.partNumber.minLength}個字符`);
      }
      if (partData.partNumber.length > PART_VALIDATION_RULES.partNumber.maxLength) {
        errors.push(`零件編號不能超過${PART_VALIDATION_RULES.partNumber.maxLength}個字符`);
      }
      if (!PART_VALIDATION_RULES.partNumber.pattern.test(partData.partNumber)) {
        errors.push('零件編號只能包含字母、數字、連字符和底線');
      }
    }

    // 驗證類別
    if (partData.category !== undefined) {
      if (partData.category.trim().length < PART_VALIDATION_RULES.category.minLength) {
        errors.push(`類別至少需要${PART_VALIDATION_RULES.category.minLength}個字符`);
      }
      if (partData.category.length > PART_VALIDATION_RULES.category.maxLength) {
        errors.push(`類別不能超過${PART_VALIDATION_RULES.category.maxLength}個字符`);
      }
    }

    // 其他欄位驗證...
    if (partData.description !== undefined && partData.description && partData.description.length > PART_VALIDATION_RULES.description.maxLength) {
      errors.push(`描述不能超過${PART_VALIDATION_RULES.description.maxLength}個字符`);
    }

    if (partData.unitPrice !== undefined) {
      if (partData.unitPrice < PART_VALIDATION_RULES.unitPrice.min) {
        errors.push(`單價不能小於${PART_VALIDATION_RULES.unitPrice.min}`);
      }
      if (partData.unitPrice > PART_VALIDATION_RULES.unitPrice.max) {
        errors.push(`單價不能超過${PART_VALIDATION_RULES.unitPrice.max}`);
      }
    }

    if (partData.currency !== undefined && partData.currency && !PART_VALIDATION_RULES.currency.pattern.test(partData.currency)) {
      errors.push('幣別必須是3位大寫字母（如TWD、USD）');
    }

    if (partData.minimumStock !== undefined) {
      if (partData.minimumStock < PART_VALIDATION_RULES.minimumStock.min) {
        errors.push(`最小庫存不能小於${PART_VALIDATION_RULES.minimumStock.min}`);
      }
      if (partData.minimumStock > PART_VALIDATION_RULES.minimumStock.max) {
        errors.push(`最小庫存不能超過${PART_VALIDATION_RULES.minimumStock.max}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證庫存操作
  private async validateStockOperation(operation: StockOperationRequest): Promise<PartValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證零件ID
    if (!operation.partId) {
      errors.push('零件ID不能為空');
    }

    // 驗證操作類型
    if (!operation.type) {
      errors.push('操作類型不能為空');
    }

    if (!['IN', 'OUT', 'ADJUSTMENT', 'RESERVED', 'RELEASED'].includes(operation.type)) {
      errors.push('無效的操作類型');
    }

    // 驗證數量
    if (operation.quantity === undefined || operation.quantity === null) {
      errors.push('數量不能為空');
    }

    if (operation.quantity <= 0 && operation.type !== 'ADJUSTMENT') {
      errors.push('數量必須大於0');
    }

    if (operation.type === 'ADJUSTMENT' && operation.quantity < 0) {
      errors.push('調整後的庫存數量不能小於0');
    }

    // 驗證原因
    if (!operation.reason || operation.reason.trim().length === 0) {
      errors.push('操作原因不能為空');
    }

    if (operation.reason && operation.reason.length > 500) {
      errors.push('操作原因不能超過500個字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
