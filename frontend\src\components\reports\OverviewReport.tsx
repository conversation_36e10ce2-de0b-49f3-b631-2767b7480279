import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Progress,
  Tag,
  Space,
  Divider
} from 'antd';
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  ToolOutlined,
  UserOutlined,
  ShoppingOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import { DashboardStatistics, StatisticsQueryParams } from '../../services/statisticsService';

const { Title, Text } = Typography;

interface OverviewReportProps {
  filters: StatisticsQueryParams;
}

// 模擬數據
const mockDashboardStats: DashboardStatistics = {
  totalRepairs: 1248,
  activeRepairs: 89,
  completedRepairs: 1159,
  totalCustomers: 456,
  totalProducts: 234,
  totalParts: 1567,
  lowStockParts: 23,
  totalRevenue: 2456789,
  averageRepairTime: 3.2,
  customerSatisfaction: 4.6,
};

const mockTrendData = [
  { date: '01/01', newRepairs: 12, completedRepairs: 8, revenue: 25600 },
  { date: '01/02', newRepairs: 15, completedRepairs: 12, revenue: 32400 },
  { date: '01/03', newRepairs: 8, completedRepairs: 14, revenue: 28900 },
  { date: '01/04', newRepairs: 18, completedRepairs: 10, revenue: 35200 },
  { date: '01/05', newRepairs: 22, completedRepairs: 16, revenue: 42100 },
  { date: '01/06', newRepairs: 14, completedRepairs: 20, revenue: 38700 },
  { date: '01/07', newRepairs: 16, completedRepairs: 18, revenue: 41300 },
];

const mockStatusData = [
  { name: '已完成', value: 65, color: '#52c41a' },
  { name: '維修中', value: 15, color: '#1890ff' },
  { name: '待檢測', value: 12, color: '#faad14' },
  { name: '待零件', value: 5, color: '#f5222d' },
  { name: '其他', value: 3, color: '#722ed1' },
];

const mockPriorityData = [
  { priority: '低', count: 45, color: '#52c41a' },
  { priority: '中', count: 32, color: '#1890ff' },
  { priority: '高', count: 18, color: '#faad14' },
  { priority: '緊急', count: 8, color: '#f5222d' },
];

const OverviewReport: React.FC<OverviewReportProps> = ({ filters }) => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStatistics>(mockDashboardStats);

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 這裡會調用 statisticsService.getDashboardStatistics()
      await new Promise(resolve => setTimeout(resolve, 500));
      setStats(mockDashboardStats);
    } catch (error) {
      console.error('Failed to fetch dashboard statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const completionRate = ((stats.completedRepairs / stats.totalRepairs) * 100).toFixed(1);
  const activeRate = ((stats.activeRepairs / stats.totalRepairs) * 100).toFixed(1);

  return (
    <div>
      {/* 核心指標卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總維修記錄"
              value={stats.totalRepairs}
              prefix={<ToolOutlined />}
              suffix={
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  +12%
                </Tag>
              }
            />
            <Progress 
              percent={Number(completionRate)} 
              size="small" 
              status="active"
              format={() => `完成率 ${completionRate}%`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="進行中維修"
              value={stats.activeRepairs}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <Tag color="orange" style={{ marginLeft: 8 }}>
                  {activeRate}%
                </Tag>
              }
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">平均維修時間: {stats.averageRepairTime}天</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="總營收"
              value={stats.totalRevenue}
              prefix={<DollarOutlined />}
              precision={0}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <Tag color="green" style={{ marginLeft: 8 }}>
                  <ArrowUpOutlined /> 8.5%
                </Tag>
              }
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">本月目標達成率: 95%</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="客戶滿意度"
              value={stats.customerSatisfaction}
              prefix={<TrophyOutlined />}
              precision={1}
              suffix="/ 5.0"
              valueStyle={{ color: '#fa8c16' }}
            />
            <Progress 
              percent={(stats.customerSatisfaction / 5) * 100} 
              size="small" 
              strokeColor="#fa8c16"
              format={() => '優秀'}
            />
          </Card>
        </Col>
      </Row>

      {/* 次要指標 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="客戶總數"
              value={stats.totalCustomers}
              prefix={<UserOutlined />}
              suffix={<Tag color="blue">+5</Tag>}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="產品總數"
              value={stats.totalProducts}
              prefix={<ShoppingOutlined />}
              suffix={<Tag color="green">+2</Tag>}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="零件總數"
              value={stats.totalParts}
              prefix={<ToolOutlined />}
              suffix={
                <Tag color="red">
                  <ExclamationCircleOutlined /> {stats.lowStockParts}缺貨
                </Tag>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 圖表區域 */}
      <Row gutter={[16, 16]}>
        {/* 維修趨勢圖 */}
        <Col xs={24} lg={16}>
          <Card title="維修趨勢分析" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={mockTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'revenue' ? `$${Number(value).toLocaleString()}` : value,
                    name === 'newRepairs' ? '新增維修' : 
                    name === 'completedRepairs' ? '完成維修' : '營收'
                  ]}
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="newRepairs" 
                  stackId="1" 
                  stroke="#1890ff" 
                  fill="#1890ff" 
                  fillOpacity={0.6}
                  name="新增維修"
                />
                <Area 
                  type="monotone" 
                  dataKey="completedRepairs" 
                  stackId="1" 
                  stroke="#52c41a" 
                  fill="#52c41a" 
                  fillOpacity={0.6}
                  name="完成維修"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 狀態分布圓餅圖 */}
        <Col xs={24} lg={8}>
          <Card title="維修狀態分布" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockStatusData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {mockStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}%`, '佔比']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 優先級分布 */}
        <Col xs={24} lg={12}>
          <Card title="優先級分布" loading={loading}>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={mockPriorityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="priority" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#1890ff">
                  {mockPriorityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 關鍵指標 */}
        <Col xs={24} lg={12}>
          <Card title="關鍵績效指標" loading={loading}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>首次修復成功率</Text>
                  <Text strong>92%</Text>
                </div>
                <Progress percent={92} strokeColor="#52c41a" />
              </div>
              
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>準時交付率</Text>
                  <Text strong>88%</Text>
                </div>
                <Progress percent={88} strokeColor="#1890ff" />
              </div>
              
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>客戶回頭率</Text>
                  <Text strong>76%</Text>
                </div>
                <Progress percent={76} strokeColor="#fa8c16" />
              </div>
              
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>技師效率</Text>
                  <Text strong>85%</Text>
                </div>
                <Progress percent={85} strokeColor="#722ed1" />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OverviewReport;
