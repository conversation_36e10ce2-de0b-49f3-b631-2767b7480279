# IACT MIO維保管理系統 部署說明

## 📦 部署方案

### 方案一：便攜式網頁應用包 (推薦)

#### 🎯 適用場景
- 單機使用或小團隊使用
- 不需要複雜的伺服器設定
- 快速部署和使用
- 數據存儲在本地瀏覽器

#### 📋 部署步驟

##### 1. 準備部署包
將以下檔案複製到目標電腦：
```
維修記錄管理工具/
├── complete-system-gui.html          # 主程式檔案
├── run.bat                          # Windows 啟動腳本
├── 啟動維修記錄管理系統.vbs          # 無視窗啟動腳本
├── 部署說明.md                      # 本說明檔案
└── README.md                        # 系統說明
```

##### 2. 系統需求
- **作業系統**：Windows 7/8/10/11, macOS, Linux
- **瀏覽器**：Chrome 80+, Firefox 75+, Edge 80+, Safari 13+
- **硬碟空間**：< 1MB (程式檔案)
- **記憶體**：512MB 可用記憶體
- **網路**：僅 SharePoint 整合時需要

##### 3. 安裝步驟
1. **複製檔案**：將整個資料夾複製到目標電腦
2. **雙擊啟動**：
   - Windows: 雙擊 `run.bat` 或 `啟動維修記錄管理系統.vbs`
   - macOS/Linux: 雙擊 `complete-system-gui.html`
3. **瀏覽器開啟**：系統會自動在預設瀏覽器中開啟

#### ✅ 優點
- **零安裝**：無需安裝任何軟體
- **跨平台**：支援所有主流作業系統
- **便攜性**：可放在 USB 隨身碟中使用
- **數據安全**：數據存儲在本地，不會外洩

#### ⚠️ 注意事項
- 數據存儲在瀏覽器的 LocalStorage 中
- 清除瀏覽器數據會導致資料遺失
- 建議定期使用系統內建的匯出功能備份數據

---

### 方案二：本地伺服器部署

#### 🎯 適用場景
- 多用戶協作使用
- 需要集中數據管理
- 區域網路內共享使用

#### 📋 部署步驟

##### 1. 安裝 Node.js 伺服器
```bash
# 安裝 Node.js (https://nodejs.org/)
# 建立專案資料夾
mkdir maintenance-system
cd maintenance-system

# 初始化專案
npm init -y

# 安裝 Express 伺服器
npm install express cors
```

##### 2. 建立伺服器檔案
建立 `server.js`：
```javascript
const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 啟用 CORS
app.use(cors());

// 靜態檔案服務
app.use(express.static('.'));

// 主頁路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'complete-system-gui.html'));
});

// 啟動伺服器
app.listen(PORT, () => {
    console.log(`維保管理系統已啟動: http://localhost:${PORT}`);
    console.log(`區域網路存取: http://[您的IP]:${PORT}`);
});
```

##### 3. 啟動伺服器
```bash
# 啟動伺服器
node server.js

# 或建立啟動腳本 start.bat
echo "node server.js" > start.bat
```

##### 4. 存取系統
- **本機存取**：http://localhost:3000
- **區域網路存取**：http://[電腦IP]:3000

#### ✅ 優點
- **多用戶存取**：支援多人同時使用
- **集中管理**：統一的數據存取點
- **網路存取**：區域網路內任何設備都可存取

#### ⚠️ 注意事項
- 需要安裝 Node.js 環境
- 需要開放防火牆端口 (3000)
- 數據仍存儲在各用戶的瀏覽器中

---

### 方案三：雲端部署

#### 🎯 適用場景
- 遠端存取需求
- 多地點協作
- 專業的雲端託管

#### 📋 部署選項

##### 1. GitHub Pages (免費)
```bash
# 1. 建立 GitHub 倉庫
# 2. 上傳檔案到倉庫
# 3. 啟用 GitHub Pages
# 4. 存取 https://username.github.io/repository-name
```

##### 2. Netlify (免費)
```bash
# 1. 註冊 Netlify 帳號
# 2. 拖拽檔案到 Netlify
# 3. 獲得自動生成的網址
```

##### 3. Vercel (免費)
```bash
# 1. 安裝 Vercel CLI
npm i -g vercel

# 2. 部署
vercel

# 3. 獲得部署網址
```

#### ✅ 優點
- **全球存取**：任何地方都可存取
- **自動備份**：雲端自動備份
- **高可用性**：專業的雲端基礎設施

#### ⚠️ 注意事項
- 需要網路連線
- 可能有存取速度限制
- 免費方案有使用限制

---

## 🔧 打包工具腳本

### Windows 打包腳本
建立 `package.bat`：
```batch
@echo off
echo 正在建立部署包...

mkdir "IACT_MIO維保管理系統_v1.0"
copy "complete-system-gui.html" "IACT_MIO維保管理系統_v1.0\"
copy "run.bat" "IACT_MIO維保管理系統_v1.0\"
copy "啟動維修記錄管理系統.vbs" "IACT_MIO維保管理系統_v1.0\"
copy "部署說明.md" "IACT_MIO維保管理系統_v1.0\"

echo 建立使用說明...
echo # IACT MIO維保管理系統 > "IACT_MIO維保管理系統_v1.0\使用說明.txt"
echo. >> "IACT_MIO維保管理系統_v1.0\使用說明.txt"
echo 1. 雙擊 run.bat 啟動系統 >> "IACT_MIO維保管理系統_v1.0\使用說明.txt"
echo 2. 系統會在瀏覽器中開啟 >> "IACT_MIO維保管理系統_v1.0\使用說明.txt"
echo 3. 使用測試帳號登入體驗功能 >> "IACT_MIO維保管理系統_v1.0\使用說明.txt"
echo 4. 數據會自動保存在瀏覽器中 >> "IACT_MIO維保管理系統_v1.0\使用說明.txt"

echo 部署包建立完成！
echo 資料夾：IACT_MIO維保管理系統_v1.0
pause
```

### 跨平台啟動腳本
建立 `start.sh` (macOS/Linux)：
```bash
#!/bin/bash
echo "啟動 IACT MIO維保管理系統..."

# 檢查作業系統
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open complete-system-gui.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open complete-system-gui.html
else
    echo "請手動開啟 complete-system-gui.html"
fi
```

---

## 📱 行動裝置支援

### 響應式設計
系統已支援行動裝置存取：
- **手機瀏覽器**：Chrome Mobile, Safari Mobile
- **平板電腦**：iPad, Android 平板
- **觸控操作**：優化的觸控界面

### PWA (Progressive Web App) 支援
可將系統安裝為手機應用：
1. 在手機瀏覽器中開啟系統
2. 點擊 "加入主畫面" 或 "安裝應用"
3. 系統會像原生應用一樣運行

---

## 🔒 數據安全和備份

### 數據存儲位置
- **LocalStorage**：存儲在瀏覽器本地
- **位置**：
  - Windows: `%LOCALAPPDATA%\Google\Chrome\User Data\Default\Local Storage`
  - macOS: `~/Library/Application Support/Google/Chrome/Default/Local Storage`

### 備份建議
1. **定期匯出**：使用系統內建的匯出功能
2. **多重備份**：將備份檔案存放在多個位置
3. **版本控制**：保留多個版本的備份檔案

### 數據遷移
```javascript
// 匯出數據 (在瀏覽器控制台執行)
const data = {};
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.startsWith('maintenance_')) {
        data[key] = localStorage.getItem(key);
    }
}
console.log(JSON.stringify(data, null, 2));

// 匯入數據
const importData = { /* 貼上匯出的數據 */ };
Object.keys(importData).forEach(key => {
    localStorage.setItem(key, importData[key]);
});
```

---

## 🚀 快速部署指南

### 5分鐘快速部署
1. **下載檔案**：複製 `complete-system-gui.html`
2. **重新命名**：改名為 `index.html` (可選)
3. **雙擊開啟**：直接在瀏覽器中開啟
4. **開始使用**：系統立即可用

### USB 隨身碟部署
1. **複製到 USB**：將所有檔案複製到 USB 隨身碟
2. **插入目標電腦**：將 USB 插入要使用的電腦
3. **執行程式**：雙擊 `run.bat` 或 `complete-system-gui.html`
4. **立即使用**：系統在任何電腦上都能運行

### 網路共享部署
1. **放置共享資料夾**：將檔案放在網路共享資料夾
2. **設定權限**：確保用戶有讀取權限
3. **建立捷徑**：在桌面建立指向檔案的捷徑
4. **多人存取**：多個用戶可同時存取

---

## 📞 技術支援

### 常見問題
1. **系統無法開啟**：檢查瀏覽器版本，建議使用 Chrome
2. **數據消失**：檢查是否清除了瀏覽器數據
3. **功能異常**：重新整理頁面或重啟瀏覽器
4. **SharePoint 連線失敗**：檢查網路連線和權限設定

### 系統需求檢查
```javascript
// 在瀏覽器控制台執行，檢查系統相容性
console.log('瀏覽器:', navigator.userAgent);
console.log('LocalStorage 支援:', typeof(Storage) !== "undefined");
console.log('螢幕解析度:', screen.width + 'x' + screen.height);
```

### 效能優化建議
- 定期清理瀏覽器快取
- 關閉不必要的瀏覽器分頁
- 確保有足夠的可用記憶體
- 定期重啟瀏覽器

**🎉 選擇最適合您需求的部署方案，享受專業的維保管理系統！**
