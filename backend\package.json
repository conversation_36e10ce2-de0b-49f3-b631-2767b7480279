{"name": "repair-management-backend", "version": "1.0.0", "description": "客退維修品記錄管理系統 - 後端API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/prisma/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.0.1", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0", "express-validator": "^6.14.3", "@prisma/client": "^4.10.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.0.3", "multer": "^1.4.5-lts.1", "sharp": "^0.31.3", "@azure/msal-node": "^1.15.0", "@microsoft/microsoft-graph-client": "^3.0.4", "isomorphic-fetch": "^3.0.0", "nodemailer": "^6.9.1", "cron": "^2.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/node": "^18.14.0", "@types/nodemailer": "^6.4.7", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "eslint": "^8.34.0", "prettier": "^2.8.4", "typescript": "^4.9.5", "tsx": "^3.12.3", "prisma": "^4.10.0", "@types/jest": "^29.4.0", "jest": "^29.4.0", "ts-jest": "^29.0.5", "supertest": "^6.3.3", "@types/supertest": "^2.0.12"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["repair-management", "express", "typescript", "prisma", "sharepoint", "api"], "author": "維修管理系統開發團隊", "license": "MIT"}