import { UserR<PERSON> } from '@prisma/client';
import { UserRepository } from '../repositories/userRepository';
import { PasswordUtils } from '../utils/password';
import { logger } from '../utils/logger';
import { 
  UserInfo, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserQueryParams,
  UserListResponse,
  UserStatistics,
  BatchUserOperation,
  BatchOperationResult,
  UserValidationResult,
  UserSearchResult
} from '../types/user';

export class UserService {
  private userRepository: UserRepository;

  constructor(userRepository: UserRepository) {
    this.userRepository = userRepository;
  }

  // 根據ID獲取用戶
  async getUserById(id: string): Promise<UserInfo | null> {
    try {
      return await this.userRepository.findById(id);
    } catch (error) {
      logger.error('獲取用戶失敗:', error);
      throw new Error('獲取用戶失敗');
    }
  }

  // 根據用戶名獲取用戶
  async getUserByUsername(username: string): Promise<UserInfo | null> {
    try {
      const user = await this.userRepository.findByUsername(username);
      if (!user) return null;

      return {
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      logger.error('根據用戶名獲取用戶失敗:', error);
      throw new Error('獲取用戶失敗');
    }
  }

  // 創建用戶
  async createUser(userData: CreateUserRequest, createdBy?: string): Promise<UserInfo> {
    try {
      // 驗證用戶資料
      const validation = await this.validateUserData(userData);
      if (!validation.isValid) {
        throw new Error(`用戶資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 檢查用戶名和電子郵件是否已存在
      const existence = await this.userRepository.checkExistence(
        userData.username,
        userData.email
      );

      if (existence.usernameExists) {
        throw new Error('用戶名已存在');
      }

      if (existence.emailExists) {
        throw new Error('電子郵件已存在');
      }

      // 處理密碼
      let passwordHash: string;
      if (userData.password) {
        // 驗證密碼強度
        const passwordValidation = PasswordUtils.validatePassword(userData.password);
        if (!passwordValidation.isValid) {
          throw new Error(`密碼不符合要求: ${passwordValidation.errors.join(', ')}`);
        }
        passwordHash = await PasswordUtils.hashPassword(userData.password);
      } else {
        // 生成隨機密碼
        const randomPassword = PasswordUtils.generateSecurePassword(12);
        passwordHash = await PasswordUtils.hashPassword(randomPassword);
        
        // 記錄生成的密碼（實際應用中應該通過安全方式發送給用戶）
        logger.info('為用戶生成隨機密碼:', {
          username: userData.username,
          // password: randomPassword // 生產環境中不應記錄密碼
        });
      }

      // 創建用戶
      const newUser = await this.userRepository.create({
        ...userData,
        passwordHash,
      });

      logger.info('用戶創建成功:', { 
        userId: newUser.id, 
        username: newUser.username,
        createdBy 
      });

      return newUser;
    } catch (error) {
      logger.error('創建用戶失敗:', error);
      throw error;
    }
  }

  // 更新用戶
  async updateUser(id: string, userData: UpdateUserRequest, updatedBy?: string): Promise<UserInfo> {
    try {
      // 檢查用戶是否存在
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new Error('用戶不存在');
      }

      // 如果更新電子郵件，檢查是否已存在
      if (userData.email && userData.email !== existingUser.email) {
        const existence = await this.userRepository.checkExistence(
          existingUser.username,
          userData.email,
          id
        );

        if (existence.emailExists) {
          throw new Error('電子郵件已存在');
        }
      }

      // 驗證更新資料
      const validation = await this.validateUpdateData(userData);
      if (!validation.isValid) {
        throw new Error(`更新資料驗證失敗: ${validation.errors.join(', ')}`);
      }

      // 更新用戶
      const updatedUser = await this.userRepository.update(id, userData);

      logger.info('用戶更新成功:', { 
        userId: id, 
        username: updatedUser.username,
        updatedBy,
        changes: Object.keys(userData)
      });

      return updatedUser;
    } catch (error) {
      logger.error('更新用戶失敗:', error);
      throw error;
    }
  }

  // 刪除用戶
  async deleteUser(id: string, deletedBy?: string, hardDelete: boolean = false): Promise<void> {
    try {
      // 檢查用戶是否存在
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw new Error('用戶不存在');
      }

      // 檢查是否可以刪除（例如：不能刪除最後一個管理員）
      if (existingUser.role === UserRole.ADMIN) {
        const adminCount = await this.getAdminCount();
        if (adminCount <= 1) {
          throw new Error('不能刪除最後一個管理員');
        }
      }

      if (hardDelete) {
        await this.userRepository.hardDelete(id);
        logger.info('用戶硬刪除成功:', { userId: id, deletedBy });
      } else {
        await this.userRepository.softDelete(id);
        logger.info('用戶軟刪除成功:', { userId: id, deletedBy });
      }
    } catch (error) {
      logger.error('刪除用戶失敗:', error);
      throw error;
    }
  }

  // 獲取用戶列表
  async getUserList(params: UserQueryParams): Promise<UserListResponse> {
    try {
      return await this.userRepository.findMany(params);
    } catch (error) {
      logger.error('獲取用戶列表失敗:', error);
      throw new Error('獲取用戶列表失敗');
    }
  }

  // 搜尋用戶
  async searchUsers(query: string, limit: number = 10): Promise<UserSearchResult[]> {
    try {
      if (query.length < 2) {
        throw new Error('搜尋關鍵字至少需要2個字符');
      }

      return await this.userRepository.search(query, limit);
    } catch (error) {
      logger.error('搜尋用戶失敗:', error);
      throw error;
    }
  }

  // 獲取用戶統計
  async getUserStatistics(): Promise<UserStatistics> {
    try {
      return await this.userRepository.getStatistics();
    } catch (error) {
      logger.error('獲取用戶統計失敗:', error);
      throw new Error('獲取用戶統計失敗');
    }
  }

  // 批量操作用戶
  async batchOperation(operation: BatchUserOperation, operatedBy?: string): Promise<BatchOperationResult> {
    try {
      // 驗證批量操作
      if (operation.userIds.length === 0) {
        throw new Error('未選擇任何用戶');
      }

      if (operation.userIds.length > 100) {
        throw new Error('批量操作用戶數量不能超過100個');
      }

      // 特殊檢查：如果是刪除或停用管理員
      if (operation.operation === 'delete' || operation.operation === 'deactivate') {
        const adminCount = await this.getAdminCount();
        const adminIds = await this.getAdminIds();
        const affectedAdmins = operation.userIds.filter(id => adminIds.includes(id));
        
        if (affectedAdmins.length > 0 && adminCount - affectedAdmins.length < 1) {
          throw new Error('操作會導致沒有可用的管理員');
        }
      }

      const result = await this.userRepository.batchOperation(operation);

      logger.info('批量操作完成:', { 
        operation: operation.operation,
        userCount: operation.userIds.length,
        success: result.success,
        failed: result.failed,
        operatedBy
      });

      return result;
    } catch (error) {
      logger.error('批量操作失敗:', error);
      throw error;
    }
  }

  // 激活用戶
  async activateUser(id: string, activatedBy?: string): Promise<UserInfo> {
    try {
      return await this.updateUser(id, { isActive: true }, activatedBy);
    } catch (error) {
      logger.error('激活用戶失敗:', error);
      throw error;
    }
  }

  // 停用用戶
  async deactivateUser(id: string, deactivatedBy?: string): Promise<UserInfo> {
    try {
      // 檢查是否是管理員
      const user = await this.userRepository.findById(id);
      if (user?.role === UserRole.ADMIN) {
        const adminCount = await this.getAdminCount();
        if (adminCount <= 1) {
          throw new Error('不能停用最後一個管理員');
        }
      }

      return await this.updateUser(id, { isActive: false }, deactivatedBy);
    } catch (error) {
      logger.error('停用用戶失敗:', error);
      throw error;
    }
  }

  // 更改用戶角色
  async changeUserRole(id: string, newRole: UserRole, changedBy?: string): Promise<UserInfo> {
    try {
      // 檢查是否是降級最後一個管理員
      const user = await this.userRepository.findById(id);
      if (user?.role === UserRole.ADMIN && newRole !== UserRole.ADMIN) {
        const adminCount = await this.getAdminCount();
        if (adminCount <= 1) {
          throw new Error('不能降級最後一個管理員');
        }
      }

      return await this.updateUser(id, { role: newRole }, changedBy);
    } catch (error) {
      logger.error('更改用戶角色失敗:', error);
      throw error;
    }
  }

  // 驗證用戶資料
  private async validateUserData(userData: CreateUserRequest): Promise<UserValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證用戶名
    if (!userData.username || userData.username.length < 3) {
      errors.push('用戶名至少需要3個字符');
    }

    if (userData.username && userData.username.length > 50) {
      errors.push('用戶名不能超過50個字符');
    }

    if (userData.username && !/^[a-zA-Z0-9_]+$/.test(userData.username)) {
      errors.push('用戶名只能包含字母、數字和下劃線');
    }

    // 驗證電子郵件
    if (!userData.email) {
      errors.push('電子郵件不能為空');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      errors.push('電子郵件格式無效');
    }

    // 驗證姓名
    if (!userData.fullName || userData.fullName.length < 2) {
      errors.push('姓名至少需要2個字符');
    }

    if (userData.fullName && userData.fullName.length > 100) {
      errors.push('姓名不能超過100個字符');
    }

    // 驗證角色
    if (!Object.values(UserRole).includes(userData.role)) {
      errors.push('無效的用戶角色');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 驗證更新資料
  private async validateUpdateData(userData: UpdateUserRequest): Promise<UserValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 驗證電子郵件
    if (userData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      errors.push('電子郵件格式無效');
    }

    // 驗證姓名
    if (userData.fullName !== undefined) {
      if (userData.fullName.length < 2) {
        errors.push('姓名至少需要2個字符');
      }
      if (userData.fullName.length > 100) {
        errors.push('姓名不能超過100個字符');
      }
    }

    // 驗證角色
    if (userData.role && !Object.values(UserRole).includes(userData.role)) {
      errors.push('無效的用戶角色');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // 獲取管理員數量
  private async getAdminCount(): Promise<number> {
    const stats = await this.userRepository.getStatistics();
    return stats.byRole[UserRole.ADMIN] || 0;
  }

  // 獲取管理員ID列表
  private async getAdminIds(): Promise<string[]> {
    const adminUsers = await this.userRepository.findMany({
      role: UserRole.ADMIN,
      isActive: true,
    });
    return adminUsers.users.map(user => user.id);
  }
}
