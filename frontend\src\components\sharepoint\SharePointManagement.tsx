import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Typo<PERSON>, 
  <PERSON>,
  Button,
  message,
  Alert
} from 'antd';
import { 
  CloudOutlined,
  SettingOutlined,
  FileOutlined,
  SyncOutlined,
  TeamOutlined,
  TemplateOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import SharePointConfig from './SharePointConfig';
import FileManager from './FileManager';
import DocumentSync from './DocumentSync';
import DocumentTemplates from './DocumentTemplates';
import Office365Integration from './Office365Integration';
import SharePointOverview from './SharePointOverview';

const { Title } = Typography;
const { TabPane } = Tabs;

const SharePointManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing'>('disconnected');

  const handleTestConnection = async () => {
    setConnectionStatus('testing');
    setLoading(true);
    try {
      // 這裡會調用 sharepointService.testConnection()
      await new Promise(resolve => setTimeout(resolve, 2000));
      setConnectionStatus('connected');
      message.success('SharePoint連接測試成功');
    } catch (error) {
      setConnectionStatus('disconnected');
      message.error('SharePoint連接測試失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    setLoading(true);
    try {
      // 這裡會調用 sharepointService.syncAllFiles()
      await new Promise(resolve => setTimeout(resolve, 3000));
      message.success('文檔同步完成');
    } catch (error) {
      message.error('文檔同步失敗');
    } finally {
      setLoading(false);
    }
  };

  const getConnectionStatusAlert = () => {
    if (connectionStatus === 'connected') {
      return (
        <Alert
          message="SharePoint連接正常"
          description="系統已成功連接到SharePoint，可以正常使用文檔管理功能。"
          type="success"
          showIcon
          icon={<CheckCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      );
    } else if (connectionStatus === 'testing') {
      return (
        <Alert
          message="正在測試連接"
          description="正在測試SharePoint連接，請稍候..."
          type="info"
          showIcon
          icon={<SyncOutlined spin />}
          style={{ marginBottom: 16 }}
        />
      );
    } else {
      return (
        <Alert
          message="SharePoint未連接"
          description="請先配置SharePoint設定並測試連接，然後才能使用文檔管理功能。"
          type="warning"
          showIcon
          icon={<ExclamationCircleOutlined />}
          action={
            <Button size="small" onClick={handleTestConnection}>
              測試連接
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      );
    }
  };

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <CloudOutlined />
          總覽
        </span>
      ),
      children: <SharePointOverview connectionStatus={connectionStatus} />,
    },
    {
      key: 'config',
      label: (
        <span>
          <SettingOutlined />
          SharePoint設定
        </span>
      ),
      children: <SharePointConfig onConnectionChange={setConnectionStatus} />,
    },
    {
      key: 'files',
      label: (
        <span>
          <FileOutlined />
          文件管理
        </span>
      ),
      children: <FileManager />,
      disabled: connectionStatus !== 'connected',
    },
    {
      key: 'sync',
      label: (
        <span>
          <SyncOutlined />
          文檔同步
        </span>
      ),
      children: <DocumentSync />,
      disabled: connectionStatus !== 'connected',
    },
    {
      key: 'templates',
      label: (
        <span>
          <TemplateOutlined />
          文檔模板
        </span>
      ),
      children: <DocumentTemplates />,
      disabled: connectionStatus !== 'connected',
    },
    {
      key: 'office365',
      label: (
        <span>
          <TeamOutlined />
          Office 365
        </span>
      ),
      children: <Office365Integration />,
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            ☁️ SharePoint整合
          </Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleTestConnection}
              loading={loading && connectionStatus === 'testing'}
            >
              測試連接
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={handleSync}
              loading={loading && connectionStatus !== 'testing'}
              disabled={connectionStatus !== 'connected'}
            >
              同步文檔
            </Button>
          </Space>
        </div>
      </div>

      {getConnectionStatusAlert()}

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </div>
  );
};

export default SharePointManagement;
