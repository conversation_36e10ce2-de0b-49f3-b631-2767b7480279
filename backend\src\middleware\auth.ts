import { Request, Response, NextFunction } from 'express';
import { JWTUtils, JWTPayload } from '../utils/jwt';
import { logger } from '../utils/logger';
import { createError } from './errorHandler';

// 擴展 Request 介面以包含用戶資訊
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

// 用戶角色枚舉
export enum UserRole {
  ADMIN = 'ADMIN',
  TECHNICIAN = 'TECHNICIAN',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE',
  VIEWER = 'VIEWER',
}

// 權限等級映射
const ROLE_HIERARCHY: Record<UserRole, number> = {
  [UserRole.VIEWER]: 1,
  [UserRole.CUSTOMER_SERVICE]: 2,
  [UserRole.TECHNICIAN]: 3,
  [UserRole.ADMIN]: 4,
};

// 認證中間件
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 從 Authorization header 獲取 token
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      throw createError('未提供認證令牌', 401);
    }

    // 檢查 Bearer token 格式
    const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
    if (!tokenMatch) {
      throw createError('認證令牌格式錯誤', 401);
    }

    const token = tokenMatch[1];

    // 驗證 token
    const payload = JWTUtils.verifyAccessToken(token);
    
    // 檢查 token 是否即將過期
    if (JWTUtils.isTokenExpiringSoon(token)) {
      res.setHeader('X-Token-Refresh-Needed', 'true');
    }

    // 將用戶資訊添加到請求對象
    req.user = payload;
    
    logger.debug(`用戶認證成功: ${payload.username} (${payload.role})`);
    next();
  } catch (error) {
    logger.warn('認證失敗:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    });

    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        next(createError('認證令牌已過期', 401));
      } else if (error.message.includes('invalid')) {
        next(createError('無效的認證令牌', 401));
      } else {
        next(createError('認證失敗', 401));
      }
    } else {
      next(createError('認證失敗', 401));
    }
  }
};

// 可選認證中間件（允許匿名訪問）
export const optionalAuthenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader) {
      const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
      if (tokenMatch) {
        const token = tokenMatch[1];
        const payload = JWTUtils.verifyAccessToken(token);
        req.user = payload;
        
        logger.debug(`可選認證成功: ${payload.username}`);
      }
    }
    
    next();
  } catch (error) {
    // 可選認證失敗時不阻止請求，但記錄日誌
    logger.debug('可選認證失敗，繼續處理請求:', error);
    next();
  }
};

// 角色授權中間件
export const authorize = (...allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const userRole = req.user.role as UserRole;
      
      // 檢查用戶角色是否在允許的角色列表中
      if (!allowedRoles.includes(userRole)) {
        logger.warn('權限不足:', {
          userId: req.user.userId,
          userRole,
          allowedRoles,
          path: req.path,
          method: req.method,
        });
        
        throw createError('權限不足', 403);
      }

      logger.debug(`權限檢查通過: ${req.user.username} (${userRole})`);
      next();
    } catch (error) {
      next(error);
    }
  };
};

// 最小權限等級授權中間件
export const authorizeMinLevel = (minRole: UserRole) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const userRole = req.user.role as UserRole;
      const userLevel = ROLE_HIERARCHY[userRole];
      const minLevel = ROLE_HIERARCHY[minRole];

      if (userLevel < minLevel) {
        logger.warn('權限等級不足:', {
          userId: req.user.userId,
          userRole,
          userLevel,
          minRole,
          minLevel,
          path: req.path,
          method: req.method,
        });
        
        throw createError('權限等級不足', 403);
      }

      logger.debug(`權限等級檢查通過: ${req.user.username} (${userRole}, level: ${userLevel})`);
      next();
    } catch (error) {
      next(error);
    }
  };
};

// 資源所有者或管理員授權中間件
export const authorizeOwnerOrAdmin = (getUserIdFromParams: (req: Request) => string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createError('用戶未認證', 401);
      }

      const userRole = req.user.role as UserRole;
      const userId = req.user.userId;
      const resourceUserId = getUserIdFromParams(req);

      // 管理員可以訪問所有資源
      if (userRole === UserRole.ADMIN) {
        logger.debug(`管理員權限通過: ${req.user.username}`);
        next();
        return;
      }

      // 資源所有者可以訪問自己的資源
      if (userId === resourceUserId) {
        logger.debug(`資源所有者權限通過: ${req.user.username}`);
        next();
        return;
      }

      logger.warn('資源訪問權限不足:', {
        userId,
        resourceUserId,
        userRole,
        path: req.path,
        method: req.method,
      });

      throw createError('無權訪問此資源', 403);
    } catch (error) {
      next(error);
    }
  };
};

// 檢查用戶是否為活躍狀態的中間件
export const requireActiveUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw createError('用戶未認證', 401);
    }

    // 這裡可以添加檢查用戶是否為活躍狀態的邏輯
    // 例如從資料庫查詢用戶狀態
    // const user = await getUserById(req.user.userId);
    // if (!user.isActive) {
    //   throw createError('用戶帳戶已被停用', 403);
    // }

    next();
  } catch (error) {
    next(error);
  }
};

// API 金鑰認證中間件（用於系統間調用）
export const authenticateApiKey = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    const validApiKey = process.env.API_KEY;

    if (!apiKey) {
      throw createError('未提供 API 金鑰', 401);
    }

    if (!validApiKey) {
      throw createError('API 金鑰未配置', 500);
    }

    if (apiKey !== validApiKey) {
      logger.warn('無效的 API 金鑰嘗試:', {
        providedKey: apiKey.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      throw createError('無效的 API 金鑰', 401);
    }

    logger.debug('API 金鑰認證成功');
    next();
  } catch (error) {
    next(error);
  }
};

// 組合認證中間件（JWT 或 API 金鑰）
export const authenticateJwtOrApiKey = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;
  const apiKey = req.headers['x-api-key'];

  if (authHeader) {
    // 嘗試 JWT 認證
    authenticate(req, res, next);
  } else if (apiKey) {
    // 嘗試 API 金鑰認證
    authenticateApiKey(req, res, next);
  } else {
    next(createError('未提供認證憑證', 401));
  }
};

// 導出角色常量
export { UserRole as Role };

// 工具函數：檢查用戶是否有特定權限
export const hasPermission = (userRole: string, requiredRole: UserRole): boolean => {
  const userLevel = ROLE_HIERARCHY[userRole as UserRole];
  const requiredLevel = ROLE_HIERARCHY[requiredRole];
  
  return userLevel >= requiredLevel;
};

// 工具函數：獲取用戶權限等級
export const getUserPermissionLevel = (userRole: string): number => {
  return ROLE_HIERARCHY[userRole as UserRole] || 0;
};
