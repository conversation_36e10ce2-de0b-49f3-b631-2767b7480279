import React from 'react';
import { Card, Typography } from 'antd';
import { StatisticsQueryParams } from '../../services/statisticsService';

const { Title } = Typography;

interface CustomerAnalyticsProps {
  filters: StatisticsQueryParams;
}

const CustomerAnalytics: React.FC<CustomerAnalyticsProps> = ({ filters }) => {
  return (
    <Card>
      <Title level={3}>客戶分析</Title>
      <p>客戶分析功能開發中...</p>
      <ul>
        <li>客戶價值分析</li>
        <li>客戶忠誠度統計</li>
        <li>客戶維修頻率分析</li>
        <li>客戶滿意度趨勢</li>
      </ul>
    </Card>
  );
};

export default CustomerAnalytics;
